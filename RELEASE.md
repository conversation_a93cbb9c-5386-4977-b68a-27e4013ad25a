# Voice Release Instructions

## Inital setup

It is required to have 'develop' branch which is based on nexmo-ops/develop
and 'master' which is connected to nexmoinc/master

For example:

$ git remote -v
nexmoinc        **************:nexmoinc/voice.git (fetch)
nexmoinc        **************:nexmoinc/voice.git (push)
origin  **************:nexmo-ops/voice.git (fetch)
origin  **************:nexmo-ops/voice.git (push)

*** In the above example: 
origin is the name of the remote repo of nexmo-ops
nexmoinc is the name of the remote repo of nexmoince

## Setting up the master branch

Make sure you are in develop branch and have the latest version:

```bash
$ git checkout develop                   # change to develop
$ git fetch origin develop               # fetch HEAD
$ git pull --ff-only origin develop      # pull changes
```
Make sure the CHANGELIST.md file is updated with your latest changes list and the tag number (x.y.z) of this release.
Take a note of the head version (will be used later in the merge)

Afterwards, change to the master branch update your local copy:

```bash
$ git checkout [local-master-branch-name]                    # change to master
$ git fetch [remote-name]              # fetch HEAD
$ git pull --ff-only [remote-name] [master-branch-name]      # pull changes
```

For example:
```bash
$ git checkout master                   # change to master
$ git fetch origin master               # fetch HEAD
$ git pull --ff-only origin master      # pull changes
```

Or:
```bash
$ git checkout nexmoincmaster                   # change to master
$ git fetch nexmoinc                # fetch HEAD
$ git pull --ff-only nexmoinc nexmoincmaster      # pull changes
```

Take a note of the head version (will be used later as the rollback target)

Merge the commit from the develop branch you wish to release, into the master branch:

```bash
$ git merge --ff-only <commit-sha>
```

For example: While d2ffaf3 is the latest commit-id on your develop branch
git merge d2ffaf3


Tag the release. Use [semantic versioning](http://semver.org/) for tagging the release. 
The version number you choose here is important, because **you will use it in the JIRA release** as well.

```bash
$ git tag -a x.y.z -m 'bugfix'
```
Looking for the last release: Jira => Releases 
Or
Check the git log for the latest tag

In any case:
Verify it is matching the CHANGELIST.md content

Once you are comfortable with everything, you can push the branch and the tag

```bash
$ git push [remote-name] [local-master-branch-name]:[remote-master-branch-name]   # pushing the branch
$ git push [remote-name] <x.y.z>  # pushing the tag
```

For example:
```bash
$ git push origin master   # pushing the branch  - in this cas 'master is both local and remote branch name
$ git push origin <x.y.z>  # pushing the tag
```

Or

```bash
$ git push nexmoinc nexmoincmaster:master # pushing the branch  
$ git push nexmoinc <x.y.z>  # pushing the tag
```

Take a note of the head version (will be used as the production deployment reference)


## Sharing the release intent with Operations and Support

Create a ticket in JIRA, on the OPS board, so that your friendly OPS person is aware of the release. Set the ticket type as **Deploy** and fill the following information:

```
deploy: <commit-hash>
rollback: <rollback-hash>
changes: <any changes operations will need to make as part of the deployment>
```

Link all the new features' epics to the deployment JIRA.

Inform the support team at least 24 hours before you do the release if there is going to be downtime during the release. 

## Finalising the release in JIRA

Move all the relevant tickets to the **Ready to release** column, and click the **Release** link. 

The version you put here **should be the same** as the one you have used for that tag.

## Smoke tests and Notifying about the release

* After the Operations ticket has been completed, please inform the QA's to run the smoke tests.

## Prepare the next release
Add the new next tag to CHANGELIST.md and checki-in to deployment

## Checklist

* Did you merge all the feature you want to release to the `develop` branch?
* Did all the regressions pass?
* Did you take note of the commit hash you wish to merge on to the `master` branch?
* Did you ran the regressions against the chosen commit hash in the `develop` branch?
* Did you take note of the commit hash you need to rollback to, in case something goes wrong?
* Did you merge the commit hash on to the `master` branch?
* Did you create an OPS ticket that states **the commit hash to be deployed** and **the rollback hash** and inclued any **changes** operations need to make?
* Did you assign that ticket to the proper OPS person?
