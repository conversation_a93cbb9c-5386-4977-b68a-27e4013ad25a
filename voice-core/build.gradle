apply plugin:'java'

group 'com.nexmo'

project.ext {
    //FIXME: This is ignored and we get nexmo-utils-********.180536.000+nexmoinc+master+********1.jar
    nexmoUtilsVersion = '********.150459.000+nexmoinc+master+********1'

    //Seems like we do not use these libs any longer
    //nexmoMediaClientVersion = '********.112947.000+nexmo-ops+master+********1'
    //nexmoCountersClientVersion = '********.105521.000+nexmoinc+master+********1'

    //Messaging.jar include a reference to a specific release of com.googlecode.libphonenumber:libphonenumber
    //As part of SIP-227 we would like to be able to update this library version without upgrading the whole
    //messaging.jar. SIP-227 relates to adding support for Vietnamise numbers (prefix 84)

    //The messaging.jar latest version (27/11/2018) includes com.googlecode.libphonenumber:libphonenumber:8.9.16
    //SIPApp/ TTSApp should use the latest library as provided by messaging.jar in order to maintain the same level
    //of numer verification results.

    //Every specific number which requires an upgrade to this library should be included in the tests at
    //voice-core/src/test/java/com/nexmo/voice/core/api/util/GooglePhonenumberLibTest.java

    libphonenumberVersion = '8.13.43'
    asteriskClientVersion = '3.34.0'
    caffeineVersion = '2.8.8'
    //logVersion is commented out as messaging jar has version 2.17.1
    //logVersion = '2.17.0'
    authVersion = '20200527.105138.000+nexmoinc+master+********1'
    hazelcastVersion = '4.2.6'
    mchangeVersion = '*******'
    h2Version = '2.1.210'
    xmlbeansVersion = '3.0.0'
}

if (System.getenv('ARTIFACTORY_HOME')) {
    def artifactoryHome = System.getenv('ARTIFACTORY_HOME')
    println("Using ARTIFACTORY_HOME=$artifactoryHome")
    repositories {
        mavenCentral()
        maven {url "http://$artifactoryHome/artifactory/libs-release"}
        maven {url "http://$artifactoryHome/artifactory/plugins-release-local"}
    }
}

dependencies {


    def excludeLibs = { exclude group: 'com.googlecode.libphonenumber', module: 'libphonenumber'
                        exclude group: 'org.asteriskjava', module: 'asterisk-java'
                        //exclude group: 'log4j', module: 'log4j'
                        exclude group: 'org.jvnet.staxex'
                        //exclude group: 'org.apache.logging.log4j'
                        exclude group: 'com.hazelcast', module: 'hazelcast'
                        exclude group: 'com.mchange', module: 'c3p0'
                        exclude group: 'com.h2database', module: 'h2'
                        exclude group: 'org.apache.xmlbeans', module: 'xmlbeans'
    }

    compile "com.nexmo:messaging-voice:$messagingVersion", excludeLibs
    compile "com.googlecode.libphonenumber:libphonenumber:$libphonenumberVersion"
    compile "org.asteriskjava:asterisk-java:$asteriskClientVersion"
    compile "com.github.ben-manes.caffeine:caffeine:$caffeineVersion"

    compile "com.hazelcast:hazelcast:$hazelcastVersion"
    compile "com.mchange:c3p0:$mchangeVersion"
    compile "com.h2database:h2:$h2Version"
    compile "org.apache.xmlbeans:xmlbeans:$xmlbeansVersion"

//    compile "org.apache.logging.log4j:log4j-core:$logVersion"
//    compile "org.apache.logging.log4j:log4j-api:$logVersion"
//    compile "org.apache.logging.log4j:log4j-1.2-api:$logVersion"
//    compile "org.apache.logging.log4j:log4j-iostreams:$logVersion"
//    compile "org.apache.logging.log4j:log4j-jcl:$logVersion"
//    compile "org.apache.logging.log4j:log4j-jul:$logVersion"
//    compile "org.apache.logging.log4j:log4j-slf4j-impl:$logVersion"
//    compile "org.apache.logging.log4j:log4j-taglib:$logVersion"

    compile "com.nexmo:nexmo-utils:$nexmoUtilsVersion"
    //compile "com.nexmo:media-client:$nexmoMediaClientVersion"
    //compile "com.nexmo:********-client:$nexmoCountersClientVersion"
    compile 'io.prometheus:simpleclient:0.12.0'
    compile 'io.prometheus:simpleclient_hotspot:0.12.0'
    compile 'io.prometheus:simpleclient_caffeine:0.12.0'

    compile "com.nexmo:nexmo-inter********-auth:$authVersion"
    compile "com.baidu:leansoft-bigqueue:0.7.3"
    compile 'software.amazon.awssdk:secretsmanager:2.28.2'

    testCompile "org.powermock:powermock-module-junit4:2.0.9"
    testCompile "org.powermock:powermock-api-mockito2:2.0.9"
    testCompile 'org.hamcrest:hamcrest-all:1.3'
    testCompile "junit:junit:4.13.1"
    testCompile "com.github.npathai:hamcrest-optional:2.0.0"
    testCompile 'org.mockito:mockito-core:3.5.0'
    testCompile 'org.mockito:mockito-inline:3.5.0'
}

test {
    testLogging.showExceptions = true
    maxParallelForks = 3
}

apply plugin: "com.nexmo.gradle.common-lib-build"
