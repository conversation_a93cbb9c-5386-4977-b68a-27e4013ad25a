package com.thepeachbeetle.messaging.hub.config.pricing;

/**
 * SIP-1925:
 * The com.thepeachbeetle.messaging.hub.config.pricing.NoNetworkOrPrefixMessagePriceMatrix
 * class is locally overridden in this project to fix this specific issue. If/when it's fixed
 * in messaging.jar, we can consider updating to the corresponding version or back-porting
 * the fix to the -2020-03-30_114334-ce490f6988de6b947220413b928711b44d9feb9a version.
 */

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * @version
 */
public final class NoNetworkOrPrefixMessagePriceMatrix extends SimpleMessagePriceMatrix {

    private static final long serialVersionUID = 5755328903461417532L;

    private static final Logger Log = Logger.getLogger(NoNetworkOrPrefixMessagePriceMatrix.class.getName());

    private Map<String, Collection<Price>> pricesPerAccount;
    private Map<String, Collection<Price>> pricesPerPricingGroup;
    private Collection<Price> pricesAllAccounts;

    private Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerAccount;
    private Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerPricingGroup;
    private NavigableMap<String, Collection<Price>> senderPrefixPricesAllAccounts;

    protected NoNetworkOrPrefixMessagePriceMatrix(final int product,
                                                  final String rootNodeName,
                                                  final boolean performPriceLookup,
                                                  final BigDecimal defaultPrice,
                                                  final boolean rejectSubmissionIfNoPriceFound,
                                                  final boolean useDb,
                                                  final String dbMatrixId,
                                                  final boolean useLdap,
                                                  final String ldapBaseDn,
                                                  final List<Price> prices) {
        super(product,
              rootNodeName,
              performPriceLookup,
              defaultPrice,
              rejectSubmissionIfNoPriceFound,
              useDb,
              dbMatrixId,
              useLdap,
              ldapBaseDn,
              prices);

        populateMaps(prices);
    }

    protected void populateMaps(final List<Price> prices) {

        // generate a set of 'prefix maps' 'per account/price-group'

        final Map<String, Collection<Price>> pricesPerAccount = new HashMap<>();
        final Map<String, Collection<Price>> pricesPerPricingGroup = new HashMap<>();
        final Collection<Price> pricesAllAccounts = new ArrayList<>();

        final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerAccount = new HashMap<>();
        final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerPricingGroup = new HashMap<>();
        final NavigableMap<String, Collection<Price>> senderPrefixPricesAllAccounts = new TreeMap<>();

        if (prices != null)
            for (final Price price: prices)
                populateMapsWithPrice(price,
                                      pricesPerAccount,
                                      pricesPerPricingGroup,
                                      pricesAllAccounts,
                                      senderPrefixPricesPerAccount,
                                      senderPrefixPricesPerPricingGroup,
                                      senderPrefixPricesAllAccounts);

        this.pricesPerAccount = pricesPerAccount;
        this.pricesPerPricingGroup = pricesPerPricingGroup;
        this.pricesAllAccounts = pricesAllAccounts;

        this.senderPrefixPricesPerAccount = senderPrefixPricesPerAccount;
        this.senderPrefixPricesPerPricingGroup = senderPrefixPricesPerPricingGroup;
        this.senderPrefixPricesAllAccounts = senderPrefixPricesAllAccounts;
    }

    private static void populateMapsWithPrice(final Price price,
                                              final Map<String, Collection<Price>> pricesPerAccount,
                                              final Map<String, Collection<Price>> pricesPerPricingGroup,
                                              final Collection<Price> pricesAllAccounts,

                                              final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerAccount,
                                              final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerPricingGroup,
                                              final NavigableMap<String, Collection<Price>> senderPrefixPricesAllAccounts) {
        if (price.getNetwork() != null)
            return;
        if (price.getPrefix() != null)
            return;
        if (price.getAccount() != null && !price.getAccount().trim().equals("")) {
            if (price.getSenderPrefix() == null) {
                Collection<Price> list = pricesPerAccount.get(price.getAccount().trim().toLowerCase());
                if (list == null) {
                    list = new ArrayList<>();
                    pricesPerAccount.put(price.getAccount().trim().toLowerCase(), list);
                }
                list.add(price);
            } else {
                NavigableMap<String, Collection<Price>> map = senderPrefixPricesPerAccount.get(price.getAccount().trim().toLowerCase());
                if (map == null) {
                    map = new TreeMap<>();
                    senderPrefixPricesPerAccount.put(price.getAccount().trim().toLowerCase(), map);
                }
                Collection<Price> list = map.get(price.getSenderPrefix());
                if (list == null) {
                    list = new ArrayList<>();
                    map.put(price.getSenderPrefix(), list);
                }
                list.add(price);
            }
        } else if (price.getPricingGroup() != null && !price.getPricingGroup().equals("")) {
            if (price.getSenderPrefix() == null) {
                Collection<Price> list = pricesPerPricingGroup.get(price.getPricingGroup().trim().toLowerCase());
                if (list == null) {
                    list = new ArrayList<>();
                    pricesPerPricingGroup.put(price.getPricingGroup().trim().toLowerCase(), list);
                }
                list.add(price);
            } else {
                NavigableMap<String, Collection<Price>> map = senderPrefixPricesPerPricingGroup.get(price.getPricingGroup().trim().toLowerCase());
                if (map == null) {
                    map = new TreeMap<>();
                    senderPrefixPricesPerPricingGroup.put(price.getPricingGroup().trim().toLowerCase(), map);
                }
                Collection<Price> list = map.get(price.getSenderPrefix());
                if (list == null) {
                    list = new ArrayList<>();
                    map.put(price.getSenderPrefix(), list);
                }
                list.add(price);
            }
        } else {
            if (price.getSenderPrefix() == null)
                pricesAllAccounts.add(price);
            else {
                Collection<Price> list = senderPrefixPricesAllAccounts.get(price.getSenderPrefix());
                if (list == null) {
                    list = new ArrayList<>();
                    senderPrefixPricesAllAccounts.put(price.getSenderPrefix(), list);
                }
                list.add(price);
            }
        }
    }

    private static void removePriceFromMaps(final Price price,
                                            final Map<String, Collection<Price>> pricesPerAccount,
                                            final Map<String, Collection<Price>> pricesPerPricingGroup,
                                            final Collection<Price> pricesAllAccounts,

                                            final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerAccount,
                                            final Map<String, NavigableMap<String, Collection<Price>>> senderPrefixPricesPerPricingGroup,
                                            final NavigableMap<String, Collection<Price>> senderPrefixPricesAllAccounts) {
        if (price.getNetwork() != null)
            return;
        if (price.getPrefix() != null)
            return;
        if (price.getAccount() != null && !price.getAccount().trim().equals("")) {
            if (price.getSenderPrefix() == null) {
                final Collection<Price> list = pricesPerAccount.get(price.getAccount().trim().toLowerCase());
                removePrice(list, price);
            } else {
                final NavigableMap<String, Collection<Price>> map = senderPrefixPricesPerAccount.get(price.getAccount().trim().toLowerCase());
                if (map != null) {
                    final Collection<Price> list = map.get(price.getSenderPrefix());
                    removePrice(list, price);
                }
            }
        } else if (price.getPricingGroup() != null && !price.getPricingGroup().equals("")) {
            if (price.getSenderPrefix() == null) {
                final Collection<Price> list = pricesPerPricingGroup.get(price.getPricingGroup().trim().toLowerCase());
                removePrice(list, price);
            } else {
                final NavigableMap<String, Collection<Price>> map = senderPrefixPricesPerPricingGroup.get(price.getPricingGroup().trim().toLowerCase());
                if (map != null) {
                    final Collection<Price> list = map.get(price.getSenderPrefix());
                    removePrice(list, price);
                }
            }
        } else {
            if (price.getSenderPrefix() == null)
                removePrice(pricesAllAccounts, price);
            else {
                final Collection<Price> list = senderPrefixPricesAllAccounts.get(price.getSenderPrefix());
                removePrice(list, price);
            }
        }
    }

    @Override
    protected void injectPriceIntoMaps(final Price newPrice) {
        populateMapsWithPrice(newPrice,
                              this.pricesPerAccount,
                              this.pricesPerPricingGroup,
                              this.pricesAllAccounts,
                              this.senderPrefixPricesPerAccount,
                              this.senderPrefixPricesPerPricingGroup,
                              this.senderPrefixPricesAllAccounts);
    }

    @Override
    protected void removePriceFromMaps(final Price price) {
        removePriceFromMaps(price,
                            this.pricesPerAccount,
                            this.pricesPerPricingGroup,
                            this.pricesAllAccounts,
                            this.senderPrefixPricesPerAccount,
                            this.senderPrefixPricesPerPricingGroup,
                            this.senderPrefixPricesAllAccounts);
    }

    @Override
    public Price checkMapsFirst(final LOOKUP_PASS lookupPass,
                                final String msisdn,
                                final String sender,
                                final String network,
                                final String accountId,
                                final String pricingGroupId,
                                final String masterAccountId,
                                final String masterAccountPricingGroupId) {
        Collection<Price> prices = null;
        NavigableMap<String, Collection<Price>> senderPrefixPrices = null;
        if (lookupPass == LOOKUP_PASS.PER_ACCOUNT) {
            if (accountId != null) {
                prices = this.pricesPerAccount.get(accountId);
                senderPrefixPrices = this.senderPrefixPricesPerAccount.get(accountId);
            }
        } else if (lookupPass == LOOKUP_PASS.PER_PRICING_GROUP_ID) {
            if (pricingGroupId != null) {
                prices = this.pricesPerPricingGroup.get(pricingGroupId);
                senderPrefixPrices = this.senderPrefixPricesPerPricingGroup.get(pricingGroupId);
            }
        } else if (lookupPass == LOOKUP_PASS.PER_MASTER_ACCOUNT) {
            if (masterAccountId != null) {
                prices = this.pricesPerAccount.get(masterAccountId);
                senderPrefixPrices = this.senderPrefixPricesPerAccount.get(masterAccountId);
            }
        } else if (lookupPass == LOOKUP_PASS.PER_MASTER_PRICING_GROUP_ID) {
            if (masterAccountPricingGroupId != null) {
                prices = this.pricesPerPricingGroup.get(masterAccountPricingGroupId);
                senderPrefixPrices = this.senderPrefixPricesPerPricingGroup.get(masterAccountPricingGroupId);
            }
        } else if (lookupPass == LOOKUP_PASS.GLOBAL_RULES) {
            prices = this.pricesAllAccounts;
            senderPrefixPrices = this.senderPrefixPricesAllAccounts;
        }

        // first -- check sender-prefix maps ....

        final Collection<Price> list = selectSenderPrefixList(senderPrefixPrices, sender);
        if (list != null && list.size() >= 1) // SIP-1925: this was incorrectly "> 1" in the original
            return list.iterator().next();

        // check the main maps .....

        if (prices == null || prices.size() < 1)
            return null;

        final Price price = prices.iterator().next();
        if (price != null)
            return price;

        return null;
    }

    @Override
    protected Price checkMapsFirstGetDefaultPrice() {
        return getDefaultPrice();
    }

    private static Collection<Price> selectSenderPrefixList(final NavigableMap<String, Collection<Price>> maps, final String senderPrefix) {
        if (maps == null || maps.isEmpty())
            return null;
        if (senderPrefix == null)
            return null;
        final NavigableMap.Entry<String, Collection<Price>> entry = maps.floorEntry(senderPrefix);
        if (entry == null)
            return null;
        Collection<Price> list = entry.getValue();
        if (senderPrefix.startsWith(entry.getKey()))
            return list; // its a good match ...

        // See if we matched any parent prefixes ...

        // first, see if we matched any digits at all, if so, there may be a parent prefix ...
        int matchingDigits = 0;
        for (int i=0;i<entry.getKey().length();i++) {
            if (senderPrefix.length() <= i)
                break;
            if (senderPrefix.charAt(i) == entry.getKey().charAt(i))
                matchingDigits++;
            else
                break;
        }

        if (matchingDigits == 0)
            return null;

        // start looking for prefixes that might match the digits we did actually match, starting with the longest and getting shorter
        for (int i=matchingDigits;i>0;i--) {
            final String tryPrefix = senderPrefix.substring(0, i);
            list = maps.get(tryPrefix);
            if (list != null)
                return list;
        }

        // no matching prefixes found ..
        return null;
    }

    @Override
    public Collection<String> dumpLookupMapsDebugInfo() {
        final Collection<String> list = new ArrayList<>();

        list.add("===============================================================================================");
        list.add("======================== LOOKUP MATRIX FOR NO NETWORK OR PREFIX ===============================");
        list.add("===============================================================================================");
        list.add("");

        list.add("---- prices per account ----");
        for (final Map.Entry<String, Collection<Price>> entry: this.pricesPerAccount.entrySet())
            for (final Price price: entry.getValue())
                list.add("    ACC [ " + entry.getKey() + " ] --> PRICE [ " + price.toString() + " ] ");
        list.add("---- end of prices per account ----");
        list.add("");
        list.add("---- prices per pricing group ----");
        for (final Map.Entry<String, Collection<Price>> entry: this.pricesPerPricingGroup.entrySet())
            for (final Price price: entry.getValue())
                list.add("    GROUP [ " + entry.getKey() + " ] --> PRICE [ " + price.toString() + " ] ");
        list.add("---- end of prices per pricing group ----");
        list.add("");
        list.add("---- prices for all accounts ----");
        for (final Price price: this.pricesAllAccounts)
            list.add(price.toString());
        list.add("---- end of prices for all accounts ----");
        list.add("");

        list.add("--------------------------------------------------------");
        list.add("---------------- sender prefix maps --------------------");
        list.add("--------------------------------------------------------");

        list.add("---- prices per account per sender-prefix ----");
        for (final Map.Entry<String, NavigableMap<String, Collection<Price>>> entry: this.senderPrefixPricesPerAccount.entrySet())
            for (final Map.Entry<String, Collection<Price>> entry2: entry.getValue().entrySet())
                for (final Price price: entry2.getValue())
                    list.add("    ACC [ " + entry.getKey() + " ] :: SENDER-PREFIX [ " + entry2.getKey() + " ] --> PRICE [ " + price.toString() + " ] ");
        list.add("---- end of prices per account per sender-prefix ----");
        list.add("");
        list.add("---- prices per pricing group per sender-prefix ----");
        for (final Map.Entry<String, NavigableMap<String, Collection<Price>>> entry: this.senderPrefixPricesPerPricingGroup.entrySet())
            for (final Map.Entry<String, Collection<Price>> entry2: entry.getValue().entrySet())
                for (final Price price: entry2.getValue())
                    list.add("    GROUP [ " + entry.getKey() + " ] :: SENDER-PREFIX [ " + entry2.getKey() + " ] --> PRICE [ " + price.toString() + " ] ");
        list.add("---- end of prices per pricing group per sender-prefix ----");
        list.add("");
        list.add("---- prices for all accounts per sender-prefix ----");
        for (final Map.Entry<String, Collection<Price>> entry: this.senderPrefixPricesAllAccounts.entrySet())
            for (final Price price: entry.getValue())
                list.add("    ALL-ACCOUNTS ::  SENDER-PREFIX [ " + entry.getKey() + " ] --> PRICE [ " + price.toString() + " ] ");
        list.add("---- end of prices for all accounts per sender-prefix ----");
        list.add("");
        list.add("");

        list.add("===============================================================================================");
        list.add("===================== END OF LOOKUP MATRIX FOR NO NETWORK OR PREFIX ===========================");
        list.add("===============================================================================================");

        return list;
    }

}
