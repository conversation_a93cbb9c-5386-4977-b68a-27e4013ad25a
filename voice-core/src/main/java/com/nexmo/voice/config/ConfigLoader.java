package com.nexmo.voice.config;

import com.thepeachbeetle.common.period.Periods;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import com.thepeachbeetle.messaging.hub.config.db.ConfigDBConfigLoader;

/**
 * <AUTHOR>
 */
public class ConfigLoader extends NestedXmlHandler {

    private final Config config;

    private final String coreNode;
    private final String coreLogNode;
    private final String coreLogApplicationNode;
    private final String coreLogCallLoggingNode;
    private final String coreLogCallInboundLoggingNode;
    private final String coreLogRejectedLoggingNode;
    private final String coreLogAttemptLoggingNode;

    private final String pddCalculatorNode;
    private final String callChargerNode;

    private final String ttsLogCallLoggingNode;
    private final String ttsLogRejectedLoggingNode;
    private final String ttsLogAttemptLoggingNode;


    private final String shutdownCooldownNode;

    public ConfigLoader(final String nodeName) {
        super(nodeName);

        this.config = new Config();

        this.coreNode = getSubNodeName("core");
        this.coreLogNode = getSubNodeName(this.coreNode, "log");
        this.coreLogApplicationNode = getSubNodeName(this.coreLogNode, "application-rejected-logging");
        this.coreLogCallLoggingNode = getSubNodeName(this.coreLogNode, "call-logging");
        this.coreLogCallInboundLoggingNode = getSubNodeName(this.coreLogNode, "call-inbound-logging");
        this.coreLogRejectedLoggingNode = getSubNodeName(this.coreLogNode, "rejected-logging");
        this.coreLogAttemptLoggingNode = getSubNodeName(this.coreLogNode, "attempt-logging");
        this.shutdownCooldownNode = getSubNodeName(this.coreNode, "shutdown-cooldown");
        this.pddCalculatorNode = getSubNodeName(this.coreNode, "pdd-calculator");
        this.callChargerNode = getSubNodeName(this.coreNode, "call-charger");
        this.ttsLogCallLoggingNode = getSubNodeName(this.coreLogNode, "tts-call-logging");
        this.ttsLogRejectedLoggingNode = getSubNodeName(this.coreLogNode, "tts-rejected-logging");
        this.ttsLogAttemptLoggingNode = getSubNodeName(this.coreLogNode, "tts-attempt-logging");
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
        } else if (this.coreNode.equals(node)) {
            this.config.setInstanceId(content.getAttribute("instance-id", true));
        } else if (this.coreLogNode.equals(node)) {
            this.config.setStatsLogDir(content.getAttribute("dir", true));
            this.config.setLog4jPropertiesFile(content.getAttribute("log4j-properties-file", true));
            this.config.setLogLevel(content.getAttribute("log-level", false, "INFO"));
        } else if (this.coreLogApplicationNode.equals(node)) {
            this.config.setApplicationLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setApplicationLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.coreLogCallLoggingNode.equals(node)) {
            this.config.setCallLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setCallLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.coreLogCallInboundLoggingNode.equals(node)) {
            this.config.setCallInboundLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setCallInboundLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.coreLogRejectedLoggingNode.equals(node)) {
            this.config.setRejectedLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setRejectedLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.coreLogAttemptLoggingNode.equals(node)) {
            this.config.setAttemptLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setAttemptLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.shutdownCooldownNode.equals(node)) {
            String shutdownCooldownTimeUnit = content.getAttribute("unit", false, Periods.PERIOD_SECOND.getKey());
            this.config.setShutdownCooldownTimeUnit(Periods.getMatching(shutdownCooldownTimeUnit));
            this.config.setShutdownCooldownTimeCount(parseInt(content.getAttribute("count", false), 90));
        } else if (this.pddCalculatorNode.equals(node)) {
            this.config.setPddCacheDuration(parseLong(content.getAttribute("duration", true)));
        } else if (this.callChargerNode.equals(node)) {
            this.config.setCallChargerCacheDuration(parseLong(content.getAttribute("duration", true)));
        } else if (this.ttsLogCallLoggingNode.equals(node)) {
            this.config.setTTSLogCallLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setTTSCallLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.ttsLogRejectedLoggingNode.equals(node)) {
            this.config.setTTSRejectedLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setTTSRejectedLoggingFilePrefix(content.getAttribute("file-prefix", true));
        } else if (this.ttsLogAttemptLoggingNode.equals(node)) {
            this.config.setTTSAttemptLoggingEnabled(parseBoolean(content.getAttribute("enabled", true)));
            this.config.setTTSAttemptLoggingFilePrefix(content.getAttribute("file-prefix", true));
        }

    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config.setConfigDbConfig(ConfigDBConfigLoader.getLatestConfigDB());
            notifyComplete();
        }
    }


    public Config getConfig() {
        return this.config;
    }

}
