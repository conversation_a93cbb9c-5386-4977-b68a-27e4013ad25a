package com.nexmo.voice.config.sip;

/*
 * <asterisk-agi-server enabled="true"
 *                      hostname="0.0.0.0"
 *                      port="5050"
 *                      pool-size="20"
 *                      maximum-pool-size="100" />
 */

import org.jdom.Element;


public class AsteriskAgiServerConfig implements java.io.Serializable {

    private static final long serialVersionUID = 1232226360832594834L;

    private final boolean enabled;
    private final String hostname;
    private final int port;
    private final int poolSize;
    private final int maxPoolSize;

    public AsteriskAgiServerConfig(boolean enabled, String hostname, int port, int poolSize, int maxPoolSize) {
        this.enabled = enabled;
        this.hostname = hostname;
        this.port = port;
        this.poolSize = poolSize;
        this.maxPoolSize = maxPoolSize;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public String getHostname() {
        return this.hostname;
    }

    public int getPort() {
        return this.port;
    }

    public int getPoolSize() {
        return this.poolSize;
    }

    public int getMaximumPoolSize() {
        return this.maxPoolSize;
    }

    public Element toXML() {

        final Element asteriskManager = new Element("asterisk-manager");
        asteriskManager.setAttribute("enabled", String.valueOf(this.enabled));
        asteriskManager.setAttribute("hostname", this.hostname);
        asteriskManager.setAttribute("port", String.valueOf(this.port));
        asteriskManager.setAttribute("pool-size", String.valueOf(this.poolSize));
        asteriskManager.setAttribute("maximum-pool-size", String.valueOf(this.maxPoolSize));

        return asteriskManager;
    }

}
