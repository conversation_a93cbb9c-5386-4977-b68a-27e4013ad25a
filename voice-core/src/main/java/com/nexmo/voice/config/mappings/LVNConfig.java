package com.nexmo.voice.config.mappings;

/*
 * <lvns>
 *   <lvn number="8" group="DAL">Some description text!</lvn>
 *   <lvn number="14" group="SNG" />
 *   <lvn number="15:30" group="LON" />
 * </lvns>
 */

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;

public class LVNConfig implements java.io.Serializable {

    private static final long serialVersionUID = 1389371276700010571L;

    private final String number;
    private final String group;
    private final String description;

    public LVNConfig(final String number,
                     final String group,
                     final String description) {
        this.number = number;
        this.group = group;
        this.description = description;
    }

    public String getNumber() {
        return this.number;
    }

    public String getGroup() {
        return this.group;
    }

    public String getDescriptionText() {
        return this.description;
    }

    public Element toXML() {
        Element lvn = new Element("lvn");

        lvn.setAttribute("number", this.getNumber());
        lvn.setAttribute("group", this.getGroup());

        if (this.getDescriptionText() != null)
            lvn.setText(this.getDescriptionText());

        return lvn;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof LVNConfig))
            return false;
        LVNConfig other = (LVNConfig) o;

        // Mandatory parameters
        if (!this.number.equals(other.number))
            return false;
        if (!this.group.equals(other.group))
            return false;

        // Optional parameters
        if (this.description != null) {
            if (!this.description.equals(other.description))
                return false;
        } else if (other.description != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + number.hashCode();
        result = prime * result + group.hashCode();
        result = prime * result + ((description == null) ? 0 : description.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("LVN: ");
        sb.append(number);
        sb.append("=>");
        sb.append(group);
        if (description != null) {
            sb.append("[");
            sb.append(description);
            sb.append("]");
        }
        return sb.toString();
    }

}
