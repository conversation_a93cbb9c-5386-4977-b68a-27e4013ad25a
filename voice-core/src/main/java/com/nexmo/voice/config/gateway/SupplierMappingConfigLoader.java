package com.nexmo.voice.config.gateway;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class SupplierMappingConfigLoader extends NestedXmlHandler {

    private static final String DEFAULT_ENDPOINT = "*******";

    public static final String ROOT_NODE = "supplier-mapping";

    private final boolean skipDB;

    private MtCostMatrixConfigLoader mtCostMatrixConfigLoader;
    private MoCostMatrixConfigLoader moCostMatrixConfigLoader;

    private SupplierMappingConfig config;

    private String name;
    private String endpoint;
    private String numberPrefix;
    private String callerId;

    public SupplierMappingConfigLoader(final String nodeName, final boolean skipDB) {
        super(nodeName);
        this.skipDB = skipDB;
        this.config = null;
    }

    public SupplierMappingConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.name = content.getAttribute("name", true); // Always required
            this.endpoint = content.getAttribute("endpoint", false, DEFAULT_ENDPOINT);
            this.numberPrefix = content.getAttribute("number-prefix", false);
            this.callerId = content.getAttribute("caller-id", false);
            this.mtCostMatrixConfigLoader = new MtCostMatrixConfigLoader(getSubNodeName(MtCostMatrixConfigLoader.MT_COST_MATRIX_ROOT_NODE_NAME), this.name, this.skipDB);
            this.moCostMatrixConfigLoader = new MoCostMatrixConfigLoader(getSubNodeName(MoCostMatrixConfigLoader.MO_COST_MATRIX_ROOT_NODE_NAME), this.name, this.skipDB);
        }
        // FIXME: Why not addHandler()?
        this.mtCostMatrixConfigLoader.startNode(node, content);
        this.moCostMatrixConfigLoader.startNode(node, content);
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        this.mtCostMatrixConfigLoader.endNode(node, contentData);
        this.moCostMatrixConfigLoader.endNode(node, contentData);

        if (getNodeName().equals(node)) {
            this.config = new SupplierMappingConfig(this.name,
                                                    this.endpoint,
                                                    this.callerId,
                                                    this.numberPrefix,
                                                    this.mtCostMatrixConfigLoader.getMatrix(),
                                                    this.moCostMatrixConfigLoader.getMatrix());
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        // FIXME: Never called! Because we don't do addHandler()?
//System.out.println("SupplierMappingConfigLoader.notifyComplete("+childHandler+")");
    }

}
