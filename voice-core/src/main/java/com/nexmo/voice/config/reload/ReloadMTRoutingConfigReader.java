package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingConfigLoader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;


public class ReloadMTRoutingConfigReader extends XmlAbstractReader {

    private final MtRoutingConfigLoader mtRoutingConfigLoader;
    private final Config config;

    public ReloadMTRoutingConfigReader(Config config) {
        this.config = config;
        this.mtRoutingConfigLoader = new MtRoutingConfigLoader(ConfigReader.ROOT_NODE + "." + MtRoutingConfigLoader.MT_ROUTING_NODE_NAME,
                                                               config.getConfigDbConfig(),
                                                               false); // skipDB
        addHandler(this.mtRoutingConfigLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (this.mtRoutingConfigLoader.getConfig() != null)
            if (childHandler == this.mtRoutingConfigLoader)
                this.config.setMtRoutingConfig(this.mtRoutingConfigLoader.getConfig());
    }

}
