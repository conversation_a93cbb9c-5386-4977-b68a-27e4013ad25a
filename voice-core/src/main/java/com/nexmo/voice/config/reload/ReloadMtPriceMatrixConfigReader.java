package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.thepeachbeetle.messaging.hub.config.pricing.MtPriceMatrixConfigLoader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;


public class ReloadMtPriceMatrixConfigReader extends XmlAbstractReader {

    private final MtPriceMatrixConfigLoader mtPriceMatrixConfigLoader;

    private final Config config;

    public ReloadMtPriceMatrixConfigReader(Config config) {
        this.mtPriceMatrixConfigLoader = new MtPriceMatrixConfigLoader(ConfigReader.ROOT_NODE + "." + MtPriceMatrixConfigLoader.MT_PRICE_MATRIX_ROOT_NODE_NAME,
                                                                       false); // skipDB
        addHandler(this.mtPriceMatrixConfigLoader);

        this.config = config;
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler == this.mtPriceMatrixConfigLoader)
            this.config.setMtPriceMatrixList(this.mtPriceMatrixConfigLoader.getMatrix());
    }

}
