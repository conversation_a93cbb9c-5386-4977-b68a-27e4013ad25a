package com.nexmo.voice.config.callblocks;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;
import org.apache.log4j.Logger;


public class CallBlockConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "call-block";

    private String account;
    private String destinationCountryCode;
    private String callIdPrefix;

    private CallBlockConfig config;

    private static final Logger Log = Logger.getLogger(CallBlockConfigLoader.class.getName());

    public CallBlockConfigLoader(String nodeName) {
        super(nodeName);
    }

    public CallBlockConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.account =  content.getAttribute("account", false);
            this.destinationCountryCode =  content.getAttribute("destination", false);
            this.callIdPrefix =  content.getAttribute("call-id", false);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new CallBlockConfig(this.account, this.destinationCountryCode, this.callIdPrefix);
            notifyComplete();
        }
    }

}
