package com.nexmo.voice.config.callblocks;

import com.thepeachbeetle.common.xml.LoaderException;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Collectors;

public class CallBlocksConfigUtils {
    private static final Logger Log = Logger.getLogger(CallBlocksConfigUtils.class.getName());

    public static CallBlocksConfig loadConfigFromXMLFile(String filename) {
        Log.info("Loading XML file \"" + filename + "\"");
        if (filename == null)
            return null;

        try {
            CallBlocksConfigReader reader = new CallBlocksConfigReader(filename);
            reader.read(new File(filename));
            CallBlocksConfig config = reader.getConfig();
            Log.info("Successfully loaded XML file \"" + filename + "\"");
            return config;
        } catch (LoaderException ex) {
            Log.error("Failed to load XML file \"" + filename + "\"", ex);
            return null;
        }
    }

    public static String selectFile(String path, String mask, String version) {
        if (path == null)
            return null;
        if (mask == null)
            mask = "*.xml";
        if (version == null)
            version = "LATEST";

        List<String> files = null;
        try {
            files = getFileList(path, mask);
        } catch (IOException ex) {
            Log.error("Failed to retrieve file list for path = \"" + path + "\" and mask \"" + mask + "\"", ex);
            return null;
        }
        if ((files == null) || files.isEmpty())
            return null;

        String chosen = null;
        if ("LATEST".equalsIgnoreCase(version)) {
            // ISO 8601 timestamp means most recent is lexicographically last
            Collections.sort(files);
            chosen = files.get(files.size() - 1);
        } else {
            for (String f : files) {
                if (f.contains(version)) { // FIXME: handle multiple matches
                    chosen = f;
                    break;
                }
            }
        }

        Log.info("selectFile(\"" + path + "\", \"" + mask + "\", \"" + version + "\") => \"" + chosen + "\"");

        if (chosen == null)
            return null;
        else
            return FileSystems.getDefault().getPath(path, chosen).toString();
    }

    public static String compareConfigs(CallBlocksConfig oldConfig, CallBlocksConfig newConfig) {
        final CallBlocksConfigUtils.ConfigDiffs diff = diffConfigs(oldConfig, newConfig);
        return diff.toString();
    }

    private static CallBlocksConfigUtils.ConfigDiffs diffConfigs(CallBlocksConfig left, CallBlocksConfig right) {
        Log.info("CallBlocksConfigUtils.diffConfigs(): Starting diffConfigs...");
        final Set<String> leftKeys = left.getCallBlockAccounts();
        Log.info("CallBlocksConfigUtils.diffConfigs(): Got old config ...");
        final Set<String> rightKeys = right.getCallBlockAccounts();
        Log.info("CallBlocksConfigUtils.diffConfigs(): Got new config ...");

        Set<String> added = new HashSet<String>(rightKeys);
        added.removeAll(leftKeys);
        Log.info("CallBlocksConfigUtils.diffConfigs(): remove leftkeys from rightkeys...");

        Set<String> removed = new HashSet<String>(leftKeys);
        removed.removeAll(rightKeys);
        Log.info("CallBlocksConfigUtils.diffConfigs(): remove rightkeys from leftkeys...");

        Set<String> intersection = new HashSet<String>(leftKeys);
        intersection.retainAll(rightKeys);

        Set<String> changed = new TreeSet<String>();
        Log.info("CallBlocksConfigUtils.diffConfigs(): looking at intersection...");
        for (String key : intersection) {
            HashMap<String, ArrayList<String>> leftCallBlock = left.getCallBlockMap().get(key);
            HashMap<String, ArrayList<String>> rightCallBlock = right.getCallBlockMap().get(key);
            if (!leftCallBlock.equals(rightCallBlock))
                changed.add(key);
        }

        Log.info("CallBlocksConfigUtils.diffConfigs(): returning ConfigDiffs...");
        return new CallBlocksConfigUtils.ConfigDiffs(added, removed, changed);
    }

    //
    // Internal data structures
    //
    private static List<String> getFileList(String path, String mask) throws IOException {
        final FileSystem fs = FileSystems.getDefault();
        final PathMatcher pm = fs.getPathMatcher("glob:" + mask);

        return Files.list(Paths.get(path))
                .map(p -> p.getFileName()) // Filename *only*, otherwise we need to match full path
                .filter(p -> pm.matches(p))
                .map(p -> p.toString())
                .collect(Collectors.toList());
    }

    private static class ConfigDiffs {
        private final Set<String> added;
        private final Set<String> removed;
        private final Set<String> changed;

        private ConfigDiffs(Set<String> added, Set<String> removed, Set<String> changed) {
            this.added = new TreeSet<>(added);
            this.removed = new TreeSet<>(removed);
            this.changed = new TreeSet<>(changed);
        }

        public boolean isEmpty() {
            return added.isEmpty() && removed.isEmpty() && changed.isEmpty();
        }

        @Override
        public String toString() {
            if (isEmpty())
                return "No Differences";

            StringBuilder sb = new StringBuilder();
            sb.append("Differences:\n");
            if (!added.isEmpty()) {
                sb.append("Accounts added: ");
                sb.append(added.toString());
                sb.append("\n");
            }
            if (!removed.isEmpty()) {
                sb.append("Accounts removed: ");
                sb.append(removed.toString());
                sb.append("\n");
            }
            if (!changed.isEmpty()) {
                sb.append("Accounts changed: ");
                sb.append(changed.toString());
                sb.append("\n");
            }
            return sb.toString();
        }
    }

}
