package com.nexmo.voice.config.prefix;

/*
 * <prefix-map country-code="806">
 *   <mapping prefix="12012010" group="806103" />
 *   <mapping prefix="12012011" group="806103" />
 *   <mapping prefix="12012012" group="806104" />
 *   <mapping prefix="12012013" group="806103" />
 *   <mapping prefix="12012014" group="806103" />
 *   <mapping prefix="12012015" group="806103" />
 *   <mapping prefix="12012016" group="806103" />
 *   <mapping prefix="12012017" group="806103" />
 *   <mapping prefix="12012018" group="806103" />
 * </prefix-map>
 */

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import org.apache.commons.lang3.StringUtils;
import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;

public class PrefixMapConfig implements java.io.Serializable {
    private static final long serialVersionUID = 2494611885247886910L;

    private final String countryCode;
    private final List<MappingConfig> mappingsList;
    private final boolean enabled;

    private final TreeMap<String, MappingConfig> prefixToGroupMap = new TreeMap<String, MappingConfig>();

    public PrefixMapConfig(String countryCode, List<MappingConfig> mappingsList) {
        this(countryCode, mappingsList, true);
    }

    public PrefixMapConfig(String countryCode, List<MappingConfig> mappingsList, boolean enabled) {
        this.countryCode = StringUtils.trimToNull(countryCode);

        // Store original config
        this.mappingsList = mappingsList;

        // Build prefix map
        for (MappingConfig mapping : mappingsList) {
            final String prefix = mapping.getPrefix();
            final String group = mapping.getGroup();

            // Check group starts with "806" country code
            if ((this.countryCode != null) && !group.startsWith(this.countryCode))
                throw new IllegalArgumentException("Prefix \"" + prefix + "\" maps to a number (\"" + group + "\") which doesn't begin with \"" + this.countryCode + "\"");

            // Build map
            if (prefixToGroupMap.containsKey(prefix)) {
                final String value = prefixToGroupMap.get(prefix).getGroup();
                if (!value.equals(group))
                    throw new IllegalArgumentException("Different mappings for prefix \"" + prefix + "\": \"" + group + "\" vs \"" + value + "\"");
            }
            prefixToGroupMap.put(prefix, mapping);
        }

        // Enabled?
        this.enabled = enabled;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public boolean isEmpty() {
        return prefixToGroupMap.isEmpty();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public int size() {
        return prefixToGroupMap.size();
    }

    public String getGroup(String prefix) { // Exact match
        final MappingConfig mapping = prefixToGroupMap.get(prefix);
        if (mapping == null)
            return null;
        else
            return mapping.getGroup();
    }

    public MappingConfig get(String prefix) { // Exact match
        return prefixToGroupMap.get(prefix);
    }

    public MappingConfig lookup(String number) { // Prefix matching
        final String floorKey = prefixToGroupMap.floorKey(number);
        if (floorKey == null) // No match, lower than all defined prefixes
            return null;

        if (number.startsWith(floorKey)) // Exact match
            return get(floorKey);

        // Count back digits to find best match
        String prefix = floorKey;
        while (!prefix.isEmpty()) {
            if (number.startsWith(prefix) && prefixToGroupMap.containsKey(prefix)) {
                return get(prefix);
            }
            prefix = prefix.substring(0, prefix.length() - 1);
        }
        return null;
    }

    public PrefixMapConfig cloneWithEnabled(boolean enabled) {
        return new PrefixMapConfig(this.countryCode, this.mappingsList, enabled);
    }

    public Element toXML() {
        return toXML(false);
    }

    private Element toXML(boolean full) {
        Element prefixMap = new Element("prefix-map");

        // Country code
        prefixMap.setAttribute("country-code", this.getCountryCode());

        if (full) {
            for (MappingConfig mapping : this.mappingsList) {
                Element groupXML = mapping.toXML();
                prefixMap.addContent(groupXML);
            }
        } else {
            prefixMap.setText("..." + Integer.toString(size()) + " prefix mappings...");
        }
        return prefixMap;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof PrefixMapConfig))
            return false;
        PrefixMapConfig other = (PrefixMapConfig) o;
        if (!this.mappingsList.equals(other.mappingsList))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + mappingsList.hashCode();
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump PrefixMapConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

    /*package*/ String diff(PrefixMapConfig other) {
        return diff(other, 10);
    }

    /*package*/ String diff(PrefixMapConfig other, int limit) {
        Map<String, MappingConfig> leftMap = new HashMap<String, MappingConfig>();
        for (MappingConfig g : this.mappingsList) leftMap.put(g.getPrefix(), g);
        final Set<String> leftPrefixes = leftMap.keySet();

        Map<String, MappingConfig> rightMap = new HashMap<String, MappingConfig>();
        for (MappingConfig g : other.mappingsList) rightMap.put(g.getPrefix(), g);
        final Set<String> rightPrefixes = rightMap.keySet();

        Set<String> addedPrefixes = new HashSet<String>(rightPrefixes);
        addedPrefixes.removeAll(leftPrefixes);

        Set<String> removedPrefixes = new HashSet<String>(leftPrefixes);
        removedPrefixes.removeAll(rightPrefixes);

        Set<String> intersectionPrefixes = new HashSet<String>(leftPrefixes);
        intersectionPrefixes.retainAll(rightPrefixes);
        Set<String> changedPrefixes = new TreeSet<String>();
        for (String key : intersectionPrefixes) {
            MappingConfig leftMapping = leftMap.get(key);
            MappingConfig rightMapping = rightMap.get(key);
            if (!leftMapping.equals(rightMapping))
                changedPrefixes.add(key);
        }


        StringBuilder sb = new StringBuilder();
        boolean changes = false;
        sb.append("Differences:\n");
        if (!addedPrefixes.isEmpty()) {
            sb.append("Prefixes added: ");
            sb.append(limitList(addedPrefixes, limit));
            sb.append("\n");
            changes = true;
        }
        if (!removedPrefixes.isEmpty()) {
            sb.append("Prefixes removed: ");
            sb.append(limitList(removedPrefixes, limit));
            sb.append("\n");
            changes = true;
        }
        if (!changedPrefixes.isEmpty()) {
            sb.append("Prefixes changed: ");
            sb.append(limitList(changedPrefixes, limit));
            sb.append("\n");
            changes = true;
        }
        if (changes)
            return sb.toString();
        else
            return "No Differences";
    }

    private static List<String> limitList(Collection<String> original, int limit) {
        List<String> ret = new ArrayList<String>(limit + 1);
        int i = 0;
        Iterator<String> it = original.iterator();
        while (it.hasNext() && (i++ < limit)) {
            ret.add(it.next());
        }
        if (original.size() > limit)
            ret.add("... and " + (original.size() - limit) + " more ...");
        return ret;
    }

}
