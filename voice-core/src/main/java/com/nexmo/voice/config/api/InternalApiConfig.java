package com.nexmo.voice.config.api;

import java.math.BigDecimal;

import org.jdom.Element;

public class InternalApiConfig implements java.io.Serializable {

    private static final long serialVersionUID = -8471233472358349035L;

    private final boolean enabled;
    private final PublicEndpointConfig endpointConfig;
    private final boolean balanceCheckEnabled;
    private final BigDecimal minBalance;
    private final boolean concurrentCapacityCheckEnabled;

    public InternalApiConfig(final boolean enabled,
                             final PublicEndpointConfig publicEndpointConfig,
                             final boolean balanceCheckEnabled,
                             final BigDecimal minBalance,
                             final boolean concurrentCapacityCheckEnabled) {
        this.enabled = enabled;
        this.endpointConfig = publicEndpointConfig;
        this.balanceCheckEnabled = balanceCheckEnabled;
        this.minBalance = minBalance;
        this.concurrentCapacityCheckEnabled = concurrentCapacityCheckEnabled;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public PublicEndpointConfig getEndpointConfig() {
        return this.endpointConfig;
    }

    public BigDecimal getMinBalance() {
        return this.minBalance;
    }

    public boolean isBalanceCheckEnabled() {
        return this.balanceCheckEnabled;
    }

    public boolean isConcurrentCapacityCheckEnabled() {
        return concurrentCapacityCheckEnabled;
    }

    public Element toXML() {
        Element proxyApiNode = new Element("internal-api");

        proxyApiNode.setAttribute("enabled", String.valueOf(this.enabled));

        if (this.endpointConfig != null) {
            Element publicEndpointNode = this.endpointConfig.toXML("endpoint");
            proxyApiNode.addContent(publicEndpointNode);
        }

        if (this.balanceCheckEnabled) {
            Element checkBalanceNode = new Element("check-balance");
            checkBalanceNode.setAttribute("enabled", String.valueOf(this.balanceCheckEnabled));
            checkBalanceNode.setAttribute("min-balance", this.minBalance.toPlainString());

            proxyApiNode.addContent(checkBalanceNode);
        }
        
        if (this.concurrentCapacityCheckEnabled) {
            Element checkConcurrentCapacityNode = new Element("check-concurrent-capacity");
            checkConcurrentCapacityNode.setAttribute("enabled", String.valueOf(this.concurrentCapacityCheckEnabled));
            proxyApiNode.addContent(checkConcurrentCapacityNode);
        }


        return proxyApiNode;
    }

}
