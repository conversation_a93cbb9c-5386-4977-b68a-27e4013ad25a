package com.nexmo.voice.config.mappings;

/*
 *   <group name="SNG" strict="false">
 *     <media>
 *       <destination name="sng1" />
 *     </media>
 *   </group>
 */

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;

public class GroupConfig implements java.io.Serializable {

    private static final long serialVersionUID = 97065712703088041L;

    private final String name;
    private final boolean strict;
    private final List<MediaDestination> destinations;

    public GroupConfig(final String name,
                       final boolean strict,
                       final List<MediaDestination> destinations) {
        this.name = name;
        this.strict = strict;
        this.destinations = destinations;
    }

    public String getName() {
        return this.name;
    }

    public boolean isStrict() {
        return this.strict;
    }

    public List<String> getGeos() {
        List<String> ret = new ArrayList<String>();
        if (destinations != null) {
            for (MediaDestination dest : destinations) {
                ret.add(dest.getName());
            }
        }
        return ret;
    }

    public Element toXML() {
        Element group = new Element("group");

        group.setAttribute("name", this.getName());
        group.setAttribute("strict", Boolean.toString(this.isStrict()));

        // Destination list
        if (destinations != null) {
            Element media = new Element("media");
            group.addContent(media);
            for (MediaDestination d : destinations) {
                Element destination = d.toXML();
                media.addContent(destination);
            }
        }

        return group;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof GroupConfig))
            return false;
        GroupConfig other = (GroupConfig) o;

        // Mandatory parameters
        if (!this.name.equals(other.name))
            return false;
        if (this.strict != other.strict)
            return false;

        // Destination list
        if (this.destinations != null) {
            if (other.destinations == null)
                return false;

            final Iterator<MediaDestination> leftIterator = this.destinations.iterator();
            final Iterator<MediaDestination> rightIterator = other.destinations.iterator();
            while (leftIterator.hasNext() && rightIterator.hasNext()) {
                if (!leftIterator.next().equals(rightIterator.next()))
                    return false;
            }
            if (leftIterator.hasNext() != rightIterator.hasNext()) // Ensure lists are both finished
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + name.hashCode();
        result = prime * result + (strict ? 1231 : 1237);

        // Destination list
        if (destinations != null) {
            for (MediaDestination d : destinations) {
                result = prime * result + ((d == null) ? 0 : d.hashCode());
            }
        }

        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Group(");
        sb.append(name);
        sb.append(") => ");
        if (strict) sb.append("STRICTLY ");
        sb.append(destinations);
        return sb.toString();
    }

}
