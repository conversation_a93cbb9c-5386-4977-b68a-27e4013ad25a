package com.nexmo.voice.config.mappings;

/*
 *   <media>
 *     <destination name="sng1" />
 *   </media>
 */

import java.util.List;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;

public class MediaDestination implements java.io.Serializable {

    private static final long serialVersionUID = 706215649903088046L;

    private final String name;
    private final String description;

    public MediaDestination(final String name,
                            final String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return this.name;
    }

    public String getDescriptionText() {
        return this.description;
    }

    public Element toXML() {
        Element destination = new Element("destination");

        destination.setAttribute("name", this.getName());

        if (this.getDescriptionText() != null)
            destination.setText(this.getDescriptionText());

        return destination;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MediaDestination))
            return false;
        MediaDestination other = (MediaDestination) o;

        // Mandatory parameters
        if (!this.name.equals(other.name))
            return false;

        // Optional parameters
        if (this.description != null) {
            if (!this.description.equals(other.description))
                return false;
        } else if (other.description != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + name.hashCode();
        result = prime * result + ((description == null) ? 0 : description.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(name);
        if (description != null) {
            sb.append("[");
            sb.append(description);
            sb.append("]");
        }
        return sb.toString();
    }

}
