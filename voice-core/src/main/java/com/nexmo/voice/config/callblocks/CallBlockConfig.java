package com.nexmo.voice.config.callblocks;

import org.jdom.Element;

import java.io.Serializable;

public class CallBlockConfig implements Serializable {

    private static final long serialVersionUID = 8183232078055032155L;

    private String account;
    private String destinationCountryCode;
    private String callIdPrefix;

    public CallBlockConfig(String account, String destinationCountryCode, String callIdPrefix) {
        this.account = account;
        this.destinationCountryCode = destinationCountryCode;
        this.callIdPrefix = callIdPrefix;
    }

    public String getAccount() {
        return account;
    }

    public String getDestinationCountryCode() {
        return destinationCountryCode;
    }

    public String getCallIdPrefix() {
        return callIdPrefix;
    }

    public Element toXML() {
        final Element callBlock = new Element(CallBlockConfigLoader.ROOT_NODE);
        callBlock.setAttribute("account", this.getAccount());
        callBlock.setAttribute("destination", this.getDestinationCountryCode());
        callBlock.setAttribute("call-id", this.getCallIdPrefix());
        return callBlock;
    }

    @Override
    public String toString() {
        return "CallBlockConfig [account=" + this.getAccount() + "; destination=" + this.getDestinationCountryCode() + "; call-id=" + this.getCallIdPrefix() + "]";
    }

}
