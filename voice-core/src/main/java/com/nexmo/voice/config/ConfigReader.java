package com.nexmo.voice.config;

import com.nexmo.voice.config.********.DynamoDbConfigLoader;
import com.nexmo.voice.core.callblocking.CallblockingServiceConfigLoader;
import com.nexmo.voice.config.callblocks.CallBlocksConfigLoader;
import com.nexmo.voice.config.gateway.TTSGatewayInfoMatrixConfigLoader;
import com.nexmo.voice.config.metrics.MetricsConfigLoader;
import com.nexmo.voice.core.billing.vquota.VQuotaServiceConfig;
import com.nexmo.voice.core.billing.vquota.VQuotaServiceConfigLoader;
import com.nexmo.voice.core.domains.DomainsServiceConfig;
import com.nexmo.voice.core.domains.DomainsServiceConfigLoader;
import com.nexmo.voice.core.emergency.EmergencyCallingConfig;
import com.nexmo.voice.core.emergency.EmergencyCallingConfigLoader;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceConfig;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceConfigLoader;
import com.nexmo.voice.core.viam.ViamAuthConfig;
import com.nexmo.voice.core.viam.ViamAuthConfigLoader;
import com.thepeachbeetle.common.app.jmx.JMXConfigLoader;
import com.thepeachbeetle.common.app.monitoring.MonitoringConfigLoader;
import com.thepeachbeetle.common.cache.CacheCoreConfigLoader;
import com.thepeachbeetle.common.callback.config.CallbackConfigLoader;
import com.thepeachbeetle.common.cluster.config.ClusterCoreConfigLoader;
import com.thepeachbeetle.common.http.config.HttpServerConfigLoader;
import com.thepeachbeetle.common.http.connectionfactory.config.HttpCallbackBlockedIpRangeConfigLoader;
import com.thepeachbeetle.common.persistentclusteredcache.config.CachePersistenceConfigLoader;
import com.thepeachbeetle.common.ssl.SSLClientConfigLoader;
import com.thepeachbeetle.common.throttle.counters.CountersConfigLoader;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.thepeachbeetle.deployment.check.AppStartupDeploymentCheck;
import com.thepeachbeetle.deployment.config.DeploymentCheckConfigLoader;
import com.thepeachbeetle.messaging.hub.config.db.ConfigDBConfigLoader;
import com.thepeachbeetle.messaging.hub.config.db.ShortCodesDBConfigLoader;
import com.thepeachbeetle.messaging.hub.config.pricing.MoPriceMatrixConfigLoader;
import com.thepeachbeetle.messaging.hub.config.pricing.MtPriceMatrixConfigLoader;
import com.thepeachbeetle.messaging.hub.config.randomize.SenderRandomizerConfigLoader;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingConfigLoader;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodesConfigLoader;
import com.thepeachbeetle.messaging.hub.core.provisioning.client.config.ProvisioningApiClientConfigLoader;
import com.thepeachbeetle.messaging.hub.core.quota.client.config.QuotaApiClientConfigLoader;
import com.thepeachbeetle.services.networks.client.config.NetworkMatrixConfigLoader;

import com.nexmo.common.api.config.AuthConfigLoader;

import com.nexmo.voice.config.accounts.AccountCapabilitiesConfigLoader;
import com.nexmo.voice.config.api.InternalApiConfigLoader;
import com.nexmo.voice.config.applications.ApplicationsServiceConfigLoader;
import com.nexmo.voice.config.caches.CacheControlConfigLoader;
import com.nexmo.voice.config.cdrs.CdrsConfigLoader;
import com.nexmo.voice.config.charging.ChargingUpdaterConfigLoader;
import com.nexmo.voice.config.gateway.SIPGatewayInfoMatrixConfigLoader;
import com.nexmo.voice.config.gateway.asterisk.AsteriskManagerConfigLoader;
import com.nexmo.voice.config.********.LVNMappingsConfigLoader;
import com.nexmo.voice.config.prefix.PrefixMapConfigLoader;
import com.nexmo.voice.config.quota.ExtendedQuotaApiClientConfigLoader;
import com.nexmo.voice.config.sip.AsteriskAgiServerConfigLoader;

/**
 * <AUTHOR> Cook
 */
public class ConfigReader extends XmlAbstractReader {

    public final static String ROOT_NODE = "voice";

    private final ConfigLoader configLoader;
    private final DeploymentCheckConfigLoader deploymentCheckConfigLoader;
    private final JMXConfigLoader jmxConfigLoader;
    private final HttpServerConfigLoader httpServerConfigLoader;
    private final HttpServerConfigLoader httpInternalApiEndpointServerConfigLoader;
    private final ClusterCoreConfigLoader clusterCoreConfigLoader;
    private final MonitoringConfigLoader monitoringConfigLoader;
    private final ConfigDBConfigLoader configDbConfigLoader;
    private final ShortCodesDBConfigLoader shortCodesDBConfigLoader;
    private final CacheCoreConfigLoader cacheCoreConfigLoader;
    private final MtRoutingConfigLoader mtRoutingConfigLoader;
    private final ShortCodesConfigLoader shortCodesConfigLoader;
    private final ProvisioningApiClientConfigLoader provisioningApiClientConfigLoader;
    private final QuotaApiClientConfigLoader quotaApiConfigLoader;
    private final ExtendedQuotaApiClientConfigLoader extendedQuotaApiConfigLoader;
    private final ChargingConfigLoader chargingConfigLoader;
    private final MtPriceMatrixConfigLoader mtPriceMatrixConfigLoader;
    private final MoPriceMatrixConfigLoader moPriceMatrixConfigLoader;
    private final NetworkMatrixConfigLoader networkMatrixConfigLoader;
    private final CallbackConfigLoader callbackConfigLoader;
    private final CallbackConfigLoader eventCallbackConfigLoader;
    private final SIPGatewayInfoMatrixConfigLoader sipGatewayInfoMatrixConfigLoader;
    private final TTSGatewayInfoMatrixConfigLoader ttsGatewayInfoMatrixConfigLoader;
    private final LVNMappingsConfigLoader lvnMappingsConfigLoader;
    private final PrefixMapConfigLoader prefixMapConfigLoader;
    private final CacheControlConfigLoader cacheControlConfigLoader;
    private final ApplicationsServiceConfigLoader applicationsServiceConfigLoader;
    private final CallblockingServiceConfigLoader callblockingServiceConfigLoader;
    private final AuthConfigLoader authConfigLoader;
    private final ChargingUpdaterConfigLoader chargingUpdaterConfigLoader;
    private final CachePersistenceConfigLoader contextCacheConfigLoader;
    private final SSLClientConfigLoader sslClientConfigLoader;
    private final AsteriskManagerConfigLoader asteriskManagerConfigLoader;
    private final AsteriskAgiServerConfigLoader asteriskAgiServerConfigLoader;
    private final CountersConfigLoader countersConfigLoader;
    private final InternalApiConfigLoader internalApiConfigLoader;
    private final AccountCapabilitiesConfigLoader accountCapabilitiesConfigLoader;
    private final WhitelistedNumbersConfigLoader whitelistedNumbersConfigLoader;
    private final HttpCallbackBlockedIpRangeConfigLoader httpCallbackBlockedIpRangeConfigLoader;
    private final MetricsConfigLoader metricsConfigLoader;
    private final CdrsConfigLoader cdrsConfigLoader;
    private final CallBlocksConfigLoader callBlocksConfigLoader;
    private final DomainsServiceConfigLoader domainsServiceConfigLoader;
    private final EmergencyCallingConfigLoader emergencyCallingConfigLoader;
    private final DynamoDbConfigLoader ********ConfigLoader;
    private final VQuotaServiceConfigLoader vQuotaServiceConfigLoader;
    private final ViamAuthConfigLoader viamAuthConfigLoader;
    private final EmergencyAddressServiceConfigLoader emergencyAddressServiceConfigLoader;
    private final SenderRandomizerConfigLoader senderRandomizerConfigLoader;

    public ConfigReader() {
        this(false); // skipDB
    }

    public ConfigReader(boolean skipDB) {
        this.configLoader = new ConfigLoader(ROOT_NODE);
        addHandler(this.configLoader);
        this.deploymentCheckConfigLoader = new DeploymentCheckConfigLoader(ROOT_NODE + ".core." + DeploymentCheckConfigLoader.ROOT_NODE,
                                                                           skipDB,
                                                                           new AppStartupDeploymentCheck("voice", this.configLoader.getConfig()),
                                                                           this);
        addHandler(this.deploymentCheckConfigLoader);
        this.jmxConfigLoader = new JMXConfigLoader(ROOT_NODE + ".core." + JMXConfigLoader.ROOT_NODE);
        addHandler(this.jmxConfigLoader);
        this.httpServerConfigLoader = new HttpServerConfigLoader(ROOT_NODE + ".core." + HttpServerConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.httpServerConfigLoader);
        this.httpInternalApiEndpointServerConfigLoader = new HttpServerConfigLoader(ROOT_NODE + ".core.http-internal-api", skipDB);
        addHandler(this.httpInternalApiEndpointServerConfigLoader);
        this.clusterCoreConfigLoader = new ClusterCoreConfigLoader(ROOT_NODE + ".core." + ClusterCoreConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.clusterCoreConfigLoader);
        this.monitoringConfigLoader = new MonitoringConfigLoader(ROOT_NODE + ".core." + MonitoringConfigLoader.ROOT_NODE);
        addHandler(this.monitoringConfigLoader);
        this.configDbConfigLoader = new ConfigDBConfigLoader(ROOT_NODE + ".core." + ConfigDBConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.configDbConfigLoader);
        this.shortCodesDBConfigLoader = new ShortCodesDBConfigLoader(ROOT_NODE + ".core." + ShortCodesDBConfigLoader.ROOT_NODE);
        addHandler(this.shortCodesDBConfigLoader);
        this.cacheCoreConfigLoader = new CacheCoreConfigLoader(ROOT_NODE + ".core." + CacheCoreConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.cacheCoreConfigLoader);
        this.shortCodesConfigLoader = new ShortCodesConfigLoader(ROOT_NODE + "." + ShortCodesConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.shortCodesConfigLoader);
        this.provisioningApiClientConfigLoader = new ProvisioningApiClientConfigLoader(ROOT_NODE + "." + ProvisioningApiClientConfigLoader.ROOT_NODE);
        addHandler(this.provisioningApiClientConfigLoader);
        this.quotaApiConfigLoader = new QuotaApiClientConfigLoader(ROOT_NODE + ".core." + QuotaApiClientConfigLoader.ROOT_NODE);
        addHandler(this.quotaApiConfigLoader);
        this.extendedQuotaApiConfigLoader = new ExtendedQuotaApiClientConfigLoader(ROOT_NODE + ".core." + ExtendedQuotaApiClientConfigLoader.ROOT_NODE);
        addHandler(this.extendedQuotaApiConfigLoader);
        this.contextCacheConfigLoader = new CachePersistenceConfigLoader(ROOT_NODE + ".core." + "context-cache", skipDB);
        addHandler(this.contextCacheConfigLoader);
        this.networkMatrixConfigLoader = new NetworkMatrixConfigLoader(ROOT_NODE + ".core." + NetworkMatrixConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.networkMatrixConfigLoader);
        this.chargingConfigLoader = new ChargingConfigLoader(ROOT_NODE + "." + ChargingConfigLoader.ROOT_NODE);
        addHandler(this.chargingConfigLoader);
        this.mtPriceMatrixConfigLoader = new MtPriceMatrixConfigLoader(ROOT_NODE + "." + MtPriceMatrixConfigLoader.MT_PRICE_MATRIX_ROOT_NODE_NAME, skipDB);
        addHandler(this.mtPriceMatrixConfigLoader);
        this.moPriceMatrixConfigLoader = new MoPriceMatrixConfigLoader(ROOT_NODE + "." + MoPriceMatrixConfigLoader.MO_PRICE_MATRIX_ROOT_NODE_NAME, skipDB);
        addHandler(this.moPriceMatrixConfigLoader);
        this.mtRoutingConfigLoader = new MtRoutingConfigLoader(ROOT_NODE + "." + MtRoutingConfigLoader.MT_ROUTING_NODE_NAME, this.configLoader.getConfig().getConfigDbConfig(), skipDB);
        addHandler(this.mtRoutingConfigLoader);
        this.callbackConfigLoader = new CallbackConfigLoader(ROOT_NODE + ".core." + CallbackConfigLoader.ROOT_NODE);
        addHandler(this.callbackConfigLoader);
        this.eventCallbackConfigLoader = new CallbackConfigLoader(ROOT_NODE + ".core.event-" + CallbackConfigLoader.ROOT_NODE);
        addHandler(this.eventCallbackConfigLoader);
        this.sipGatewayInfoMatrixConfigLoader = new SIPGatewayInfoMatrixConfigLoader(ROOT_NODE + "." + SIPGatewayInfoMatrixConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.sipGatewayInfoMatrixConfigLoader);
        this.ttsGatewayInfoMatrixConfigLoader = new TTSGatewayInfoMatrixConfigLoader(ROOT_NODE + "." + TTSGatewayInfoMatrixConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.ttsGatewayInfoMatrixConfigLoader);
        this.lvnMappingsConfigLoader = new LVNMappingsConfigLoader(ROOT_NODE + "." + LVNMappingsConfigLoader.ROOT_NODE);
        addHandler(this.lvnMappingsConfigLoader);
        this.prefixMapConfigLoader = new PrefixMapConfigLoader(ROOT_NODE + "." + PrefixMapConfigLoader.ROOT_NODE);
        addHandler(this.prefixMapConfigLoader);
        this.callBlocksConfigLoader = new CallBlocksConfigLoader(ROOT_NODE + "." + CallBlocksConfigLoader.ROOT_NODE);
        addHandler(this.callBlocksConfigLoader);
        this.cacheControlConfigLoader = new CacheControlConfigLoader(ROOT_NODE + "." + CacheControlConfigLoader.ROOT_NODE);
        addHandler(this.cacheControlConfigLoader);
        this.applicationsServiceConfigLoader = new ApplicationsServiceConfigLoader(ROOT_NODE + "." + ApplicationsServiceConfigLoader.ROOT_NODE);
        addHandler(this.applicationsServiceConfigLoader);
        this.callblockingServiceConfigLoader = new CallblockingServiceConfigLoader(ROOT_NODE + "." + CallblockingServiceConfigLoader.ROOT_NODE);
        addHandler(this.callblockingServiceConfigLoader);
        this.authConfigLoader = new AuthConfigLoader(ROOT_NODE + "." + AuthConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.authConfigLoader);
        this.chargingUpdaterConfigLoader = new ChargingUpdaterConfigLoader(ROOT_NODE + "." + ChargingUpdaterConfigLoader.ROOT_NODE);
        addHandler(this.chargingUpdaterConfigLoader);
        this.internalApiConfigLoader = new InternalApiConfigLoader(ROOT_NODE + ".core.http-api." + InternalApiConfigLoader.ROOT_NODE);
        addHandler(this.internalApiConfigLoader);
        this.sslClientConfigLoader = new SSLClientConfigLoader(ROOT_NODE + ".core." + SSLClientConfigLoader.ROOT_NODE);
        addHandler(this.sslClientConfigLoader);
        this.asteriskManagerConfigLoader = new AsteriskManagerConfigLoader(ROOT_NODE + "." + AsteriskManagerConfigLoader.ROOT_NODE);
        addHandler(this.asteriskManagerConfigLoader);
        this.asteriskAgiServerConfigLoader = new AsteriskAgiServerConfigLoader(ROOT_NODE + "." + AsteriskAgiServerConfigLoader.ROOT_NODE);
        addHandler(this.asteriskAgiServerConfigLoader);
        this.countersConfigLoader = new CountersConfigLoader(ROOT_NODE + ".core." + CountersConfigLoader.ROOT_NODE, skipDB);
        addHandler(this.countersConfigLoader);
        this.accountCapabilitiesConfigLoader = new AccountCapabilitiesConfigLoader(ROOT_NODE + ".core.accounts." + AccountCapabilitiesConfigLoader.ROOT_NODE);
        addHandler(this.accountCapabilitiesConfigLoader);
        this.whitelistedNumbersConfigLoader = new WhitelistedNumbersConfigLoader(ROOT_NODE + "." + WhitelistedNumbersConfigLoader.ROOT_NODE);
        addHandler(this.whitelistedNumbersConfigLoader);
        this.httpCallbackBlockedIpRangeConfigLoader = new HttpCallbackBlockedIpRangeConfigLoader(ROOT_NODE + ".core." + HttpCallbackBlockedIpRangeConfigLoader.ROOT_NODE);
        addHandler(this.httpCallbackBlockedIpRangeConfigLoader);
        this.metricsConfigLoader = new MetricsConfigLoader(ROOT_NODE + ".core." + MetricsConfigLoader.ROOT_NODE);
        addHandler(this.metricsConfigLoader);
        this.cdrsConfigLoader = new CdrsConfigLoader(ROOT_NODE + "." + CdrsConfigLoader.ROOT_NODE);
        addHandler(this.cdrsConfigLoader);
        this.domainsServiceConfigLoader = new DomainsServiceConfigLoader(ROOT_NODE + "." + DomainsServiceConfig.ROOT_NODE);
        addHandler(this.domainsServiceConfigLoader);
        this.emergencyCallingConfigLoader = new EmergencyCallingConfigLoader(ROOT_NODE + "." + EmergencyCallingConfig.ROOT_NODE);
        addHandler(this.emergencyCallingConfigLoader);
        this.********ConfigLoader = new DynamoDbConfigLoader(ROOT_NODE + "." + DynamoDbConfigLoader.ROOT_NODE);
        addHandler(this.********ConfigLoader);
        this.vQuotaServiceConfigLoader = new VQuotaServiceConfigLoader(ROOT_NODE + "." + VQuotaServiceConfig.ROOT_NODE);
        addHandler(this.vQuotaServiceConfigLoader);
        this.viamAuthConfigLoader = new ViamAuthConfigLoader(ROOT_NODE + "." + ViamAuthConfig.ROOT_NODE);
        addHandler(this.viamAuthConfigLoader);
        this.emergencyAddressServiceConfigLoader = new EmergencyAddressServiceConfigLoader(ROOT_NODE + "." + EmergencyAddressServiceConfig.ROOT_NODE);
        addHandler(this.emergencyAddressServiceConfigLoader);
        this.senderRandomizerConfigLoader = new SenderRandomizerConfigLoader(ROOT_NODE + "." + SenderRandomizerConfigLoader.SENDER_RANDOMIZER_NODE_NAME, skipDB);
        addHandler(this.senderRandomizerConfigLoader);
    }

    public Config getConfig() {
        return this.configLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        this.configLoader.getConfig().setConfigXmlFileLocation(getCurrentFile());
        if (childHandler == this.deploymentCheckConfigLoader)
            this.configLoader.getConfig().setDeploymentCheckConfig(this.deploymentCheckConfigLoader.getConfig());
        if (childHandler == this.jmxConfigLoader)
            this.configLoader.getConfig().setJMXConfig(this.jmxConfigLoader.getConfig());
        if (childHandler == this.httpServerConfigLoader)
            this.configLoader.getConfig().setHttpServerConfig(this.httpServerConfigLoader.getConfig());
        if (childHandler == this.httpInternalApiEndpointServerConfigLoader)
            this.configLoader.getConfig().setHttpInternalApiEndpointConfig(this.httpInternalApiEndpointServerConfigLoader.getConfig());
        if (childHandler == this.clusterCoreConfigLoader)
            this.configLoader.getConfig().setClusterConfig(this.clusterCoreConfigLoader.getConfig());
        if (childHandler == this.configDbConfigLoader)
            this.configLoader.getConfig().setConfigDbConfig(ConfigDBConfigLoader.getLatestConfigDB());
        if (childHandler == this.shortCodesDBConfigLoader)
            this.configLoader.getConfig().setShortCodesDBConfig(this.shortCodesDBConfigLoader.getConfig());
        if (childHandler == this.monitoringConfigLoader)
            this.configLoader.getConfig().setMonitoringConfig(this.monitoringConfigLoader.getConfig());
        if (childHandler == this.cacheCoreConfigLoader)
            this.configLoader.getConfig().setCacheCoreConfig(this.cacheCoreConfigLoader.getConfig());
        if (childHandler == this.mtRoutingConfigLoader)
            this.configLoader.getConfig().setMtRoutingConfig(this.mtRoutingConfigLoader.getConfig());
        if (childHandler == this.shortCodesConfigLoader)
            this.configLoader.getConfig().setShortCodes(this.shortCodesConfigLoader.getShortCodes());
        if (childHandler == this.provisioningApiClientConfigLoader)
            this.configLoader.getConfig().setProvisioningApiClientConfig(this.provisioningApiClientConfigLoader.getConfig());
        if (childHandler == this.quotaApiConfigLoader)
            this.configLoader.getConfig().setQuotaAPIConfig(this.quotaApiConfigLoader.getConfig());
        if (childHandler == this.extendedQuotaApiConfigLoader)
            this.configLoader.getConfig().setExtendedQuotaAPIConfig(this.extendedQuotaApiConfigLoader.getConfig());
        if (childHandler == this.chargingConfigLoader)
            this.configLoader.getConfig().setChargingConfig(this.chargingConfigLoader.getConfig());
        if (childHandler == this.moPriceMatrixConfigLoader)
            this.configLoader.getConfig().setMoPriceMatrixList(this.moPriceMatrixConfigLoader.getMatrix());
        if (childHandler == this.mtPriceMatrixConfigLoader)
            this.configLoader.getConfig().setMtPriceMatrixList(this.mtPriceMatrixConfigLoader.getMatrix());
        if (childHandler == this.networkMatrixConfigLoader)
            this.configLoader.getConfig().setNetworksMatrixConfig(this.networkMatrixConfigLoader.getConfig());
        if (childHandler == this.callbackConfigLoader)
            this.configLoader.getConfig().setCallbackConfig(this.callbackConfigLoader.getConfig());
        if (childHandler == this.eventCallbackConfigLoader)
            this.configLoader.getConfig().setEventCallbackConfig(this.eventCallbackConfigLoader.getConfig());
        if (childHandler == this.sipGatewayInfoMatrixConfigLoader)
            this.configLoader.getConfig().setSIPGatewayInfoMatrixConfig(this.sipGatewayInfoMatrixConfigLoader.getConfig(), this.sipGatewayInfoMatrixConfigLoader.getSource());
        if (childHandler == this.ttsGatewayInfoMatrixConfigLoader)
            this.configLoader.getConfig().setTTSGatewayInfoMatrixConfig(this.ttsGatewayInfoMatrixConfigLoader.getConfig(), this.ttsGatewayInfoMatrixConfigLoader.getSource());
        if (childHandler == this.lvnMappingsConfigLoader)
            this.configLoader.getConfig().setLVNMappingsConfig(this.lvnMappingsConfigLoader.getConfig(), this.lvnMappingsConfigLoader.getSource());
        if (childHandler == this.prefixMapConfigLoader)
            this.configLoader.getConfig().setPrefixMapConfig(this.prefixMapConfigLoader.getConfig(), this.prefixMapConfigLoader.getSource());
        if (childHandler == this.callBlocksConfigLoader)
            this.configLoader.getConfig().setCallBlocksConfig(this.callBlocksConfigLoader.getConfig(), this.callBlocksConfigLoader.getSource());
        if (childHandler == this.cacheControlConfigLoader)
            this.configLoader.getConfig().setCachesConfig(this.cacheControlConfigLoader.getConfig());
        if (childHandler == this.applicationsServiceConfigLoader)
            this.configLoader.getConfig().setApplicationsServiceConfig(this.applicationsServiceConfigLoader.getConfig());
        if (childHandler == this.callblockingServiceConfigLoader)
            this.configLoader.getConfig().setCallblockingServiceConfig(this.callblockingServiceConfigLoader.getConfig());
        if (childHandler == this.authConfigLoader)
            this.configLoader.getConfig().setAuthConfig(this.authConfigLoader.getAuthConfig());
        if (childHandler == this.chargingUpdaterConfigLoader)
            this.configLoader.getConfig().setChargingUpdaterConfig(this.chargingUpdaterConfigLoader.getConfig());
        if (childHandler == this.contextCacheConfigLoader)
            this.configLoader.getConfig().setContextCacheConfig(this.contextCacheConfigLoader.getConfig());
        if (childHandler == this.sslClientConfigLoader)
            this.configLoader.getConfig().setSSLClientConfig(this.sslClientConfigLoader.getConfig());
        if (childHandler == this.asteriskManagerConfigLoader)
            this.configLoader.getConfig().setAsteriskManagerConfig(this.asteriskManagerConfigLoader.getConfig());
        if (childHandler == this.asteriskAgiServerConfigLoader)
            this.configLoader.getConfig().setAsteriskAgiServerConfig(this.asteriskAgiServerConfigLoader.getConfig());
        if (childHandler == this.internalApiConfigLoader)
            this.configLoader.getConfig().setInternalApiConfig(this.internalApiConfigLoader.getConfig());
        if (childHandler == this.accountCapabilitiesConfigLoader)
            this.configLoader.getConfig().setAccountCapabilitiesConfig(this.accountCapabilitiesConfigLoader.getConfig());
        if (childHandler == this.whitelistedNumbersConfigLoader)
            this.configLoader.getConfig().setWhitelistedNumbersConfig(this.whitelistedNumbersConfigLoader.getConfig());
        if (childHandler == this.httpCallbackBlockedIpRangeConfigLoader)
            this.configLoader.getConfig().setHttpCallbackBlockedIpRangeConfig(this.httpCallbackBlockedIpRangeConfigLoader.getConfig());
        if (childHandler == this.metricsConfigLoader)
            this.configLoader.getConfig().setMetricsConfig(this.metricsConfigLoader.getConfig());
        if (childHandler == this.cdrsConfigLoader)
            this.configLoader.getConfig().setCdrsConfig(this.cdrsConfigLoader.getConfig());
        if (childHandler == this.domainsServiceConfigLoader)
            this.configLoader.getConfig().setDomainsServiceConfig(this.domainsServiceConfigLoader.getConfig());
        if (childHandler == this.emergencyCallingConfigLoader)
            this.configLoader.getConfig().setEmergencyCallingConfig(this.emergencyCallingConfigLoader.getConfig());
        if (childHandler == this.********ConfigLoader)
            this.configLoader.getConfig().setDynamoDbConfig(this.********ConfigLoader.getConfig());
        if (childHandler == this.vQuotaServiceConfigLoader)
            this.configLoader.getConfig().setVQuotaServiceConfig(this.vQuotaServiceConfigLoader.getConfig());
        if (childHandler == this.viamAuthConfigLoader)
            this.configLoader.getConfig().setViamAuthConfig(this.viamAuthConfigLoader.getConfig());
        if (childHandler == this.emergencyAddressServiceConfigLoader)
            this.configLoader.getConfig().setEmergencyAddressServiceConfig(this.emergencyAddressServiceConfigLoader.getConfig());
        if (childHandler == this.senderRandomizerConfigLoader)
            this.configLoader.getConfig().setSenderRandomizerConfig(this.senderRandomizerConfigLoader.getConfig());
    }

}
