package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.ChargingConfigLoader;
import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;


public class ReloadChargingConfigReader extends XmlAbstractReader {

    private final ChargingConfigLoader chargingConfigLoader;
    private final Config config;


    public ReloadChargingConfigReader(Config config) {
        this.config = config;

        this.chargingConfigLoader = new ChargingConfigLoader(ConfigReader.ROOT_NODE + "." + ChargingConfigLoader.ROOT_NODE);
        addHandler(this.chargingConfigLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler == this.chargingConfigLoader)
            this.config.setChargingConfig(this.chargingConfigLoader.getConfig());
    }

}
