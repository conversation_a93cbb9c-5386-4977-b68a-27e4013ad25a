package com.nexmo.voice.config.callblocks;

import org.apache.log4j.Logger;
import org.jdom.Element;

import java.io.Serializable;
import java.util.*;

/*
* <call-blocks>
*    <call-block account="*" destination="SG" call-id="***********" />
*    <call-block account="*" destination="SG" call-id="***********" />
*    <call-block account="12345" destination="TH" call-id="***********" />
* </call-blocks>
 */

/**
 * <AUTHOR>
 */
public class CallBlocksConfig implements Serializable {
    private static final long serialVersionUID = -827825787636204899L;

    public HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap;

    private static final Logger Log = Logger.getLogger(CallBlocksConfig.class.getName());

    public CallBlocksConfig(HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap) {
        this.callBlockMap = callBlockMap;
    }

    public HashMap<String, HashMap<String, ArrayList<String>>> getCallBlockMap() {
        return this.callBlockMap;
    }

    public Set<String> getCallBlockAccounts() {
        return this.callBlockMap.keySet();
    }

    public boolean hasCallBlocks() {
        return !this.callBlockMap.isEmpty();
    }

    public boolean hasCallBlock(String account, String destination, String callId) {
        List<String> accountList = Arrays.asList("*", account);

        for (String accountItem : accountList) {
            if (this.callBlockMap.containsKey(accountItem)) {
                if (this.callBlockMap.get(accountItem).containsKey(destination)) {
                    if (this.callBlockMap.get(accountItem).get(destination).contains(callId)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public Element toXML() {
        Element callBlocks = new Element(CallBlocksConfigLoader.ROOT_NODE);

        for (HashMap.Entry<String, HashMap<String, ArrayList<String>>> callBlock : this.getCallBlockMap().entrySet()) {
            for (HashMap.Entry<String, ArrayList<String>> destination : callBlock.getValue().entrySet()) {
                for (String callId : destination.getValue()) {
                    Element callBlockElement = new Element(CallBlockConfigLoader.ROOT_NODE);
                    callBlockElement.setAttribute("account",callBlock.getKey());
                    callBlockElement.setAttribute("destination", destination.getKey());
                    callBlockElement.setAttribute("call-id", callId);
                    callBlocks.addContent(callBlockElement);
                }
            }
        }

        return callBlocks;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CallBlocksConfig that = (CallBlocksConfig) o;
        return Objects.equals(callBlockMap, that.callBlockMap);
    }

    @Override
    public int hashCode() {
        return Objects.hash(callBlockMap);
    }
}
