package com.nexmo.voice.config.applications;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class InterServiceAuthConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "inter-service-auth";
    
    public InterServiceAuthConfig config;

    public InterServiceAuthConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public InterServiceAuthConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <inter-service-auth>
            String issuer = content.getAttribute("issuer", true);
            String privateKeyFile = content.getAttribute("private-key-file", true);
            String publicKeysPath = content.getAttribute("public-keys-path", true);
            config = new InterServiceAuthConfig(issuer, privateKeyFile, publicKeysPath);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // <inter-service-auth>
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
