package com.nexmo.voice.config.reload;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.config.gateway.TTSGatewayInfoMatrixConfigLoader;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;


public class ReloadTTSGatewayInfoMatrixConfigReader extends XmlAbstractReader {

    private final TTSGatewayInfoMatrixConfigLoader gatewayInfoMatrixConfigLoader;
    private final Config config;


    public ReloadTTSGatewayInfoMatrixConfigReader(Config config) {
        this.config = config;
        this.gatewayInfoMatrixConfigLoader = new TTSGatewayInfoMatrixConfigLoader(ConfigReader.ROOT_NODE + "." + TTSGatewayInfoMatrixConfigLoader.ROOT_NODE, false);
        addHandler(this.gatewayInfoMatrixConfigLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) {
        if (childHandler == this.gatewayInfoMatrixConfigLoader)
            this.config.setTTSGatewayInfoMatrixConfig(this.gatewayInfoMatrixConfigLoader.getConfig(), this.gatewayInfoMatrixConfigLoader.getSource());
    }

}
