package com.nexmo.voice.config.prefix;


public class PrefixMapSource implements java.io.Serializable {

    private static final long serialVersionUID = 5421826940441864356L;

    private final String source;

    public PrefixMapSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof PrefixMapSource))
            return false;
        PrefixMapSource other = (PrefixMapSource) o;

        return this.source.equals(other.source);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + source.hashCode();
        return result;
    }

}
