package com.nexmo.voice.config.cdrs;

/*
 * <cdrs cdr-type="<%= @cdr_type %>" />
 */

import org.jdom.Element;

import com.nexmo.voice.core.types.CDRType;

public class CdrsConfig implements java.io.Serializable {

    private static final long serialVersionUID = 1232226360832594836L;

    private final CDRType cdrType;
    private final boolean backupCdrs;

    public CdrsConfig(CDRType cdrType, boolean backupCdrs) {
        this.cdrType = cdrType;
        this.backupCdrs = backupCdrs;
    }

    public CDRType getCDRType() {
        return this.cdrType;
    }
    
    public boolean shouldBackupCDRs() {
        return this.backupCdrs;
    }

    public Element toXML() {
        final Element cdrs = new Element("cdrs");
        cdrs.setAttribute("cdr-type", String.valueOf(this.cdrType.getType()));
        cdrs.setAttribute("backup-cdrs", String.valueOf(this.backupCdrs));
        return cdrs;
    }

    @Override
    public String toString() {
        return "CdrsConfig [cdrType=" + cdrType + ", backupCdrs=" + backupCdrs + "]";
    }

}
