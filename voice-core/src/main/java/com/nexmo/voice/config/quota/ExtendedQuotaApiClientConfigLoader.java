package com.nexmo.voice.config.quota;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public final class ExtendedQuotaApiClientConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "extended-quota-api";

    private boolean logEnabled;
    private boolean allowSkipBalanceCheckOnAGI;

    private ExtendedQuotaApiClientConfig config;

    public ExtendedQuotaApiClientConfigLoader(String nodeName) {
        super(nodeName);
    }

    public ExtendedQuotaApiClientConfig getConfig() {
        return this.config;
    }


    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.logEnabled = parseBoolean(content.getAttribute("log-enabled", false));
            this.allowSkipBalanceCheckOnAGI = parseBoolean(content.getAttribute("allow-skip-balance-check-on-agi", false));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new ExtendedQuotaApiClientConfig(this.logEnabled, this.allowSkipBalanceCheckOnAGI);
            notifyComplete();
        }
    }


}
