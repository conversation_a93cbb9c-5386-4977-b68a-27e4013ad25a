package com.nexmo.voice.config.gateway;

import java.util.HashMap;
import java.util.Map;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class SIPGatewayInfoMatrixConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "gateway-info";

    public final SupplierMappingConfigLoader supplierMappingConfigLoader;
    public final Map<String, SupplierMappingConfig> supplierToInfoMap;

    public SIPGatewayInfoMatrixConfig config;
    public GatewayInfoMatrixSource source;
    public String path;
    public String mask;
    public String version;

    public SIPGatewayInfoMatrixConfigLoader(final String nodeName, final boolean skipDB) {
        this(nodeName, "sip.xml", skipDB);
    }

    public SIPGatewayInfoMatrixConfigLoader(final String nodeName, final String source, final boolean skipDB) {
        super(nodeName);
        this.supplierMappingConfigLoader = new SupplierMappingConfigLoader(getSubNodeName(nodeName, SupplierMappingConfigLoader.ROOT_NODE), skipDB);
        addHandler(this.supplierMappingConfigLoader);
        this.supplierToInfoMap = new HashMap<>();
        this.source = new GatewayInfoMatrixSource(source);
    }

    public SIPGatewayInfoMatrixConfig getConfig() {
        return this.config;
    }

    public GatewayInfoMatrixSource getSource() {
        return this.source;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.path = content.getAttribute("path", false);
            this.mask = content.getAttribute("filemask", false);
            this.version = content.getAttribute("version", false, "LATEST");
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            String filename = GatewayInfoMatrixConfigUtils.selectFile(this.path, this.mask, this.version);
            if (filename != null) {
                // Ignore body, load from file instead
                this.source = new GatewayInfoMatrixSource(filename); // TODO: Add path/mask/version
                this.config = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(filename);
            } else {
                this.config = new SIPGatewayInfoMatrixConfig(this.supplierToInfoMap);
            }
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof SupplierMappingConfigLoader) {
            SupplierMappingConfig sc = ((SupplierMappingConfigLoader) childHandler).getConfig();
            this.supplierToInfoMap.put(sc.getName(), sc);
        }
    }

}
