package com.nexmo.voice.config.charging;

import org.jdom.Element;

import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.thepeachbeetle.common.period.Periods;

public class UpdateTaskConfig implements java.io.Serializable {

    private static final long serialVersionUID = 4848468705379414179L;

    private final VoiceApplicationType voiceApplicationType;
    private final long sweepingPeriod; // in millis

    private final int sweepingCount;
    private final Periods sweepingUnit;

    public UpdateTaskConfig(final VoiceApplicationType voiceApplicationType,
                            final int sweepingCount,
                            final Periods sweepingUnit) {
        this.voiceApplicationType = voiceApplicationType;
        this.sweepingPeriod = Periods.asMilliseconds(sweepingUnit, sweepingCount);

        this.sweepingCount = sweepingCount;
        this.sweepingUnit = sweepingUnit;
    }

    public VoiceApplicationType getVoiceApplicationType() {
        return this.voiceApplicationType;
    }

    public long getSweepingPeriod() {
        return this.sweepingPeriod;
    }

    public Element toXML() {
        Element updateTask = new Element("update-task");

        updateTask.setAttribute("product-class", this.voiceApplicationType.toString());
        updateTask.setAttribute("sweeping-interval-count", String.valueOf(this.sweepingCount));
        updateTask.setAttribute("sweeping-interval-unit", this.sweepingUnit == null ? "" : this.sweepingUnit.getKey());

        return updateTask;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.voiceApplicationType == null) ? 0 : this.voiceApplicationType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        UpdateTaskConfig other = (UpdateTaskConfig) obj;
        if (this.voiceApplicationType == null) {
            if (other.voiceApplicationType != null)
                return false;
        } else if (!this.voiceApplicationType.equals(other.voiceApplicationType))
            return false;
        return true;
    }
}
