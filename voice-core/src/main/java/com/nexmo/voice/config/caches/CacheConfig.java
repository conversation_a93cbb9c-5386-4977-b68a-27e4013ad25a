package com.nexmo.voice.config.caches;

/*
 * <caches>
 *   <cache type="application" size="1000" expiry="PT60M" refresh="PT10M" />
 * </caches>
 */

import org.jdom.Element;

import java.time.Duration;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;

public class CacheConfig implements java.io.Serializable {

    private static final long serialVersionUID = 5149718888540381079L;

    private final CacheType type;
    private final int size;
    private final Duration expiry;
    private final Duration refresh;

    public CacheConfig(final String type, final String size, final String expiry, final String refresh) {
        this.type = CacheType.from(type);

        if (size != null)
            this.size = Integer.valueOf(size);
        else
            this.size = this.type.getDefaultSize();
        if ((this.size < 0) || (this.size > 1000000))
            throw new IllegalArgumentException("Invalid size: " + size);

        if (expiry != null)
            this.expiry = Duration.parse(expiry);
        else
            this.expiry = this.type.getDefaultExpiry();
        if (this.expiry.isNegative())
            throw new IllegalArgumentException("Expiry duration cannot be negative");

        if (refresh != null)
            this.refresh = Duration.parse(refresh);
        else
            this.refresh = this.type.getDefaultRefresh();
        if (this.refresh.isNegative())
            throw new IllegalArgumentException("Refresh duration cannot be negative");
    }

    public CacheType getType() {
        return this.type;
    }

    public int getSize() {
        return this.size;
    }

    public Duration getExpiry() {
        return this.expiry;
    }

    public Duration getRefresh() {
        return this.refresh;
    }

    public Element toXML() {
        Element cache = new Element("cache");

        cache.setAttribute("type", this.getType().toString());
        cache.setAttribute("size", Integer.toString(this.getSize()));
        cache.setAttribute("expiry", this.getExpiry().toString());
        cache.setAttribute("refresh", this.getRefresh().toString());

        return cache;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof CacheConfig))
            return false;
        CacheConfig other = (CacheConfig) o;

        // Mandatory parameters
        if (!this.type.equals(other.type))
            return false;
        if (this.size != other.size)
            return false;
        if (!this.expiry.equals(other.expiry))
            return false;
        if (!this.refresh.equals(other.refresh))
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + type.toString().hashCode();
        result = prime * result + size;
        result = prime * result + expiry.hashCode();
        result = prime * result + refresh.hashCode();
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CacheConfig: ");
        sb.append(type);
        sb.append("=>");
        sb.append(Integer.toString(size));
        sb.append(",");
        sb.append(expiry);
        sb.append(",");
        sb.append(refresh);
        return sb.toString();
    }

}
