package com.nexmo.voice.config.mappings;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.xml.LoaderException;

public class LVNMappingsConfigUtils {

    private static final Logger Log = Logger.getLogger(LVNMappingsConfigUtils.class.getName());

    public static LVNMappingsConfig loadConfigFromXMLFile(String filename) {
        Log.info("Loading XML file \"" + filename + "\"");
        if (filename == null)
            return null;

        try {
            LVNMappingsConfigReader reader = new LVNMappingsConfigReader(filename);
            reader.read(new File(filename));
            LVNMappingsConfig config = reader.getConfig();
            Log.info("Successfully loaded XML file \"" + filename + "\"");
            return config;
        } catch (LoaderException ex) {
            Log.error("Failed to load XML file \"" + filename + "\"", ex);
            return null;
        }
    }

    public static String selectFile(String path, String mask, String version) {
        if (path == null)
            return null;
        if (mask == null)
            mask = "*.xml";
        if (version == null)
            version = "LATEST";

        List<String> files = null;
        try {
            files = getFileList(path, mask);
        } catch (IOException ex) {
            Log.error("Failed to retrieve file list for path = \"" + path + "\" and mask \"" + mask + "\"", ex);
            return null;
        }
        if ((files == null) || files.isEmpty())
            return null;

        String chosen = null;
        if ("LATEST".equalsIgnoreCase(version)) {
            // ISO 8601 timestamp means most recent is lexicographically last
            Collections.sort(files);
            chosen = files.get(files.size() - 1);
        } else {
            for (String f : files) {
                if (f.contains(version)) { // FIXME: handle multiple matches
                    chosen = f;
                    break;
                }
            }
        }

        Log.info("selectFile(\"" + path + "\", \"" + mask + "\", \"" + version + "\") => \"" + chosen + "\"");

        if (chosen == null)
            return null;
        else
            return FileSystems.getDefault().getPath(path, chosen).toString();
    }

    public static String compareConfigs(LVNMappingsConfig oldConfig, LVNMappingsConfig newConfig) {
        return oldConfig.diff(newConfig);
    }

    //
    // Internal helper methods
    //
    private static List<String> getFileList(String path, String mask) throws IOException {
        final FileSystem fs = FileSystems.getDefault();
        final PathMatcher pm = fs.getPathMatcher("glob:" + mask);

        return Files.list(Paths.get(path))
                    .map(p -> p.getFileName()) // Filename *only*, otherwise we need to match full path
                    .filter(p -> pm.matches(p))
                    .map(p -> p.toString())
                    .collect(Collectors.toList());
    }

}