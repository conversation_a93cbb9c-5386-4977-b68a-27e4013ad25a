package com.nexmo.voice.config.gateway.asterisk;


import org.jdom.Element;

import com.thepeachbeetle.common.period.Periods;


public class AsteriskManagerConfig implements java.io.Serializable {

    private static final long serialVersionUID = 1232226360832594835L;

    private final boolean enabled;
    private final String productClass;
    private final String hostname;
    private final int port;
    private final String username;
    private final String password;

    private final boolean originateSocketEnabled;
    private final String originateSocketHostname;
    private final int originateSocketPort;
    private final String originateSocketUsername;
    private final String originateSocketPassword;

    private final boolean pingThreadEnabled;
    private final long pingThreadInterval;
    private final long pingThreadTimeout;

    private final boolean initialRetryEnabled;
    private final long initialRetryMax;
    private final int initialRetryTimeout;
    private final Periods initialRetryUnit;

    private final long initialRetryInterval;

    public AsteriskManagerConfig(final boolean enabled,
                                 final String product,
                                 final String hostname,
                                 final int port,
                                 final String username,
                                 final String password,
                                 final boolean pingThreadEnabled,
                                 final long pingThreadInterval,
                                 final long pingThreadTimeout,
                                 final boolean initialRetryEnabled,
                                 final long initialRetryMax,
                                 final int initialRetryTimeout,
                                 final Periods initialRetryUnit,
                                 final boolean originateSocketEnabled,
                                 final String originateSocketHostname,
                                 final int originateSocketPort,
                                 final String originateSocketUsername,
                                 final String originateSocketPassword) {
        this.enabled = enabled;
        this.productClass = product;
        this.hostname = hostname;
        this.port = port;
        this.username = username;
        this.password = password;
        this.pingThreadEnabled = pingThreadEnabled;
        this.pingThreadInterval = pingThreadInterval;
        this.pingThreadTimeout = pingThreadTimeout;

        this.initialRetryEnabled = initialRetryEnabled;
        this.initialRetryMax = initialRetryMax;
        this.initialRetryTimeout = initialRetryTimeout;
        this.initialRetryUnit = initialRetryUnit;

        this.initialRetryInterval = Periods.asMilliseconds(initialRetryUnit, initialRetryTimeout);

        this.originateSocketEnabled = originateSocketEnabled;
        this.originateSocketHostname = originateSocketHostname;
        this.originateSocketPort = originateSocketPort;
        this.originateSocketPassword = originateSocketPassword;
        this.originateSocketUsername = originateSocketUsername;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public String getProductClass() {
        return this.productClass;
    }

    public String getHostname() {
        return this.hostname;
    }

    public int getPort() {
        return this.port;
    }

    public String getUsername() {
        return this.username;
    }

    public String getPassword() {
        return this.password;
    }

    public boolean isPingThreadEnabled() {
        return this.pingThreadEnabled;
    }

    public long getPingThreadInterval() {
        return this.pingThreadInterval;
    }

    public long getPingThreadTimeout() {
        return this.pingThreadTimeout;
    }

    public boolean isInitialRetryEnabled() {
        return this.initialRetryEnabled;
    }

    public long getInitialRetryMax() {
        return this.initialRetryMax;
    }

    public long getInitialRetryInterval() {
        return this.initialRetryInterval;
    }

    public boolean isOriginateSocketEnabled() {
        return this.originateSocketEnabled;
    }

    public String getOriginateSocketHostname() {
        return this.originateSocketHostname;
    }

    public int getOriginateSocketPort() {
        return this.originateSocketPort;
    }

    public String getOriginateSocketUsername() {
        return this.originateSocketUsername;
    }

    public String getOriginateSocketPassword() {
        return this.originateSocketPassword;
    }

    public Element toXML() {
        final Element asteriskManager = new Element("asterisk-manager");
        asteriskManager.setAttribute("enabled", String.valueOf(this.enabled));
        asteriskManager.setAttribute("product-class", this.productClass);
        asteriskManager.setAttribute("hostname", this.hostname);
        asteriskManager.setAttribute("port", String.valueOf(this.port));
        asteriskManager.setAttribute("username", this.username);
        asteriskManager.setAttribute("password", this.password);

        final Element pingThread = new Element("asterisk-ping-thread");
        pingThread.setAttribute("enabled", String.valueOf(this.enabled));
        pingThread.setAttribute("interval", "" + this.pingThreadInterval);
        pingThread.setAttribute("timeout", "" + this.pingThreadTimeout);
        asteriskManager.addContent(pingThread);

        final Element originateSocket = new Element("asterisk-originate-socket");
        originateSocket.setAttribute("hostname", this.hostname);
        originateSocket.setAttribute("port", String.valueOf(this.port));
        originateSocket.setAttribute("username", this.username);
        originateSocket.setAttribute("password", this.password);
        asteriskManager.addContent(originateSocket);

        final Element initialRetry = new Element("initial-retry");
        initialRetry.setAttribute("enabled", String.valueOf(this.enabled));
        initialRetry.setAttribute("number-retries", String.valueOf(this.initialRetryMax));
        initialRetry.setAttribute("timeout", String.valueOf(this.initialRetryTimeout));
        initialRetry.setAttribute("unit", this.initialRetryUnit == null ? "" : this.initialRetryUnit.getKey());

        return asteriskManager;
    }

}
