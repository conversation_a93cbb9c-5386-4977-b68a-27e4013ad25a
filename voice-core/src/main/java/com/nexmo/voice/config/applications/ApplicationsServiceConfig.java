package com.nexmo.voice.config.applications;

/*
 * <applications-service enabled="true" applications-service-host="applications-service.qa" applications-service-port="4170" base-path="/internal/applications">
 *   <inter-service-auth issuer="" private-key-file="" public-keys-path="" />
 *   <!-- TODO: TLS params to enable HTTPS --> 
 * </applications-service>
 */

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;


public class ApplicationsServiceConfig implements java.io.Serializable {
    private final static Logger Log = LogManager.getLogger(ApplicationsServiceConfig.class);
    private static final long serialVersionUID = 624716791915619819L;

    private final static String PROTOCOL = "http";
    
    private final boolean enabled;
    private final String host;
    private final int port;
    private final String basePath;
    private final InterServiceAuthConfig authConfig;

    private final String failoverFirstHost;
    private final String failoverSecondHost;
    private final int timeout;
    private final int retryCount;
    private final int retryTimeout;

    public ApplicationsServiceConfig(boolean enabled, String host, int port, String basePath, InterServiceAuthConfig authConfig, String failoverFirst, String failoverSecond, int retryCount, int timeout, int retryTimeout) {
        this.enabled = enabled;
        this.host = host;
        this.port = port;
        this.basePath = basePath;
        this.authConfig = authConfig;
        this.failoverFirstHost = failoverFirst;
        this.failoverSecondHost = failoverSecond;
        this.retryCount = retryCount;
        this.timeout = timeout;
        this.retryTimeout = retryTimeout;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public InterServiceAuthConfig getInterServiceAuthConfig() {
        return authConfig;
    }

    public String constructUri(String applicationId) {
        StringBuilder sb = new StringBuilder();
        sb.append(PROTOCOL);
        sb.append("://");
        sb.append(host);
        sb.append(":");
        sb.append(Integer.toString(port));
        if (!basePath.startsWith("/"))
            sb.append("/");
        sb.append(basePath);
        if (!basePath.endsWith("/"))
            sb.append("/");
        sb.append(applicationId);
        return sb.toString();
    }

    public String constructUri(String host, String applicationId) {
        StringBuilder sb = new StringBuilder();
        sb.append(PROTOCOL);
        sb.append("://");
        sb.append(host);
        sb.append(":");
        sb.append(Integer.toString(port));
        if (!basePath.startsWith("/"))
            sb.append("/");
        sb.append(basePath);
        if (!basePath.endsWith("/"))
            sb.append("/");
        sb.append(applicationId);
        return sb.toString();
    }

    public Element toXML() {
        Element applicationsService = new Element("applications-service");
        applicationsService.setAttribute("enabled", Boolean.toString(this.enabled));
        applicationsService.setAttribute("applications-service-host", this.host);
        applicationsService.setAttribute("applications-service-failover-self-host", this.failoverFirstHost);
        applicationsService.setAttribute("applications-service-failover-sister-host", this.failoverSecondHost);
        applicationsService.setAttribute("applications-service-port", Integer.toString(this.port));
        applicationsService.setAttribute("base-path", this.basePath);
        applicationsService.setAttribute("retry-count", Integer.toString(this.retryCount));
        applicationsService.setAttribute("timeout", Integer.toString(this.timeout));
        applicationsService.setAttribute("retry-timeout", Integer.toString(this.retryTimeout));

        Element authXML = authConfig.toXML();
        applicationsService.addContent(authXML);

        return applicationsService;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump ApplicationsServiceConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

    public String getFailoverFirstHost() {
        return failoverFirstHost;
    }

    public String getFailoverSecondHost() {
        return failoverSecondHost;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public int getRetryTimeout() {
        return retryTimeout;
    }

    public int getTimeout() {
        return timeout;
    }
}
