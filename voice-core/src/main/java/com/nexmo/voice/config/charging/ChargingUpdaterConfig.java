package com.nexmo.voice.config.charging;

import java.util.Collections;
import java.util.Set;

import org.jdom.Element;

import com.thepeachbeetle.common.period.Periods;

public class ChargingUpdaterConfig implements java.io.Serializable {

    private static final long serialVersionUID = -4812087408946513697L;

    private final boolean enabled;
    private final long shutdownPeriod;
    private final Set<UpdateTaskConfig> updateTaskConfigs;

    private final int shutdownCount;
    private final Periods shutdownUnit;

    public ChargingUpdaterConfig(final boolean enabled,
                                 final int shutdownCount,
                                 final Periods shutdownUnit,
                                 Set<UpdateTaskConfig> updateTaskConfigs) {
        this.enabled = enabled;
        this.shutdownPeriod = Periods.asMilliseconds(shutdownUnit, shutdownCount);
        this.updateTaskConfigs = Collections.unmodifiableSet(updateTaskConfigs);

        this.shutdownCount = shutdownCount;
        this.shutdownUnit = shutdownUnit;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public long getShutdownPeriod() {
        return this.shutdownPeriod;
    }

    public Set<UpdateTaskConfig> getUpdateTaskConfigs() {
        return this.updateTaskConfigs;
    }

    public Element toXML() {
        Element chargingUpdater = new Element("charging-updater");

        chargingUpdater.setAttribute("enabled", String.valueOf(this.enabled));
        chargingUpdater.setAttribute("shutdown-interval-count", String.valueOf(this.shutdownCount));
        chargingUpdater.setAttribute("shutdown-interval-unit", this.shutdownUnit == null ? "" : this.shutdownUnit.getKey());

        for (UpdateTaskConfig taskConfig : this.updateTaskConfigs)
            chargingUpdater.addContent(taskConfig.toXML());

        return chargingUpdater;
    }

}
