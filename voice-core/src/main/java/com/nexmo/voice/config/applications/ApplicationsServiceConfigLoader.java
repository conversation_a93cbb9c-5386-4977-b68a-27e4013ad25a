package com.nexmo.voice.config.applications;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class ApplicationsServiceConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "applications-service";

    private static final String DEFAULT_ENABLED = "false";
    private static final String DEFAULT_PATH = "/internal/applications";

    private final InterServiceAuthConfigLoader authConfigLoader;

    private boolean enabled;
    private String host;
    private int port;
    private String basePath;
    private InterServiceAuthConfig authConfig;

    private ApplicationsServiceConfig config;

    private String failoverFirstHost;
    private String failoverSecondHost;
    private int timeout;
    private int retryCount;
    private int retryTimeout;

    public ApplicationsServiceConfigLoader(final String nodeName) {
        super(nodeName);

        authConfigLoader = new InterServiceAuthConfigLoader(getSubNodeName(nodeName, InterServiceAuthConfigLoader.ROOT_NODE));
        addHandler(authConfigLoader);
    }

    public ApplicationsServiceConfig getConfig() {
        return config;
    }


    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <applications-service>
            this.enabled = parseBoolean(content.getAttribute("enabled", false, DEFAULT_ENABLED));
            this.host = content.getAttribute("applications-service-host", this.enabled);
            this.port = parseInt(content.getAttribute("applications-service-port", this.enabled));
            this.basePath = content.getAttribute("base-path", false, DEFAULT_PATH);
            this.failoverFirstHost = content.getAttribute("applications-service-failover-self-host", this.enabled);
            this.failoverSecondHost = content.getAttribute("applications-service-failover-sister-host", this.enabled);
            this.timeout = parseInt(content.getAttribute("timeout", false, String.valueOf(this.timeout)));
            this.retryCount = parseInt(content.getAttribute("retry-count", false, String.valueOf(this.retryCount)));
            this.retryTimeout = parseInt(content.getAttribute("retry-timeout", false, String.valueOf(this.retryTimeout)));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new ApplicationsServiceConfig(this.enabled, this.host, this.port, this.basePath, this.authConfig, this.failoverFirstHost, this.failoverSecondHost, this.retryCount, this.timeout, this.retryTimeout);
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof InterServiceAuthConfigLoader) {
            authConfig = ((InterServiceAuthConfigLoader) childHandler).getConfig();
        }
    }

}
