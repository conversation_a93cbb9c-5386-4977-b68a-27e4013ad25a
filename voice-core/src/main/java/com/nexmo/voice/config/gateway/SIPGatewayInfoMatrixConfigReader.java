package com.nexmo.voice.config.gateway;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

/**
 * Read gateway info (suppliers) config from a separate file...
 * 
 * Based on com.nexmo.voice.config.ConfigReader
 */
public class SIPGatewayInfoMatrixConfigReader extends XmlAbstractReader {

    public final static String ROOT_NODE = "gateway-info";

    private final SIPGatewayInfoMatrixConfigLoader gatewayInfoMatrixConfigLoader;


    public SIPGatewayInfoMatrixConfigReader(String source) {
        this(source, false); // skipDB
    }

    public SIPGatewayInfoMatrixConfigReader(String source, boolean skipDB) {
        this.gatewayInfoMatrixConfigLoader = new SIPGatewayInfoMatrixConfigLoader(ROOT_NODE, source, skipDB);
        addHandler(this.gatewayInfoMatrixConfigLoader);
    }

    public SIPGatewayInfoMatrixConfig getConfig() {
        return this.gatewayInfoMatrixConfigLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
