package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.config.api.InternalApiConfigLoader;

public class ReloadInternalApiConfigReader extends XmlAbstractReader {

    private final InternalApiConfigLoader internalApiConfigLoader;
    private final Config config;


    public ReloadInternalApiConfigReader(Config config) {
        this.config = config;

        this.internalApiConfigLoader = new InternalApiConfigLoader(ConfigReader.ROOT_NODE + ".core.http-api." + InternalApiConfigLoader.ROOT_NODE);
        addHandler(this.internalApiConfigLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler == this.internalApiConfigLoader)
            this.config.setInternalApiConfig(this.internalApiConfigLoader.getConfig());
    }

}
