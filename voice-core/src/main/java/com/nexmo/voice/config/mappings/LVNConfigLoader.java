package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class LVNConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "lvns";
    public static final String LIST_NODE = "lvn";

    private final String listNodeName;

    private LVNConfig config;

    private String number;
    private String group;
    private String description;

    public LVNConfigLoader(final String nodeName) {
        super(nodeName);
        this.listNodeName = getSubNodeName(LIST_NODE);
    }

    public LVNConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <lvns>
            // Start of list
        } else if (this.listNodeName.equals(node)) { // <lvn>
            config = null;
            number = content.getAttribute("number", true);
            group = content.getAttribute("group", true);
            description = null; // Passed to endNode() as contentData
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // </lvns>
            // End of list
        } else if (this.listNodeName.equals(node)) { // </lvn>
            if ((contentData != null) && !contentData.trim().isEmpty()) {
                description = contentData.trim();
            }
            config = new LVNConfig(number, group, description);
            notifyComplete();
            config = null;
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        // Never called, because we don't have any child handlers
    }

}
