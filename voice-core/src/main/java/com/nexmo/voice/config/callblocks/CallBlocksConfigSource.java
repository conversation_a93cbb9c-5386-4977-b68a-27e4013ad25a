package com.nexmo.voice.config.callblocks;

import org.apache.log4j.Logger;

import java.io.Serializable;
import java.util.Objects;

public class CallBlocksConfigSource implements Serializable {

    private static final long serialVersionUID = -277527096904471100L;

    private static final Logger Log = Logger.getLogger(CallBlocksConfigLoader.class.getName());

    private final String source;

    public CallBlocksConfigSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CallBlocksConfigSource that = (CallBlocksConfigSource) o;
        return getSource().equals(that.getSource());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSource());
    }
}
