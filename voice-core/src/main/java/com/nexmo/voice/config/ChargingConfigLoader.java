package com.nexmo.voice.config;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import com.nexmo.voice.config.ChargingConfig.CountrySpecificInfo;


public class ChargingConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "charging";

    private final String countrySpecificNode;
    private final String priceNode;

    private ChargingConfig config;

    private BigDecimal minPrice;
    private BigDecimal defaultCost;
    private BigDecimal defaultSipPrice;
    private BigDecimal defaultSipCost;
    private BigDecimal defaultSipDialInPrice;
    private BigDecimal defaultSipDestinationPrice;
    private BigDecimal defaultInboundPrice;
    private BigDecimal defaultInboundCost;
    private long minIncrement;
    private long recurringIncrement;
    private BigDecimal ttsNGMinPrice;

    private Map<String, CountrySpecificInfo> countrySpecificInfo;

    public ChargingConfigLoader(final String nodeName) {
        super(nodeName);
        this.countrySpecificNode = getSubNodeName(nodeName, "country-specific");
        this.priceNode = getSubNodeName(nodeName, "price");
    }

    public ChargingConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.minPrice = getAttributeAsBigDecimal(content, "min-price");
            this.defaultCost = getAttributeAsBigDecimal(content, "default-cost");
            this.defaultSipPrice = getAttributeAsBigDecimal(content, "default-sip-price");
            this.defaultSipCost = getAttributeAsBigDecimal(content, "default-sip-cost");
            this.defaultSipDialInPrice = getAttributeAsBigDecimal(content, "default-sip-dial-in-price");
            this.defaultSipDestinationPrice = getAttributeAsBigDecimal(content, "default-sip-destination-price");
            this.defaultInboundPrice = getAttributeAsBigDecimal(content, "default-inbound-price");
            this.defaultInboundCost = getAttributeAsBigDecimal(content, "default-inbound-cost");
            this.minIncrement = parseLong(content.getAttribute("min-increment", true));
            this.recurringIncrement = parseLong(content.getAttribute("recurring-increment", true));
            this.ttsNGMinPrice = getAttributeAsBigDecimal(content, "tts-min-price");
        } else if (this.countrySpecificNode.equals(node)) {
            this.countrySpecificInfo = new HashMap<>();
        } else if (this.priceNode.equals(node)) {
            final String countryCode = content.getAttribute("code", true);
            final long minIncrement = parseLong(content.getAttribute("min-increment", true));
            final long recurringIncrement = parseLong(content.getAttribute("recurring-increment", true));
            final CountrySpecificInfo countryInfo = new CountrySpecificInfo(countryCode,
                    minIncrement,
                    recurringIncrement);
            this.countrySpecificInfo.put(countryCode, countryInfo);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new ChargingConfig(this.minPrice,
                                             this.defaultCost,
                                             this.defaultSipPrice,
                                             this.defaultSipCost,
                                             this.defaultSipDialInPrice,
                                             this.defaultSipDestinationPrice,
                                             this.defaultInboundPrice,
                                             this.defaultInboundCost,
                                             this.minIncrement,
                                             this.recurringIncrement,
                                             this.ttsNGMinPrice,
                                             this.countrySpecificInfo);
            notifyComplete();
        }
    }

    private static BigDecimal getAttributeAsBigDecimal(XmlContent content, String attrName) throws LoaderException {
        String attrStr = content.getAttribute(attrName, true);
        BigDecimal value = null;
        try {
            value = new BigDecimal(attrStr).setScale(6, RoundingMode.HALF_UP);
        } catch (NumberFormatException ex) {
            throw new LoaderException("Failed to convert number " + attrStr + " into a BigDecimal", ex);
        }
        return value;
    }
}
