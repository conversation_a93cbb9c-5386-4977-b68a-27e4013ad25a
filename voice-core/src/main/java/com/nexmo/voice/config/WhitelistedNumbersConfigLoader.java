package com.nexmo.voice.config;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public final class WhitelistedNumbersConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "whitelisted-numbers";

    private final String numberNodeName;
    private final Set<String> numbers;

    private boolean enabled;

    private WhitelistedNumbersConfig config;

    public WhitelistedNumbersConfigLoader(String nodeName) {
        super(nodeName);
        this.numberNodeName = getSubNodeName(nodeName, "number");
        this.numbers = new HashSet<>();
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node))
            this.enabled = parseBoolean(content.getAttribute("enabled", false), false);
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (this.numberNodeName.equals(node)) {
            if (StringUtils.isNotBlank(contentData))
                this.numbers.add(contentData.trim());
        } else if (getNodeName().equals(node)) {
            this.config = new WhitelistedNumbersConfig(this.enabled, this.numbers);
            notifyComplete();
        }
    }

    public WhitelistedNumbersConfig getConfig() {
        return this.config;
    }

}
