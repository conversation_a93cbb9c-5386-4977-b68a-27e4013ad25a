package com.nexmo.voice.config.charging;

import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.thepeachbeetle.common.period.Periods;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public final class UpdateTaskConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "update-task";

    private UpdateTaskConfig config;

    private VoiceApplicationType voiceApplicationType;
    private int sweepingCount;
    private Periods sweepingUnit;

    public UpdateTaskConfigLoader(final String nodeName) {
        super(nodeName);
        this.config = null;
    }

    public UpdateTaskConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            String productClass = content.getAttribute("product-class", true);
            if (!VoiceApplicationType.isValidKey(productClass))
                throw new LoaderException("Not valid 'product-class' detected ['" + productClass + "']");
            this.voiceApplicationType = VoiceApplicationType.getMatching(productClass);
            this.sweepingCount = parseInt(content.getAttribute("sweeping-interval-count", false), 1);
            String key = content.getAttribute("sweeping-interval-unit", false, Periods.PERIOD_SECOND.getKey());
            this.sweepingUnit = Periods.getMatching(key);
        }

    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)){
            this.config = new UpdateTaskConfig(this.voiceApplicationType, this.sweepingCount, this.sweepingUnit);
            notifyComplete();
        }
    }
}
