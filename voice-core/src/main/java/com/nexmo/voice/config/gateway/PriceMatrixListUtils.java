package com.nexmo.voice.config.gateway;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.messaging.hub.config.pricing.MessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;

public class PriceMatrixListUtils {

    public static boolean arePriceMatrixListsEquivalent(PriceMatrixList left, PriceMatrixList right) {
        if ((left == null) && (right == null))
            return true;
        else if ((left == null) || (right == null))
            return false;

        final Map<Integer, MessagePriceMatrix> leftMap = left.getMatrix();
        final Map<Integer, MessagePriceMatrix> rightMap = right.getMatrix();

        // Compare keysets
        Set<Integer> ourKeys = leftMap.keySet();
        Set<Integer> theirKeys = rightMap.keySet();
        if (!ourKeys.equals(theirKeys))
            return false;

        // Compare actual price matrix
        for (Integer key : ourKeys) {
            MessagePriceMatrix leftPrices = leftMap.get(key);
            MessagePriceMatrix rightPrices = rightMap.get(key);
            if (!areMessagePriceMatricesEquivalent(leftPrices, rightPrices)) {
                return false;
            }
        }

        return true;
    }

    public static int hashPriceMatrixList(PriceMatrixList val) {
        final int prime = 31;
        int result = 1;
        result = prime * result + hashIntegerToMessagePriceMatrixMap(val.getMatrix());
        return result;
    }

    public static boolean areMessagePriceMatricesEquivalent(MessagePriceMatrix left, MessagePriceMatrix right) {
        if ((left == null) && (right == null))
            return true;
        else if ((left == null) || (right == null))
            return false;

        if (left.getProduct() != right.getProduct())
            return false;
        if (!isSame(left.isPerformPriceLookup(), right.isPerformPriceLookup()))
            return false;
        if (!arePricesEquivalent(left.getDefaultPrice(), right.getDefaultPrice()))
            return false;
        if (!isSame(left.isRejectSubmissionIfNoPriceFound(), right.isRejectSubmissionIfNoPriceFound()))
            return false;
        if (!isSame(left.isUseDb(), right.isUseDb()))
            return false;
        // TODO: db matrix ID
        if (!isSame(left.isUseLdap(), right.isUseLdap()))
            return false;
        // TODO: base DN

        // Price list
        final Iterator<Price> leftIterator = left.getPrices().iterator();
        final Iterator<Price> rightIterator = right.getPrices().iterator();
        while (leftIterator.hasNext() && rightIterator.hasNext()) {
            if (!arePricesEquivalent(leftIterator.next(), rightIterator.next()))
                return false;
        }
        if (!isSame(leftIterator.hasNext(), rightIterator.hasNext())) // Ensure lists are both finished
            return false;

        return true;
    }

    public static int hashMessagePriceMatrix(MessagePriceMatrix val) {
        final int prime = 31;
        int result = 1;
        result = prime * result + val.getProduct();
        result = prime * result + (val.isPerformPriceLookup() ? 1231 : 1237);
        result = prime * result + hashPrice(val.getDefaultPrice());
        result = prime * result + (val.isRejectSubmissionIfNoPriceFound() ? 1231 : 1237);
        result = prime * result + (val.isUseDb() ? 1231 : 1237);
        // TODO: db matrix ID
        result = prime * result + (val.isUseLdap() ? 1231 : 1237);
        // TODO: base DN

        for (Price p : val.getPrices()) {
            result = prime * result + ((p == null) ? 0 : hashPrice(p));
        }
        return result;
    }

    public static boolean arePricesEquivalent(Price left, Price right) {
        if ((left == null) && (right == null))
            return true;
        else if ((left == null) || (right == null))
            return false;

        boolean match = left.matches(right); // Only checks metadata, not actual price
        if (!match)
            return false;

        // Compare prices
        final BigDecimal leftPrice = left.getPrice();
        final BigDecimal rightPrice = right.getPrice();
        return (leftPrice.compareTo(rightPrice) == 0); // Ignores scale, "2.0" == "2.000"
    }

    public static int hashPrice(Price val) {
        // Cheat and use to string representation as the basis for our hash
        return val.toString().hashCode();
    }


    //
    // Internal helper methods, based on the approach used in OpenJava JDK
    //
    private static int hashIntegerToMessagePriceMatrixMap(Map<Integer, MessagePriceMatrix> map) {
        // Sum of hashs of (key,value) entries in map, same value regardless of order
        int result = 0;
        for (Map.Entry<Integer, MessagePriceMatrix> e : map.entrySet()) {
            result += hashIntegerToMessagePriceMatrixMapEntry(e);
        }
        return result;
    }

    private static int hashIntegerToMessagePriceMatrixMapEntry(Map.Entry<Integer, MessagePriceMatrix> entry) {
        // Hash of key XOR hash of value
        final Integer key = entry.getKey();
        final MessagePriceMatrix value = entry.getValue();

        return ((key == null) ? 0 : key.hashCode()) ^ ((value == null) ? 0 : hashMessagePriceMatrix(value));
    }

    private static boolean isSame(boolean left, boolean right) {
        if (left) {
            return right;
        } else {
            return !right;
        }
    }

}