package com.nexmo.voice.config.applications;

/*
 * <inter-service-auth issuer="" private-key-file="" public-keys-path="" />
 */

import java.time.Duration;
import java.util.Collection;
import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;


public class InterServiceAuthConfig implements java.io.Serializable {
    private static final long serialVersionUID = -264761981916791915L;

    private final String issuer;
    private final String privateKeyFile;
    private final String publicKeysPath;

    public InterServiceAuthConfig(String issuer, String privateKeyFile, String publicKeysPath) {
        this.issuer = issuer;
        this.privateKeyFile = privateKeyFile;
        this.publicKeysPath = publicKeysPath;
    }

    public String getIssuer() {
        return issuer;
    }

    public String getPrivateKeyFile() {
        return privateKeyFile;
    }

    public String getPublicKeysPath() {
        return publicKeysPath;
    }

    public Element toXML() {
        Element authConfig = new Element("inter-service-auth");
        authConfig.setAttribute("issuer", issuer);
        authConfig.setAttribute("private-key-file", privateKeyFile);
        authConfig.setAttribute("public-keys-path", publicKeysPath);
        return authConfig;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump InterServiceAuthConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

}
