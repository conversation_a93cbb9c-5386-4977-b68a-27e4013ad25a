package com.nexmo.voice.config;

import com.nexmo.voice.config.********.DynamoDbConfig;
import com.nexmo.voice.core.callblocking.CallblockingServiceConfig;
import com.nexmo.voice.config.callblocks.CallBlocksConfig;
import com.nexmo.voice.config.callblocks.CallBlocksConfigSource;
import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import com.nexmo.voice.config.gateway.TTSGatewayInfoMatrixConfig;
import com.nexmo.voice.config.metrics.MetricsConfig;
import com.nexmo.voice.core.billing.vquota.VQuotaServiceConfig;
import com.nexmo.voice.core.domains.DomainsServiceConfig;
import com.nexmo.voice.core.emergency.EmergencyCallingConfig;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceConfig;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceConfigLoader;
import com.nexmo.voice.core.types.VoiceProduct;
import com.nexmo.voice.core.viam.ViamAuthConfig;
import com.thepeachbeetle.common.period.Periods;
import com.thepeachbeetle.messaging.hub.config.randomize.SenderRandomizerConfig;
import org.jdom.Element;

import com.thepeachbeetle.common.app.config.AppConfig;
import com.thepeachbeetle.common.callback.config.CallbackConfig;
import com.thepeachbeetle.common.http.config.HttpServerConfig;
import com.thepeachbeetle.common.http.connectionfactory.config.HttpCallbackBlockedIpRangeConfig;
import com.thepeachbeetle.common.persistentclusteredcache.config.CachePersistenceConfig;

import com.thepeachbeetle.deployment.config.DeploymentCheckConfig;
import com.thepeachbeetle.messaging.hub.config.db.ShortCodesDBConfig;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingConfig;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodes;
import com.thepeachbeetle.messaging.hub.core.provisioning.client.config.ProvisioningApiClientConfig;
import com.thepeachbeetle.messaging.hub.core.quota.client.config.QuotaApiClientConfig;
import com.thepeachbeetle.services.networks.client.config.NetworkMatrixConfig;

import com.nexmo.common.api.config.AuthConfig;

import com.nexmo.voice.config.accounts.AccountCapabilitiesConfig;
import com.nexmo.voice.config.api.InternalApiConfig;
import com.nexmo.voice.config.applications.ApplicationsServiceConfig;
import com.nexmo.voice.config.caches.CacheControlConfig;
import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.config.charging.ChargingUpdaterConfig;
import com.nexmo.voice.config.gateway.SIPGatewayInfoMatrixConfig;
import com.nexmo.voice.config.gateway.GatewayInfoMatrixSource;
import com.nexmo.voice.config.gateway.asterisk.AsteriskManagerConfig;
import com.nexmo.voice.config.********.LVNMappingsConfig;
import com.nexmo.voice.config.********.LVNMappingsSource;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.config.prefix.PrefixMapSource;
import com.nexmo.voice.config.quota.ExtendedQuotaApiClientConfig;
import com.nexmo.voice.config.sip.AsteriskAgiServerConfig;

import static com.nexmo.voice.core.types.VoiceProduct.TTS;

/**
 * <AUTHOR> Cook
 */
public class Config extends AppConfig {

    private static final long serialVersionUID = -3878471912839390745L;

    private DeploymentCheckConfig deploymentCheckConfig = null;

    private HttpServerConfig httpServerConfig = null;
    private HttpServerConfig httpInternalApiEndpointConfig = null;
    private HttpCallbackBlockedIpRangeConfig httpCallbackBlockedIpRangeConfig = null;

    private String log4jPropertiesFile = "log4j.properties";
    private String statsLogDir = "logs";
    private String logLevel;

    private boolean applicationLoggingEnabled = true;
    private String applicationLoggingFilePrefix = "application";

    private boolean callLoggingEnabled = true;
    private String callLoggingFilePrefix = "call";

    private boolean callInboundLoggingEnabled = false;
    private String callInboundLoggingFilePrefix = "call-inbound";

    private boolean rejectedLoggingEnabled = true;
    private String rejectedLoggingFilePrefix = "rejected";

    private boolean attemptLoggingEnabled = true;
    private String attemptLoggingFilePrefix = "attempt";

    private long pddCacheDuration;
    private long callChargerCacheDuration;

    private ShortCodesDBConfig shortCodesDbConfig;

    private ProvisioningApiClientConfig provisioningApiClientConfig;

    private MtRoutingConfig mtRoutingConfig;
    private ShortCodes shortCodes;

    private QuotaApiClientConfig quotaAPIConfig;
    private ExtendedQuotaApiClientConfig extendedQuotaAPIConfig;

    private PriceMatrixList moPriceMatrixList;
    private PriceMatrixList mtPriceMatrixList;

    private ChargingConfig chargingConfig;

    private NetworkMatrixConfig networksMatrixConfig;

    private CallbackConfig callbackConfig = null;
    private CallbackConfig eventCallbackConfig = null;

    private SIPGatewayInfoMatrixConfig sipGatewayInfoMatrixConfig;
    private GatewayInfoMatrixSource gatewayInfoMatrixSource;

    private LVNMappingsConfig lvnMappingsConfig;
    private LVNMappingsSource lvnMappingsSource;

    private PrefixMapConfig prefixMapConfig;
    private PrefixMapSource prefixMapSource;

    private CacheControlConfig cachesConfig;
    private ApplicationsServiceConfig applicationsServiceConfig;
    private CallblockingServiceConfig callblockingServiceConfig;

    private AuthConfig authConfig;

    private InternalApiConfig internalApiConfig;

    private ChargingUpdaterConfig chargingUpdaterConfig;

    private int shutdownCooldownTimeCount;
    private Periods shutdownCooldownTimeUnit;

    private CachePersistenceConfig contextCacheConfig;

    private AsteriskManagerConfig asteriskManagerConfig;

    private AsteriskAgiServerConfig asteriskAgiServerConfig;

    private AccountCapabilitiesConfig accountCapabilitiesConfig;

    private WhitelistedNumbersConfig whitelistedNumbersConfig;

    private CallBlocksConfig callBlocksConfig;
    private CallBlocksConfigSource callBlocksConfigSource;

    private boolean ttsLogCallLoggingEnabled = true;
    private String ttsCallLoggingFilePrefix = "tts-call";

    private boolean ttsRejectedLoggingEnabled = true;
    private String ttsRejectedLoggingFilePrefix = "tts-rejected";

    private boolean ttsAttemptLoggingEnabled = true;
    private String ttsAttemptLoggingFilePrefix = "tts-attempt";


    private TTSGatewayInfoMatrixConfig ttsGatewayInfoMatrixConfig;
    private GatewayInfoMatrixSource ttsGatewayInfoMatrixSource;
    private MetricsConfig metricsConfig;
    private CdrsConfig cdrsConfig;

    private DomainsServiceConfig domainsServiceConfig;
    private DynamoDbConfig dynamoDbConfig;
    private VQuotaServiceConfig vQuotaServiceConfig;

    private EmergencyCallingConfig emergencyCallingConfig;
    private ViamAuthConfig viamAuthConfig;
    private EmergencyAddressServiceConfig emergencyAddressServiceConfig;
    private SenderRandomizerConfig senderRandomizerConfig;

    public Config() {
    }

    public DeploymentCheckConfig getDeploymentCheckConfig() {
        return this.deploymentCheckConfig;
    }

    public void setDeploymentCheckConfig(DeploymentCheckConfig deploymentCheckConfig) {
        this.deploymentCheckConfig = deploymentCheckConfig;
    }

    public HttpServerConfig getHttpServerConfig() {
        return this.httpServerConfig;
    }

    public void setHttpServerConfig(HttpServerConfig httpServerConfig) {
        this.httpServerConfig = httpServerConfig;
    }

    public HttpServerConfig getHttpInternalApiEndpointConfig() {
        return this.httpInternalApiEndpointConfig;
    }

    public void setHttpInternalApiEndpointConfig(HttpServerConfig httpInternalApiEndpointConfig) {
        this.httpInternalApiEndpointConfig = httpInternalApiEndpointConfig;
    }

    public String getLog4jPropertiesFile() {
        return this.log4jPropertiesFile;
    }

    public void setLog4jPropertiesFile(String log4jPropertiesFile) {
        this.log4jPropertiesFile = log4jPropertiesFile;
    }

    public String getLogLevel() {
        return this.logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public String getStatsLogDir() {
        return this.statsLogDir;
    }

    public void setStatsLogDir(String statsLogDir) {
        this.statsLogDir = statsLogDir;
    }

    public boolean isApplicationLoggingEnabled() {
        return this.applicationLoggingEnabled;
    }

    public void setApplicationLoggingEnabled(boolean applicationLoggingEnabled) {
        this.applicationLoggingEnabled = applicationLoggingEnabled;
    }

    public String getApplicationLoggingFilePrefix() {
        return this.applicationLoggingFilePrefix;
    }

    public void setApplicationLoggingFilePrefix(String applicationLoggingFilePrefix) {
        this.applicationLoggingFilePrefix = applicationLoggingFilePrefix;
    }

    public boolean isCallLoggingEnabled() {
        return this.callLoggingEnabled;
    }

    public void setCallLoggingEnabled(boolean callLoggingEnabled) {
        this.callLoggingEnabled = callLoggingEnabled;
    }

    public String getCallLoggingFilePrefix() {
        return this.callLoggingFilePrefix;
    }

    public void setCallLoggingFilePrefix(String callLoggingFilePrefix) {
        this.callLoggingFilePrefix = callLoggingFilePrefix;
    }

    public boolean isCallInboundLoggingEnabled() {
        return this.callInboundLoggingEnabled;
    }

    public void setCallInboundLoggingEnabled(boolean callInboundLoggingEnabled) {
        this.callInboundLoggingEnabled = callInboundLoggingEnabled;
    }

    public String getCallInboundLoggingFilePrefix() {
        return this.callInboundLoggingFilePrefix;
    }

    public void setCallInboundLoggingFilePrefix(String callInboundLoggingFilePrefix) {
        this.callInboundLoggingFilePrefix = callInboundLoggingFilePrefix;
    }

    public boolean isRejectedLoggingEnabled() {
        return this.rejectedLoggingEnabled;
    }

    public void setRejectedLoggingEnabled(boolean rejectedLoggingEnabled) {
        this.rejectedLoggingEnabled = rejectedLoggingEnabled;
    }

    public boolean isAttemptLoggingEnabled() {
        return this.attemptLoggingEnabled;
    }

    public void setAttemptLoggingEnabled(boolean attemptLoggingEnabled) {
        this.attemptLoggingEnabled = attemptLoggingEnabled;
    }

    public String getAttemptLoggingFilePrefix() {
        return this.attemptLoggingFilePrefix;
    }

    public void setAttemptLoggingFilePrefix(String attemptLoggingFilePrefix) {
        this.attemptLoggingFilePrefix = attemptLoggingFilePrefix;
    }

    public String getRejectedLoggingFilePrefix() {
        return this.rejectedLoggingFilePrefix;
    }

    public void setRejectedLoggingFilePrefix(String rejectedLoggingFilePrefix) {
        this.rejectedLoggingFilePrefix = rejectedLoggingFilePrefix;
    }

    public void setTTSLogCallLoggingEnabled(boolean ttsLogCallLoggingNode) {
        this.ttsLogCallLoggingEnabled = ttsLogCallLoggingNode;
    }

    public void setTTSCallLoggingFilePrefix(String ttsCallLoggingFilePrefix) {
        this.ttsCallLoggingFilePrefix = ttsCallLoggingFilePrefix;
    }

    public void setTTSRejectedLoggingEnabled(boolean ttsRejectedLoggingEnabled) {
        this.ttsRejectedLoggingEnabled = ttsRejectedLoggingEnabled;
    }

    public void setTTSRejectedLoggingFilePrefix(String ttsRejectedLoggingFilePrefix) {
        this.ttsRejectedLoggingFilePrefix = ttsRejectedLoggingFilePrefix;
    }

    public void setTTSAttemptLoggingEnabled(boolean ttsAttemptLoggingEnabled) {
        this.ttsAttemptLoggingEnabled = ttsAttemptLoggingEnabled;
    }

    public void setTTSAttemptLoggingFilePrefix(String ttsAttemptLoggingFilePrefix) {
        this.ttsAttemptLoggingFilePrefix = ttsAttemptLoggingFilePrefix;
    }

    public boolean isTTSLogCallLoggingEnabled() {
        return ttsLogCallLoggingEnabled;
    }

    public String getTTSCallLoggingFilePrefix() {
        return ttsCallLoggingFilePrefix;
    }

    public boolean isTTSRejectedLoggingEnabled() {
        return ttsRejectedLoggingEnabled;
    }

    public String getTTSRejectedLoggingFilePrefix() {
        return ttsRejectedLoggingFilePrefix;
    }

    public boolean isTTSAttemptLoggingEnabled() {
        return ttsAttemptLoggingEnabled;
    }

    public String getTTSAttemptLoggingFilePrefix() {
        return ttsAttemptLoggingFilePrefix;
    }

    public void setHttpCallbackBlockedIpRangeConfig(HttpCallbackBlockedIpRangeConfig httpCallbackBlockedIpRangeConfig) {
        this.httpCallbackBlockedIpRangeConfig = httpCallbackBlockedIpRangeConfig;
    }

    public HttpCallbackBlockedIpRangeConfig getHttpCallbackBlockedIpRangeConfig() {
        return this.httpCallbackBlockedIpRangeConfig;
    }

    public MtRoutingConfig getMtRoutingConfig() {
        return this.mtRoutingConfig;
    }

    public void setMtRoutingConfig(MtRoutingConfig mtRoutingConfig) {
        this.mtRoutingConfig = mtRoutingConfig;
    }

    public ShortCodes getShortCodes() {
        return this.shortCodes;
    }

    public void setShortCodes(ShortCodes shortCodes) {
        this.shortCodes = shortCodes;
    }

    public QuotaApiClientConfig getQuotaAPIConfig() {
        return this.quotaAPIConfig;
    }

    public void setQuotaAPIConfig(QuotaApiClientConfig quotaAPIConfig) {
        this.quotaAPIConfig = quotaAPIConfig;
    }

    public ExtendedQuotaApiClientConfig getExtendedQuotaAPIConfig() {
        return this.extendedQuotaAPIConfig;
    }

    public void setExtendedQuotaAPIConfig(ExtendedQuotaApiClientConfig extendedQuotaAPIConfig) {
        this.extendedQuotaAPIConfig = extendedQuotaAPIConfig;
    }

    public PriceMatrixList getMoPriceMatrixList() {
        return this.moPriceMatrixList;
    }

    public void setMoPriceMatrixList(PriceMatrixList priceMatrixList) {
        this.moPriceMatrixList = priceMatrixList;
    }

    public PriceMatrixList getMtPriceMatrixList() {
        return this.mtPriceMatrixList;
    }

    public void setMtPriceMatrixList(PriceMatrixList priceMatrixList) {
        this.mtPriceMatrixList = priceMatrixList;
    }

    public ChargingConfig getChargingConfig() {
        return this.chargingConfig;
    }

    public void setChargingConfig(ChargingConfig chargingConfig) {
        this.chargingConfig = chargingConfig;
    }

    public NetworkMatrixConfig getNetworksMatrixConfig() {
        return this.networksMatrixConfig;
    }

    public void setNetworksMatrixConfig(NetworkMatrixConfig networksMatrixConfig) {
        this.networksMatrixConfig = networksMatrixConfig;
    }

    public ShortCodesDBConfig getShortCodesDbConfig() {
        return this.shortCodesDbConfig;
    }

    public void setShortCodesDBConfig(ShortCodesDBConfig shortCodesDbConfig) {
        this.shortCodesDbConfig = shortCodesDbConfig;
    }

    public ProvisioningApiClientConfig getProvisioningApiClientConfig() {
        return provisioningApiClientConfig;
    }

    public void setProvisioningApiClientConfig(ProvisioningApiClientConfig provisioningApiClientConfig) {
        this.provisioningApiClientConfig = provisioningApiClientConfig;
    }

    public CallbackConfig getCallbackConfig() {
        return this.callbackConfig;
    }

    public void setCallbackConfig(CallbackConfig callbackConfig) {
        this.callbackConfig = callbackConfig;
    }

    public CallbackConfig getEventCallbackConfig() {
        return this.eventCallbackConfig;
    }

    public void setEventCallbackConfig(CallbackConfig eventCallbackConfig) {
        this.eventCallbackConfig = eventCallbackConfig;
    }

    public synchronized GatewayInfoMatrixConfig getSIPGatewayInfoMatrixConfig() {
        return this.sipGatewayInfoMatrixConfig;
    }

    public synchronized GatewayInfoMatrixSource getSIPGatewayInfoMatrixSource() {
        return this.gatewayInfoMatrixSource;
    }

    public synchronized void setSIPGatewayInfoMatrixConfig(SIPGatewayInfoMatrixConfig sipGatewayInfoMatrixConfig, GatewayInfoMatrixSource gatewayInfoMatrixSource) {
        this.sipGatewayInfoMatrixConfig = sipGatewayInfoMatrixConfig;
        this.gatewayInfoMatrixSource = gatewayInfoMatrixSource;
    }

    public synchronized GatewayInfoMatrixConfig getTTSGatewayInfoMatrixConfig() {
        return this.ttsGatewayInfoMatrixConfig;
    }

    public synchronized GatewayInfoMatrixSource getTTSGatewayInfoMatrixSource() {
        return ttsGatewayInfoMatrixSource;
    }

    public synchronized GatewayInfoMatrixConfig selectGatewayInfoMatrixConfig(VoiceProduct voiceProduct) {
        return TTS.equals(voiceProduct) ? this.ttsGatewayInfoMatrixConfig : this.sipGatewayInfoMatrixConfig;
    }

    public synchronized void setTTSGatewayInfoMatrixConfig(TTSGatewayInfoMatrixConfig ttsGatewayInfoMatrixConfig, GatewayInfoMatrixSource ttsGatewayInfoMatrixSource) {
        this.ttsGatewayInfoMatrixConfig = ttsGatewayInfoMatrixConfig;
        this.ttsGatewayInfoMatrixSource = ttsGatewayInfoMatrixSource;
    }

    public synchronized LVNMappingsConfig getLVNMappingsConfig() {
        return this.lvnMappingsConfig;
    }

    public synchronized LVNMappingsSource getLVNMappingsSource() {
        return this.lvnMappingsSource;
    }

    public synchronized void setLVNMappingsConfig(LVNMappingsConfig lvnMappingsConfig, LVNMappingsSource lvnMappingsSource) {
        this.lvnMappingsConfig = lvnMappingsConfig;
        this.lvnMappingsSource = lvnMappingsSource;
    }

    public synchronized PrefixMapConfig getPrefixMapConfig() {
        return this.prefixMapConfig;
    }

    public synchronized PrefixMapSource getPrefixMapSource() {
        return this.prefixMapSource;
    }

    public synchronized void setPrefixMapConfig(PrefixMapConfig prefixMapConfig, PrefixMapSource prefixMapSource) {
        this.prefixMapConfig = prefixMapConfig;
        this.prefixMapSource = prefixMapSource;
    }

    public CacheControlConfig getCachesConfig() {
        return this.cachesConfig;
    }

    public void setCachesConfig(CacheControlConfig cachesConfig) {
        this.cachesConfig = cachesConfig;
    }

    public ApplicationsServiceConfig getApplicationsServiceConfig() {
        return this.applicationsServiceConfig;
    }

    public void setApplicationsServiceConfig(ApplicationsServiceConfig applicationsServiceConfig) {
        this.applicationsServiceConfig = applicationsServiceConfig;
    }

    public CallblockingServiceConfig getCallblockingServiceConfig() {
        return this.callblockingServiceConfig;
    }

    public void setCallblockingServiceConfig(CallblockingServiceConfig callBlockingServiceConfig) {
        this.callblockingServiceConfig = callBlockingServiceConfig;
    }

    public AuthConfig getAuthConfig() {
        return this.authConfig;
    }

    public void setAuthConfig(AuthConfig authConfig) {
        this.authConfig = authConfig;
    }

    public ChargingUpdaterConfig getChargingUpdaterConfig() {
        return this.chargingUpdaterConfig;
    }

    public void setChargingUpdaterConfig(ChargingUpdaterConfig chargingUpdaterConfig) {
        this.chargingUpdaterConfig = chargingUpdaterConfig;
    }

    public VQuotaServiceConfig getVQuotaServiceConfig() {
        return this.vQuotaServiceConfig;
    }

    public void setVQuotaServiceConfig(VQuotaServiceConfig vQuotaServiceConfig) {
        this.vQuotaServiceConfig = vQuotaServiceConfig;
    }

    public int getShutdownCooldownTimeCount() {
        return this.shutdownCooldownTimeCount;
    }

    public void setShutdownCooldownTimeCount(int shutdownCooldownTimeCount) {
        this.shutdownCooldownTimeCount = shutdownCooldownTimeCount;
    }

    public Periods getShutdownCooldownTimeUnit() {
        return this.shutdownCooldownTimeUnit;
    }

    public void setShutdownCooldownTimeUnit(Periods shutdownCooldownTimeUnit) {
        this.shutdownCooldownTimeUnit = shutdownCooldownTimeUnit;
    }

    public CachePersistenceConfig getContextCacheConfig() {
        return this.contextCacheConfig;
    }

    public void setContextCacheConfig(CachePersistenceConfig contextCacheConfig) {
        this.contextCacheConfig = contextCacheConfig;
    }

    public AsteriskManagerConfig getAsteriskManagerConfig() {
        return this.asteriskManagerConfig;
    }

    public void setAsteriskManagerConfig(AsteriskManagerConfig asteriskManagerConfig) {
        this.asteriskManagerConfig = asteriskManagerConfig;
    }

    public AsteriskAgiServerConfig getAsteriskAgiServerConfig() {
        return this.asteriskAgiServerConfig;
    }

    public void setAsteriskAgiServerConfig(AsteriskAgiServerConfig config) {
        this.asteriskAgiServerConfig = config;
    }

    public InternalApiConfig getInternalApiConfig() {
        return this.internalApiConfig;
    }

    public void setInternalApiConfig(InternalApiConfig internalApiConfig) {
        this.internalApiConfig = internalApiConfig;
    }

    public AccountCapabilitiesConfig getAccountCapabilitiesConfig() {
        return this.accountCapabilitiesConfig;
    }

    public void setAccountCapabilitiesConfig(AccountCapabilitiesConfig accountCapabilitiesConfig) {
        this.accountCapabilitiesConfig = accountCapabilitiesConfig;
    }

    public WhitelistedNumbersConfig getWhitelistedNumbersConfig() {
        return this.whitelistedNumbersConfig;
    }

    public void setWhitelistedNumbersConfig(WhitelistedNumbersConfig whitelistedNumbersConfig) {
        this.whitelistedNumbersConfig = whitelistedNumbersConfig;
    }

    public long getPddCacheDuration() {
        return this.pddCacheDuration;
    }

    public void setPddCacheDuration(long pddCacheDuration) {
        this.pddCacheDuration = pddCacheDuration;
    }

    public long getCallChargerCacheDuration() {
        return this.callChargerCacheDuration;
    }

    public void setCallChargerCacheDuration(long callerChargerCacheDuration) {
        this.callChargerCacheDuration = callerChargerCacheDuration;
    }

    public MetricsConfig getMetricsConfig() {
        return metricsConfig;
    }

    public void setMetricsConfig(MetricsConfig metricsConfig) {
        this.metricsConfig = metricsConfig;
    }
    public CdrsConfig getCdrsConfig() {
        return this.cdrsConfig;
    }
    
    public void setCdrsConfig(CdrsConfig cdrsConfig) {
        this.cdrsConfig = cdrsConfig;
    }

    public synchronized CallBlocksConfig getCallBlocksConfig() {
        return this.callBlocksConfig;
    }

    public synchronized CallBlocksConfigSource getCallBlocksConfigSource() {
        return this.callBlocksConfigSource;
    }

    public synchronized void setCallBlocksConfig(CallBlocksConfig callBlocksConfig, CallBlocksConfigSource callBlocksConfigSource) {
        this.callBlocksConfig = callBlocksConfig;
        this.callBlocksConfigSource = callBlocksConfigSource;
    }

    public void setDomainsServiceConfig(DomainsServiceConfig domainsServiceConfig) {
        this.domainsServiceConfig = domainsServiceConfig;
    }

    public synchronized DomainsServiceConfig getDomainsServiceConfig() {
        return this.domainsServiceConfig;
    }

    public void setEmergencyCallingConfig(EmergencyCallingConfig emergencyCallingConfig) {
        this.emergencyCallingConfig = emergencyCallingConfig;
    }

    public synchronized  EmergencyCallingConfig getEmergencyCallingConfig() {
        return this.emergencyCallingConfig;
    }

    public synchronized DynamoDbConfig getDynamoDbConfig() {
        return this.dynamoDbConfig;
    }

    public void setDynamoDbConfig(DynamoDbConfig dynamoDbConfig) {
        this.dynamoDbConfig = dynamoDbConfig;
    }

    public void setViamAuthConfig(ViamAuthConfig viamAuthConfig) {
        this.viamAuthConfig = viamAuthConfig;
    }

    public synchronized ViamAuthConfig getViamAuthConfig() {
        return this.viamAuthConfig;
    }

    public void setEmergencyAddressServiceConfig(EmergencyAddressServiceConfig emergencyAddressServiceConfig) {
        this.emergencyAddressServiceConfig = emergencyAddressServiceConfig;
    }

    public synchronized EmergencyAddressServiceConfig getEmergencyAddressServiceConfig() {
        return this.emergencyAddressServiceConfig;
    }

    public synchronized SenderRandomizerConfig getSenderRandomizerConfig() { return this.senderRandomizerConfig; }
    public void setSenderRandomizerConfig(SenderRandomizerConfig senderRandomizerConfig) {
        this.senderRandomizerConfig = senderRandomizerConfig;

    }

    @Override
    public Element toXML() {

        Element root = new Element("voice");

        Element core = super.toXML();

        if (this.deploymentCheckConfig != null)
            core.addContent(this.deploymentCheckConfig.toXML());

        if (this.httpServerConfig != null)
            core.addContent(this.httpServerConfig.toXML());

        Element accounts = new Element("accounts");

        if (this.accountCapabilitiesConfig != null)
            accounts.addContent(this.accountCapabilitiesConfig.toXML());

        core.addContent(accounts);

        Element httpApi = new Element("http-api");

        if (this.internalApiConfig != null) {
            Element internalApiEndpoint = this.internalApiConfig.toXML();
            httpApi.addContent(internalApiEndpoint);
        }

        if (this.httpCallbackBlockedIpRangeConfig != null)
            core.addContent(this.httpCallbackBlockedIpRangeConfig.toXML());

        core.addContent(httpApi);

        if (this.callbackConfig != null)
            core.addContent(this.callbackConfig.toXML());

        Element log = new Element("log");
        log.setAttribute("dir", this.statsLogDir);
        log.setAttribute("log4j-properties-file", this.log4jPropertiesFile);
        log.setAttribute("log-level", this.logLevel);

        Element callLogging = new Element("call-logging");
        callLogging.setAttribute("enabled", "" + this.callLoggingEnabled);
        callLogging.setAttribute("file-prefix", "" + this.callLoggingFilePrefix);
        log.addContent(callLogging);

        Element callInboundLogging = new Element("call-inbound-logging");
        callLogging.setAttribute("enabled", "" + this.callInboundLoggingEnabled);
        callLogging.setAttribute("file-prefix", "" + this.callInboundLoggingFilePrefix);
        log.addContent(callInboundLogging);

        Element rejectedLogging = new Element("rejected-logging");
        rejectedLogging.setAttribute("enabled", "" + this.rejectedLoggingEnabled);
        rejectedLogging.setAttribute("file-prefix", "" + this.rejectedLoggingFilePrefix);
        log.addContent(rejectedLogging);

        Element shutdownCooldown = new Element("shutdown-cooldown");
        shutdownCooldown.setAttribute("unit", this.shutdownCooldownTimeUnit == null ? "" : this.shutdownCooldownTimeUnit.getKey());
        shutdownCooldown.setAttribute("count", String.valueOf(this.shutdownCooldownTimeCount));
        core.addContent(log);

        Element pddCalculator = new Element("pdd-calculator");
        shutdownCooldown.setAttribute("duration", String.valueOf(this.pddCacheDuration));
        core.addContent(pddCalculator);

        Element callCharger = new Element("call-charger");
        shutdownCooldown.setAttribute("duration", String.valueOf(this.callChargerCacheDuration));
        core.addContent(callCharger);

        if (this.shortCodesDbConfig != null)
            core.addContent(this.shortCodesDbConfig.toXML());

        if (this.provisioningApiClientConfig != null)
            core.addContent(this.provisioningApiClientConfig.toXML());

        if (this.quotaAPIConfig != null)
            core.addContent(this.quotaAPIConfig.toXML());

        if (this.extendedQuotaAPIConfig != null)
            core.addContent(this.extendedQuotaAPIConfig.toXML());

        if (this.networksMatrixConfig != null)
            core.addContent(this.networksMatrixConfig.toXML());

        if (this.contextCacheConfig != null)
            core.addContent(this.contextCacheConfig.toXML("context-cache"));

        root.addContent(core);

        if (this.whitelistedNumbersConfig != null)
            root.addContent(this.whitelistedNumbersConfig.toXML());

        if (this.shortCodes != null)
            root.addContent(this.shortCodes.toXML());

        if (this.mtRoutingConfig != null)
            root.addContent(this.mtRoutingConfig.toXML());

        if (this.mtPriceMatrixList != null)
            root.addContent(this.mtPriceMatrixList.toXML());

        if (this.moPriceMatrixList != null)
            root.addContent(this.moPriceMatrixList.toXML());

        if (this.chargingConfig != null)
            root.addContent(this.chargingConfig.toXML());

        if (this.sipGatewayInfoMatrixConfig != null)
            root.addContent(this.sipGatewayInfoMatrixConfig.toXML());

        if (this.ttsGatewayInfoMatrixConfig != null)
            root.addContent(this.ttsGatewayInfoMatrixConfig.toXML());

        if (this.prefixMapConfig != null)
            root.addContent(this.prefixMapConfig.toXML());

        if (this.callBlocksConfig != null)
            root.addContent(this.callBlocksConfig.toXML());

        if (this.authConfig != null)
            root.addContent(this.authConfig.toXML());

        if (this.chargingUpdaterConfig != null)
            root.addContent(this.chargingUpdaterConfig.toXML());

        if (this.asteriskManagerConfig != null)
            root.addContent(this.asteriskManagerConfig.toXML());

        if (this.asteriskAgiServerConfig != null)
            root.addContent(this.asteriskAgiServerConfig.toXML());

        if (this.metricsConfig != null)
            root.addContent(this.metricsConfig.toXML());
        
        if (this.cdrsConfig != null)
            root.addContent(this.cdrsConfig.toXML());

        if (this.domainsServiceConfig != null)
            root.addContent(this.domainsServiceConfig.toXML());

        if (this.emergencyCallingConfig != null)
            root.addContent(this.emergencyCallingConfig.toXML());

        if (this.viamAuthConfig != null)
            root.addContent(this.viamAuthConfig.toXML());

        if (this.emergencyAddressServiceConfig != null)
            root.addContent(this.emergencyAddressServiceConfig.toXML());

        if (this.senderRandomizerConfig != null)
            root.addContent(this.senderRandomizerConfig.toXML());

        return root;
    }


}
