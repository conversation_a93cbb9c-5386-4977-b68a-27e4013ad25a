package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.config.gateway.SIPGatewayInfoMatrixConfigLoader;


public class ReloadSIPGatewayInfoMatrixConfigReader extends XmlAbstractReader {

    private final SIPGatewayInfoMatrixConfigLoader gatewayInfoMatrixConfigLoader;
    private final Config config;


    public ReloadSIPGatewayInfoMatrixConfigReader(Config config) {
        this.config = config;

        this.gatewayInfoMatrixConfigLoader = new SIPGatewayInfoMatrixConfigLoader(ConfigReader.ROOT_NODE + "." + SIPGatewayInfoMatrixConfigLoader.ROOT_NODE, false);
        addHandler(this.gatewayInfoMatrixConfigLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler == this.gatewayInfoMatrixConfigLoader)
            this.config.setSIPGatewayInfoMatrixConfig(this.gatewayInfoMatrixConfigLoader.getConfig(), this.gatewayInfoMatrixConfigLoader.getSource());
    }

}
