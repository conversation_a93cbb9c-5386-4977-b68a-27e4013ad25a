package com.nexmo.voice.config.cdrs;

import com.nexmo.voice.core.types.CDRType;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class CdrsConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "cdrs";
    
    private String cdrTypeName;
    private boolean backupCdrs;

    private CdrsConfig config;

    public CdrsConfigLoader(String nodeName) {
        super(nodeName);
    }

    public CdrsConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.cdrTypeName =  content.getAttribute("cdr-type", false);
            this.backupCdrs =  parseBoolean(content.getAttribute("backup-cdrs", false));
       }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new CdrsConfig(CDRType.of(cdrTypeName), backupCdrs);
            notifyComplete();
        }
    }

}
