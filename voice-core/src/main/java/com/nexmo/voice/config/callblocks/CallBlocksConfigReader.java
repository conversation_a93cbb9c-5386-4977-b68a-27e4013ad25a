package com.nexmo.voice.config.callblocks;

import com.nexmo.voice.config.gateway.SIPGatewayInfoMatrixConfig;
import com.nexmo.voice.config.gateway.SIPGatewayInfoMatrixConfigLoader;
import com.nexmo.voice.core.jmx.CallBlocksJMX;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;
import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
public class CallBlocksConfigReader extends XmlAbstractReader {

    public final static String ROOT_NODE = "call-blocks";

    private CallBlocksConfigLoader callBlocksConfigLoader;

    private static final Logger Log = Logger.getLogger(CallBlocksConfigReader.class.getName());


    public CallBlocksConfigReader(String source) {
        this.callBlocksConfigLoader = new CallBlocksConfigLoader(ROOT_NODE, source);
        addHandler(this.callBlocksConfigLoader);
    }

    public CallBlocksConfig getConfig() {
        return this.callBlocksConfigLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
