package com.nexmo.voice.config.caches;

/*
 * <cache-control enabled="true">
 *   <caches>
 *     <cache type="application" size="1000" expiry="PT60M" refresh="PT10M" />
 *   </caches>
 * </cache-control>
 */

import java.time.Duration;
import java.util.Collection;
import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;


public class CacheControlConfig implements java.io.Serializable {
    private static final long serialVersionUID = 911156198624716719L;

    private final boolean enabled;
    private final Map<CacheType, CacheConfig> caches;

    public CacheControlConfig(boolean enabled, Collection<CacheConfig> caches) {
        this.enabled = enabled;
        this.caches = new EnumMap<>(CacheType.class);
        for (CacheConfig cache : caches) {
            this.caches.put(cache.getType(), cache);
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

    public int getCacheCount() {
        return caches.size();
    }

    public CacheConfig getCacheConfig(CacheType type) {
        return caches.get(type);
    }

    public Set<CacheType> getCachesPresent() {
        return caches.keySet();
    }

    public CacheControlConfig cloneWithEnabled(boolean enabled) {
        return new CacheControlConfig(enabled, this.caches.values());
    }

    public Element toXML() {
        Element cacheControl = new Element("cache-control");
        cacheControl.setAttribute("enabled", Boolean.toString(this.isEnabled()));

        Element cachesTag = new Element("caches");
        cacheControl.addContent(cachesTag);
        for (CacheConfig cache : this.caches.values()) {
            Element cacheXML = cache.toXML();
            cachesTag.addContent(cacheXML);
        }

        return cacheControl;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump CacheControlConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

}
