package com.nexmo.voice.config.gateway;

/*
 * <tts-gateway-info>
 * </tts-gateway-info>
 */

import org.jdom.Element;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

public class TTSGatewayInfoMatrixConfig extends AbstractGatewayInfoMatrixConfig implements Serializable {

    private static final long serialVersionUID = 571985432413192144L;

    public TTSGatewayInfoMatrixConfig(Map<String, SupplierMappingConfig> supplierToInfoMap) {
        super(supplierToInfoMap);
    }

    @Override
    public String getXMLNodeName() {
        return TTSGatewayInfoMatrixConfigLoader.ROOT_NODE;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof TTSGatewayInfoMatrixConfig))
            return false;
        return GatewayInfoMatrixConfigUtils.areConfigsEquivalent(this, (TTSGatewayInfoMatrixConfig) o);
    }
}
