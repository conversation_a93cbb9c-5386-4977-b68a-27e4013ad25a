package com.nexmo.voice.config.mappings;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.mappings.LVNMappingsConfig;
import com.nexmo.voice.config.mappings.LVNMappingsConfigLoader;

/**
 * Read LVN Mapping (numbers) config from a separate file...
 * 
 * Based on com.nexmo.voice.config.ConfigReader
 */
public class LVNMappingsConfigReader extends XmlAbstractReader {

    public final static String ROOT_NODE = "lvn-mappings";

    private final LVNMappingsConfigLoader lvnMappingsConfigLoader;


    public LVNMappingsConfigReader(String source) {
        this.lvnMappingsConfigLoader = new LVNMappingsConfigLoader(ROOT_NODE, source);
        addHandler(this.lvnMappingsConfigLoader);
    }

    public LVNMappingsConfig getConfig() {
        return this.lvnMappingsConfigLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
