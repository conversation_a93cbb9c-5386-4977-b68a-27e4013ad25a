package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingTargetGroupConfigLoader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;

public class ReloadMTRoutingTargetGroupsConfigReader extends XmlAbstractReader {

    private final MtRoutingTargetGroupConfigLoader mtRoutingTargetGroupConfigLoader;
    private final Config config;

    public ReloadMTRoutingTargetGroupsConfigReader(final Config config) {
        this(config, null);
    }

    public ReloadMTRoutingTargetGroupsConfigReader(Config config, final String desiredRoutingTargetGroupId) {
        this.config = config;
        this.mtRoutingTargetGroupConfigLoader = new MtRoutingTargetGroupConfigLoader(ConfigReader.ROOT_NODE + "." + MtRoutingTargetGroupConfigLoader.MT_ROUTING_NODE_NAME,
                                                                                     config.getMtRoutingConfig(),
                                                                                     config.getConfigDbConfig(),
                                                                                     false, // skipDB
                                                                                     desiredRoutingTargetGroupId);
        addHandler(this.mtRoutingTargetGroupConfigLoader);
    }

}
