package com.nexmo.voice.config.quota;

/**
 * These QUotaClient configuration paramters are used only by the SIPApp extended version
 * of the quota client.
 *
 * <AUTHOR>
 */


import org.jdom.Element;

public class ExtendedQuotaApiClientConfig {

    private final boolean logEnabled;
    private final boolean allowSkipBalanceCheckOnAGI;

    public ExtendedQuotaApiClientConfig(final boolean logEnabled, final boolean allowSkipBalanceCheckOnAGI) {
        this.logEnabled = logEnabled;
        this.allowSkipBalanceCheckOnAGI = allowSkipBalanceCheckOnAGI;

    }

    public boolean isLogEnabled() {
        return this.logEnabled;
    }

    public boolean isAllowSkipBalanceCheckOnAGI() {
        return this.allowSkipBalanceCheckOnAGI;
    }

    public Element toXML() {
        final Element xml = new Element("extended-quota-api");
        xml.setAttribute("log-enabled", "" + this.logEnabled);
        xml.setAttribute("allow-skip-balance-check-on-agi", "" + this.allowSkipBalanceCheckOnAGI);
        return xml;
    }


}
