package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class LVNMappingsConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "lvn-mappings";

    private final GroupConfigLoader groupConfigLoader;
    private final LVNConfigLoader lvnConfigLoader;

    private final List<GroupConfig> groups;
    private final List<LVNConfig> lvns;

    public LVNMappingsConfig config;
    public LVNMappingsSource source;
    public String path;
    public String mask;
    public String version;

    public LVNMappingsConfigLoader(final String nodeName) {
        this(nodeName, "sip.xml");
    }

    public LVNMappingsConfigLoader(final String nodeName, final String sourceName) {
        super(nodeName);

        groupConfigLoader = new GroupConfigLoader(getSubNodeName(nodeName, GroupConfigLoader.ROOT_NODE));
        addHandler(groupConfigLoader);
        lvnConfigLoader = new LVNConfigLoader(getSubNodeName(nodeName, LVNConfigLoader.ROOT_NODE));
        addHandler(lvnConfigLoader);

        groups = new ArrayList<GroupConfig>();
        lvns = new ArrayList<LVNConfig>();
        source = new LVNMappingsSource(sourceName);
    }

    public LVNMappingsConfig getConfig() {
        return config;
    }

    public LVNMappingsSource getSource() {
        return source;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            path = content.getAttribute("path", false);
            mask = content.getAttribute("filemask", false);
            version = content.getAttribute("version", false, "LATEST");
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            String filename = LVNMappingsConfigUtils.selectFile(path, mask, version);
            if (filename != null) {
                // Ignore body, load from file instead
                source = new LVNMappingsSource(filename);
                config = LVNMappingsConfigUtils.loadConfigFromXMLFile(filename);
            } else {
                config = new LVNMappingsConfig(groups, lvns);
            }
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof GroupConfigLoader) {
            GroupConfig gc = ((GroupConfigLoader) childHandler).getConfig();
            groups.add(gc);
        } else if (childHandler instanceof LVNConfigLoader) {
            LVNConfig lvn = ((LVNConfigLoader) childHandler).getConfig();
            lvns.add(lvn);
        }
    }

}
