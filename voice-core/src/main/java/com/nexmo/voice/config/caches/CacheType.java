package com.nexmo.voice.config.caches;

import java.time.Duration;

public enum CacheType {
    APPLICATION     ("application", 1000, Duration.ofMinutes(60), Duration.ofMinutes(10)),
    ACCOUNT         ("account", 10000, Duration.ofMinutes(60), Duration.ofMinutes(10)),
    RANDOM_POOL     ("random", 100, Duration.ofMinutes(60), Duration.ofMinutes(10));

    private final String key;
    private final int size;
    private final Duration expiry;
    private final Duration refresh;

    private CacheType(final String key, final int size, final Duration expiry, final Duration refresh) {
        this.key = key;
        this.size = size;
        this.expiry = expiry;
        this.refresh = refresh;
    }

    public static CacheType from(final String key) {
        if (key == null)
            throw new IllegalArgumentException("CacheType cannot be null");

        for (CacheType val : CacheType.values())
            if (val.getKey().equals(key))
                return val;
        throw new IllegalArgumentException("Unknown CacheType: \"" + key + "\"");
    }

    public String getKey() {
        return this.key;
    }

    public int getDefaultSize() {
        return this.size;
    }

    public Duration getDefaultExpiry() {
        return this.expiry;
    }

    public Duration getDefaultRefresh() {
        return this.refresh;
    }

    @Override
    public String toString() {
        return this.key;
    }

}