package com.nexmo.voice.config.gateway.asterisk;

import com.thepeachbeetle.common.period.Periods;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class AsteriskManagerConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "asterisk-manager";

    private boolean enabled;
    private String productClass;
    private String hostname;
    private int port;
    private String username;
    private String password;
    private boolean pingThreadEnabled;
    private long pingThreadInterval;
    private long pingThreadTimeout;
    private boolean initialRetryEnabled;
    private long initialRetryMax;
    private int initialRetryTimeout;
    private Periods initialRetryUnit;

    private boolean originateSocketEnabled;
    private String originateSocketHostname;
    private int originateSocketPort;
    private String originateSocketUsername;
    private String originateSocketPassword;

    private AsteriskManagerConfig config;

    private final String asteriskPingThreadNode;
    private final String initialRetryNode;
    private final String originateSocketNode;

    public AsteriskManagerConfigLoader(String nodeName) {
        super(nodeName);
        this.asteriskPingThreadNode = getSubNodeName("asterisk-ping-thread");
        this.initialRetryNode = getSubNodeName("initial-retry");
        this.originateSocketNode = getSubNodeName("asterisk-originate-socket");
    }

    public AsteriskManagerConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.enabled = parseBoolean(content.getAttribute("enabled", false));
            this.productClass = content.getAttribute("product-class", false, "sip").toLowerCase();
            this.hostname = content.getAttribute("hostname", this.enabled);
            this.port = parseInt(content.getAttribute("port", this.enabled));
            this.username = content.getAttribute("username", this.enabled);
            this.password = content.getAttribute("password", this.enabled);
        } else if (this.asteriskPingThreadNode.equals(node)) {
            this.pingThreadEnabled = parseBoolean(content.getAttribute("enabled", false));
            this.pingThreadInterval = parseLong(content.getAttribute("interval", this.pingThreadEnabled));
            this.pingThreadTimeout = parseLong(content.getAttribute("timeout", this.pingThreadEnabled));
        } else if (this.initialRetryNode.equals(node)) {
            this.initialRetryEnabled = parseBoolean(content.getAttribute("enabled", false));
            this.initialRetryMax = parseLong(content.getAttribute("number-retries", this.initialRetryEnabled));
            this.initialRetryTimeout = parseInt(content.getAttribute("timeout", this.initialRetryEnabled));
            String unit = content.getAttribute("unit", this.initialRetryEnabled);
            this.initialRetryUnit = Periods.getMatching(unit);
        } else if (this.originateSocketNode.equals(node)) {
            this.originateSocketEnabled = parseBoolean(content.getAttribute("enabled", false, "false"));
            this.originateSocketHostname = content.getAttribute("hostname", this.originateSocketEnabled);
            this.originateSocketPort = parseInt(content.getAttribute("port", this.originateSocketEnabled));
            this.originateSocketUsername = content.getAttribute("username", this.originateSocketEnabled);
            this.originateSocketPassword = content.getAttribute("password", this.originateSocketEnabled);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new AsteriskManagerConfig(this.enabled,
                                                    this.productClass,
                                                    this.hostname,
                                                    this.port,
                                                    this.username,
                                                    this.password,
                                                    this.pingThreadEnabled,
                                                    this.pingThreadInterval,
                                                    this.pingThreadTimeout,
                                                    this.initialRetryEnabled,
                                                    this.initialRetryMax,
                                                    this.initialRetryTimeout,
                                                    this.initialRetryUnit,
                                                    this.originateSocketEnabled,
                                                    this.originateSocketHostname,
                                                    this.originateSocketPort,
                                                    this.originateSocketUsername,
                                                    this.originateSocketPassword);
            notifyComplete();
        }
    }

}
