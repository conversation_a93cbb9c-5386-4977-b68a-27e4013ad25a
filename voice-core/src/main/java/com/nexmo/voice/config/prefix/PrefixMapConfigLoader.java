package com.nexmo.voice.config.prefix;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class PrefixMapConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "prefix-map";

    private final MappingConfigLoader mappingConfigLoader;

    private final List<MappingConfig> mappings;

    public PrefixMapConfig config;
    public PrefixMapSource source;

    public String countryCode;
    public String path;
    public String mask;
    public String version;

    public PrefixMapConfigLoader(final String nodeName) {
        this(nodeName, "sip.xml");
    }

    public PrefixMapConfigLoader(final String nodeName, final String sourceName) {
        super(nodeName);

        mappingConfigLoader = new MappingConfigLoader(getSubNodeName(nodeName, MappingConfigLoader.ROOT_NODE));
        addHandler(mappingConfigLoader);

        mappings = new ArrayList<MappingConfig>();
        source = new PrefixMapSource(sourceName);
    }

    public PrefixMapConfig getConfig() {
        return config;
    }

    public PrefixMapSource getSource() {
        return source;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            path = content.getAttribute("path", false);
            mask = content.getAttribute("filemask", false);
            version = content.getAttribute("version", false, "LATEST");
            countryCode = content.getAttribute("country-code", false);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            String filename = PrefixMapConfigUtils.selectFile(path, mask, version);
            if (filename != null) {
                // Ignore body, load from file instead
                source = new PrefixMapSource(filename);
                config = PrefixMapConfigUtils.loadConfigFromXMLFile(filename);
            } else {
                config = new PrefixMapConfig(countryCode, mappings);
            }
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof MappingConfigLoader) {
            MappingConfig mapping = ((MappingConfigLoader) childHandler).getConfig();
            mappings.add(mapping);
        }
    }

}
