package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class GroupConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "groups";
    public static final String LIST_NODE = "group";

    private final MediaDestinationLoader mediaDestinationLoader;

    private final String listNodeName;

    private GroupConfig config;

    private String name;
    private boolean strict;
    private List<MediaDestination> destinations;

    public GroupConfigLoader(final String nodeName) {
        super(nodeName);
        this.listNodeName = getSubNodeName(LIST_NODE);
        this.mediaDestinationLoader = new MediaDestinationLoader(getSubNodeName(listNodeName, MediaDestinationLoader.ROOT_NODE));
        addHandler(this.mediaDestinationLoader);
    }

    public GroupConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <groups>
        } else if (listNodeName.equals(node)) { // <group>
            name = content.getAttribute("name", true);
            strict = parseBoolean(content.getAttribute("strict", true));
            destinations = new ArrayList<MediaDestination>();
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // <groups>
        } else if (listNodeName.equals(node)) { // </group>
            this.config = new GroupConfig(name, strict, destinations);
            notifyComplete();
            this.config = null;
        } else {
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof MediaDestinationLoader) {
            List<MediaDestination> media = ((MediaDestinationLoader) childHandler).getDestinations();
            destinations.addAll(media);
        }
    }

}
