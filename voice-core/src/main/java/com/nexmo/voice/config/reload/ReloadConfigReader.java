package com.nexmo.voice.config.reload;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigLoader;
import com.nexmo.voice.config.ConfigReader;


/**
 * <AUTHOR>
 */
public class ReloadConfigReader extends XmlAbstractReader {

    private final Config config;
    private final ConfigLoader configLoader;

    public ReloadConfigReader(final Config config) {
        this.config = config;

        this.configLoader = new ConfigLoader(ConfigReader.ROOT_NODE);
        addHandler(this.configLoader);
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
