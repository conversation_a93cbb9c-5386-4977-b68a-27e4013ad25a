package com.nexmo.voice.config.dynamodb;

public class DynamoDbConfig implements  java.io.Serializable {
    private final long timeToLive;
    private final boolean deleteAfterFindItem;
    private final int findItemTimeout;

    public DynamoDbConfig(final long ttl, final boolean delete, final int findItemTimeout) {
        this.timeToLive = ttl;
        this.deleteAfterFindItem = delete;
        this.findItemTimeout = findItemTimeout;
    }

    public long getTimeToLive() {
        return this.timeToLive;
    }

    public boolean getDeleteAfterFindItem() {
        return this.deleteAfterFindItem;
    }

    public int getFindItemTimeout() {
        return this.findItemTimeout;
    }

}
