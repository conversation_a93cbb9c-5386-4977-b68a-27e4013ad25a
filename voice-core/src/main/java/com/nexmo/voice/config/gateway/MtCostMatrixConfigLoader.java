package com.nexmo.voice.config.gateway;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.iso.Currency;
import com.thepeachbeetle.common.xml.LoaderException;

import com.thepeachbeetle.messaging.hub.config.db.ConfigDBConfigLoader;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.db.DB;
import com.thepeachbeetle.messaging.hub.core.db.price.DBMTCost;
import com.thepeachbeetle.messaging.hub.ldap.LdapEntityNotFoundException;
import com.thepeachbeetle.messaging.hub.ldap.entities.LDAPCostMatrixEntry;

import static com.thepeachbeetle.messaging.hub.core.Product.PRODUCT_VOICE_CALL;

/**
 * Copied from package com.thepeachbeetle.messaging.hub.config.pricing so that
 * all of the XML parsing is in one place.
 */
public final class MtCostMatrixConfigLoader extends MessagePriceMatrixConfigLoader {

    private static final Logger Log = Logger.getLogger(MtCostMatrixConfigLoader.class.getName());

    private static final int DEFAULT_PRODUCT = (int) PRODUCT_VOICE_CALL;

    public static final String MT_COST_MATRIX_ROOT_NODE_NAME = "mt-product-cost-matrixes";
    public static final String MT_COST_MATRIX_NODE_NAME = "mt-cost-matrix";

    private String gatewayName;

    private final boolean skipDB;

    private PriceMatrixList matrix = null;

    public MtCostMatrixConfigLoader(final String nodeName,
                                    final String gatewayName,
                                    final boolean skipDB) {
        super(nodeName,
              MT_COST_MATRIX_ROOT_NODE_NAME,
              MT_COST_MATRIX_NODE_NAME);
        this.gatewayName = gatewayName;
        this.skipDB = skipDB;
    }

    @Override
    public void setPriceMatrix(final PriceMatrixList matrixList) {
        // This list will be empty in the new format... insert a single default-values matrix
        if (matrixList.getMatrix().isEmpty()) {
            matrixList.getMatrix().put(DEFAULT_PRODUCT, getDefaultPriceMatrix());
        }
        this.matrix = matrixList;
    }

    public PriceMatrixList getMatrix() {
        return this.matrix;
    }

    @Override
    protected List<Price> getAllPriceMatrixEntriesFromLdap() {
        final List<Price> list = new ArrayList<>();
        if (!this.skipDB) {
            final String baseDn = this.ldapBaseDn;
            try {
                final boolean includeSubdirs = false;
                final List<LDAPCostMatrixEntry> entries = LDAPCostMatrixEntry.findLDAPCostMatrixEntries("(objectclass=LDAPCostMatrixEntry)", baseDn, includeSubdirs);
                for (final LDAPCostMatrixEntry entry : entries)
                    list.add(new Price(entry));
            } catch (final LdapEntityNotFoundException e) {
                Log.debug("LDAP does not contain any cost matrix entries at base dn [ " + baseDn + " ] ");
            }
        }
        return list;
    }

    @Override
    protected List<Price> getAllPriceMatrixEntriesFromDb(final String dbMatrixId, final int product, final Currency currency) throws LoaderException {
        try {
            final List<Price> rules = new ArrayList<>();
            if (!this.skipDB) {
                String groupId = this.gatewayName;
                if (dbMatrixId != null && !dbMatrixId.isEmpty())
                    groupId = dbMatrixId;
                final List<DBMTCost> dbRules = DB.getAllMtCostMatrixEntriesFromDbForGateway(ConfigDBConfigLoader.getLatestConfigDB(),
                                                                                            product,
                                                                                            currency,
                                                                                            groupId);
                if (dbRules != null)
                    for (final DBMTCost dbRule : dbRules)
                        rules.add(new Price(dbRule));
            }
            return rules;
        } catch (final Exception e) {
            throw new LoaderException("Failed to load MT Cost matrix entries from DB", e);
        }
    }

}
