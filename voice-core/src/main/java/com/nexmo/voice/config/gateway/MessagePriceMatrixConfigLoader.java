package com.nexmo.voice.config.gateway;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.iso.Currency;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import com.thepeachbeetle.messaging.hub.config.pricing.MessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.PrefixAndNetworkMapMessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Product;
import com.thepeachbeetle.messaging.hub.core.Product.UnknownProductException;
import com.thepeachbeetle.messaging.hub.ldap.LdapEntityNotFoundException;
import com.thepeachbeetle.messaging.hub.ldap.entities.LDAPPriceMatrixEntry;

/**
 * Copied from package com.thepeachbeetle.messaging.hub.config.pricing so that
 * all of the XML parsing is in one place.
 */
public abstract class MessagePriceMatrixConfigLoader extends NestedXmlHandler {

    private static final Logger Log = Logger.getLogger(MessagePriceMatrixConfigLoader.class.getName());

    private static final String DEFAULT_PRODUCT = "VOICE-CALL";
    private static final String DEFAULT_PRICE = "0.0666";
    private static final String DEFAULT_PERFORM_LOOKUP = "true";
    private static final String DEFAULT_REJECT_SUBMISSION = "false";
    private static final String DEFAULT_USE_DB = "true";
    private static final String DEFAULT_USE_LDAP = "false";

    public static final String MT_PRICE_MATRIX_NODE_NAME = "mt-price-matrix";

    private final String rootNodeName;
    private final String matrixNodeName;

    private final String matrixNode;
    private final String priceNode;

    private boolean performPriceLookup;
    private BigDecimal defaultPrice;
    private boolean rejectSubmissionIfNoPriceFound;

    private boolean useDb;
    private String dbMatrixId;
    private boolean useLdap;
    protected String ldapBaseDn;

    private List<Price> currentPrices;
    private Currency currentCurrency;
    private int currentProduct;
    private String currentAccount;
    private String currentNetwork;
    private String currentPrefix;
    private String currentSenderPrefix;
    private String currentPricingGroup;
    private String description;

    private PriceMatrixList matrixes;

    public MessagePriceMatrixConfigLoader(String nodeName,
                                          final String rootNodeName,
                                          final String matrixNodeName) {
        super(nodeName);

        this.rootNodeName = rootNodeName;
        this.matrixNodeName = matrixNodeName;

        this.matrixNode = getSubNodeName(matrixNodeName);
        this.priceNode = getSubNodeName(this.matrixNode, "price");
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.matrixes = new PriceMatrixList(this.rootNodeName);
        } else if (this.matrixNode.equals(node)) {
            this.defaultPrice = parseBigDecimal(content.getAttribute("default-price", false, DEFAULT_PRICE));
            this.performPriceLookup = parseBoolean(content.getAttribute("perform-price-lookup", false, DEFAULT_PERFORM_LOOKUP));
            this.rejectSubmissionIfNoPriceFound = parseBoolean(content.getAttribute("reject-submission-if-no-price-found", false, DEFAULT_REJECT_SUBMISSION));
            this.useDb = parseBoolean(content.getAttribute("db", false, DEFAULT_USE_DB));
            this.dbMatrixId = content.getAttribute("db-matrix-id", false);
            this.useLdap = parseBoolean(content.getAttribute("ldap", false, DEFAULT_USE_LDAP));
            this.ldapBaseDn = content.getAttribute("ldap-base-dn", this.useLdap);

            String product = content.getAttribute("product", false, DEFAULT_PRODUCT);
            product = product.trim().toUpperCase();
            try {
                this.currentProduct = Product.identifyProduct(product);
            } catch (UnknownProductException e) {
                throw new LoaderException("Unrecognized Product [ " + product + " ] ");
            }

            if (this.matrixes.getMatrix().containsKey(this.currentProduct))
                throw new LoaderException("Duplicate 'product' type [ " + product + " ] when declaring matrixes [ " + this.rootNodeName + " ] ");

            this.currentPrices = null;
        } else if (this.priceNode.equals(node)) {
            this.currentCurrency = Currency.get(content.getAttribute("currency", false));
            this.currentNetwork = content.getAttribute("network", false);
            this.currentPrefix = content.getAttribute("prefix", false);
            this.currentSenderPrefix = content.getAttribute("sender-prefix", false);
            this.currentAccount = content.getAttribute("account", false);
            this.currentPricingGroup = content.getAttribute("pricing-group", false);

            this.description = content.getAttribute("description", false);

            if (this.currentNetwork != null)
                this.currentNetwork = this.currentNetwork.trim().toUpperCase();

            if (this.currentNetwork == null && this.currentPrefix == null && this.currentAccount == null && this.currentPricingGroup == null)
                throw new LoaderException("Pricing Rule with no match attributes declared ...");
            if (this.currentAccount != null &&
                !this.currentAccount.trim().equals("") &&
                this.currentPricingGroup != null &&
                !this.currentPricingGroup.trim().equals(""))
                throw new LoaderException("Pricing Rule cannot match both an account AND a pricing-group ...");

            if (this.currentAccount != null)
                this.currentAccount = this.currentAccount.trim().toLowerCase();
            if (this.currentPricingGroup != null)
                this.currentPricingGroup = this.currentPricingGroup.trim().toLowerCase();

            if (this.currentPrices == null)
                this.currentPrices = new ArrayList<>();
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            setPriceMatrix(this.matrixes);
            notifyComplete();
        } else if (this.matrixNode.equals(node)) {
            instanciateMatrixForProduct(this.currentProduct);
        } else if (this.priceNode.equals(node)) {
            BigDecimal price = parseBigDecimal(contentData);
            String countryCode = null; // .. use the network matrix to determine the country ..
            this.currentPrices.add(new Price(this.currentProduct,
                                             this.currentCurrency,
                                             this.currentNetwork,
                                             this.currentPrefix,
                                             this.currentSenderPrefix,
                                             this.currentAccount,
                                             this.currentPricingGroup,
                                             price,
                                             this.description,
                                             countryCode,
                                             null,  // dateCreated
                                             null));// timeLastUpdated
        }
    }

    private void instanciateMatrixForProduct(final int product) throws LoaderException {
        if (this.useLdap) {
            List<Price> ldapPrices = getAllPriceMatrixEntriesFromLdap();
            if (ldapPrices != null) {
                if (this.currentPrices == null)
                    this.currentPrices = new ArrayList<>();
                this.currentPrices.addAll(ldapPrices);
            }
        }

        if (this.useDb) {
            Currency currency = null; // -- TODO --
            List<Price> dbPrices = getAllPriceMatrixEntriesFromDb(this.dbMatrixId, product, currency);
            if (dbPrices != null) {
                if (this.currentPrices == null)
                    this.currentPrices = new ArrayList<>();
                this.currentPrices.addAll(dbPrices);
            }
        }

        MessagePriceMatrix matrix = new PrefixAndNetworkMapMessagePriceMatrix(product,
                                                                              this.matrixNodeName,
                                                                              this.performPriceLookup,
                                                                              this.defaultPrice,
                                                                              this.rejectSubmissionIfNoPriceFound,
                                                                              this.useDb,
                                                                              this.dbMatrixId,
                                                                              this.useLdap,
                                                                              this.ldapBaseDn,
                                                                              this.currentPrices);
        this.matrixes.getMatrix().put(this.currentProduct, matrix);
    }

    protected abstract List<Price> getAllPriceMatrixEntriesFromLdap();

    protected abstract List<Price> getAllPriceMatrixEntriesFromDb(final String dbMatrixId, final int product, final Currency currency) throws LoaderException;

    protected abstract void setPriceMatrix(PriceMatrixList matrix);

    protected MessagePriceMatrix getDefaultPriceMatrix() {
        try {
            List<Price> prices = new ArrayList<>();

            if (parseBoolean(DEFAULT_USE_LDAP)) {
                List<Price> ldapPrices = getAllPriceMatrixEntriesFromLdap();
                if (ldapPrices != null) {
                    prices.addAll(ldapPrices);
                }
            }

            if (parseBoolean(DEFAULT_USE_DB)) {
                Currency currency = null; // -- TODO --
                List<Price> dbPrices = getAllPriceMatrixEntriesFromDb(null, Product.PRODUCT_VOICE_CALL, currency);
                if (dbPrices != null) {
                    prices.addAll(dbPrices);
                }
            }
            return new PrefixAndNetworkMapMessagePriceMatrix(Product.PRODUCT_VOICE_CALL,
                                                             this.matrixNodeName,
                                                             parseBoolean(DEFAULT_PERFORM_LOOKUP),
                                                             parseBigDecimal(DEFAULT_PRICE),
                                                             parseBoolean(DEFAULT_REJECT_SUBMISSION),
                                                             parseBoolean(DEFAULT_USE_DB),
                                                             null,
                                                             parseBoolean(DEFAULT_USE_LDAP),
                                                             null,
                                                             prices);
        } catch (LoaderException ex) {
            Log.error("Cannot create a default price matrix", ex);
            return null;
        }
    }

}
