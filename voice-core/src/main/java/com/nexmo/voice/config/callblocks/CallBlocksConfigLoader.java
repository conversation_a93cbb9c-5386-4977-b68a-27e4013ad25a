package com.nexmo.voice.config.callblocks;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;

public class CallBlocksConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "call-blocks";

    private CallBlockConfigLoader callBlockConfigLoader;
    public HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap;

    public CallBlocksConfigSource source;
    public CallBlocksConfig config;

    public String path;
    public String mask;
    public String version;

    private static final Logger Log = Logger.getLogger(CallBlocksConfigLoader.class.getName());

    public CallBlocksConfigLoader(final String nodeName) {
        this(nodeName, "sip.xml");
    }

    public CallBlocksConfigLoader(final String nodeName, final String source) {
        super(nodeName);
        this.callBlockConfigLoader = new CallBlockConfigLoader(getSubNodeName(nodeName, CallBlockConfigLoader.ROOT_NODE));
        addHandler(this.callBlockConfigLoader);
        this.source = new CallBlocksConfigSource(source);
        this.callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
    }

    public CallBlocksConfigSource getSource() {
        return source;
    }

    public CallBlocksConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <call-blocks>
            this.path = content.getAttribute("path", false);
            this.mask = content.getAttribute("filemask", false);
            this.version = content.getAttribute("version", false, "LATEST");
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            String filename = CallBlocksConfigUtils.selectFile(this.path, this.mask, this.version);
            if (filename != null) {
                // Ignore body, load from file instead
                this.source = new CallBlocksConfigSource(filename); 
                this.config = CallBlocksConfigUtils.loadConfigFromXMLFile(filename);
            } else {
                this.config = new CallBlocksConfig(this.callBlockMap);
            }
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof CallBlockConfigLoader) {
            CallBlockConfig callBlockConfig = ((CallBlockConfigLoader) childHandler).getConfig();
            if (!this.callBlockMap.containsKey(callBlockConfig.getAccount())) {
                // Account apikey doesn't exist yet in hash so create entry first before adding destination prefix block
                this.callBlockMap.put(callBlockConfig.getAccount(), new HashMap<String, ArrayList<String>>());
            }
            if (!this.callBlockMap.get(callBlockConfig.getAccount()).containsKey(callBlockConfig.getDestinationCountryCode())) {
                // Destination prefix doesn't exist yet in hash so create entry first before adding call-id prefix block
                this.callBlockMap.get(callBlockConfig.getAccount()).put(callBlockConfig.getDestinationCountryCode(), new ArrayList<String>());
            }
            this.callBlockMap.get(callBlockConfig.getAccount()).get(callBlockConfig.getDestinationCountryCode()).add(callBlockConfig.getCallIdPrefix());
        }
    }

}
