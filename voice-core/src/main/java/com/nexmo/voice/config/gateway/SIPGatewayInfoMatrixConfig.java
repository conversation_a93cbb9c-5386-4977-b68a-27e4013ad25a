package com.nexmo.voice.config.gateway;

/*
 * <gateway-info>
 * </gateway-info>
 */

import java.io.Serializable;
import java.util.Map;

public class SIPGatewayInfoMatrixConfig extends AbstractGatewayInfoMatrixConfig implements Serializable {

    private static final long serialVersionUID = 8934474175648321445L;


    public SIPGatewayInfoMatrixConfig(Map<String, SupplierMappingConfig> supplierToInfoMap) {
        super(supplierToInfoMap);
    }

    @Override
    public String getXMLNodeName() {
        return SIPGatewayInfoMatrixConfigLoader.ROOT_NODE;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SIPGatewayInfoMatrixConfig))
            return false;
        return GatewayInfoMatrixConfigUtils.areConfigsEquivalent(this, (SIPGatewayInfoMatrixConfig) o);
    }

}
