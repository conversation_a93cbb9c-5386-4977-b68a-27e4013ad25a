package com.nexmo.voice.config.mappings;


public class LVNMappingsSource implements java.io.Serializable {

    private static final long serialVersionUID = 2793987654498721445L;

    private final String source;

    public LVNMappingsSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof LVNMappingsSource))
            return false;
        LVNMappingsSource other = (LVNMappingsSource) o;

        return this.source.equals(other.source);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + source.hashCode();
        return result;
    }

}
