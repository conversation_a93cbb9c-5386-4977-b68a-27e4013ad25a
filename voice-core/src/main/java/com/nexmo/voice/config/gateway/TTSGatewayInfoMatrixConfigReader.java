package com.nexmo.voice.config.gateway;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

/**
 * Read gateway info (suppliers) config from a separate file...
 * 
 * Based on com.nexmo.voice.config.ConfigReader
 */
public class TTSGatewayInfoMatrixConfigReader extends XmlAbstractReader {

    private final TTSGatewayInfoMatrixConfigLoader ttsGatewayInfoMatrixConfigLoader;


    public TTSGatewayInfoMatrixConfigReader(String source) {
        this(source, false); // skipDB = false
    }

    public TTSGatewayInfoMatrixConfigReader(String source, boolean skipDB) {
        this.ttsGatewayInfoMatrixConfigLoader = new TTSGatewayInfoMatrixConfigLoader(TTSGatewayInfoMatrixConfigLoader.ROOT_NODE, source, skipDB);
        addHandler(this.ttsGatewayInfoMatrixConfigLoader);
    }

    public TTSGatewayInfoMatrixConfig getConfig() {
        return this.ttsGatewayInfoMatrixConfigLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) {
    }

}
