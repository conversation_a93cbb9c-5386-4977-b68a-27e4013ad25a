package com.nexmo.voice.config.sip;

import java.lang.invoke.MethodHandles;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class AsteriskAgiServerConfigLoader extends NestedXmlHandler {

    private static final Logger Log = Logger.getLogger(MethodHandles.lookup().lookupClass().getName());

    public static final String ROOT_NODE = "asterisk-agi-server";

    private boolean enabled;
    private String hostname;
    private int port;
    private int poolSize;
    private int maxPoolSize;

    private AsteriskAgiServerConfig config;


    public AsteriskAgiServerConfigLoader(String nodeName) {
        super(nodeName);
    }

    public AsteriskAgiServerConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.enabled = parseBoolean(content.getAttribute("enabled", false));
            this.hostname = content.getAttribute("hostname", this.enabled);
            this.port = parseInt(content.getAttribute("port", this.enabled));
            this.poolSize = parseInt(content.getAttribute("pool-size", this.enabled));
            this.maxPoolSize = parseInt(content.getAttribute("maximum-pool-size", this.enabled));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new AsteriskAgiServerConfig(this.enabled,
                                                    this.hostname,
                                                    this.port,
                                                    this.poolSize,
                                                    this.maxPoolSize);
            notifyComplete();
        }
    }

}
