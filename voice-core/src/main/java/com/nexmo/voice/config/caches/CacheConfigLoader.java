package com.nexmo.voice.config.caches;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class CacheConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "caches";
    public static final String LIST_NODE = "cache";

    private final String listNodeName;

    private CacheConfig config;

    public CacheConfigLoader(final String nodeName) {
        super(nodeName);
        this.listNodeName = getSubNodeName(LIST_NODE);
    }

    public CacheConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <caches>
            // Start of list
        } else if (this.listNodeName.equals(node)) { // <cache>
            String type = content.getAttribute("type", true);
            String size = content.getAttribute("size", false);
            String expiry = content.getAttribute("expiry", false);
            String refresh = content.getAttribute("refresh", false);
            config = new CacheConfig(type, size, expiry, refresh);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // </caches>
            // End of list
        } else if (this.listNodeName.equals(node)) { // </cache>
            notifyComplete();
            config = null;
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        // Never called, because we don't have any child handlers
    }

}
