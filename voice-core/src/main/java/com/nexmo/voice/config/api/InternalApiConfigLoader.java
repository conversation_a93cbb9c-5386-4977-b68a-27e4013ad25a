package com.nexmo.voice.config.api;

import java.math.BigDecimal;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class InternalApiConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "internal-api";

    private final String checkBalanceNodeName;
    private final String checkConcurrentCapacityNodeName;
    private final String endpointNodeName;

    private boolean enabled;

    private boolean endpointEnabled;
    private String context;
    private String jsonPath;
    
    private PublicEndpointConfig checkBalanceEndpointConfig;

    private boolean checkBalanceEnabled;
    private boolean checkConcurrentCapacityEnabled;
    private BigDecimal minBalance;

    private InternalApiConfig config;

    public InternalApiConfigLoader(final String nodeName) {
        super(nodeName);

        this.checkBalanceNodeName = getSubNodeName("check-balance");
        this.checkConcurrentCapacityNodeName = getSubNodeName("check-concurrent-capacity");
        this.endpointNodeName = getSubNodeName("endpoint");
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.enabled = parseBoolean(content.getAttribute("enabled", true));
        } else if (this.checkBalanceNodeName.equals(node)) {
            this.checkBalanceEnabled = parseBoolean(content.getAttribute("enabled", true));
            this.minBalance = parseBigDecimal(content.getAttribute("min-balance", true), BigDecimal.ZERO);
        } else if (this.checkConcurrentCapacityNodeName.equals(node)) {
            this.checkConcurrentCapacityEnabled = parseBoolean(content.getAttribute("enabled", false));
        } else if (this.endpointNodeName.equals(node)) {
            this.endpointEnabled = parseBoolean(content.getAttribute("enabled", true));
            this.context = content.getAttribute("context", true);
            this.jsonPath = content.getAttribute("json-path", true);
        } 
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new InternalApiConfig(this.enabled,
                                                this.checkBalanceEndpointConfig,
                                                this.checkBalanceEnabled,
                                                this.minBalance,
                                                this.checkConcurrentCapacityEnabled);
            notifyComplete();
        } else if (this.endpointNodeName.equals(node)) {
            this.checkBalanceEndpointConfig = new PublicEndpointConfig(this.endpointEnabled,
                                                                       this.context,
                                                                       this.jsonPath,
                                                                       null); // xmlPath
        } 
    }

    public InternalApiConfig getConfig() {
        return this.config;
    }
}
