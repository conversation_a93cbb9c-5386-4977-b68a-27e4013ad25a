package com.nexmo.voice.config.prefix;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class MappingConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "mapping";

    private MappingConfig config;

    public MappingConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public MappingConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <mapping>
            String prefix = content.getAttribute("prefix", true);
            String group = content.getAttribute("group", true);
            config = new MappingConfig(prefix, group);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // </mapping>
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        // Never called!
    }

}
