package com.nexmo.voice.config.gateway;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.xml.LoaderException;

public class GatewayInfoMatrixConfigUtils {

    private static final Logger Log = Logger.getLogger(GatewayInfoMatrixConfigUtils.class.getName());

    public static SIPGatewayInfoMatrixConfig loadConfigFromXMLFile(String filename) {
        if (filename == null)
            return null;

        try {
            SIPGatewayInfoMatrixConfigReader reader = new SIPGatewayInfoMatrixConfigReader(filename);
            reader.read(new File(filename));
            return reader.getConfig();
        } catch (LoaderException ex) {
            Log.error("Failed to load XML file \"" + filename + "\"", ex);
            return null;
        }
    }

    public static TTSGatewayInfoMatrixConfig loadTTSConfigInfoFromXMLFile(String filename) {
        if (filename == null)
            return null;

        try {
            TTSGatewayInfoMatrixConfigReader reader = new TTSGatewayInfoMatrixConfigReader(filename);
            reader.read(new File(filename));
            return reader.getConfig();
        } catch (LoaderException ex) {
            Log.error("Failed to load XML file \"" + filename + "\"", ex);
            return null;
        }
    }

    public static String selectFile(String path, String mask, String version) {
        if (path == null)
            return null;
        if (mask == null)
            mask = "*.xml";
        if (version == null)
            version = "LATEST";

        List<String> files = null;
        try {
            files = getFileList(path, mask);
        } catch (IOException ex) {
            Log.error("Failed to retrieve file list for path = \"" + path + "\" and mask \"" + mask + "\"", ex);
            return null;
        }
        if ((files == null) || files.isEmpty())
            return null;

        String chosen = null;
        if ("LATEST".equalsIgnoreCase(version)) {
            // ISO 8601 timestamp means most recent is lexicographically last
            Collections.sort(files);
            chosen = files.get(files.size() - 1);
        } else {
            for (String f : files) {
                if (f.contains(version)) { // FIXME: handle multiple matches
                    chosen = f;
                    break;
                }
            }
        }

        if (chosen == null)
            return null;
        else
            return FileSystems.getDefault().getPath(path, chosen).toString();
    }

    public static String compareConfigs(GatewayInfoMatrixConfig oldConfig, GatewayInfoMatrixConfig newConfig) {
        final ConfigDiffs diff = diffConfigs(oldConfig, newConfig);
        return diff.toString();
    }

    public static boolean areConfigsEquivalent(GatewayInfoMatrixConfig left, GatewayInfoMatrixConfig right) {
        if ((left == null) && (right == null))
            return true;
        else if ((left == null) || (right == null))
            return false;

        // Compare keysets
        Set<String> leftKeys = left.getGatewayNames();
        Set<String> rightKeys = right.getGatewayNames();
        if (!leftKeys.equals(rightKeys))
            return false;

        // Compare each SupplierMappingConfig
        for (String key : leftKeys) {
            SupplierMappingConfig leftSupplier = left.getGatewayInfo(key);
            SupplierMappingConfig rightSupplier = right.getGatewayInfo(key);
            if (!leftSupplier.equals(rightSupplier)) {
                return false;
            }
        }

        return true;
    }

    private static ConfigDiffs diffConfigs(GatewayInfoMatrixConfig left, GatewayInfoMatrixConfig right) {
        final Set<String> leftKeys = left.getGatewayNames();
        final Set<String> rightKeys = right.getGatewayNames();

        Set<String> added = new HashSet<String>(rightKeys);
        added.removeAll(leftKeys);

        Set<String> removed = new HashSet<String>(leftKeys);
        removed.removeAll(rightKeys);

        Set<String> intersection = new HashSet<String>(leftKeys);
        intersection.retainAll(rightKeys);

        Set<String> changed = new TreeSet<String>();
        for (String key : intersection) {
            SupplierMappingConfig leftSupplier = left.getGatewayInfo(key);
            SupplierMappingConfig rightSupplier = right.getGatewayInfo(key);
            if (!leftSupplier.equals(rightSupplier))
                changed.add(key);
        }

        return new ConfigDiffs(added, removed, changed);
    }


    //
    // Internal helper methods
    //
    private static List<String> getFileList(String path, String mask) throws IOException {
        final FileSystem fs = FileSystems.getDefault();
        final PathMatcher pm = fs.getPathMatcher("glob:" + mask);

        return Files.list(Paths.get(path))
                    .map(p -> p.getFileName()) // Filename *only*, otherwise we need to match full path
                    .filter(p -> pm.matches(p))
                    .map(p -> p.toString())
                    .collect(Collectors.toList());
    }


    //
    // Internal data structures
    //
    private static class ConfigDiffs {
        private final Set<String> added;
        private final Set<String> removed;
        private final Set<String> changed;

        private ConfigDiffs(Set<String> added, Set<String> removed, Set<String> changed) {
            this.added = new TreeSet<>(added);
            this.removed = new TreeSet<>(removed);
            this.changed = new TreeSet<>(changed);
        }

        public boolean isEmpty() {
            return added.isEmpty() && removed.isEmpty() && changed.isEmpty();
        }

        @Override
        public String toString() {
            if (isEmpty())
                return "No Differences";

            StringBuilder sb = new StringBuilder();
            sb.append("Differences:\n");
            if (!added.isEmpty()) {
                sb.append("Gateways added: ");
                sb.append(added.toString());
                sb.append("\n");
            }
            if (!removed.isEmpty()) {
                sb.append("Gateways removed: ");
                sb.append(removed.toString());
                sb.append("\n");
            }
            if (!changed.isEmpty()) {
                sb.append("Gateways changed: ");
                sb.append(changed.toString());
                sb.append("\n");
            }
            return sb.toString();
        }
    }

}