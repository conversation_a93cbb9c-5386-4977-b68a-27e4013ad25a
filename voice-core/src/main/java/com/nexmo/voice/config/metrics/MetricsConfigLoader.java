package com.nexmo.voice.config.metrics;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class MetricsConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "metrics";
    private final String listenNode = this.getSubNodeName("listen");
    private MetricsConfig config;
    private boolean enabled;
    private String listenAddr;
    private int listenPort;
    private String context;
    private int minThreads;
    private int maxThreads;
    private int keepAliveTimeout;

    public MetricsConfigLoader(String nodeName) {
        super(nodeName);
    }

    public void startNode(String node, XmlContent content) throws LoaderException {
        if (this.getNodeName().equals(node)) {
            this.enabled = this.parseBoolean(content.getAttribute("enabled", true));
        } else if (this.listenNode.equals(node)) {
            this.listenAddr = content.getAttribute("addr", true);
            this.listenPort = this.parseInt(content.getAttribute("port", true));
            this.context = content.getAttribute("context", true);
            this.minThreads = this.parseInt(content.getAttribute("min-threads", true));
            this.maxThreads = this.parseInt(content.getAttribute("max-threads", true));
            this.keepAliveTimeout = this.parseInt(content.getAttribute("keep-alive-timeout", true));
        }

    }

    public void endNode(String node, String contentData) throws LoaderException {
        if (this.getNodeName().equals(node)) {
            this.config = new MetricsConfig(this.enabled, this.listenAddr, this.listenPort, this.context, minThreads, maxThreads, keepAliveTimeout);
            this.notifyComplete();
        }

    }

    public MetricsConfig getConfig() {
        return this.config;
    }
}
