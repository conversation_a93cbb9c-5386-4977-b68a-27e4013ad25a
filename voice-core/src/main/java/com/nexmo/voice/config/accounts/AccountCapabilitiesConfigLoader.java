package com.nexmo.voice.config.accounts;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class AccountCapabilitiesConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "account-capabilities";

    private final String forceGatewayCapabilityNode;
    private final String droppedCallOverwriteFromNode;
    private final String submitDroppedCallNode;
    private final String forceRingingNode;

    private boolean forceGatewayCapabilityRequired;
    private String forceGatewayCapability;
    private boolean droppedCallOverwriteFromRequired;
    private String droppedCallOverwriteFrom;
    private boolean submitDroppedCallRequired;
    private String submitDroppedCall;
    private boolean forceRingingRequired;
    private String forceRinging;

    private AccountCapabilitiesConfig config;

    public AccountCapabilitiesConfigLoader(final String nodeName) {
        super(nodeName);
        this.forceGatewayCapabilityNode = getSubNodeName(nodeName, "force-gateway");
        this.droppedCallOverwriteFromNode = getSubNodeName(nodeName, "dropped-call-overwrite-from");
        this.submitDroppedCallNode = getSubNodeName(nodeName, "submit-dropped-call");
        this.forceRingingNode = getSubNodeName(nodeName, "force-ringing");
    }

    public AccountCapabilitiesConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (this.forceGatewayCapabilityNode.equals(node)) {
            this.forceGatewayCapabilityRequired = parseBoolean(content.getAttribute("enabled", false), false);
            this.forceGatewayCapability = content.getAttribute("name", this.forceGatewayCapabilityRequired);
        } else if (this.droppedCallOverwriteFromNode.equals(node)) {
            this.droppedCallOverwriteFromRequired = parseBoolean(content.getAttribute("enabled", false), false);
            this.droppedCallOverwriteFrom = content.getAttribute("name", this.droppedCallOverwriteFromRequired);
        } else if (this.submitDroppedCallNode.equals(node)) {
            this.submitDroppedCallRequired = parseBoolean(content.getAttribute("enabled", false), false);
            this.submitDroppedCall = content.getAttribute("name", this.submitDroppedCallRequired);
        } else if (this.forceRingingNode.equals(node)) {
            this.forceRingingRequired = parseBoolean(content.getAttribute("enabled", false), false);
            this.forceRinging = content.getAttribute("name", this.forceRingingRequired);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new AccountCapabilitiesConfig(this.forceGatewayCapabilityRequired,
                                                        this.forceGatewayCapability,
                                                        this.droppedCallOverwriteFromRequired,
                                                        this.droppedCallOverwriteFrom,
                                                        this.submitDroppedCallRequired,
                                                        this.submitDroppedCall,
                                                        this.forceRingingRequired,
                                                        this.forceRinging);
            notifyComplete();
        }
    }
}

