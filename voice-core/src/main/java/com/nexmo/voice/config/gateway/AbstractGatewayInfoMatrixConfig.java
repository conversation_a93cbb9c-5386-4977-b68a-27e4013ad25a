package com.nexmo.voice.config.gateway;

/*
 * <XML-NODE>
 *     <supplier-mapping name="annecto"
 *                       endpoint="91.208.111.8"
 *                       number-prefix=""
 *                       caller-id="">
 *         <mt-product-cost-matrixes>
 *             <mt-cost-matrix product="VOICE"
 *                          default-price="0.0666"
 *                          perform-price-lookup="true"
 *                          reject-submission-if-no-price-found="false"
 *                          db="true"
 *                          ldap="false">
 *             </mt-cost-matrix>
 *         </mt-product-cost-matrixes>
 *     </supplier-mapping>
 *     <supplier-mapping name="idt"
 *                       endpoint="213.166.103.1"
 *                       number-prefix="4444#"
 *                       caller-id="">
 *         <mt-product-cost-matrixes>
 *             <mt-cost-matrix product="VOICE"
 *                          default-price="0.0666"
 *                          perform-price-lookup="true"
 *                          reject-submission-if-no-price-found="false"
 *                          db="true"
 *                          ldap="false">
 *             </mt-cost-matrix>
 *         </mt-product-cost-matrixes>
 *     </supplier-mapping>
 *     <supplier-mapping name="aql"
 *                       endpoint="sip.aql.com"
 *                       number-prefix=""
 *                       caller-id="<EMAIL>">
 *         <mt-product-cost-matrixes>
 *             <mt-cost-matrix product="VOICE"
 *                          default-price="0.0666"
 *                          perform-price-lookup="true"
 *                          reject-submission-if-no-price-found="false"
 *                          db="true"
 *                          ldap="false">
 *             </mt-cost-matrix>
 *         </mt-product-cost-matrixes>
 *     </supplier-mapping>
 * </XML-NODE>
 */

import org.jdom.Element;

import java.util.Map;
import java.util.Set;

abstract class AbstractGatewayInfoMatrixConfig implements GatewayInfoMatrixConfig {

    private final Map<String, SupplierMappingConfig> supplierToInfoMap;

    public AbstractGatewayInfoMatrixConfig(Map<String, SupplierMappingConfig> supplierToInfoMap) {
        this.supplierToInfoMap = supplierToInfoMap;
    }

    public abstract String getXMLNodeName();

    @Override
    public SupplierMappingConfig getGatewayInfo(String gatewayName) {
        return this.supplierToInfoMap.get(gatewayName);
    }

    @Override
    public Set<String> getGatewayNames() {
        return supplierToInfoMap.keySet();
    }

    @Override
    public int getGatewayCount() {
        return supplierToInfoMap.size();
    }

    @Override
    public boolean hasGateways() {
        return !supplierToInfoMap.isEmpty();
    }

    @Override
    public Element toXML() {
        final Element gatewayInfo = new Element(getXMLNodeName());
        this.supplierToInfoMap.values().stream().map(SupplierMappingConfig::toXML).forEach(gatewayInfo::addContent);
        return gatewayInfo;
    }


    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + supplierToInfoMap.hashCode();
        return result;
    }

}
