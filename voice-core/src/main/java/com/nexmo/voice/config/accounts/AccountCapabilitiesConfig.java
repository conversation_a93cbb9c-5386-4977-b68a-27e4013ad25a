package com.nexmo.voice.config.accounts;

import java.io.Serializable;

import org.jdom.Element;

public class AccountCapabilitiesConfig implements Serializable {

    private static final long serialVersionUID = -4987788697996947361L;

    private final boolean forceGatewayCapabilityRequired;
    private final String forceGatewayCapability;
    private final boolean droppedCallOverwriteFromRequired;
    private final String droppedCallOverwriteFrom;
    private final boolean submitDroppedCallRequired;
    private final String submitDroppedCall;
    private final boolean forceRingingCapabilityRequired;
    private final String forceRingingCapability;

    public AccountCapabilitiesConfig(boolean accountCapabilityForceGatewayCapabilityRequired,
                                     String accountCapabilityForceGatewayCapability,
                                     boolean accountCapabilityDroppedCallOverwriteFromRequired,
                                     String accountCapabilityDroppedCallOverwriteFrom,
                                     boolean accountCapabilitySubmitDroppedCallRequired,
                                     String accountCapabilitySubmitDroppedCall,
                                     boolean forceRingingCapabilityRequired,
                                     String forceRingingCapability) {
        this.forceGatewayCapabilityRequired = accountCapabilityForceGatewayCapabilityRequired;
        this.forceGatewayCapability = accountCapabilityForceGatewayCapability;
        this.droppedCallOverwriteFromRequired = accountCapabilityDroppedCallOverwriteFromRequired;
        this.droppedCallOverwriteFrom = accountCapabilityDroppedCallOverwriteFrom;
        this.submitDroppedCallRequired = accountCapabilitySubmitDroppedCallRequired;
        this.submitDroppedCall = accountCapabilitySubmitDroppedCall;
        this.forceRingingCapabilityRequired = forceRingingCapabilityRequired;
        this.forceRingingCapability = forceRingingCapability;
    }

    public boolean isForceGatewayCapabilityRequired() {
        return this.forceGatewayCapabilityRequired;
    }

    public String getForceGatewayCapability() {
        return this.forceGatewayCapability;
    }

    public boolean isDroppedCallOverwriteFromRequired() {
        return this.droppedCallOverwriteFromRequired;
    }

    public String getDroppedCallOverwriteFrom() {
        return this.droppedCallOverwriteFrom;
    }

    public boolean isSubmitDroppedCallRequired() {
        return this.submitDroppedCallRequired;
    }

    public String getSubmitDroppedCall() {
        return this.submitDroppedCall;
    }

    public boolean isForceRingingCapabilityRequired() {
        return this.forceRingingCapabilityRequired;
    }

    public String getForceRingingCapability() {
        return this.forceRingingCapability;
    }

    public Element toXML() {
        Element accountCapabilities = new Element("account-capabilities");

        if (this.forceGatewayCapability != null) {
            Element capability = new Element("force-gateway");
            capability.setAttribute("enabled", String.valueOf(this.forceGatewayCapabilityRequired));
            capability.setAttribute("name", this.forceGatewayCapability);

            accountCapabilities.addContent(capability);
        }

        if (this.droppedCallOverwriteFrom != null) {
            Element capability = new Element("dropped-call-overwrite-from");
            capability.setAttribute("enabled", String.valueOf(this.droppedCallOverwriteFromRequired));
            capability.setAttribute("name", this.droppedCallOverwriteFrom);

            accountCapabilities.addContent(capability);
        }

        if (this.submitDroppedCall != null) {
            Element capability = new Element("submit-dropped-call");
            capability.setAttribute("enabled", String.valueOf(this.submitDroppedCallRequired));
            capability.setAttribute("name", this.submitDroppedCall);

            accountCapabilities.addContent(capability);
        }

        if (this.forceRingingCapability != null) {
            Element capability = new Element("force-ringing");
            capability.setAttribute("enabled", String.valueOf(this.forceRingingCapabilityRequired));
            capability.setAttribute("name", this.forceRingingCapability);

            accountCapabilities.addContent(capability);
        }

        return accountCapabilities;
    }
}

