package com.nexmo.voice.config;

/*
 * <whitelisted-numbers enabled="true">
 *     <number>123589721</number>
 *     <number>123589721</number>
 * </whitelisted-numbers>
 */

import java.util.Collections;
import java.util.Set;

import org.jdom.Element;

public class WhitelistedNumbersConfig implements java.io.Serializable {

    private static final long serialVersionUID = 952232110990481224L;

    private final boolean enabled;
    private final Set<String> whitelistedNumbers;

    public WhitelistedNumbersConfig(boolean enabled, Set<String> numbers) {
        this.enabled = enabled;

        if (numbers == null)
            this.whitelistedNumbers = Collections.emptySet();
        else
            this.whitelistedNumbers = numbers;
    }

    //Tally: Shouldn't we add a verification whether the feature is enabled or not??
    public boolean isWhitelisted(String number) {
        if (number == null)
            return false;

        for (String whitelisted : this.whitelistedNumbers) {
            if (number.startsWith(whitelisted))
                return true;
        }

        return false;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public Element toXML() {
        Element root = new Element(WhitelistedNumbersConfigLoader.ROOT_NODE);

        root.setAttribute("enabled", String.valueOf(this.enabled));

        for (String number : this.whitelistedNumbers) {
            Element numberNode = new Element("number");
            numberNode.addContent(number);
            root.addContent(numberNode);
        }

        return root;
    }
}
