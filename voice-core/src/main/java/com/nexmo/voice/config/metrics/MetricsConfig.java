package com.nexmo.voice.config.metrics;


import com.nexmo.voice.core.sip.api.metrics.PrometheusScrapeServlet;
import com.thepeachbeetle.common.app.ServerStartException;
import com.thepeachbeetle.common.http.HttpServer;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.hotspot.DefaultExports;
import org.apache.log4j.Logger;
import org.jdom.Element;

/**
 * <metrics enabled="true">
 *         <listen addr="0.0.0.0" port="7770" context="metrics" min-threads="1" max-threads="10" keep-alive-timeout="30000"/>
 * </metrics>
 */
public class MetricsConfig {

    public static final String SERVER_NAME = "metrics-api";
    public static final String HUB_NAME = "core";

    private static final Logger Log = Logger.getLogger(MetricsConfig.class.getName());
    private final boolean enabled;
    private final String listenAddr;
    private final int listenPort;
    private final String context;
    private final int minThreads;
    private final int maxThreads;
    private final int keepAliveTimeout;


    /**
     * Histogram buckets for quantiles covers .005 seconds(5ms) to 2.5 seconds
     */
    public static final double[] DEFAULT_DURATION_BUCKETS = new double[]{.005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5 };

    public MetricsConfig(final boolean enabled,
                         final String listenAddr,
                         final int listenPort,
                         final String context,
                         int minThreads,
                         int maxThreads,
                         int keepAliveTimeout) {
        this.enabled = enabled;
        this.listenAddr = listenAddr;
        this.listenPort = listenPort;
        this.context = context;
        this.minThreads = minThreads;
        this.maxThreads = maxThreads;
        this.keepAliveTimeout = keepAliveTimeout;
    }


    public void init() throws ServerStartException {
        Log.info(":::::: METRICS :: LISTEN ENABLED [ " + this.enabled + " ] LISTEN ON [ " + this.listenAddr + " / " + this.listenPort + " ] ::::::::::::");
        if (this.enabled) {
            try {
                HttpServer.getInstance().initializeHttpListener(HUB_NAME,
                        SERVER_NAME,
                        this.listenAddr,
                        this.listenPort,
                        false,
                        null,
                        null, // access logs prefix
                        this.minThreads, // minThreads
                        this.maxThreads, // maxThreads
                        this.keepAliveTimeout, // keepAliveTimeout
                        false, // jmxEnabled
                        false, // sslListenEnabled
                        null, // sslListenAddr
                        -1, // sslListenPort
                        null, // sslListenKeystoreLocation
                        null, // sslListenKeystorePassword
                        null); // sslListenKeyPassword
                loadBasicMetrics();
                final PrometheusScrapeServlet prometheusScrapeServlet = new PrometheusScrapeServlet(CollectorRegistry.defaultRegistry);
                HttpServer.getInstance().addServletToHttpContext(HUB_NAME, SERVER_NAME, this.context, prometheusScrapeServlet.clientPath(), prometheusScrapeServlet, null, null);
                HttpServer.getInstance().startHttpServer(HUB_NAME, SERVER_NAME);
                Log.info("::::::::::: METRICS LISTENER STARTED ON [ " + this.listenAddr + " / " + this.listenPort + " ] ::::::::::::");
            } catch (final Exception e) {
                Log.fatal("Failed to initialize the metrics http interface .......aborting .....", e);
                throw new ServerStartException("Failed to initialize the metrics http interface .......aborting .....", e);
            }
        }
    }

    private void loadBasicMetrics() {
        DefaultExports.initialize();
    }




    public Element toXML() {
        final Element metrics = new Element("metrics");
        metrics.setAttribute("enabled", "" + this.enabled);

        final Element listen = new Element("listen");
        listen.setAttribute("addr", this.listenAddr == null ? "0.0.0.0" : this.listenAddr);
        listen.setAttribute("port", "" + this.listenPort);
        listen.setAttribute("context", this.context);
        metrics.addContent(listen);

        return metrics;
    }
}
