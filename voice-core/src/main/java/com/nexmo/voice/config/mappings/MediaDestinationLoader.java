package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class MediaDestinationLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "media";
    public static final String LIST_NODE = "destination";

    private final String listNodeName;

    private List<MediaDestination> destinationList;

    private String name;
    private String description;

    public MediaDestinationLoader(final String nodeName) {
        super(nodeName);
        this.listNodeName = getSubNodeName(LIST_NODE);
    }

    public List<MediaDestination> getDestinations() {
        return this.destinationList;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <media>
            destinationList = new ArrayList<MediaDestination>();
        } else if (this.listNodeName.equals(node)) { // <destination>
            name = content.getAttribute("name", true);
            description = null; // FIXME!
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) { // </media>
            notifyComplete();
        } else if (this.listNodeName.equals(node)) { // </destination>
            MediaDestination dst = new MediaDestination(name, description);
            destinationList.add(dst);
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        // Never called!
    }

}
