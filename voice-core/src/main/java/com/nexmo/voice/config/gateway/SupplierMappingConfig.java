package com.nexmo.voice.config.gateway;

/*
 *     <********-mapping name="annecto"
 *                       endpoint="91.208.111.8"
 *                       number-prefix=""
 *                       caller-id="">
 *         <mt-product-cost-matrixes>
 *             <mt-cost-matrix product="VOICE"
 *                             default-price="0.0666"
 *                             perform-price-lookup="true"
 *                             reject-submission-if-no-price-found="false"
 *                             db="true"
 *                             ldap="false">
 *             </mt-cost-matrix>
 *         </mt-product-cost-matrixes>
 *         <mo-product-cost-matrixes>
 *             <mo-cost-matrix product="VOICE"
 *                             default-price="0.0666"
 *                             perform-price-lookup="true"
 *                             reject-submission-if-no-price-found="false"
 *                             db="true"
 *                             ldap="false">
 *             </mo-cost-matrix>
 *         </mo-product-cost-matrixes>
 *     </********-mapping>
 */

import java.util.Set;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;

public class SupplierMappingConfig implements java.io.Serializable {

    private static final long serialVersionUID = -6899371276700010571L;

    private final String name;
    private final String endpoint;
    private final String callerId;
    private final String numberPrefix;
    private final PriceMatrixList costMatrix;
    private final PriceMatrixList inboundCostMatrix;

    public SupplierMappingConfig(final String name,
                                 final String endpoint,
                                 final String callerId,
                                 final String numberPrefix,
                                 final PriceMatrixList costMatrix,
                                 final PriceMatrixList inboundCostMatrix) {
        this.name = name;
        this.endpoint = endpoint;
        this.callerId = callerId;
        this.numberPrefix = numberPrefix;
        this.costMatrix = costMatrix;
        this.inboundCostMatrix = inboundCostMatrix;
    }

    public String getName() {
        return this.name;
    }

    public String getEndpoint() {
        return this.endpoint;
    }

    public String getCallerId() {
        return this.callerId;
    }

    public String getNumberPrefix() {
        return this.numberPrefix;
    }

    public PriceMatrixList getCostMatrix() {
        return this.costMatrix;
    }

    public PriceMatrixList getInboundCostMatrix() {
        return this.inboundCostMatrix;
    }

    public Element toXML() {
        Element ********Mapping = new Element("********-mapping");

        ********Mapping.setAttribute("name", this.getName());
        ********Mapping.setAttribute("endpoint", this.getEndpoint());

        if (this.getCallerId() != null)
            ********Mapping.setAttribute("caller-id", this.getCallerId());
        if (this.getNumberPrefix() != null)
            ********Mapping.setAttribute("number-prefix", this.getNumberPrefix());

        if (this.costMatrix != null)
            ********Mapping.addContent(this.costMatrix.toXML());

        if (this.inboundCostMatrix != null)
            ********Mapping.addContent(this.inboundCostMatrix.toXML());

        return ********Mapping;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SupplierMappingConfig))
            return false;
        SupplierMappingConfig other = (SupplierMappingConfig) o;

        // Mandatory parameters
        if (!this.name.equals(other.name))
            return false;
        if (!this.endpoint.equals(other.endpoint))
            return false;

        // Optional parameters
        if (this.callerId != null) {
            if (!this.callerId.equals(other.callerId))
                return false;
        } else if (other.callerId != null) {
            return false;
        }
        if (this.numberPrefix != null) {
            if (!this.numberPrefix.equals(other.numberPrefix))
                return false;
        } else if (other.numberPrefix != null) {
            return false;
        }

        // MT cost matrix list
        if ((this.costMatrix != null) && (other.costMatrix != null)) {
            boolean same = PriceMatrixListUtils.arePriceMatrixListsEquivalent(this.costMatrix, other.costMatrix);
            if (!same)
                return false;
        } else if ((this.costMatrix != null) || (other.costMatrix != null)) {
            return false;
        } // Else, both are null

        // MO cost matrix list
        if ((this.inboundCostMatrix != null) && (other.inboundCostMatrix != null)) {
            boolean same = PriceMatrixListUtils.arePriceMatrixListsEquivalent(this.inboundCostMatrix, other.inboundCostMatrix);
            if (!same)
                return false;
        } else if ((this.inboundCostMatrix != null) || (other.inboundCostMatrix != null)) {
            return false;
        } // Else, both are null

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + name.hashCode();
        result = prime * result + endpoint.hashCode();
        result = prime * result + ((callerId == null) ? 0 : callerId.hashCode());
        result = prime * result + ((numberPrefix == null) ? 0 : numberPrefix.hashCode());
        result = prime * result + ((costMatrix == null) ? 0 : PriceMatrixListUtils.hashPriceMatrixList(costMatrix));
        result = prime * result + ((inboundCostMatrix == null) ? 0 : PriceMatrixListUtils.hashPriceMatrixList(inboundCostMatrix));
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump SupplierMappingConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

}
