package com.nexmo.voice.config.api;

import org.jdom.Element;


public class PublicEndpointConfig implements java.io.Serializable {

    private static final long serialVersionUID = -2879817651325721318L;

    private final boolean enabled;
    private final String context;
    private final String jsonPath;
    private final String xmlPath;

    public PublicEndpointConfig(final boolean enabled,
                                final String context,
                                final String jsonPath,
                                final String xmlPath) {
        this.enabled = enabled;
        this.context = context;
        this.jsonPath = jsonPath;
        this.xmlPath = xmlPath;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public String getContext() {
        return this.context;
    }

    public String getJsonPath() {
        return this.jsonPath;
    }

    public String getXmlPath() {
        return this.xmlPath;
    }

    public Element toXML(String nodeName) {
        Element servletNode = new Element(nodeName);

        servletNode.setAttribute("enabled", String.valueOf(this.enabled));
        servletNode.setAttribute("context", this.context);

        if (this.jsonPath != null)
            servletNode.setAttribute("json-path", this.jsonPath);
        if (this.xmlPath != null)
            servletNode.setAttribute("xml-path", this.xmlPath);

        return servletNode;
    }

}
