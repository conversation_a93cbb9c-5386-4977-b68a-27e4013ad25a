package com.nexmo.voice.config;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Map.Entry;

import org.jdom.Element;


public class ChargingConfig implements java.io.Serializable {

    private static final long serialVersionUID = -5997022776907594301L;
    private final BigDecimal minPrice;
    private final BigDecimal defaultCost;
    private final BigDecimal defaultSipPrice;
    private final BigDecimal defaultSipCost;
    private final BigDecimal defaultSipDialInPrice;
    private final BigDecimal defaultSipDestinationPrice;
    private final BigDecimal defaultInboundPrice;
    private final BigDecimal defaultInboundCost;
    private final long minIncrement;
    private final long recurringIncrement;
    private final BigDecimal ttsNGMinPrice;

    private final Map<String, CountrySpecificInfo> countrySpecificInfo;

    private final CountrySpecificInfo defaultSpecificInfo;

    public ChargingConfig(final BigDecimal minPrice,
                          final BigDecimal defaultCost,
                          final BigDecimal defaultSipPrice,
                          final BigDecimal defaultSipCost,
                          final BigDecimal defaultSipDialInPrice,
                          final BigDecimal defaultSipDestinationPrice,
                          final BigDecimal defaultInboundPrice,
                          final BigDecimal defaultInboundCost,
                          final long minIncrement,
                          final long recurringIncrement,
                          final BigDecimal ttsNGMinPrice,
                          final Map<String, CountrySpecificInfo> countrySpecificInfo) {
        this.minPrice = minPrice;
        this.defaultCost = defaultCost;
        this.defaultSipPrice = defaultSipPrice;
        this.defaultSipCost = defaultSipCost;
        this.defaultSipDialInPrice = defaultSipDialInPrice;
        this.defaultSipDestinationPrice = defaultSipDestinationPrice;

        this.defaultInboundPrice = defaultInboundPrice;
        this.defaultInboundCost = defaultInboundCost;
        this.minIncrement = minIncrement;
        this.recurringIncrement = recurringIncrement;
        this.countrySpecificInfo = countrySpecificInfo;

        this.ttsNGMinPrice = ttsNGMinPrice;

        this.defaultSpecificInfo = new CountrySpecificInfo(null,
                                                           this.minIncrement,
                                                           this.recurringIncrement);
    }

    public BigDecimal getMinPrice() {
        return this.minPrice;
    }

    public BigDecimal getDefaultCost() {
        return this.defaultCost;
    }

    public BigDecimal getDefaultSipPrice() {
        return this.defaultSipPrice;
    }

    public BigDecimal getDefaultSipCost() {
        return this.defaultSipCost;
    }

    public BigDecimal getDefaultSipDialInPrice() {
        return this.defaultSipDialInPrice;
    }

    public BigDecimal getDefaultSipDestinationPrice() {
        return this.defaultSipDestinationPrice;
    }

    public BigDecimal getDefaultInboundPrice() {
        return this.defaultInboundPrice;
    }

    public BigDecimal getDefaultInboundCost() {
        return this.defaultInboundCost;
    }

    public long getMinIncrement() {
        return this.minIncrement;
    }

    public long getRecurringIncrement() {
        return this.recurringIncrement;
    }

    public BigDecimal getTTSNGMinPrice() {
        return this.ttsNGMinPrice;
    }

    //Tally: verify this in case of special countries are configured with different charging intervals.
    //The while searching for the countryCode in the special config section, it should be converted to lower case.
    //At the moment it will always return the defaults (6 secs)
    public CountrySpecificInfo getCountrySpecificInfo(String countryCode) {
        if (countryCode == null)
            return this.defaultSpecificInfo;

        CountrySpecificInfo info = this.countrySpecificInfo.get(countryCode);
        if (info == null)
            info = this.defaultSpecificInfo;

        return info;
    }

    public static class CountrySpecificInfo implements java.io.Serializable {

        private static final long serialVersionUID = 4790981466801290620L;

        private final String countryCode;
        private final long minIncrement;
        private final long recurringIncrement;

        public CountrySpecificInfo(final String countryCode,
                                   final long minIncrement,
                                   final long recurringIncrement) {
            this.countryCode = countryCode;
            this.minIncrement = minIncrement;
            this.recurringIncrement = recurringIncrement;
        }

        public String getCountryCode() {
            return this.countryCode;
        }

        public long getMinIncrement() {
            return this.minIncrement;
        }

        public long getRecurringIncrement() {
            return this.recurringIncrement;
        }

    }

    public Element toXML() {
        Element root = new Element("charging");

        root.setAttribute("min-price", this.minPrice.toPlainString());
        root.setAttribute("default-cost", this.defaultCost.toPlainString());
        root.setAttribute("default-sip-price", this.defaultSipPrice.toPlainString());
        root.setAttribute("default-sip-cost", this.defaultSipCost.toPlainString());
        root.setAttribute("default-sip-dial-in-price", this.defaultSipDialInPrice.toPlainString());
        root.setAttribute("default-sip-destination-price", this.defaultSipDestinationPrice.toPlainString());
        root.setAttribute("default-inbound-price", this.defaultInboundPrice.toPlainString());
        root.setAttribute("default-inbound-cost", this.defaultInboundCost.toPlainString());
        root.setAttribute("min-increment", "" + this.minIncrement);
        root.setAttribute("recurring-increment", "" + this.recurringIncrement);
        root.setAttribute("tts-min-price", "" + this.ttsNGMinPrice);

        Element countrySpecific = new Element("country-specific");
        for (Entry<String, CountrySpecificInfo> entry : this.countrySpecificInfo.entrySet()) {
            Element price = new Element("price");
            price.setAttribute("code", entry.getKey());
            price.setAttribute("min-increment", "" + entry.getValue().minIncrement);
            price.setAttribute("recurring-increment", "" + entry.getValue().recurringIncrement);
            countrySpecific.addContent(price);
        }

        root.addContent(countrySpecific);
        return root;
    }

}
