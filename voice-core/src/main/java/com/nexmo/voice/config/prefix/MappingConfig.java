package com.nexmo.voice.config.prefix;

/*
 *   <mapping prefix="12012010" group="806103" />
 */

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.jdom.Element;

import com.thepeachbeetle.common.util.StringUtil;
import com.thepeachbeetle.common.xml.XmlOutputterUtil;


public class MappingConfig implements java.io.Serializable {
    private static final long serialVersionUID = 2045083846927156044L;

    private static final int GROUP_NUMBER_DIGITS = 6;

    private final String prefix;
    private final String group;

    public MappingConfig(final String prefix, final String group) {
        if (prefix == null)
            throw new IllegalArgumentException("Prefix cannot be null");
        this.prefix = prefix.trim();

        if (this.prefix.isEmpty())
            throw new IllegalArgumentException("Prefix cannot be empty");
        else if (!StringUtil.containsOnlyNumbers(this.prefix))
            throw new IllegalArgumentException("Prefix must contain only numbers (was '" + this.prefix + "')");

        if (group == null)
            throw new IllegalArgumentException("Group cannot be null");
        this.group = group.trim();

        if (this.group.isEmpty())
            throw new IllegalArgumentException("Group cannot be empty");
        else if (this.group.length() != GROUP_NUMBER_DIGITS)
            throw new IllegalArgumentException("Group must be exactly six digits (was '" + this.group + "')");
        else if (!StringUtil.containsOnlyNumbers(this.group))
            throw new IllegalArgumentException("Group must contain only numbers (was '" + this.group + "')");
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getGroup() {
        return this.group;
    }


    public Element toXML() {
        Element mapping = new Element("mapping");

        mapping.setAttribute("prefix", this.getPrefix());
        mapping.setAttribute("group", this.getGroup());

        return mapping;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MappingConfig))
            return false;
        MappingConfig other = (MappingConfig) o;

        // Mandatory parameters
        if (!this.prefix.equals(other.prefix))
            return false;
        if (!this.group.equals(other.group))
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + prefix.hashCode();
        result = prime * result + group.hashCode();

        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(prefix);
        sb.append("=>");
        sb.append(group);
        return sb.toString();
    }

}
