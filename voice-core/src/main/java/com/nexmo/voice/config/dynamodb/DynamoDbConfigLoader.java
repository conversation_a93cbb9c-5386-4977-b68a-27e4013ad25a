package com.nexmo.voice.config.dynamodb;


import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DynamoDbConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "dynamodb-defaults";
    private final static Logger Log = LogManager.getLogger(DynamoDbConfigLoader.class);

    private long timeToLive;
    private boolean deleteItemAfterFind;
    private int findItemTimeout;

    private DynamoDbConfig config;
    public DynamoDbConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public DynamoDbConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws  LoaderException {
        if (getNodeName().equals(node)) {
            this.timeToLive = parseLong(content.getAttribute("time-to-live", true));
            this.deleteItemAfterFind = parseBoolean(content.getAttribute("delete-item-after-find", true));
            this.findItemTimeout = parseInt(content.getAttribute("find-item-timeout", true));
            Log.info("startNode:" + this.timeToLive + " deleteItem:" + this.deleteItemAfterFind);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new DynamoDbConfig(this.timeToLive, this.deleteItemAfterFind, this.findItemTimeout);
            notifyComplete();
        }
    }
}
