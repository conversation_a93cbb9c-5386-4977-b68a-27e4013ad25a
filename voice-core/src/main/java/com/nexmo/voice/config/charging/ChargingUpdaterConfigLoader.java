package com.nexmo.voice.config.charging;

import java.util.HashSet;
import java.util.Set;

import com.thepeachbeetle.common.period.Periods;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public final class ChargingUpdaterConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "charging-updater";

    private ChargingUpdaterConfig config;

    private boolean enabled;
    private int shutdownCount;
    private Periods shutdownUnit;
    private final Set<UpdateTaskConfig> updateTasks;

    private final UpdateTaskConfigLoader updateTaskConfigLoader;

    public ChargingUpdaterConfigLoader(final String nodeName) {
        super(nodeName);
        this.updateTasks = new HashSet<>();
        this.config = null;
        this.updateTaskConfigLoader = new UpdateTaskConfigLoader(getSubNodeName(nodeName, UpdateTaskConfigLoader.ROOT_NODE));
        addHandler(this.updateTaskConfigLoader);
    }

    public ChargingUpdaterConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.enabled = parseBoolean(content.getAttribute("enabled", true));
            this.shutdownCount = parseInt(content.getAttribute("shutdown-interval-count", false), 30);
            String key = content.getAttribute("shutdown-interval-unit", false, Periods.PERIOD_SECOND.getKey());
            this.shutdownUnit = Periods.getMatching(key);
        }

    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new ChargingUpdaterConfig(this.enabled,
                                                    this.shutdownCount,
                                                    this.shutdownUnit,
                                                    this.updateTasks);
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof UpdateTaskConfigLoader) {
            UpdateTaskConfig uc = ((UpdateTaskConfigLoader) childHandler).getConfig();

            boolean alreadyContainsSimilarConfig = !this.updateTasks.add(uc);
            if (alreadyContainsSimilarConfig)
                throw new LoaderException("Duplicated UpdateTaskConfig node for product-class: " + uc.getVoiceApplicationType());
        }
    }

}
