package com.nexmo.voice.config.prefix;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

import com.nexmo.voice.config.mappings.LVNMappingsConfig;
import com.nexmo.voice.config.mappings.LVNMappingsConfigLoader;

/**
 * Read Prefix Map config from a separate file...
 * 
 * Based on com.nexmo.voice.config.ConfigReader
 */
public class PrefixMapConfigReader extends XmlAbstractReader {

    public final static String ROOT_NODE = "prefix-map";

    private final PrefixMapConfigLoader prefixMapConfigLoader;


    public PrefixMapConfigReader(String source) {
        this.prefixMapConfigLoader = new PrefixMapConfigLoader(ROOT_NODE, source);
        addHandler(this.prefixMapConfigLoader);
    }

    public PrefixMapConfig getConfig() {
        return this.prefixMapConfigLoader.getConfig();
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
    }

}
