package com.nexmo.voice.config.mappings;

/*
 * <lvn-mappings>
 *   <groups>
 *     <group name="DAL" strict="false">
 *       <media>
 *         <destination name="dal13" />
 *       </media>
 *     </group>
 *   </groups>
 *   <lvns>
 *     <lvn number="51" group="DAL" />
 *     <lvn number="123456789" group="DAL">Some description text</lvn>
 *   </lvns>
 * </lvn-mappings>
 */

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import org.jdom.Element;

import com.thepeachbeetle.common.xml.XmlOutputterUtil;

public class LVNMappingsConfig implements java.io.Serializable {
    private static final long serialVersionUID = 6483159524202821445L;

    private static final long MAX_RANGE = 10000L; // Limit of 10,000 LVNs in a single block

    private final List<GroupConfig> groups;
    private final List<LVNConfig> lvns;

    private final Map<String, GroupConfig> numberToGeosMap = new HashMap<String, GroupConfig>();

    public LVNMappingsConfig(List<GroupConfig> groups, List<LVNConfig> lvns) {
        // Store original config
        this.groups = groups;
        this.lvns = lvns;

        // Build look-up map
        Map<String, GroupConfig> groupToInfoMap = new HashMap<String, GroupConfig>();
        for (GroupConfig g : groups) {
            groupToInfoMap.put(g.getName(), g);
        }
        for (LVNConfig lvn : lvns) {
            final String number = lvn.getNumber();
            final String groupName = lvn.getGroup();
            if (!groupToInfoMap.containsKey(groupName))
                throw new IllegalArgumentException("Unknown group '" + groupName + "' for LVN " + number);
            final GroupConfig geos = groupToInfoMap.get(groupName);

            if (number.contains(":")) {
                String[] split = number.split(":");
                if (split.length != 2)
                    throw new IllegalArgumentException("Invalid format for LVN range: " + number);
                long low = Long.valueOf(split[0]);
                long high = Long.valueOf(split[1]);
                if (low > high)
                    throw new IllegalArgumentException("Inverted LVN range: " + number);
                if ((high - low + 1L) > MAX_RANGE)
                    throw new IllegalArgumentException("Too many LVNs in range: " + number);

                // Loop through range
                for (long n = low; n <= high; n++) {
                    numberToGeosMap.put(Long.toString(n), geos);
                }
            } else {
                numberToGeosMap.put(lvn.getNumber(), geos);
            }
        }
    }

    // Accessors
    public GroupConfig getDestinationGeos(String number) {
        return numberToGeosMap.get(number);
    }

    public boolean isEmpty() {
        return numberToGeosMap.isEmpty();
    }

    public Element toXML() {
        Element lvnMappings = new Element("lvn-mappings");
        Element groupsTag = new Element("groups");
        lvnMappings.addContent(groupsTag);
        for (GroupConfig group : this.groups) {
            Element groupXML = group.toXML();
            groupsTag.addContent(groupXML);
        }
        Element lvnsTag = new Element("lvns");
        lvnMappings.addContent(lvns);
        for (LVNConfig lvnMapping : this.lvns) {
            Element lvnXML = lvnMapping.toXML();
            lvnsTag.addContent(lvnXML);
        }
        return lvnMappings;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof LVNMappingsConfig))
            return false;
        LVNMappingsConfig other = (LVNMappingsConfig) o;
        if (!this.groups.equals(other.groups))
            return false;
        if (!this.lvns.equals(other.lvns))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + groups.hashCode();
        result = prime * result + lvns.hashCode();
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump SupplierMappingConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }

    /*package*/ String diff(LVNMappingsConfig other) {
        Map<String, GroupConfig> leftMap = new HashMap<String, GroupConfig>();
        for (GroupConfig g : this.groups) leftMap.put(g.getName(), g);
        final Set<String> leftGroups = leftMap.keySet();

        Map<String, GroupConfig> rightMap = new HashMap<String, GroupConfig>();
        for (GroupConfig g : other.groups) rightMap.put(g.getName(), g);
        final Set<String> rightGroups = rightMap.keySet();

        Set<String> addedGroups = new HashSet<String>(rightGroups);
        addedGroups.removeAll(leftGroups);

        Set<String> removedGroups = new HashSet<String>(leftGroups);
        removedGroups.removeAll(rightGroups);

        Set<String> intersectionGroups = new HashSet<String>(leftGroups);
        intersectionGroups.retainAll(rightGroups);
        Set<String> changedGroups = new TreeSet<String>();
        for (String key : intersectionGroups) {
            GroupConfig leftGroup = leftMap.get(key);
            GroupConfig rightGroup = rightMap.get(key);
            if (!leftGroup.equals(rightGroup))
                changedGroups.add(key);
        }

        final Set<String> leftNumbers = this.numberToGeosMap.keySet();
        final Set<String> rightNumbers = other.numberToGeosMap.keySet();

        Set<String> addedNumbers = new HashSet<String>(rightNumbers);
        addedNumbers.removeAll(leftNumbers);

        Set<String> removedNumbers = new HashSet<String>(leftNumbers);
        removedNumbers.removeAll(rightNumbers);

        Set<String> intersectionNumbers = new HashSet<String>(leftNumbers);
        intersectionNumbers.retainAll(rightNumbers);
        Set<String> changedNumbers = new TreeSet<String>();
        for (String key : intersectionNumbers) {
            GroupConfig leftGeos = this.numberToGeosMap.get(key);
            GroupConfig rightGeos = other.numberToGeosMap.get(key);
            if (!leftGeos.equals(rightGeos))
                changedNumbers.add(key);
        }

        StringBuilder sb = new StringBuilder();
        boolean changes = false;
        sb.append("Differences:\n");
        if (!addedGroups.isEmpty()) {
            sb.append("Groups added: ");
            sb.append(addedGroups);
            sb.append("\n");
            changes = true;
        }
        if (!removedGroups.isEmpty()) {
            sb.append("Groups removed: ");
            sb.append(removedGroups);
            sb.append("\n");
            changes = true;
        }
        if (!changedGroups.isEmpty()) {
            sb.append("Groups changed: ");
            sb.append(changedGroups);
            sb.append("\n");
            changes = true;
        }
        if (!addedNumbers.isEmpty()) {
            sb.append("LVNs added: ");
            sb.append(addedNumbers);
            sb.append("\n");
            changes = true;
        }
        if (!removedNumbers.isEmpty()) {
            sb.append("LVNs removed: ");
            sb.append(removedNumbers);
            sb.append("\n");
            changes = true;
        }
        if (!changedNumbers.isEmpty()) {
            sb.append("LVNs changed: ");
            sb.append(changedNumbers);
            sb.append("\n");
            changes = true;
        }
        if (changes)
            return sb.toString();
        else
            return "No Differences";
    }

}
