package com.nexmo.voice.config.caches;

import java.util.ArrayList;
import java.util.List;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class CacheControlConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "cache-control";

    private static final String DEFAULT_ENABLED = "true";

    private final CacheConfigLoader cacheConfigLoader;

    private final List<CacheConfig> caches;
    private boolean enabled;

    public CacheControlConfig config;

    public CacheControlConfigLoader(final String nodeName) {
        super(nodeName);

        cacheConfigLoader = new CacheConfigLoader(getSubNodeName(nodeName, CacheConfigLoader.ROOT_NODE));
        addHandler(cacheConfigLoader);

        caches = new ArrayList<CacheConfig>();
    }

    public CacheControlConfig getConfig() {
        return config;
    }


    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <cache-control>
            this.enabled = parseBoolean(content.getAttribute("enabled", false, DEFAULT_ENABLED));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new CacheControlConfig(this.enabled, this.caches);
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof CacheConfigLoader) {
            CacheConfig cache = ((CacheConfigLoader) childHandler).getConfig();
            caches.add(cache);
        }
    }

}
