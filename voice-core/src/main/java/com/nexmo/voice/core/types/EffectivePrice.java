package com.nexmo.voice.core.types;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.prefix.MappingConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.core.sip.AsteriskAGIServer;
import com.thepeachbeetle.common.util.StringUtil;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Message;
import com.thepeachbeetle.messaging.hub.core.Product;


public class EffectivePrice extends EffectiveAmount implements Serializable {

    private static final Logger Log = LogManager.getLogger(EffectivePrice.class);

    public EffectivePrice(final BigDecimal amount, final String prefix) {
        this(amount, prefix, null);
    }

    public EffectivePrice(final BigDecimal amount, final String prefix, final String prefixGroup) {
        super(USAGE.PRICE, -1L, scale(amount), prefix, prefixGroup, null);
    }

    public EffectivePrice(final Price price) {
        super(USAGE.PRICE, timestampFromPricingRule(price), scale(price.getPrice()), price.getPrefix(), null, price.getSenderPrefix());
    }

    public EffectivePrice(final Price price, final MappingConfig prefixMapping) {
        super(USAGE.PRICE, timestampFromPricingRule(price), scale(price.getPrice()), prefixMapping.getPrefix(), prefixMapping.getGroup(), price.getSenderPrefix());
    }

    public BigDecimal getPrice() {
        return this.getAmount();
    }

    @Override
    public String toString() {
        return "EffectivePrice: " + super.toString();
    }

    public static EffectivePrice getPriceFromMatrixOrDefault(final PriceMatrixList priceMatrixList,
                                                             final SmppAccount account,
                                                             final String to,
                                                             final String from,
                                                             final String appId,
                                                             final String network,
                                                             final BigDecimal defaultPrice,
                                                             final boolean isVAPIOutboundToVBC,
                                                             final PrefixMapConfig prefixMap,
                                                             final ChargingConfig chargingConfig,
                                                             final VoiceProduct voiceProduct,
                                                             final String purposeDesc){
        return getPriceFromMatrixOrDefault(priceMatrixList,
                account,
                to,
                from,
                appId,
                network,
                defaultPrice,
                isVAPIOutboundToVBC,
                prefixMap,
                chargingConfig,
                voiceProduct,
                purposeDesc,
                false,
                false);
    }

    public static EffectivePrice getPriceFromMatrixOrDefault(final PriceMatrixList priceMatrixList,
                                                             final SmppAccount account,
                                                             final String to,
                                                             final String from,
                                                             final String appId,
                                                             final String network,
                                                             final BigDecimal defaultPrice,
                                                             final boolean isVAPIOutboundToVBC,
                                                             final PrefixMapConfig prefixMap,
                                                             final ChargingConfig chargingConfig,
                                                             final VoiceProduct voiceProduct,
                                                             final String purposeDesc,
                                                             final boolean isProgrammableSip,
                                                             final boolean isEmergencyCall) {
        if (Log.isDebugEnabled()) {
            Log.debug("getPriceFromMatrixOrDefault: priceMatrixList={}  account={} to={} from={} appId={} network={} defaultPrice={} isVAPIOutboundToVBC={} prefixMap={} chargingConfig={} voiceProduct={} purposeDesc={}, isProgrammableSip={}",
                    priceMatrixList, account.getSysId(), to, from, appId, network, defaultPrice.toPlainString(), isVAPIOutboundToVBC, prefixMap, chargingConfig, voiceProduct, purposeDesc, isProgrammableSip);
        }

        if(isEmergencyCall) {
            // emergency call has zero price
            if (Log.isDebugEnabled())
                Log.debug("Price zero on emergency calls.");
            return new EffectivePrice(BigDecimal.ZERO, "emergency-call-price");
        }

        //appId has value when it is an inbound call with no LVN. The call request will refer directly to applicationId (VBC)
        //Programmable SIP calls also have appId set, they are being excluded so their price can be fetched from tne user account
        if (!isProgrammableSip && Objects.nonNull(appId) && !appId.trim().isEmpty()) {
            if (Log.isDebugEnabled())
                Log.debug("Price zero on non LVN inbound calls. appId: " + appId + " purposeDesc: " + purposeDesc);
            return new EffectivePrice(BigDecimal.ZERO, "default-inbound-to-application-price");
        }

        //isVAPIOutboundToVBC is true only for VAPI-to-VBC outbound calls. In this case the cost is Zero
        if (isVAPIOutboundToVBC) {
            if (Log.isDebugEnabled())
                Log.debug("Price zero on VAPI-to-VBC OUTBOUND calls. purposeDesc: " + purposeDesc);
            return new EffectivePrice(BigDecimal.ZERO, "default-outbound-to-vbc-price");
        }

        if (!isProgrammableSip && !StringUtil.containsOnlyNumbers(to)) {
            if (Log.isDebugEnabled())
                Log.debug("getPriceFromMatrixOrDefault: to={} is NOT numeric and isProgrammableSip is {}", to, isProgrammableSip);

            //For inbound (not VBC) calls that would be the LVN and then it will include numbers
            //For outbound calls that would be the Sip address or the application_id which do not include numbers
            //SIP-287: For outbound sip destination, we now use the sip outbound price, but the cost is still zero
            //SIP-259: For outbound sip destination, if the voice product is SIP - the price is zero, if the voice product is API we now use the sip outbound price
            EffectivePrice effectivePrice = null;
            if (Objects.nonNull(to) && to.startsWith("sip:") && VoiceProduct.CALL_API.equals(voiceProduct))
                effectivePrice = new EffectivePrice(chargingConfig.getDefaultSipDestinationPrice(), "default-sip-destination-price");
            else
                effectivePrice = new EffectivePrice(BigDecimal.ZERO, "default-destination-price");

            if (Log.isDebugEnabled())
                Log.debug("Price " + effectivePrice + " on non numeric destination (to) " + to + " purposeDesc: " + purposeDesc);
            return effectivePrice;
        }

        if (Log.isDebugEnabled())
            Log.debug("getPriceFromMatrixOrDefault: to={},  isProgrammableSip = {}", to, isProgrammableSip);

        EffectivePrice ret = null;

        if (priceMatrixList != null) {
            // Create a fake Message to hold the 'from' number
            Message message = null;
            if (from != null) {
                message = new Message(Message.TYPE_OTHER, Product.PRODUCT_VOICE_CALL);
                message.oa = from;
            }
            if (isProgrammableSip) {
                ret = getProgrammableSIPPrice(priceMatrixList, account, defaultPrice, to);
            }
            else if ((prefixMap != null) && prefixMap.isEnabled() && isPrefixGroupPricingEnabled(account)) {
                String prefixGroup = null;
                try {
                    MappingConfig virtualNumber = prefixMap.lookup(to);

                    if (virtualNumber != null) {
                        prefixGroup = virtualNumber.getGroup();
                        Price priceForNumber = priceMatrixList.getPriceRuleIgnoreDefault(Product.PRODUCT_VOICE_CALL,
                                                                                         prefixGroup,
                                                                                         network, // network
                                                                                         message, // message
                                                                                         account);
                        ret = new EffectivePrice(priceForNumber, virtualNumber);
                    }
                } catch (Exception ex) {
                    Log.warn("Could not find price rule for prefix group " + prefixGroup + " (from " + from + ") account " + account.getSysId() + " network " + network + " purposeDesc: " + purposeDesc, ex);
                }
            }

            // Either we have no prefixMap *or* the prefixMap lookup has failed
            //not executed for programmable sip
            if (ret == null) {
                try {
                    Price priceForNumber = priceMatrixList.getPriceRule(Product.PRODUCT_VOICE_CALL,
                                                                        to,
                                                                        network, // network
                                                                        message, // message
                                                                        account);
                    ret = new EffectivePrice(priceForNumber);
                } catch (Exception ex) {
                    Log.warn("Could not find price rule for destination " + to + " (from " + from + ") account " + account.getSysId() + " network " + network + " purposeDesc: " + purposeDesc, ex);
                }
            }
        }

        // wrap defaultPrice
        if (ret == null) {
            Log.warn("Using DEFAULT price rule for destination " + to + " (from " + from + ") account  " + account.getSysId() + " network " + network + " purposeDesc: " + purposeDesc);
            ret = new EffectivePrice(scale(defaultPrice), "default-price");
        }

        if (Log.isDebugEnabled()) {
            BigDecimal price = ret.getPrice();
            String priceStr = Objects.nonNull(price) ? price.toPlainString() : "null";
            String prefix = ret.getPrefix();
            Log.debug("Account: {} to: {} from: {} appId: {} network: {} about to return price: {} prefix: {} purposeDesc: {} isProgrammableSip: {}",
                    account.getSysId(), to, from, appId, network, priceStr, prefix, purposeDesc, isProgrammableSip);
        }
        return ret;
    }

    private static EffectivePrice getProgrammableSIPPrice(PriceMatrixList priceMatrixList, SmppAccount account, BigDecimal defaultPrice, String to) {
        if (Log.isDebugEnabled()) {
            Log.debug("Fetching Programmable SIP price for account {}", account.getSysId());
        }
        final String prefix = StringUtils.trimToEmpty(to).startsWith("sip:") ? "default-sip-destination-price" : "default-sip-originated-price";
        try {
            final Price programmablePrice = priceMatrixList.getPriceRule(Product.PRODUCT_SIP,
                    "", //to could be bogus hence we are ignoring it
                    "", // network
                    new Message(Message.TYPE_OTHER, Product.PRODUCT_SIP),
                    account);
            Log.info("Fetched programmable SIP Price {}  for account {}", programmablePrice, account.getSysId());
            if (Objects.nonNull(programmablePrice.getAccount())) {
                return new EffectivePrice(programmablePrice);
            }
            return new EffectivePrice(programmablePrice.getPrice(), prefix);
        } catch (Exception ex) {
            Log.warn("Could not fetch programmable SIP price rule for  for account {}, using default price {}",
                    account.getSysId(), defaultPrice, ex);
            return new EffectivePrice(defaultPrice, prefix);
        }
    }


    private static boolean isPrefixGroupPricingEnabled(SmppAccount account) {
        if (account.getCapabilities() == null)
            return false;

        return account.getCapabilities().contains(AsteriskAGIServer.PREFIX_GROUP_CAPABILITY_NAME);
    }
}
