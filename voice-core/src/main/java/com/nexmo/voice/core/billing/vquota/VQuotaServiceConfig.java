package com.nexmo.voice.core.billing.vquota;

import com.nexmo.voice.core.billing.vquota.currentbalance.CurrentBalanceApiConfig;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiConfig;
import com.thepeachbeetle.common.xml.XmlOutputterUtil;
import org.jdom.Element;

public class VQuotaServiceConfig implements java.io.Serializable  {
    private static final long serialVersionUID = -8556280985146837610L;
    public static final String ROOT_NODE = "vquota-service";
    public static final String ENABLED_ATTR = "enabled";
    public static final String API_SECRET_NAME_ATTR = "secret-name";
    private final boolean enabled;
    private final String secretName;
    private final CurrentBalanceApiConfig currentBalanceApiConfig;
    private final PriceImpactApiConfig priceImpactApiConfig;

    public VQuotaServiceConfig(final boolean enabled, final String secretName, CurrentBalanceApiConfig currentBalanceApiConfig, PriceImpactApiConfig priceImpactApiConfig) {
        this.enabled = enabled;
        this.secretName = secretName;
        this.currentBalanceApiConfig = currentBalanceApiConfig;
        this.priceImpactApiConfig = priceImpactApiConfig;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public String getSecretName() {
        return secretName;
    }

    public CurrentBalanceApiConfig getCurrentBalanceApiConfig() {
        return currentBalanceApiConfig;
    }

    public PriceImpactApiConfig getPriceImpactApiConfig() {
        return priceImpactApiConfig;
    }

    public Element toXML() {
        Element vQuotaServiceElement = new Element(ROOT_NODE);
        vQuotaServiceElement.setAttribute(ENABLED_ATTR, Boolean.toString(this.enabled));
        vQuotaServiceElement.setAttribute(API_SECRET_NAME_ATTR, this.secretName);

        Element currentBalanceApiXML = currentBalanceApiConfig.toXML();
        vQuotaServiceElement.addContent(currentBalanceApiXML);

        Element priceImpactApiXML = priceImpactApiConfig.toXML();
        vQuotaServiceElement.addContent(priceImpactApiXML);

        return vQuotaServiceElement;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        Element root = this.toXML();
        try {
            String xml = XmlOutputterUtil.toString(root);
            sb.append(xml);
        } catch (Exception e) {
            sb.append("ERROR: Unable to dump VQuotaServiceConfig, because ");
            sb.append(e);
        }
        return sb.toString();
    }
}