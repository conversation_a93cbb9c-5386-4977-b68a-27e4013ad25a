package com.nexmo.voice.core.routing;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.nexmo.voice.core.randomize.Randomizer;
import com.thepeachbeetle.common.xml.LoaderException;

import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccounts;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.config.routing.CompositeMtRoutingRule;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingConfig;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRules;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingTargetGroup;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingTargetGroup.Target;
import com.thepeachbeetle.messaging.hub.core.Core;
import com.thepeachbeetle.messaging.hub.core.Message;
import com.thepeachbeetle.messaging.hub.core.ServerDelivererThreadPool;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.concat.ConcatCache;
import com.thepeachbeetle.messaging.hub.core.cost.CostMatrixes;
import com.thepeachbeetle.messaging.hub.core.downstreamstatuscache.DownstreamStatusCacheCore;
import com.thepeachbeetle.messaging.hub.core.downstreamstatuscache.DownstreamStatusCacheCore.BindStatus;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.exceptions.DropMessageException;
import com.thepeachbeetle.messaging.hub.core.exceptions.NoPriceFoundException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RandomPoolNoMatchSoRejectException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RoutingException;
import com.thepeachbeetle.messaging.hub.core.randomize.AccountRandomPools;
import com.thepeachbeetle.messaging.hub.core.routing.GenericMTRouter;
import com.thepeachbeetle.messaging.hub.core.routing.MTRoutingRuleAndGateway;

/*
 * This class purpose is to overwrite the MTRouter access to a custom local Randomizer not the one from the hub
 * Obviously it would be nicer to be able to inject a Randomizer in the GenericMTRouter but it will require too many changes in the messaging code.
 */
public class VoiceMTRouter extends GenericMTRouter {

    private static final Logger Log = Logger.getLogger(VoiceMTRouter.class.getName());

    public VoiceMTRouter(final MtRoutingConfig mtRoutingConfig) {
        super(mtRoutingConfig);
    }


    //
    // =============================================================================================================
    //

    public ServerDelivererThreadPool getDestinationGatewayForMessage(final Message message,
                                                                     final SmppAccount smppAccount) throws DropMessageException,
                                                                                                           RoutingException,
                                                                                                           RandomPoolNoMatchSoRejectException {

        // firstly, if message already has a gw, check its ok, if so, use it ...
        if (message.gateway != null) {
            ServerDelivererThreadPool gateway = Core.getInstance().getGateway(message.gateway);
            if (gateway != null && gateway.isAvailable())
                return gateway;
        }

        // Next , check whether we have a 'force-route' submission set for this message and try to action it ..
        ServerDelivererThreadPool forcedGateway = useForcedRouteIfPresent(message);
        if (forcedGateway != null)
            return forcedGateway;

        // Next, check whether there is a route forced by the short-code used as a sender for this message ...
        forcedGateway = useShortCodeRouteIfPresent(message);
        if (forcedGateway != null && !isShortCodeRoutingAllowDropRoutingRulesToOverride())
            return forcedGateway;

        // Allow the router to try to identify a route for this message .
        MTRoutingRuleAndGateway ruleAndGateway = getRoutingRuleAndDestinationGatewayForMessage(message, smppAccount);
        if (forcedGateway != null)
            return forcedGateway; // We have allowed the routing rules picker to potentially 'drop' the message, but we are not interested in whatever route it picked
        if (ruleAndGateway != null) {
            if (ruleAndGateway.getRule() != null)
                message.mtRoutingRuleSequence = ruleAndGateway.getRule().getSeq().longValue();
            return ruleAndGateway.getGateway();
        }
        return null;
    }

    private MTRoutingRuleAndGateway getRoutingRuleAndDestinationGatewayForMessage(final Message message,
                                                                                  final SmppAccount smppAccount) throws DropMessageException,
                                                                                                                        RoutingException,
                                                                                                                        RandomPoolNoMatchSoRejectException {
        boolean doNotDropOrVerify = false;
        return getRoutingRuleAndDestinationGatewayForMessage(message, smppAccount, doNotDropOrVerify);
    }

    private ServerDelivererThreadPool useForcedRouteIfPresent(final Message message) throws DropMessageException {
        ServerDelivererThreadPool gateway = null;

        // check wether we have a 'force-route' submission set for this message and try to action it ..
        if (message.forceRoute != null) {
            gateway = Core.getInstance().getGateway(message.forceRoute);
            if (gateway == null) {
                Log.warn("MT-FORCE :: BAD ROUTE .. it points to a non-existant gw [ " + message.forceRoute + " ] ");
                throw new DropMessageException("MT-FORCE :: BAD ROUTE .. it points to a non-existant gw [ " + message.forceRoute + " ] ");
            }
            message.gateway = message.forceRoute;
            return gateway;
        }

        return gateway;
    }

    private ServerDelivererThreadPool useShortCodeRouteIfPresent(final Message message) throws DropMessageException {
        ServerDelivererThreadPool gateway = null;

        // The route might have been pre-determined because we are sending using a 'ShortCode' as the originator, which has a fixed route associated with it
        if (message.routeForcedByShortCode != null) {
            gateway = Core.getInstance().getGateway(message.routeForcedByShortCode);
            if (gateway == null) {
                Log.warn("ROUTE-FORCED-BY-SHORT-CODE :: BAD ROUTE .. it points to a non-existant gw [ " + message.routeForcedByShortCode + " ] ");
                throw new DropMessageException("ROUTE-FORCED-BY-SHORT-CODE :: BAD ROUTE .. it points to a non-existant gw [ " + message.routeForcedByShortCode + " ] ");
            }
            message.gateway = message.routeForcedByShortCode;
            return gateway;
        }

        return gateway;
    }

    /**
     * doNotDropOrVerify == this will be called by tools and api's that want to retreive the rule that would cause a drop, or fail because, for eg, the gw does not exist....
     * would not be used in the conventional message routing path, which would instead want to have the drop exception thrown as early as possible ...
     *
     * @throws RandomPoolNoMatchSoRejectException
     */
    public MTRoutingRuleAndGateway getRoutingRuleAndDestinationGatewayForMessage(final Message message,
                                                                                 final SmppAccount smppAccount,
                                                                                 final boolean doNotDropOrVerify) throws DropMessageException,
                                                                                                                         RoutingException,
                                                                                                                         RandomPoolNoMatchSoRejectException {
        // firstly, if message already has a gw, check its ok, if so, use it ...
        if (!doNotDropOrVerify && message.gateway != null) {
            boolean takeForced = true;
            if (message.routeForcedByShortCode != null && isShortCodeRoutingAllowDropRoutingRulesToOverride())
                takeForced = false;
            if (takeForced) {
                ServerDelivererThreadPool gateway = Core.getInstance().getGateway(message.gateway);
                if (gateway != null)
                    return new MTRoutingRuleAndGateway(null, gateway, gateway.getName());
            }
        }

        // Attempt to route this MT
        CompositeMtRoutingRule crule = pickRule(message, smppAccount);
        MtRoutingRule rule = crule.getRuleUsedForRouting();

        if (rule == null)
            return null;
        Log.info("rule picked is [ " + rule + "] for account " + smppAccount.getSysId() + " for message " + message.toString());
        if (rule.isDrop() && !doNotDropOrVerify) {
            message.mtRoutingRuleSequence = rule.getSeq().longValue();
            throw new DropMessageException("Routing Rule Says DROP", rule.getSeq());
        }

        // if message already has a gw (ie, short-code routed and we are allowing drop-rules to over-ride, check its ok, if so, use it ...
        if (!doNotDropOrVerify && message.gateway != null) {
            ServerDelivererThreadPool gateway = Core.getInstance().getGateway(message.gateway);
            if (gateway != null)
                return new MTRoutingRuleAndGateway(null, gateway, gateway.getName());
            Log.warn("ROUTE-FORCED-BY-SHORT-CODE :: BAD ROUTE .. it points to a non-existant gw [ " + message.routeForcedByShortCode + " ] ");
            throw new DropMessageException("ROUTE-FORCED-BY-SHORT-CODE :: BAD ROUTE .. it points to a non-existant gw [ " + message.routeForcedByShortCode + " ] ");
        }

        String gatewayId = rule.getRouteToGatewayId();
        String targetGroupId = rule.getRouteToTargetGroupId();

        Collection<MtRoutingTargetGroup.Target> additionalGateways = null;

        // if the rule has a target of a target-group, then pick the most appropriate target from that group ...
        if (gatewayId == null && targetGroupId != null) {
            gatewayId = pickGatewayIdFromTargetGroup(message, targetGroupId, rule, smppAccount, doNotDropOrVerify);
            if (gatewayId == null && !doNotDropOrVerify) {
                Log.warn("MT ROUTE FAILURE... unable to pick a suitable gateway from target group [ " + targetGroupId + " ] ");
                return new MTRoutingRuleAndGateway(rule, null, null, additionalGateways);
            }

            // Allow the concat core to influence things here, if there has been a previous concat part, then make sure all parts match
            message.gateway = gatewayId;
            Core core = Core.getInstance();
            if (core != null && core.getCoreConfig() != null)
                ConcatCache.getInstance().registerOrUpdateMessage(message);
            gatewayId = message.gateway;

            additionalGateways = getAdditionalGatewaysFromTargetGroup(targetGroupId, gatewayId, rule);
            message.routingTargetGroup = targetGroupId;
        }

        ServerDelivererThreadPool gateway = null;
        if (!doNotDropOrVerify) {
            gateway = gatewayId == null ? null : Core.getInstance().getGateway(gatewayId);
            if (gateway == null) {
                Log.error("BAD MT ROUTING RULE .. it points to a non-existant gw [ " + gatewayId + " ] .. RULE [ " + rule + " ] ");
                message.gateway = null;
                return new MTRoutingRuleAndGateway(rule, null, gatewayId, additionalGateways);
            }
            if (!gateway.isAvailable()) {
                Log.error("BAD MT ROUTING RULE .. it points to a non-available gw [ " + gatewayId + " ] .. RULE [ " + rule + " ] ");
                message.gateway = null;
                return new MTRoutingRuleAndGateway(rule, null, gatewayId, additionalGateways);
            }
        }
        message.gateway = gatewayId;

        // See if the routing rule has asked us to stamp the MT with some parameters for use by the GW implementation
        if (rule.getPropertiesToAddToRoutedMessage() != null)
            for (Map.Entry<String, String> entry : rule.getPropertiesToAddToRoutedMessage().entrySet())
                message.setParam(entry.getKey(), entry.getValue());
        if (rule.getForceServiceType() != null && !rule.getForceServiceType().equals(""))
            message.serviceType = rule.getForceServiceType();
        if (rule.getForceOa() != null && !rule.getForceOa().equals("")) {
            if (message.originalSender == null)
                message.originalSender = message.oa;
            message.oa = rule.getForceOa();
            message.senderChangedByRoutingRule = true;
        }

        if (rule.getForceOaFromRandomPool() != null && !rule.getForceOaFromRandomPool().isEmpty()) {
            String forceSender = Randomizer.getInstance().randomizeSenderAddress(rule, message.da);
            if (Log.isDebugEnabled())
                Log.debug("forceSender [ " + forceSender + " ] .. RULE [ " + rule + " ] da [ " + message.da + "]");
            if (forceSender != null) { // random
                message.senderChangedByRoutingRule = true;
                message.oa = forceSender;
            }
        }

        if (rule.getForceOaFromAccountRandomPool() != null && !rule.getForceOaFromAccountRandomPool().isEmpty()) {
            try {
                if (AccountRandomPools.randomizeSenderAddress(smppAccount, rule.getForceOaFromAccountRandomPool(), message))
                    message.senderChangedByRoutingRule = true;
            } catch (AccountsException e) {
                throw new RoutingException("Error talking to account random pools :: pool [ " + rule.getForceOaFromAccountRandomPool() + " ] ....,", e);
            }
        }

        return new MTRoutingRuleAndGateway(rule, gateway, gatewayId, additionalGateways);
    }

    /**
     * Select the rule lists in order of priority
     *
     *  product-class / account
     *  account
     *  product-class account-routing-group
     *  account-routing-group
     *  product-class / master-account
     *  master-account
     *  product-class master-account-routing-group
     *  master-account-routing-group
     *  product-class / all-accounts
     *  all-accounts
     *
     */
    protected List<LinkedList<MtRoutingRule>> pickRoutingRules(final Message message, final SmppAccount smppAccount) throws RoutingException {
        if (getRoutingConfig() == null)
            return null;
        MtRoutingRules mtRoutingRules = getRoutingConfig().getMtRoutingRules();
        List<LinkedList<MtRoutingRule>> ruleSets = new ArrayList<>();

        String productClass = null;
        if (message.productClass != null && !message.productClass.isEmpty())
            productClass = message.productClass.trim().toLowerCase();

        // check for a set of rules that matches on super-account (the original submitting account)
        if (message.superBindSysID != null) {
            if (productClass != null) {
                LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccountPerProductClass(message.product).get(productClass + "::" + message.superBindSysID.trim().toLowerCase());
                if (rules != null)
                    ruleSets.add(rules);
            }
            LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccount(message.product).get(message.superBindSysID.trim().toLowerCase());
            // If we have found an account specific set for the 'super-account', then add these to the ruleset worklist
            if (rules != null)
                ruleSets.add(rules);
        }

        // check for a set of rules that matches on the submitting account, or the submitting accounts 'routing group'
        if (message.bindSystemID != null) {
            if (productClass != null) {
                LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccountPerProductClass(message.product).get(productClass + "::" + message.bindSystemID.trim().toLowerCase());
                if (rules != null)
                    ruleSets.add(rules);
            }
            LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccount(message.product).get(message.bindSystemID.trim().toLowerCase());
            // If we have found an account specific set, then add these to the ruleset worklist
            if (rules != null)
                ruleSets.add(rules);
        }
        if (smppAccount != null && smppAccount.getRoutingGroupId() != null) {
            if (productClass != null) {
                LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerRoutingGroupPerProductClass(message.product).get(productClass + "::" + smppAccount.getRoutingGroupId().trim().toLowerCase());
                if (rules != null)
                    ruleSets.add(rules);
            }
            LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerRoutingGroup(message.product).get(smppAccount.getRoutingGroupId().trim().toLowerCase());
            // If we have found an account routing group specific set, then add these to the ruleset worklist
            if (rules != null)
                ruleSets.add(rules);
        }

        // If this account has a 'master account' then look for a set of rules that matches the master account, or the master accounts 'routing group'
        if (smppAccount != null && smppAccount.getMasterAccountId() != null && smppAccount.isMasterAccountUseMasterRouting()) {
            if (productClass != null) {
                LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccountPerProductClass(message.product).get(productClass + "::" + smppAccount.getMasterAccountId().trim().toLowerCase());
                if (rules != null)
                    ruleSets.add(rules);
            }
            LinkedList<MtRoutingRule> rules = mtRoutingRules.getMtRoutingRulesPerAccount(message.product).get(smppAccount.getMasterAccountId().trim().toLowerCase());
            // If we have found an account specific set, then add these to the ruleset worklist
            if (rules != null)
                ruleSets.add(rules);
            SmppAccount masterAccount = null;
            try {
                masterAccount = Accounts.getInstance().getSmppAccount(smppAccount.getMasterAccountId());
            } catch (AccountsException e) {
                throw new RoutingException("Failed to retrieve master account [ " + smppAccount.getMasterAccountId() + " ] ");
            }
            if (masterAccount != null && masterAccount.getRoutingGroupId() != null) {
                if (productClass != null) {
                    rules = mtRoutingRules.getMtRoutingRulesPerRoutingGroupPerProductClass(message.product).get(productClass + "::" + masterAccount.getRoutingGroupId().trim().toLowerCase());
                    if (rules != null)
                        ruleSets.add(rules);
                }
                rules = mtRoutingRules.getMtRoutingRulesPerRoutingGroup(message.product).get(masterAccount.getRoutingGroupId().trim().toLowerCase());
                // If we have found an account routing group specific set, then add these to the ruleset worklist
                if (rules != null)
                    ruleSets.add(rules);
            }
        }

        // add the default 'all accounts' set of rules for this product class if we can find one
        if (productClass != null) {
            Map<String, LinkedList<MtRoutingRule>> perProductClass = mtRoutingRules.getMtRoutingRulesAllAccountsPerProductClass(message.product);
            if (perProductClass != null) {
                LinkedList<MtRoutingRule> rules = perProductClass.get(productClass);
                if (rules != null)
                    ruleSets.add(rules);
            }
        }

        // Add the default 'all accounts' set of rules to the worklist
        ruleSets.add(mtRoutingRules.getMtRoutingRulesAllAccounts(message.product));

        return ruleSets;
    }

    private Collection<MtRoutingTargetGroup.Target> getAdditionalGatewaysFromTargetGroup(final String targetGroupId,
                                                                                         final String selectedGatewayId,
                                                                                         final MtRoutingRule rule) {
        MtRoutingTargetGroup group = null;
        if (getRoutingConfig() != null &&
                getRoutingConfig().getMtRoutingTargetGroups() != null
                && targetGroupId != null)
            group = getRoutingConfig().getMtRoutingTargetGroups().get(targetGroupId.toUpperCase());
        if (group == null || group.getTargets() == null || group.getTargets().length == 0) {
            Log.error("BAD MT ROUTING RULE .. it points to a non-existant or empty target-group [ " + targetGroupId + " ] .. RULE [ " + rule + " ] ");
            return null;
        }

        Collection<MtRoutingTargetGroup.Target> additionalGateways = null;
        for (MtRoutingTargetGroup.Target target : group.getTargets()) {
            if (target.getGatewayId().equals(selectedGatewayId))
                continue;
            if (additionalGateways == null)
                additionalGateways = new ArrayList<>();
            additionalGateways.add(target);
        }

        return additionalGateways;
    }

    private String pickGatewayIdFromTargetGroup(final Message message,
                                                final String targetGroupId,
                                                final MtRoutingRule rule,
                                                final SmppAccount smppAccount,
                                                final boolean doNotDropOrVerify) {
        MtRoutingTargetGroup group = null;
        if (getRoutingConfig() != null &&
                getRoutingConfig().getMtRoutingTargetGroups() != null
                && targetGroupId != null)
            group = getRoutingConfig().getMtRoutingTargetGroups().get(targetGroupId.toUpperCase());
        if (group == null || group.getTargets() == null || group.getTargets().length == 0) {
            Log.error("BAD MT ROUTING RULE .. it points to a non-existant or empty target-group [ " + targetGroupId + " ] .. RULE [ " + rule + " ] ");
            return null;
        }

        if (group.getTargets().length == 1) {
            // special case for a target group with a single target,  its eigher good or its not,  no selection logic required
            if (group.getTargets()[0].isCurrentlyEligable())
                return group.getTargets()[0].getGatewayId();

            // no suitable targets were found in this group....

            // If this message has been explicitely marked to avoid certain gateways .....
            if (message.getGatewaysToAvoid() != null && message.getGatewaysToAvoid().contains(group.getTargets()[0].getGatewayId()))
                return null;
            return group.getTargets()[0].getGatewayId(); // return the only option available to us ...
        }

        if (group.isRoundRobin()) {
            String target = pickGatewayIdFromTargetGroupRoundRobin(group, message, smppAccount, doNotDropOrVerify);

            if (target == null) {
                // If nothing was available, pick the highest weighted target that is enabled .....
                // Then, if nothing is still available, take the highest weighted target wether it is available or not
                Target bestTarget = null;
                for (int pass = 1; pass <= 2; pass++) {
                    for (Target routingGroupTarget : group.getTargets()) {
                        if (pass == 1) {
                            if (!routingGroupTarget.isEnabled())
                                continue;
                            ServerDelivererThreadPool gateway = Core.getInstance().getGateway(routingGroupTarget.getGatewayId());
                            if (gateway == null)
                                continue;
                        }
                        // If this message has been explicitly marked to avoid certain gateways .....
                        if (message.getGatewaysToAvoid() != null && message.getGatewaysToAvoid().contains(routingGroupTarget.getGatewayId()))
                            continue;
                        if (bestTarget == null) {
                            bestTarget = routingGroupTarget;
                        } else {
                            if (routingGroupTarget.getWeight() > bestTarget.getWeight())
                                bestTarget = routingGroupTarget;
                        }
                    }
                    if (bestTarget != null)
                        break;
                }
                if (bestTarget != null)
                    target = bestTarget.getGatewayId();
            }

            return target;
        }

        // Cascading target group .........
        String target = pickGatewayIdFromTargetGroupFirstAvailable(group, message, smppAccount, doNotDropOrVerify);

        // If nothing was available, take the highest priority route ....
        if (target == null) {
            for (Target routingGroupTarget : group.getTargets()) {
                // If this message has been explicitly marked to avoid certain gateways .....
                if (message.getGatewaysToAvoid() != null && message.getGatewaysToAvoid().contains(routingGroupTarget.getGatewayId()))
                    continue;
                if (routingGroupTarget.isEnabled()) {
                    ServerDelivererThreadPool gateway = Core.getInstance().getGateway(routingGroupTarget.getGatewayId());
                    if (gateway == null)
                        continue;
                    target = routingGroupTarget.getGatewayId();
                    break;
                }
            }
            if (target == null) {
                for (Target routingGroupTarget : group.getTargets()) {
                    // If this message has been explicitly marked to avoid certain gateways .....
                    if (message.getGatewaysToAvoid() != null && message.getGatewaysToAvoid().contains(routingGroupTarget.getGatewayId()))
                        continue;
                    ServerDelivererThreadPool gateway = Core.getInstance().getGateway(routingGroupTarget.getGatewayId());
                    if (gateway == null)
                        continue;
                    target = routingGroupTarget.getGatewayId();
                    break;
                }
            }
        }
        return target;
    }

    private boolean targetDownstreamUnavailable(final MtRoutingTargetGroup.Target target) {
        DownstreamStatusCacheCore downstreamStatusCacheCore = null;
        Core core = Core.getInstance();
        if (core != null)
            downstreamStatusCacheCore = Core.getInstance().getDownstreamStatusCacheCore();
        if (downstreamStatusCacheCore != null && downstreamStatusCacheCore.isEnabled()) {
            if (getRoutingConfig().isTargetGroupConsiderDownstreamStatusCacheSkipTargetIfDownstreamNotBoundEnabled()) {
                BindStatus bindStatus = downstreamStatusCacheCore.getBindStatus(target.getGatewayId());
                if (bindStatus != null && !bindStatus.isBound())
                    return true;
            }
            if (getRoutingConfig().isTargetGroupConsiderDownstreamStatusCacheSkipTargetIfDownstreamHasQueueEnabled()) {
                int queueSize = downstreamStatusCacheCore.getQueueSize(target.getGatewayId());
                if (queueSize > getRoutingConfig().getTargetGroupConsiderDownstreamStatusCacheSkipTargetIfDownstreamHasQueueThreshold())
                    return true;
            }
        }

        return false;
    }

    private String pickGatewayIdFromTargetGroupFirstAvailable(final MtRoutingTargetGroup group,
                                                              final Message message,
                                                              final SmppAccount smppAccount,
                                                              final boolean doNotDropOrVerify) {
        // walk the targets inside the group and pick the first successfull match...
        for (MtRoutingTargetGroup.Target target : group.getTargets()) {
            if (!routingTargetGroupTargetOk(message, group, target, smppAccount, doNotDropOrVerify))
                continue;

            // found a suitable target.. use this...
            return target.getGatewayId();
        }

        // no suitable targets were found in this group....
        return null;
    }

    private String pickGatewayIdFromTargetGroupRoundRobin(final MtRoutingTargetGroup group,
                                                          final Message message,
                                                          final SmppAccount smppAccount,
                                                          final boolean doNotDropOrVerify) {
        boolean[] triedAlready = new boolean[group.getTargets().length];
        int targetPointer = 0;
        for (int pass = 1; pass <= group.getRoundRobinTargets().length; pass++) {

            // rotate through the targets for this group .....
            if (pass == 1) {
                // the starting point for this loop is allocated on a round-robin basis from the group
                targetPointer = group.getNextRoundRobinTargetPointer();
            } else {
                targetPointer++;
                if (targetPointer >= group.getRoundRobinTargets().length)
                    targetPointer = 0;
            }

            // we can perform a check here, to see if we have alreayd tried every target known to the group
            // if so, we can bail out and fail early ...
            if (pass > group.getTargets().length) {
                boolean stillSomeLeftToTry = false;
                for (boolean tried : triedAlready) {
                    if (!tried) {
                        stillSomeLeftToTry = true;
                        break;
                    }
                }
                // We have already tried every target known to this group, so there is no point continuing ...
                if (!stillSomeLeftToTry)
                    return null;
            }

            int targetIndex = group.getRoundRobinTargets()[targetPointer];
            MtRoutingTargetGroup.Target target = group.getTargets()[targetIndex];
            if (!routingTargetGroupTargetOk(message, group, target, smppAccount, doNotDropOrVerify)) {
                triedAlready[targetIndex] = true;
                continue;
            }

            // found a suitable target.. use this...
            return target.getGatewayId();
        }
        return null;
    }

    private boolean routingTargetGroupTargetOk(final Message message,
                                               final MtRoutingTargetGroup group,
                                               final MtRoutingTargetGroup.Target target,
                                               final SmppAccount smppAccount,
                                               final boolean doNotDropOrVerify) {
        if (!target.isCurrentlyEligable())
            return false;

        // If this message has been explicitly marked to avoid certain gateways .....
        if (message.getGatewaysToAvoid() != null && message.getGatewaysToAvoid().contains(target.getGatewayId()))
            return false;

        // Skip if this target has some downstream condition that means it is not suitable (eg, not bound to supplier or queue too big)
        if (targetDownstreamUnavailable(target))
            return false;

        if (!doNotDropOrVerify) {
            ServerDelivererThreadPool gateway = Core.getInstance().getGateway(target.getGatewayId());
            if (gateway == null)
                return false;

            if (!gateway.isGatewayAbleToAcceptMTs(message))
                return false;
        }

        // Check that we have a cost rule ....
        if (getRoutingConfig().isTargetGroupConsiderDownstreamStatusCacheSkipTargetIfNoCostFound()) {
            PriceMatrixList gatewayCostMatrix = null;
            try {
                final CostMatrixes costMatrixes = Core.getInstance().getCostMatrixes();
                if (costMatrixes != null)
                    gatewayCostMatrix = Core.getInstance().getCostMatrixes().getMtCostMatrix(message.product, target.getGatewayId());
            } catch (final LoaderException e) {
                Log.error("Failed to lookup cost matrix for gw [ " + target.getGatewayId() + " ] when doing routing target group target has cost check ", e);
                return false;
            }
            if (gatewayCostMatrix == null) {
                Log.error("could not find a cost matrix for gw [ " + target.getGatewayId() + " ] when doing routing target group target has cost check ");
                return false;
            }

            Price cost = null;
            try {
                cost = gatewayCostMatrix.getPriceRuleIgnoreDefault(message.product,
                                                                   message.da,
                                                                   message.pbOperator,
                                                                   message,
                                                                   smppAccount);
            } catch (NoPriceFoundException e) {
                Log.info("No cost found for target [ " + target.getGatewayId() + " ] found in target group [ " + group.getId() + " ] .. skipping this target ....");
            }
            if (cost == null)
                return false;
        }

        return true;
    }

    public boolean isDomesticRoutingFlag() {
        Log.debug("VoiceMTRouter isDomesticRoutingFlag: " + com.nexmo.voice.core.Core.getInstance().isDomesticRoutingFlag());
        return com.nexmo.voice.core.Core.getInstance().isDomesticRoutingFlag();
    }
}
