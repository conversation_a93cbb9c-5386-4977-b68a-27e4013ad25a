package com.nexmo.voice.core.jmx;

/**
 * <AUTHOR>
 */
public interface CachesJMXMBean {

    public boolean isEnabled();

    public void setEnabled(boolean value);

    public String[] getCaches();

    public String viewCachesConfig();

    public String viewCacheEntries(String cacheType);

    public String flushCache(String cacheType);

    public String updateCacheSize(String cacheType, int size);

    public String updateCacheExpiry(String cacheType, String period);

    public String updateCacheRefresh(String cacheType, String period);

    public String viewCacheMode(String cacheType);

    public String updateCacheMode(String cacheType, String modeType);

    public String lookupApplication(String id);
}
