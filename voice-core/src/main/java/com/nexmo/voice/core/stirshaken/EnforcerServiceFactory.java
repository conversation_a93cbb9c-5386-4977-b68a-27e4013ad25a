package com.nexmo.voice.core.stirshaken;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.types.VoiceProduct;

public final class EnforcerServiceFactory {

    private EnforcerServiceFactory() {

    }

    public static EnforcerService byProductClass(VoiceProduct productClass) {
        final EnforcerService enforcerService;
        switch (productClass) {
            case TTS:
                enforcerService = Core.getInstance().getTTSEnforcerService();
                break;
            case VERIFY:
                enforcerService = Core.getInstance().getVerifyEnforcerService();
                break;
            case SIP:
            case CALL_API:
            case VSPS:
                enforcerService = Core.getInstance().getSIPEnforcerService();
                break;
            case VBC:
                enforcerService = Core.getInstance().getVBCEnforcerService();
                break;
            default:
                throw new IllegalArgumentException(String.format("Unsupported productClass %s", productClass));
        }
        return enforcerService;
    }
}
