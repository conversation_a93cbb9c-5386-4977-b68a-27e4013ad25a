package com.nexmo.voice.core.logger;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.nexmo.voice.core.sip.event.CdrEventHandler;
import com.nexmo.voice.core.types.SIPCode;
import io.prometheus.client.Counter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.types.VoiceDirection;
import com.thepeachbeetle.common.util.DateUtil;

/**
 * This class route the rejected CDRs writing to the configured cdrs file.
 * Key-value CDRs should be written the XXX-rejected XXX=> sip or tts json
 * Json CDRs should be written to the XXX-json-rejected XXX=> sip or tts
 * 
 * <AUTHOR>
 *
 */

public class RejectedLoggerController extends SIPAppLoggerController {

    private final static Logger Log = LogManager.getLogger(RejectedLoggerController.class);
    private static final List<String> TOP_ERROR_CODES = Arrays.asList("404", "486", "487", "480", "403", "502", "408", "500", "402", "504", "501", "410", "505", "503");
    private static final String VONAGE_PREM = "vonage-prem";
    private static final List<String> VONAGE_GATEWAYS = Arrays.asList("vonage-prem", "vonage", "vonage-cnam");

    private static final Counter REJECTED_CDRS = Counter.build().name("rejected_cdr").labelNames("rejected").help(" ").register();
    private static final Counter REJECTED_CDRS_VONAGE_PREM = Counter.build().name("rejected_cdr_vonage_prem").labelNames("rejected_vonage_prem").help(" ").register();
    private static final Counter REJECTED_CDRS_VONAGE = Counter.build().name("rejected_cdr_vonage").labelNames("rejected_vonage").help(" ").register();
    private static final Counter REJECTED_CDRS_VONAGE_CNAM = Counter.build().name("rejected_cdr_vonage_cnam").labelNames("rejected_vonage_cnam").help(" ").register();

    private static final Counter REJECTED_CDRS_STATUS = Counter.build().name("rejected_cdr_status").labelNames("rejected_status").help(" ").register();
    private static final Counter REJECTED_CDRS_STATUS_VONAGE_PREM = Counter.build().name("rejected_cdr_status_vonage_prem").labelNames("vonage_prem_rejected_status").help(" ").register();


    public static final String[] RejectCDROrder = {
            "PRODUCT",
            "PRODUCT-CLASS",
            "PRODUCT-VERSION",
            "DIRECTION",
            "HOST",
            "ID",
            "LEG1-ID",
            "LEG2-ID",
            "SUPERHUB-ACC",
            "ACC",
            "FROM",
            "TO",
            "PREFIX-FROM",
            "PREFIX-TO",
            "FORCED_SENDER",
            "PREFIX-FORCED_SENDER",
            "SIP-DEST-ATTEMPT",
            "TEXT",
            "VOICE",
            "REROUTE-ADDRESS",
            "COUNTRY",
            "NET",
            "GW",
            "GWS",
            "GW_ATTEMPT",
            "CALL_RETRIES",
            "STATUS",
            "REASON",
            "REASON_DESC",
            "START",
            "END",
            "CALL_DATE",
            "ROUTING_SEQ",
            "BACKEND",
            "PDD",
            "OUTBOUND-GW",
            "REQUEST_IP",
            "CALL_ORIGIN",
            "CALL_TERMINATION",
            "CUSTOMER_DOMAIN",
            "PAYMENT-ENABLED-APP",
            "PAYMENT-ROUTE",
            "REGION",
            "TTS_XTRACE_ID",
            "CALL_BACK_URL",
            "CALL_BACK_METHOD",
            "CLIENT_REFERENCE",
            "REPEAT",
            "MACHINE_DETECTION_TYPE",
            "MACHINE_TIMEOUT",
            "LANGUAGE_NAME",
            "MB_STYLE",
            "CDR_UUID",
            "STIR_SHAKEN",
            "CARRIER_PLATFORM",
            "INTERNAL_FLAG",
            "CHARGEABLE",
            "SRTP",
            "SIP_TRANSPORT",
            "SOURCE_COUNTRY",
            "HANGUP_CAUSE",
            "RULE_ID",
            "BLOCKING_SUBSYSTEM",
            "ASTERISK_VERSION",
            "PRODUCT-PATH",
            "NUMBER_TYPE",
            "CALL_TYPE",
            "ROUTING_GROUP",
            "ROUTING_OA",
            "ROUTING_BIND_ID",
            "VPRICING-ENABLED",
            "EMERGENCY_CALL",
            "EMERGENCY_CALL_FAILOVER",
            "EMERGENCY_CALL_LOCATION_ID",
        };


    public RejectedLoggerController(String logDir, String pfx, CdrsConfig cdrsConfig) throws Exception {
        super(logDir, pfx, cdrsConfig);
    }

    public synchronized void logFailure(final VoiceContext ctx, final String status, final String errorCode,
            final String extraDesc, final String carrierCallId, final String idSuffix, final String retryGW,
            final int truncationLength, final CdrEventUserData eventUserData) {

        if (Log.isDebugEnabled())
            Log.debug("We are logging a REJECT CDR for sessionId: " + ctx.getSessionId() + " status: " + status
                    + " carrierCallId: " + carrierCallId + " idSuffix: " + idSuffix + " retryGW: " + retryGW
                    + " eventUserData: " + eventUserData + " product: " + ctx.getVoiceProduct().name());

        
        
        CDRData cdrData = buildCDRData(ctx, status, errorCode, extraDesc, carrierCallId, idSuffix, retryGW,
                truncationLength, eventUserData);
        
        logCDR(cdrData, ctx.getSessionId(), RejectCDROrder, "Finished logging the REJECTED CDR ");

        CompletableFuture.runAsync(() -> {
            REJECTED_CDRS.labels("success").inc();
        });

        if (ctx.getCurrentGateway() != null && VONAGE_GATEWAYS.contains(ctx.getCurrentGateway())) {
            if (ctx.getCurrentGateway().equals("vonage-prem"))
                CompletableFuture.runAsync(() -> {
                    REJECTED_CDRS_VONAGE_PREM.labels("success").inc();
                });
            if (ctx.getCurrentGateway().equals("vonage"))
                CompletableFuture.runAsync(() -> {
                    REJECTED_CDRS_VONAGE.labels("success").inc();
                });
            if (ctx.getCurrentGateway().equals("vonage-cnam"))
                CompletableFuture.runAsync(() -> {
                    REJECTED_CDRS_VONAGE_CNAM.labels("success").inc();
                });
        }

        if (ctx.getCurrentGateway() != null && errorCode != null && TOP_ERROR_CODES.contains(errorCode)) {
            CompletableFuture.runAsync(() -> {
                REJECTED_CDRS_STATUS.labels(errorCode).inc();
            });
            if (ctx.getCurrentGateway().equals(VONAGE_PREM)) {
                CompletableFuture.runAsync(() -> {
                    REJECTED_CDRS_STATUS_VONAGE_PREM.labels(errorCode).inc();
                });
            }
        }

    }

    protected CDRData buildCDRData(final VoiceContext ctx, final String status, final String errorCode,
            final String extraDesc, final String carrierCallId, final String idSuffix, final String retryGW,
            final int truncationLength, final CdrEventUserData eventUserData) {

        LinkedHashMap<String, String> cdrDetails = buildBaseCDRData(ctx, carrierCallId, idSuffix, truncationLength, eventUserData);
        
        //Override "rejected CDR" special values
        cdrDetails.put("LEG1-ID", "");
        cdrDetails.put("REROUTE-ADDRESS", "");
        cdrDetails.put("OUTBOUND-GW", "");
        if (Objects.nonNull(ctx.getApplicationContext()))
            ctx.getApplicationContext().populateParams(cdrDetails);

        if (Objects.nonNull(ctx.getTtsContext())) {
            ctx.getTtsContext().populateParams(cdrDetails);
        }

        //overwrite the earlier base cdr building which put cdrDetails.put("GW_ATTEMPT", "1#1")
        if ( !(Objects.nonNull(eventUserData) && Objects.nonNull(eventUserData.getCurrentGateway()))) 
          {
            if (ctx.getVoiceDirection() == VoiceDirection.OUTBOUND) {
                if ("NO_ROUTE".equals(status)) // If there is no route, there is no attempt
                    cdrDetails.remove("GW_ATTEMPT");
            }
        }
        
        //Special handling of the ID:
        //Original code:  sb.append("\"ID=").append(SipAppUtils.getNexmoUUID(ctx.getSessionId())).append("\",");
        String id = SipAppUtils.getNexmoUUID(ctx.getSessionId());
        cdrDetails.put("ID", id);

        cdrDetails.put("STATUS", status);
        cdrDetails.put("REASON", errorCode);
        cdrDetails.put("REASON_DESC", SIPAppLogger.handleQuote(extraDesc));

        //VOICEN-297 write final CDR for non-retryable failures such as user busy or callee not available.
        //if the reason code is not part of nonBlocking sip attempts we need to write this as final attempt
        try {
            SIPCode sipCode = SIPCode.getFromCode(Integer.parseInt(errorCode));
            if (!CdrEventHandler.nonBlockingSipAttemptCodes.contains(sipCode)) {
                if (status.equals(CdrEventHandler.DIAL_STATUS_CANCEL) ||
                        status.equals(CdrEventHandler.DIAL_STATUS_BUSY) ||
                        status.equals(CdrEventHandler.DIAL_STATUS_NO_ANSWER)) {
                    if (Objects.nonNull(eventUserData) && (eventUserData.getCurrentGWAttempt() < eventUserData.getAvailableGWAttempts())) {
                        // if we did not attempt all the available gws, we want to adjust the values we wrote for "GW_ATTEMPT"
                        // so reports api will treat this as final CDR.
                        cdrDetails.remove("GW_ATTEMPT");
                        String gwAttempts = eventUserData.getCurrentGWAttempt() + "#" + eventUserData.getCurrentGWAttempt();
                        cdrDetails.put("GW_ATTEMPT", gwAttempts);
                    }
                }
            }
        } catch (Exception e) {
            Log.warn("RejectedLoggerController exception " + e + " while calculating gw attempts for failures, not changing GW_ATTEMPT value");
        }

        long creationTime = ctx.getContextCreationDate();
        long startTime = ctx.getBillingInfo().getCallStartTime();
        if (startTime == 0)
            startTime = creationTime;
        long endTime = ctx.getBillingInfo().getCallEndTime();
        if (endTime == 0)
            endTime = startTime;

        String start = DateUtil.getMessageLogTimestampString(new Date(startTime));
        String end = DateUtil.getMessageLogTimestampString(new Date(endTime));
        String creationDate = DateUtil.getMessageLogTimestampString(new Date(creationTime));

        cdrDetails.put("START", start);
        cdrDetails.put("END", end);
        cdrDetails.put("CALL_DATE", creationDate);


        if (Objects.nonNull(eventUserData) && Objects.nonNull(eventUserData.getCurrentGateway()))
            cdrDetails.put("OUTBOUND-GW", eventUserData.getCurrentGateway());

        if (Objects.nonNull(ctx.getPaymentEnabled())) {
            cdrDetails.put("PAYMENT-ENABLED-APP", String.valueOf(ctx.getPaymentEnabled()));
        }
        if (Objects.nonNull(ctx.getPaymentRoute())) {
            cdrDetails.put("PAYMENT-ROUTE", ctx.getPaymentRoute());
        }
        if (Objects.nonNull(ctx.getRegion())) {
            cdrDetails.put("REGION", ctx.getRegion());
        }

        cdrDetails.put("CHARGEABLE", String.valueOf(ctx.isChargeable()));

        if (Objects.nonNull(ctx.getAsteriskVersion())) {
            cdrDetails.put("ASTERISK_VERSION", ctx.getAsteriskVersion().getCdrVersion());
        }

        CDRData cdrData = new CDRData(cdrDetails);

        return cdrData;
    }
    


}
