package com.nexmo.voice.core.monitoring.metrics;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.log4j.Logger;


public class GatewayMetrics {
    private static final Logger Log = Logger.getLogger(GatewayMetrics.class);

    private final String gatewayName;

    private long timeOfLastProcessing;
    private long timeOfLastProcessingSuccess;
    private long timeOfLastProcessingError;
    private long timeOfLastProcessingMachine;

    private final AtomicLong countOfMachineSinceLastSuccess = new AtomicLong(0L);
    private final AtomicLong countOfErrorsSinceLastSuccess = new AtomicLong(0L);
    private final AtomicLong countOfSuccessSinceLastFailure = new AtomicLong(0L);

    private final AtomicLong[] processingMinuteCount = new AtomicLong[15];
    private final AtomicLong[] processingSuccessMinuteCount = new AtomicLong[15];
    private final AtomicLong[] processingErrorMinuteCount = new AtomicLong[15];
    private final AtomicLong[] processingMachineMinuteCount = new AtomicLong[15];

    private int highestUsedMinuteCountSlot = 0;

    private AtomicLong processingInLastHour = new AtomicLong(0L);
    private AtomicLong processingSuccessInLastHour = new AtomicLong(0L);
    private AtomicLong processingErrorInLastHour = new AtomicLong(0L);
    private AtomicLong processingMachineInLastHour = new AtomicLong(0L);

    private AtomicLong processingToday = new AtomicLong(0L);
    private AtomicLong processingSuccessToday = new AtomicLong(0L);
    private AtomicLong processingErrorToday = new AtomicLong(0L);
    private AtomicLong processingMachineToday = new AtomicLong(0L);

    public GatewayMetrics(String gatewayName) {
        this.gatewayName = gatewayName;
        for (int i = 0; i < this.processingMinuteCount.length; i++) {
            this.processingMinuteCount[i] = new AtomicLong(0L);
            this.processingSuccessMinuteCount[i] = new AtomicLong(0L);
            this.processingErrorMinuteCount[i] = new AtomicLong(0L);
            this.processingMachineMinuteCount[i] = new AtomicLong(0L);
        }
    }

    public String getGatewayName() {
        return this.gatewayName;
    }

    public void logSuccess() {
        commonLogIncrements();
        this.timeOfLastProcessingSuccess = System.currentTimeMillis();
        this.processingSuccessInLastHour.incrementAndGet();
        this.processingSuccessToday.incrementAndGet();
        this.processingSuccessMinuteCount[0].incrementAndGet();

        this.countOfSuccessSinceLastFailure.incrementAndGet();
        this.countOfErrorsSinceLastSuccess.set(0);
        this.countOfMachineSinceLastSuccess.set(0);
    }

    public void logError() {
        commonLogIncrements();
        this.timeOfLastProcessingError = System.currentTimeMillis();
        this.processingErrorInLastHour.incrementAndGet();
        this.processingErrorToday.incrementAndGet();
        this.processingErrorMinuteCount[0].incrementAndGet();

        this.countOfErrorsSinceLastSuccess.incrementAndGet();
        this.countOfSuccessSinceLastFailure.set(0);
    }

    public void logMachine() {
        commonLogIncrements();
        this.timeOfLastProcessingMachine = System.currentTimeMillis();
        this.processingMachineInLastHour.incrementAndGet();
        this.processingMachineToday.incrementAndGet();
        this.processingMachineMinuteCount[0].incrementAndGet();

        this.countOfMachineSinceLastSuccess.incrementAndGet();
        this.countOfSuccessSinceLastFailure.set(0);
    }

    private void commonLogIncrements() {
        this.timeOfLastProcessing = System.currentTimeMillis();
        this.processingInLastHour.incrementAndGet();
        this.processingToday.incrementAndGet();
        this.processingMinuteCount[0].incrementAndGet();
    }

    public void doMinuteUpdates() {
        Log.debug("========== MONITORING -- MINUTE UPDATE .. GATEWAY [ " + this.gatewayName + " ] ");
        for (int i = this.processingMinuteCount.length - 1; i > 0; i--) {
            this.processingMinuteCount[i] = this.processingMinuteCount[i - 1];
            this.processingSuccessMinuteCount[i] = this.processingSuccessMinuteCount[i - 1];
            this.processingErrorMinuteCount[i] = this.processingErrorMinuteCount[i - 1];
            this.processingMachineMinuteCount[i] = this.processingMachineMinuteCount[i - 1];
        }
        this.processingMinuteCount[0] = new AtomicLong(0L);
        this.processingSuccessMinuteCount[0] = new AtomicLong(0L);
        this.processingMachineMinuteCount[0] = new AtomicLong(0L);
        this.processingErrorMinuteCount[0] = new AtomicLong(0L);

        if (this.highestUsedMinuteCountSlot < this.processingMinuteCount.length - 1)
            this.highestUsedMinuteCountSlot++;
    }

    public void doHourUpdates() {
        Log.debug("========== MONITORING -- HOUR UPDATE .. GATEWAY [ " + this.gatewayName + " ] ");
        this.processingInLastHour = new AtomicLong(0L);
        this.processingSuccessInLastHour = new AtomicLong(0L);
        this.processingErrorInLastHour = new AtomicLong(0L);
        this.processingMachineInLastHour = new AtomicLong(0L);
    }

    public void doDayUpdates() {
        Log.debug("========== MONITORING -- DAY UPDATE .. GATEWAY [ " + this.gatewayName + " ] ");
        this.processingToday = new AtomicLong(0L);
        this.processingSuccessToday = new AtomicLong(0L);
        this.processingErrorToday = new AtomicLong(0L);
        this.processingMachineToday = new AtomicLong(0L);
    }

    public Map<String, String> getMetricsOutputMap() {
        Map<String, String> map = new LinkedHashMap<>();

        map.put("GATEWAY", this.gatewayName);

        map.put("PROCESSING-1MIN", "" + getProcessingWithinLastMinute());
        map.put("PROCESSING-SUCCESS-1MIN", "" + getProcessingSuccessWithinLastMinute());
        map.put("PROCESSING-FAILURE-1MIN", "" + getProcessingErrorWithinLastMinute());
        map.put("PROCESSING-TIMEOUT-1MIN", "" + getProcessingMachineWithinLastMinute());

        map.put("PROCESSING-15MIN", "" + getProcessing15MinuteAverage());
        map.put("PROCESSING-SUCCESS-15MIN", "" + getProcessingSuccess15MinuteAverage());
        map.put("PROCESSING-FAILURE-15MIN", "" + getProcessingError15MinuteAverage());
        map.put("PROCESSING-TIMEOUT-15MIN", "" + getProcessingMachine15MinuteAverage());

        map.put("PROCESSING-HOUR", "" + getProcessingInLastHour());
        map.put("PROCESSING-SUCCESS-HOUR", "" + getProcessingSuccessInLastHour());
        map.put("PROCESSING-FAIURE-HOUR", "" + getProcessingErrorInLastHour());
        map.put("PROCESSING-TIMEOUT-HOUR", "" + getProcessingMachineInLastHour());

        map.put("PROCESSING-TODAY", "" + getProcessingToday());
        map.put("PROCESSING-SUCCESS-TODAY", "" + getProcessingSuccessToday());
        map.put("PROCESSING-FAILURE-TODAY", "" + getProcessingErrorToday());
        map.put("PROCESSING-TIMEOUT-TODAY", "" + getProcessingMachineToday());

        map.put("SINCE-LAST-PROCESSING", "" + (System.currentTimeMillis() - getTimeOfLastProcessing()));
        map.put("SINCE-LAST-PROCESSING-SUCCESS", "" + (System.currentTimeMillis() - getTimeOfLastProcessingSuccess()));
        map.put("SINCE-LAST-PROCESSING-FAILURE", "" + (System.currentTimeMillis() - getTimeOfLastProcessingError()));
        map.put("SINCE-LAST-PROCESSING-TIMEOUT", "" + (System.currentTimeMillis() - getTimeOfLastProcessingMachine()));

        map.put("COUNT-OF-FAILURE-SINCE-LAST-SUCCESS", "" + getCountOfFailureSinceLastSuccess());
        map.put("COUNT-OF-TIMEOUT-SINCE-LAST-SUCCESS", "" + getCountOfTimeoutSinceLastSuccess());
        map.put("COUNT-OF-SUCCESS-SINCE-LAST-FAILURE", "" + getCountOfSuccessSinceLastFailure());

        return map;
    }

    public long getProcessingWithinLastMinute() {
        return this.processingMinuteCount[0].get();
    }

    public long getProcessingSuccessWithinLastMinute() {
        return this.processingSuccessMinuteCount[0].get();
    }

    public long getProcessingMachineWithinLastMinute() {
        return this.processingMachineMinuteCount[0].get();
    }

    public long getProcessingErrorWithinLastMinute() {
        return this.processingErrorMinuteCount[0].get();
    }

    public long getProcessing15MinuteAverage() {
        long tot = 0;
        int slots = this.processingMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot)
            slots = this.highestUsedMinuteCountSlot + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingSuccess15MinuteAverage() {
        long tot = 0;
        int slots = this.processingSuccessMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot)
            slots = this.highestUsedMinuteCountSlot + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingSuccessMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingError15MinuteAverage() {
        long tot = 0;
        int slots = this.processingErrorMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot)
            slots = this.highestUsedMinuteCountSlot + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingErrorMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingMachine15MinuteAverage() {
        long tot = 0;
        int slots = this.processingMachineMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot)
            slots = this.highestUsedMinuteCountSlot + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingMachineMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingInLastHour() {
        return this.processingInLastHour.get();
    }

    public long getProcessingSuccessInLastHour() {
        return this.processingSuccessInLastHour.get();
    }

    public long getProcessingErrorInLastHour() {
        return this.processingErrorInLastHour.get();
    }

    public long getProcessingMachineInLastHour() {
        return this.processingMachineInLastHour.get();
    }

    public long getProcessingToday() {
        return this.processingToday.get();
    }

    public long getProcessingSuccessToday() {
        return this.processingSuccessToday.get();
    }

    public long getProcessingErrorToday() {
        return this.processingErrorToday.get();
    }

    public long getProcessingMachineToday() {
        return this.processingMachineToday.get();
    }

    public long getTimeOfLastProcessing() {
        return this.timeOfLastProcessing;
    }

    public long getTimeOfLastProcessingSuccess() {
        return this.timeOfLastProcessingSuccess;
    }

    public long getTimeOfLastProcessingError() {
        return this.timeOfLastProcessingError;
    }

    public long getTimeOfLastProcessingMachine() {
        return this.timeOfLastProcessingMachine;
    }

    public long getCountOfFailureSinceLastSuccess() {
        return this.countOfErrorsSinceLastSuccess.get();
    }

    public long getCountOfTimeoutSinceLastSuccess() {
        return this.countOfMachineSinceLastSuccess.get();
    }

    public long getCountOfSuccessSinceLastFailure() {
        return this.countOfSuccessSinceLastFailure.get();
    }
}
