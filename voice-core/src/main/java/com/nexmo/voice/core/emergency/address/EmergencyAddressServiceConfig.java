package com.nexmo.voice.core.emergency.address;

import com.nexmo.voice.core.emergency.EmergencyCallingLocaleConfig;
import org.jdom.Element;

import java.net.URI;
import java.net.URISyntaxException;

public class EmergencyAddressServiceConfig {

    public static final String ROOT_NODE = "emergency-address-service";
    public static final String ATTR_PRIMARY_SERVICE_URL = "primary-url";
    public static final String ATTR_PRIMARY_USE_BEARER_TOKEN = "primary-use-bearer-token";
    public static final String ATTR_SECONDARY_SERVICE_URL = "secondary-url";
    public static final String ATTR_SECONDARY_USE_BEARER_TOKEN = "secondary-use-bearer-token";
    public static final String ATTR_SERVICE_TIMEOUT = "timeout";

    public static final String PRIMARY_USE_BEARER_TOKEN_DEFAULT_VALUE = "false";
    public static final String SECONDARY_USE_BEARER_TOKEN_DEFAULT_VALUE = "false";
    public static final String SERVICE_TIMEOUT_DEFAULT_VALUE = "2000";

    private URI primaryServiceUrl;

    private URI secondaryServiceUrl;

    private int serviceTimeout;

    private boolean primaryUseBearerToken;

    private boolean secondaryUseBearerToken;

    public EmergencyAddressServiceConfig() {}

    public URI getPrimaryServiceUrl() {
        return primaryServiceUrl;
    }

    public void setPrimaryServiceUrl(URI serviceUrl) {
        this.primaryServiceUrl = serviceUrl;
    }

    public void setPrimaryServiceUrl(String serviceUrl) throws EmergencyAddressServiceException {
        try {
            setPrimaryServiceUrl(new URI(serviceUrl));
        } catch (URISyntaxException e) {
            throw new EmergencyAddressServiceException("Could not parse service URL: "+e.getMessage(), e);
        }
    }

    public URI getSecondaryServiceUrl() {
        return secondaryServiceUrl;
    }

    public void setSecondaryServiceUrl(URI serviceUrl) {
        this.secondaryServiceUrl = serviceUrl;
    }

    public void setSecondaryServiceUrl(String serviceUrl) throws EmergencyAddressServiceException {
        try {
            setSecondaryServiceUrl(new URI(serviceUrl));
        } catch (URISyntaxException e) {
            throw new EmergencyAddressServiceException("Could not parse service URL: "+e.getMessage(), e);
        }
    }

    public int getServiceTimeout() {
        return serviceTimeout;
    }

    public void setServiceTimeout(int serviceTimeout) {
        this.serviceTimeout = serviceTimeout;
    }

    public boolean isPrimaryUseBearerToken() {
        return this.primaryUseBearerToken;
    }

    public void setPrimaryUseBearerToken(boolean useBearerToken) {
        this.primaryUseBearerToken = useBearerToken;
    }

    public boolean isSecondaryUseBearerToken() {
        return this.secondaryUseBearerToken;
    }

    public void setSecondaryUseBearerToken(boolean useBearerToken) {
        this.secondaryUseBearerToken = useBearerToken;
    }

    @Override
    public String toString() {
        return "EmergencyAddressServiceConfig{" +
                "primaryServiceUrl=" + primaryServiceUrl +
                ", secondaryServiceUrl=" + secondaryServiceUrl +
                ", serviceTimeout=" + serviceTimeout +
                ", primaryUseBearerToken=" + primaryUseBearerToken +
                ", secondaryUseBearerToken=" + secondaryUseBearerToken +
                '}';
    }

    public Element toXML() {
        Element config = new Element(ROOT_NODE);
        config.setAttribute(ATTR_PRIMARY_SERVICE_URL, String.valueOf(this.primaryServiceUrl));
        config.setAttribute(ATTR_PRIMARY_USE_BEARER_TOKEN, Boolean.toString(this.primaryUseBearerToken));
        config.setAttribute(ATTR_SERVICE_TIMEOUT, Integer.toString(this.serviceTimeout));
        config.setAttribute(ATTR_SECONDARY_SERVICE_URL, String.valueOf(secondaryServiceUrl));
        config.setAttribute(ATTR_SECONDARY_USE_BEARER_TOKEN, Boolean.toString(this.secondaryUseBearerToken));
        return config;
    }
}
