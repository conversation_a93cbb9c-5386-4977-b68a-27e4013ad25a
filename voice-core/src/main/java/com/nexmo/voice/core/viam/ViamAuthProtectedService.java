package com.nexmo.voice.core.viam;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.AuthCache;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.auth.BasicScheme;
import org.apache.http.impl.client.BasicAuthCache;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;

public abstract class ViamAuthProtectedService {


    private static final Logger LOG = LogManager.getLogger(ViamAuthProtectedService.class);

    private ViamAuthConfig viamAuthConfig;

    private HttpClient hydraClient;

    private HttpClient portunusClient;

    private HttpClientContext context;

    private final String ACCESS_TOKEN_GRANT_TYPE_KEY = "grant_type";
    private final String ACCESS_TOKEN_GRANT_TYPE_VALUE_CLIENT_CREDENTIALS = "client_credentials";

    private final String ACCESS_TOKEN_SCOPE_KEY = "scope";

    private final String ACCESS_TOKEN_AUDIENCE_KEY = "audience";

    private static final Histogram HYDRA_SERVICE_REQUESTS_LATENCY = Histogram.build()
            .buckets(DEFAULT_DURATION_BUCKETS)
            .name("sipapp_viam_hydra_service_requests_latency")
            .help("Http Requests Latency to VIAM Hydra Service")
            .labelNames("status")
            .register();

    private static final Counter HYDRA_SERVICE_ERRORS = Counter.build()
            .name("sipapp_viam_hydra_service_error")
            .labelNames("reason")
            .help("VIAM Hydra Service error count").register();


    private static final Histogram PORTUNUS_SERVICE_REQUESTS_LATENCY = Histogram.build()
            .buckets(DEFAULT_DURATION_BUCKETS)
            .name("sipapp_viam_portunus_service_requests_latency")
            .help("Http Requests Latency to VIAM Portunus Service")
            .labelNames("status")
            .register();

    private static final Counter PORTUNUS_SERVICE_ERRORS = Counter.build()
            .name("sipapp_viam_portunus_service_error")
            .labelNames("reason")
            .help("VIAM Portunus Service error count").register();

    public ViamAuthProtectedService(ViamAuthConfig viamAuthConfig) {
        this.viamAuthConfig = viamAuthConfig;
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(
                new AuthScope(AuthScope.ANY),
                new UsernamePasswordCredentials(viamAuthConfig.getClientId(), viamAuthConfig.getClientSecret())
        );
        AuthCache authCache = new BasicAuthCache();
        authCache.put(
                new HttpHost(viamAuthConfig.getPrimaryHydraServiceUrl().getHost(), viamAuthConfig.getPrimaryHydraServiceUrl().getPort(), viamAuthConfig.getPrimaryHydraServiceUrl().getScheme()),
                new BasicScheme()
        );
        if(viamAuthConfig.getSecondaryHydraServiceUrl() != null) {
            authCache.put(
                    new HttpHost(viamAuthConfig.getSecondaryHydraServiceUrl().getHost(), viamAuthConfig.getSecondaryHydraServiceUrl().getPort(), viamAuthConfig.getSecondaryHydraServiceUrl().getScheme()),
                    new BasicScheme()
            );
        }
        context = HttpClientContext.create();
        context.setCredentialsProvider(credentialsProvider);
        context.setAuthCache(authCache);
        this.hydraClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(
                        RequestConfig.custom()
                        .setConnectTimeout(viamAuthConfig.getHydraServiceTimeout())
                        .setConnectionRequestTimeout(viamAuthConfig.getHydraServiceTimeout())
                        .setSocketTimeout(viamAuthConfig.getHydraServiceTimeout())
                        .build()
                )
                .setDefaultCredentialsProvider(credentialsProvider)
                .build();
        this.portunusClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(
                        RequestConfig.custom()
                                .setConnectTimeout(viamAuthConfig.getPortunusServiceTimeout())
                                .setConnectionRequestTimeout(viamAuthConfig.getPortunusServiceTimeout())
                                .setSocketTimeout(viamAuthConfig.getPortunusServiceTimeout())
                                .build()
                )
                .build();
    }

    protected ViamAccessTokenResponse getAccessToken(ViamScope scope, ViamAudience audience) throws ViamAuthenticationException {
        try {
            return doGetAccessToken(scope, audience, this.viamAuthConfig.getPrimaryHydraServiceUrl());
        } catch(ViamAuthenticationException e) {
            LOG.warn("Exception from primary hydra service: "+e.getMessage());
            if(this.viamAuthConfig.getSecondaryHydraServiceUrl() == null) {
                LOG.error("No secondary hydra service URL");
                throw e;
            }
        }
        return doGetAccessToken(scope, audience, this.viamAuthConfig.getSecondaryHydraServiceUrl());
    }
    private ViamAccessTokenResponse doGetAccessToken(ViamScope scope, ViamAudience audience, URI serviceUrl) throws ViamAuthenticationException {
        LOG.debug("Getting access token for scope="+scope+", audience="+audience+", hydraServiceUrl="+serviceUrl);
        HttpPost post = new HttpPost(serviceUrl);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(ACCESS_TOKEN_GRANT_TYPE_KEY, ACCESS_TOKEN_GRANT_TYPE_VALUE_CLIENT_CREDENTIALS));
        params.add(new BasicNameValuePair(ACCESS_TOKEN_SCOPE_KEY, scope.build()));
        params.add(new BasicNameValuePair(ACCESS_TOKEN_AUDIENCE_KEY, audience.value()));
        try {
            post.setEntity(new UrlEncodedFormEntity(params));
        } catch (UnsupportedEncodingException e) {
            LOG.error("Could not encode URL parameters for hydra service: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not encode URL parameters for hydra service", e);
        }

        HttpResponse response = null;
        long httpRequestStartTime = System.nanoTime();

        try {
            LOG.debug("post="+post+", entity="+post.getEntity());
            response = this.hydraClient.execute(post, this.context);
        } catch (IOException e) {
            HYDRA_SERVICE_ERRORS.labels("io_error").inc();
            LOG.error("Could not execute HTTP POST to hydra service: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not execute HTTP POST to hydra service", e);
        }
        LOG.debug("response="+response);

        if ((response == null) || (response.getStatusLine() == null)) {
            HYDRA_SERVICE_ERRORS.labels("unreadable").inc();
            LOG.error("Could not read hydra service response");
            throw new ViamAuthenticationException("Could not read hydra service response");
        }

        HYDRA_SERVICE_REQUESTS_LATENCY
                .labels(Integer.toString(response.getStatusLine().getStatusCode()))
                .observe(System.nanoTime() - httpRequestStartTime);

        if(response.getStatusLine().getStatusCode() != 200) {
            LOG.error("Hydra service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
            throw new ViamAuthenticationException("Hydra service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(response.getEntity().getContent(), ViamAccessTokenResponse.class);
        } catch(IOException e) {
            LOG.error("Could not parse hydra service response body: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not parse hydra service response body", e);
        }
    }

    protected String getExtendedToken(ViamAccessTokenResponse accessToken, String apiKey) throws ViamAuthenticationException {
        try {
            return doGetExtendedToken(accessToken, apiKey, this.viamAuthConfig.getPrimaryPortunusServiceUrl());
        } catch(ViamAuthenticationException e) {
            LOG.warn("Exception from primary portunus service: "+e.getMessage());
            if(this.viamAuthConfig.getSecondaryPortunusServiceUrl() == null) {
                LOG.error("No secondary portunus service URL");
                throw e;
            }
        }
        return doGetExtendedToken(accessToken, apiKey, this.viamAuthConfig.getSecondaryPortunusServiceUrl());
    }

    private String doGetExtendedToken(ViamAccessTokenResponse accessToken, String apiKey, URI serviceUrl) throws ViamAuthenticationException {
        LOG.debug("Getting extended token for apiKey="+apiKey+", portunusServiceUrl="+serviceUrl);

        URI extTokenUrl = null;

        try {
            extTokenUrl= new URIBuilder(serviceUrl)
                    .addParameter("identifier", "API_KEY,"+apiKey)
                    .build();
        } catch (URISyntaxException e) {
            LOG.error("Could not encode URL parameters for portunus service: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not encode URL parameters for portunus service", e);
        }

        HttpGet get = new HttpGet(extTokenUrl);
        get.setHeader(HttpHeaders.AUTHORIZATION, accessToken.getTokenType() + " " + accessToken.getAccessToken());

        HttpResponse response = null;
        long httpRequestStartTime = System.nanoTime();
        try {
            response = this.portunusClient.execute(get, this.context);
        } catch(IOException e) {
            PORTUNUS_SERVICE_ERRORS.labels("io_error").inc();
            LOG.error("Could not execute HTTP GET to portunus service: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not execute HTTP GET to portunus service", e);
        }

        if ((response == null) || (response.getStatusLine() == null)) {
            PORTUNUS_SERVICE_ERRORS.labels("unreadable").inc();
            LOG.error("Could not read portunus service response");
            throw new ViamAuthenticationException("Could not read portunus service response");
        }

        PORTUNUS_SERVICE_REQUESTS_LATENCY
                .labels(Integer.toString(response.getStatusLine().getStatusCode()))
                .observe(System.nanoTime() - httpRequestStartTime);

        if(response.getStatusLine().getStatusCode() != 200) {
            LOG.error("Portunus service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
            throw new ViamAuthenticationException("Portunus service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
        }

        ViamExtendedTokenResponse extendedToken = null;

        try {
            ObjectMapper mapper = new ObjectMapper();
            extendedToken = mapper.readValue(response.getEntity().getContent(), ViamExtendedTokenResponse.class);
        } catch(IOException e) {
            LOG.error("Could not parse portunus service response body: "+e.getMessage(), e);
            throw new ViamAuthenticationException("Could not parse portunus service response body", e);
        }

        if((extendedToken != null) && extendedToken.getAccessToken() != null) {
            return extendedToken.getAccessToken();
        }
        LOG.error("Portunus service response did not contain token");
        throw new ViamAuthenticationException("Portunus service response did not contain token");
    }



}
