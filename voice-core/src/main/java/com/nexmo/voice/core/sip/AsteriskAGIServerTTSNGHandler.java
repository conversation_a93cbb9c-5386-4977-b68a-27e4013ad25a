package com.nexmo.voice.core.sip;

/***
 *  This class is a enhanced version of the original AsteriskAGIServer.
 *  It is handling the SIP AGI requests for the SIP and CALL_API VoiceProducts.
 *
 *   *  
 */

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.nexmo.voice.core.callblocking.CallBlockingResponse;
import com.nexmo.voice.core.callblocking.CallBlockingServiceClient;
import com.nexmo.voice.core.routing.RouteData;
import com.nexmo.voice.core.routing.RouteNullPointerException;
import com.nexmo.voice.core.stirshaken.Attestation;
import com.nexmo.voice.core.stirshaken.AttestationValidationParams;
import com.nexmo.voice.core.stirshaken.EnforcerServiceFactory;
import com.nexmo.voice.core.types.*;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;
import io.prometheus.client.Counter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.fastagi.AgiChannel;
import org.asteriskjava.fastagi.AgiException;
import org.asteriskjava.fastagi.AgiRequest;
import com.nexmo.voice.core.billing.vquota.VQuotaService;

import com.nexmo.common.api.********.exceptions.ServiceException;
import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.ChargingConfig.CountrySpecificInfo;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.cache.AGIRequestContext;
import com.nexmo.voice.core.cache.TTSNGBillingInstructions;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContext.Builder;
import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.routing.VoiceMTRouter;
import com.thepeachbeetle.common.callback.types.CallbackMethod;
import com.thepeachbeetle.hlr.staticprefixmap.SimpleHlrNetworkPrefixMapLookup.Network;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodes;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import com.thepeachbeetle.messaging.hub.core.Product;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.exceptions.DropMessageException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RandomPoolNoMatchSoRejectException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RoutingException;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import com.thepeachbeetle.messaging.hub.core.exceptions.UnroutableMTException;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.routing.GenericMTRouter.Route;
import com.thepeachbeetle.********.networks.client.NetworksClient;

import static com.nexmo.voice.core.sip.AsteriskAGIServer.ASTERISK_VERSION;
import static com.nexmo.voice.core.sip.AsteriskAGIServer.getChannelVariable;
import static com.nexmo.voice.core.sip.AsteriskAGIServer.*;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import static com.nexmo.voice.core.SipAppUtils.getMappedNumberType;
import com.nexmo.voice.core.billing.QuotaUpdateDetails;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;

public class AsteriskAGIServerTTSNGHandler {

    private static final Logger Log = LogManager.getLogger(AsteriskAGIServerTTSNGHandler.class);
    private static final String BLOCKED = "block";


    private static final String VOICE_PRODUCT = "voice";
    private static final Counter TTS_CALLBLOCKING_REASON = Counter.build().name("tts_callblocking_reason").labelNames("reason_value").help(" ").register();
    private static final Counter TTS_CALLBLOCKING_RESPONSE = Counter.build().name("tts_callblocking_response").labelNames("response_value").help(" ").register();
    private static final int INITIAL_RETRY_COUNT = 0;

    // General notes and explanations:
    //
    // The current design of the SIPApp is based on creating a VoiceContext for each leg of the call.
    // The first leg context is created during the AGI Request. (i.e. here)
    // The second leg context is created during the BridgeEvent.
    // On the call completion, during the CDR Event, SIPApp decide which CDR to
    // create based on the nature of the call completion

    // It is important to notice that any call have two legs - these legs are on the sterisk level.
    // Each VoiceContext include also charging context.
    // When it is an outbound call, the leg which is really charging is the second
    // leg - it is created during the BridgeEvent.

    // As of the Voxeo removal project, this class is now handling both TTSNG-Verify calls and
    // legacy-tts calls which are handled by MB.

    // The content of the TTSNG-verify request was approved earlier in the process, in the TTSNG system.
    // i.e. the account is not banned and the destination number is a valid number.
    // Verify always send the "skip-permitted-destinations=true / bypass_permitted_destination=1" -
    // so for verify we do not check anything about the destination number.

    // The content of the legacy-tts request was also approved earlier in the legacy tts,
    // so we know the account is not banned and have enough funds. - In any case, those details are checked again on the Bridge event level.
    protected static void handleRequest(AgiRequest request, AgiChannel channel, String accountId, String nexmoUUID,
                                        String productClassOverride, VoiceProduct requestedVoiceProduct, String legacyTTSCallDetails)
            throws AgiException, ServiceException {

        Log.info("Received TTSNG AGI request: {} {} ", request.getRequestURL(), AsteriskAGIServer.getAGIReqParamsAndVars(request, channel));

        // Extract the parameters from the request ...
        HashMap<String, String> inputParams = normalizedRequestParams(nexmoUUID, request, channel);

        String direction = inputParams.get(AsteriskAGIServer.DIRECTION);
        String extension = inputParams.get(AsteriskAGIServer.EXTENSION); // This is the TTSNG "to"
        String uniqueId = inputParams.get(AsteriskAGIServer.UNIQUE_ID);
        String callerId = inputParams.get(AsteriskAGIServer.CALLER_ID); // This is the TTSNG "from"
        String channelId = inputParams.get(AsteriskAGIServer.CHANNEL_ID);
        String clientCallId = inputParams.get(AsteriskAGIServer.CLIENT_CALL_ID);
        String callOrigin = inputParams.get(AsteriskAGIServer.ORIGIN);

        String sessionId = SipAppUtils.getSessionId(nexmoUUID, 0);

        boolean isInbound = AsteriskAGIServer.INBOUND_DIRECTION.equalsIgnoreCase(direction);

        // OUTBOUND calls might be terminated by pstn, sip destination or VAPI-to-VBC.
        // For TTSNG-Verify and legacy-tts, at the moment, it is only pstn.
        String callTermination = "pstn";
        boolean isVAPIOutboundToVBC = false;

        if(Core.getInstance().isShutdownInProgress()){
            Log.warn("{} Shutdown is in progress this call will be rejected", sessionId);
            rejectCallInShutdown(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                    clientCallId, null, callOrigin, requestedVoiceProduct, accountId, isVAPIOutboundToVBC,
                    "Shutdown In Progress", requestedVoiceProduct, isInbound ? VoiceDirection.INBOUND : VoiceDirection.OUTBOUND);
            return;
        }

        Log.info(
                "TTSNG AGI request: sessionId: {} accountId: {} isInbound: {} callOrigin: {} callTermination: {} productClassHeader: {}  voiceProduct:{}",
                sessionId, accountId, isInbound, callOrigin, callTermination, productClassOverride,
                requestedVoiceProduct.name());

        // While parsing the incoming details, and fetching additional information about the call we build the voiceContext.
        // The voiceContext is also required in error cases, as it is used to handle the later CDREvent of the cancellation.
        VoiceContext.Builder voiceCtxBuilder = generateContextBuilder(inputParams, accountId, requestedVoiceProduct,
                sessionId, callTermination, isVAPIOutboundToVBC);

        voiceCtxBuilder.withClientCallId(clientCallId);

        // Verify and TTS supports only OUTBOUND - otherwise reject the call
        if (isInbound &&
                (VoiceProduct.VERIFY.equals(requestedVoiceProduct) || VoiceProduct.TTS.equals(requestedVoiceProduct))) {
            Log.warn(
                    "{} support only OUTBOUND direction. - rejecting the call. nexmoUUID= {} direction = {}.  About to set APP_REASON to {}",
                    requestedVoiceProduct, nexmoUUID, direction,
                    String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR,
                    String.format("%s supports only OUTBOUND direction.", requestedVoiceProduct));
        }

        //Add a safety barriers:
        //TTS calls can use only the legacyTTSCallDetails
        //Verify calls can use only the ttsngBillingInstructions
        TTSNGBillingInstructions ttsngBillingInstructions = null;
        if (VoiceProduct.TTS.equals(requestedVoiceProduct)) {
            if (Objects.nonNull(legacyTTSCallDetails)) {
                parseLegacyTTSCallDetails(sessionId, voiceCtxBuilder, inputParams, channel, legacyTTSCallDetails);
            }
        } else if (VoiceProduct.VERIFY.equals(requestedVoiceProduct)) {
            ttsngBillingInstructions = TTSNGBillingInstructions.parseBillingInstructions(sessionId, inputParams);
        }

        SmppAccount account = getAccount(accountId, sessionId, voiceCtxBuilder, inputParams, channel);
        // We found the account: add some more details to the builder:
        voiceCtxBuilder.withAccountPricingGroup(account.getQuotaPricingGroupId());
        voiceCtxBuilder.withMasterAccountId(account.getMasterAccountId());
        voiceCtxBuilder.withMasterAccountPricingGroup(SipAppUtils.getAccountPricingGroup(account.getMasterAccountId(), sessionId));

        final boolean isVpricingEnabled = VQuotaService.isVpricingEnabled(account);
        voiceCtxBuilder.withVpricingEnabled(isVpricingEnabled);

        // "extension" is the requested destination. Verify its validity
        verifyExtension(sessionId, extension, ttsngBillingInstructions, account, voiceCtxBuilder, inputParams, channel, requestedVoiceProduct);
        if (Log.isDebugEnabled())
            Log.debug("{}: requested destination {} is valid to use by account {} ", sessionId, extension, account.getSysId());

        // "from" is the value as asked by the sender. It later might changed to "Unknown".
        // "forcedSender" is fetched from the Randompool
        // "from" and "forceSender" are kept separately, and both included in the CDR.
        // On the end of the AGI request processing we return back to Asterisk which value to use as the caller:
        // if "forceSender" has value it will be used. If it is null, the "from" value will be used.


        // For backward compatibility: replace all spaces with _
        String cleanedCallerId = Objects.isNull(callerId) ? null : callerId.replaceAll(" ", "_");
        boolean isCallerIdE164 = AsteriskAGIServer.isValidNumber(cleanedCallerId);
        boolean isAllowedAnyCallerIdValue = AsteriskAGIServer.isAllowedAnyCallerIdValue(account, extension);
        boolean isOwnedLvn = false;
        boolean isVerifiedCallerId = false;

        ShortCode scode = getShortCodeForNumber(cleanedCallerId);
        String supplierId = (Objects.nonNull(scode) && Objects.nonNull(scode.getSupplierId())) ? scode.getSupplierId() : "";
        if (AsteriskAGIServer.isOwnedLvn(account, scode, cleanedCallerId, sessionId)) {
            Log.info("{} Account {} owns lvn {} supplierId {}", sessionId, account.getSysId(), callerId, supplierId);
            isOwnedLvn = true;
        }
        if (Objects.nonNull(scode) && scode.getShortCodeType() == ShortCodeType.VERIFIED_CLI) {
            isVerifiedCallerId = true;
        }

        String from = getFromValue(account, cleanedCallerId, isOwnedLvn, isAllowedAnyCallerIdValue, sessionId);
        if (Log.isDebugEnabled())
            Log.debug("{}: account {} will be using '{}' as 'from' (callerId)", sessionId, account, from);

        voiceCtxBuilder.withFrom(from);

        // Routing... Find the route for the requested extension (the call destination)
        Network network = getNetwork(extension, sessionId, voiceCtxBuilder, inputParams, channel);
        String outboundNetworkType = SipAppUtils.getNetworkType(network);
        String outboundNetworkName = network.getName();
        String outboundMccMnc = network.getMccMnc();
        String outboundCountryCode = network.getCountryCode();
        if (Log.isDebugEnabled())
            Log.debug("sessionId: {} using network {}", sessionId, network.toString());

        String sourceCountryCode = getSourceCountryCode(sessionId, callerId);
        
        CallBlockingResponse ttsCallblockingResponse = getTTSCallBlockingResponse(sessionId, nexmoUUID, callerId, extension, voiceCtxBuilder, account, channel, inputParams);
        if (ttsCallblockingResponse != null) {
            String action = ttsCallblockingResponse.getAction();
            String id = ttsCallblockingResponse.getId();
            voiceCtxBuilder.withId(id)
                .withBlockingSubsystem(ttsCallblockingResponse.getSubsystem());
            CompletableFuture.runAsync(() -> {
                TTS_CALLBLOCKING_REASON.labels(action).inc();
            });
            if (BLOCKED.equalsIgnoreCase(action))
                return;
        }

        Route route = getRoute(sessionId, account, from, supplierId, extension, requestedVoiceProduct, outboundMccMnc,
                voiceCtxBuilder, inputParams, channel);

        final String outboundGatewayId = route.getGatewayId();
        final String outboundAltGateways = createOutboundAltGateways(outboundGatewayId, getAlternativeGateways(route));
        Long routingRuleSeqNo = Objects.nonNull(route.getRule()) ? route.getRule().getSeq() : null;
        String routingGroup = Objects.nonNull(route.getRule()) ? route.getRule().getRoutingGroupId() : null;
        String routingOa = Objects.nonNull(route.getRule()) ? route.getRule().getOa() : null;
        String routingBindId = Objects.nonNull(route.getRule()) ? route.getRule().getBindId() : null;

        String forceSender = route.getForcedSender();
        if (Log.isDebugEnabled())
            Log.debug("{} : extension {} outboundGatewayId {} outboundAltGateways {} forceSender {} ", sessionId,
                    extension, outboundGatewayId, outboundAltGateways, forceSender);

        // CLI to use for Caller ID ("From:"), P-Asserted-Identity and source-prefix pricing
        String cli = from;
        ShortCode initialScode = scode;
        if (Objects.nonNull(forceSender)) {
            cli = forceSender;
            sourceCountryCode = getSourceCountryCode(sessionId, forceSender);
            scode = getShortCodeForNumber(forceSender);
        }

        // PREQ_4786, PREQ-4911, PREQ-5039
        // numberType, callType are for PSTN calls
        String numberType = null;
        String callType = null;
        if(!AsteriskAGIServer.DEFAULT_GATEWAY.equals(outboundGatewayId)) {
            callType = SipAppUtils.determineCallType(sourceCountryCode, outboundCountryCode);
            if(Objects.nonNull(scode)) {
                numberType = getMappedNumberType(scode.getShortCodeType());
            }

            if(numberType == null && (isAllowedAnyCallerIdValue || Objects.nonNull(forceSender))) {
                numberType = SipAppUtils.NumberType.CLIUnverified.name();
            }

            if(numberType == null) {
                numberType = SipAppUtils.NumberType.CLIMissing.name();
            }
        }
        scode = initialScode; // write back the initial scode

        // ApplicationContext is a holder for some details about the call. There is such one per leg.
        //
        // It is not so perfect at the moment, but this will be the place to set the "forced price" for Verify calls
        // Later the logic will decide where to use this price.
        // Specifically for Verify, this forced price is for the second leg, but this information is arriving during
        // the AGI request, so need to be kept in an accessible place for the second leg.
        // In generic implementation, we might force price on the first leg as well.

        // For the first leg, auxiliary (!isInbound) is actually the indicator if this
        // an inbound call or outbound call
        SIPAsteriskContext applicationContext = new SIPAsteriskContext(channelId,
                clientCallId,
                null, // For OUTBOUND it is null. For INBOUND it would be the destination that the lvn is configured to
                outboundGatewayId,
                !isInbound,
                0, // remainingAttempts
                null); // applicationId is not relevant for outbound calls

        // Add routing information to the builder
        voiceCtxBuilder.withGateway(outboundGatewayId)
                .withAlternativeGateways(new LinkedHashSet<String>(Arrays.asList(outboundAltGateways.split(","))))
                .withNetwork(outboundMccMnc).withNetworkType(outboundNetworkType).withNetworkName(outboundNetworkName)
                .withCountryCode(outboundCountryCode).withSequenceNumber(routingRuleSeqNo).withForcedSender(forceSender)
                .withRoutingGroup(routingGroup).withRoutingOa(routingOa).withRoutingBindId(routingBindId)
                .withApplicationContext(applicationContext)
                .withCallType(callType)
                .withNumberType(numberType);

        // Add sourceCountryCode
        voiceCtxBuilder.withSourceCountryCode(sourceCountryCode);

        // Price
        // The price is needed for the balance check prior to starting the call.
        // We will need this price again when we create the second leg which is actually
        // handling the charging for the outbound call.

        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig(); // Can be null

        // Fetch the official price as configured for this customer
        ChargingConfig chargingConfig = getChargingConfig(sessionId, voiceCtxBuilder, inputParams, channel);

        final CountrySpecificInfo specificInfo = chargingConfig.getCountrySpecificInfo(outboundCountryCode);
        long minimumIncrement = specificInfo.getMinIncrement();
        long recurringIncrement = specificInfo.getRecurringIncrement();

        PriceMatrixList outboundPriceMatrix = getOutboundPricesMatrix(sessionId, voiceCtxBuilder, inputParams, channel);

        EffectivePrice matrixPrice = null; // Used for passing forward to the BridgeEvent process
        matrixPrice = EffectivePrice.getPriceFromMatrixOrDefault(outboundPriceMatrix,
                                                                 account,
                                                                 extension,
                                                                 cli, // from or forceSender
                                                                 null,
                                                                 outboundMccMnc, // no REQUESTED-appId in case of outbound calls
                                                                 chargingConfig.getTTSNGMinPrice(),
                                                                 isVAPIOutboundToVBC,
                                                                 prefixMapConfig,
                                                                 chargingConfig,
                                                                 requestedVoiceProduct,
                                                                 "outbound-price-for-balance-check");

        final boolean voiceSkipQuota = AsteriskAGIServer.isVoiceSkipQuota(account);

        // For Verify requests only: check whether forcedPrice is requested and whether the provided value is valid.
        EffectivePrice agiReqForcedPrice = null;
        if (VoiceProduct.VERIFY.equals(requestedVoiceProduct) && Objects.nonNull(ttsngBillingInstructions)) {
            agiReqForcedPrice = getForcedPrice(ttsngBillingInstructions.getRequestedForcedPrice(),
                                                    requestedVoiceProduct,
                                                    sessionId,
                                                    voiceCtxBuilder,
                                                    inputParams,
                                                    channel,
                                                    isVpricingEnabled,
                                                    voiceSkipQuota);
        }

        EffectivePrice actualOutboundPrice = null; // Used for verifying available balance
        if (Objects.nonNull(agiReqForcedPrice)) {
            Log.info("{}: leg1: outboundPrice is forced by request to: {} ", sessionId, agiReqForcedPrice);
            actualOutboundPrice = agiReqForcedPrice;
        } else {
            Log.info("{}: leg1: outboundPrice is taken from the price matrix: {} ", sessionId, matrixPrice);
            actualOutboundPrice = matrixPrice;
        }

        // Inbound price of leg1 of OUTBOUND call is always zero.
        EffectivePrice inboundPrice = new EffectivePrice(BigDecimal.ZERO, "zero-inbound-price");

        if (Log.isDebugEnabled())
            Log.debug("{}: leg1: Account: {} outboundPrice: {} inboundPrice: {} ", sessionId, accountId, actualOutboundPrice, inboundPrice);

        if(voiceSkipQuota && Log.isDebugEnabled()){
            Log.debug("sessionId {}, accountId {} Balance Check will be skipped as this was marked as voice skip quota", sessionId, accountId);
        }

        if(!voiceSkipQuota) {
            // Balance check ...
            EffectivePrice quotaOverwritePrice = checkForBalanceRestrictions(account,
                    actualOutboundPrice.getPrice().add(inboundPrice.getPrice()),
                    2 * minimumIncrement,
                    sessionId,
                    voiceCtxBuilder,
                    inputParams,
                    channel,
                    agiReqForcedPrice,
                    uniqueId,
                    actualOutboundPrice.getPrefix(),
                    isVpricingEnabled);
            // The balance check response has a "feature" of also overwriting the price to zero...
            // This is done to allow some smoke tests and other production-test users to set calls even when their balance is zero.
            // I think it is risky - Deniz is looking to cancel this and find a new way to allow this to test users.
            if (Objects.nonNull(quotaOverwritePrice)) {
                Log.warn("!!!! {} account {} has forcedPrice to zero due to QuotaDisabled !!!!", sessionId, accountId);
                agiReqForcedPrice = quotaOverwritePrice; // Yak yak yak
            }
        }

        // COST - This is the the first leg of an OUTBOUND TTSNG call.
        // the COST of this leg is always taken from the configured default (which is zero)
        EffectiveCost inboundUnitCost = new EffectiveCost(chargingConfig.getDefaultInboundCost(), "default-inbound-cost");

        // Populate the builder with the first leg pricing info
        voiceCtxBuilder.withPricePerMinute(inboundPrice.getPrice())
                       .withPricePrefix(inboundPrice.getPrefix())
                       .withPricePrefixGroup(inboundPrice.getPrefixGroup())
                       .withPriceSenderPrefix(inboundPrice.getSenderPrefix())
                       .withFirstChargedSeconds(minimumIncrement)
                       .withQuotaUpdatesInterval(recurringIncrement)
                       .withCostPerMinute(inboundUnitCost.getCost())
                       .withCostPrefix(inboundUnitCost.getPrefix())
                       .withCostPrefixGroup(inboundUnitCost.getPrefixGroup())
                       .withCostSenderPrefix(inboundUnitCost.getSenderPrefix())
                       .withForcedPrice(null) // The forced price is for the charging leg (the outbound leg)
                       .withVoiceSkipQuota(voiceSkipQuota)
                       .withAsteriskVersion(AsteriskVersion.from(sessionId, getChannelVariable(sessionId, ASTERISK_VERSION, channel)))
                       .withChargeable(false); // chargeable is for the charging leg (the outbound leg in this case)

        //send if BYON is used
        AttestationValidationParams validationParam = AttestationValidationParams.builder()
                .withAccountId(account.getSysId())
                .withHasKyc(AsteriskAGIServer.hasKyc(account))
                .withHasDisableMustOwnLVN(isAllowedAnyCallerIdValue)
                .withIsCallerIdE164(isCallerIdE164)
                .withIsOwnedLvn(isOwnedLvn)
                .withDestinationCountry(outboundCountryCode)
                .withGatewayName(outboundGatewayId)
                .withForcedSender(forceSender)
                .withSessionId(sessionId)
                .withIsBYON(isVerifiedCallerId)
                .build();
        Log.info("{} Calculating  stir shaken: voiceProduct = {}, params = {}", sessionId, requestedVoiceProduct, validationParam);
        Attestation attestationLevel = EnforcerServiceFactory.byProductClass(requestedVoiceProduct).attestationLevel(validationParam);
        Log.info("{} Stir Shaken is {}", sessionId, attestationLevel);
        voiceCtxBuilder.withStirShaken(attestationLevel.getRepresentation());

        // At this point, the AGI request was processed successfully
        // Prepare the details to pass to the BridgeEvent and CDREvent processing

        boolean shouldSendCostInCallback = false;
        if (VoiceProduct.VERIFY.equals(requestedVoiceProduct) && Objects.nonNull(ttsngBillingInstructions)) {
            shouldSendCostInCallback = ttsngBillingInstructions.isCostRequiredInCallback();
        }

        AGIRequestContext agiRequestContext = new AGIRequestContext(shouldSendCostInCallback,
                                                                    agiReqForcedPrice,
                                                                    matrixPrice);
        if (Log.isDebugEnabled())
            Log.debug("{} : About to use {}", sessionId, agiRequestContext);

        voiceCtxBuilder.withAGIRequestContext(agiRequestContext);
        voiceCtxBuilder.withAlternativeGateways(new LinkedHashSet<>(Arrays.asList(outboundAltGateways.split(","))));

        VoiceContext voiceContext = voiceCtxBuilder.build();
        voiceContext.setSessionId(sessionId);
        voiceContext.setConnectionId(uniqueId);

        // Store the context in cache
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        cache.storeContext(sessionId, uniqueId, voiceContext);
        
        if (Log.isDebugEnabled())
            Log.debug("{} : On the end of the TTS AGI request, first leg context is: {}", sessionId, voiceContext.getDebugString());


        // PDD - SIP-375 was created, the PDD calculator should be refactored.
        // I think we get the PDD as part of the BridgeEvent, and there is no need to
        // calculate it
        setPDDStart(uniqueId);

        int callTimeout = AsteriskAGIServer.getCallTimeout(account);
        if (callTimeout > 0) {
            channel.setVariable(AsteriskAGIServer.SIP_ABSOLUTE_TIMEOUT, String.valueOf(callTimeout));
        }

        // Set channel's returned values
        channel.setVariable(AsteriskAGIServer.CALLERID_NAME, cli);
        channel.setVariable(AsteriskAGIServer.CALLERID_NUM, cli);
        channel.setVariable(AsteriskAGIServer.GW, outboundGatewayId);
        channel.setVariable(AsteriskAGIServer.GWS, outboundAltGateways);
        channel.setVariable(AsteriskAGIServer.ATTESTATION_LEVEL, attestationLevel.getRepresentation());

        Log.info("End of AGIServer TT request handle for SESSION-ID: {}", sessionId);
    }

    private static CallBlockingResponse getTTSCallBlockingResponse(String sessionId,
                                                                   String nexmoUUID,
                                                                   String callerId,
                                                                   String outboundDestination,
                                                                   VoiceContext.Builder voiceCtxBuilder,
                                                                   SmppAccount account,
                                                                   AgiChannel channel, HashMap<String, String> inputParams) {
        long reqTime = 0;
        CallBlockingResponse ttsCallblockingResponse = null;
        try {
            CallBlockingServiceClient callBlockingServiceClient = new CallBlockingServiceClient(Core.getInstance().getConfig().getCallblockingServiceConfig());
            reqTime = System.currentTimeMillis();
            ttsCallblockingResponse = callBlockingServiceClient.getCallBlockingRules(account.getSysId(), VOICE_PRODUCT, callerId, outboundDestination, nexmoUUID, INITIAL_RETRY_COUNT, System.nanoTime());
            if (ttsCallblockingResponse != null) {
                String action = ttsCallblockingResponse.getAction();
                String id = ttsCallblockingResponse.getId();
                if (BLOCKED.equalsIgnoreCase(action)) {
                    voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_CALLID_ENFORCER); // set flag for reason we blocked
                    SIPAsteriskContext errorAppContext = new SIPAsteriskContext(inputParams.get(AsteriskAGIServer.CHANNEL_ID),
                            inputParams.get(AsteriskAGIServer.CLIENT_CALL_ID),
                            null,
                            null,
                            false,
                            0);

                    voiceCtxBuilder.withApplicationContext(errorAppContext)
                            .withPricePerMinute(BigDecimal.ZERO)
                            .withFirstChargedSeconds(0)
                            .withQuotaUpdatesInterval(0)
                            .withCostPerMinute(BigDecimal.ZERO)
                            .withInitialChargingStatus(BillingInfo.Status.ERROR)
                            .withId(id)
                            .withBlockingSubsystem(ttsCallblockingResponse.getSubsystem())
                            .withInternalFlag(CallInternalFlag.BLOCKED_ON_CALLID_ENFORCER); // set flag for reason we blocked;

                    VoiceContext context = voiceCtxBuilder.build();
                    context.setSessionId(sessionId);
                    context.setConnectionId(inputParams.get(AsteriskAGIServer.UNIQUE_ID));

                    VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
                    cache.storeContext(sessionId, inputParams.get(AsteriskAGIServer.UNIQUE_ID), context);

                    if (Log.isDebugEnabled())
                        Log.debug("{} : Building and storing ERROR context {} ", sessionId, context.getDebugString());

                    channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.FORBIDDEN.getCode()));
                }

            } else {
                Log.warn("Allowing call with nexmoUUID {} and with response time {} to proceed, though an error occurred while invoking callblocking api", nexmoUUID, System.currentTimeMillis() - reqTime);
            }
        } catch (Exception e) {
            Log.warn("Generic exception while finding blocking rule for product voice:{} and response time is: {}", e.getMessage(), System.currentTimeMillis() - reqTime);
            if (e.getMessage().contains("HTTP 401")) {
                CompletableFuture.runAsync(() -> {
                    TTS_CALLBLOCKING_RESPONSE.labels("authentication_failure").inc();
                });
            } else {
                CompletableFuture.runAsync(() -> {
                    TTS_CALLBLOCKING_RESPONSE.labels("generic_failure").inc();
                });
            }
        }
        return ttsCallblockingResponse;
    }

    private static void parseLegacyTTSCallDetails(String sessionId, Builder voiceCtxBuilder,
                                                  HashMap<String, String> inputParams, AgiChannel channel, String legacyTTSCallDetails) throws AgiException {

        try {
            TTSContext ttsContext = TTSContext.parse(sessionId, legacyTTSCallDetails);
            voiceCtxBuilder.withCallbackUrl(ttsContext.callbackUrl)
                           .withCallbackMethod(ttsContext.callbackMethod)
                           .withTTSContext(ttsContext);
        } catch (Exception ex) {
            Log.error("{} : failed to parse {}", sessionId, legacyTTSCallDetails, ex);
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to deserialize TTS call details");
        }
    }

    // For SIPApp, the extension should be white-listed or validated by the PhoneNumberTool using the google lib.
    // For TTS-NG the extension should be defined in the account's permitted destinations list.
    // This verification might be skipped, if the skip-permitted-destinations is provided in the TTSNGBillingInstructions
    // The below section is for TTSNG-Verify. When enhancing this code to be used by both
    // SIP and TTSNG, merge the two verification methods
    private static boolean verifyExtension(String sessionId, String extension,
                                           TTSNGBillingInstructions ttsngBillingInstructions, SmppAccount account, Builder voiceCtxBuilder,
                                           HashMap<String, String> inputParams, AgiChannel channel, VoiceProduct requestedVoiceProduct) throws AgiException {

        // Verification of country code is required
        if (isPrefixGroupNumber(extension)) {
            Log.error("{} : requested extension {} is not permitted. bypassPermittedDest={}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, extension, ttsngBillingInstructions.isBypassPermittedDestinationVerification(), String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INVALID_DATA);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Destination in Vonagistan");
            return false; // Verification failed
        }

        //Bypass permitted destinations verification is allowed only for Verify requests.
        if (VoiceProduct.VERIFY.equals(requestedVoiceProduct) &&
                Objects.nonNull(ttsngBillingInstructions) &&
                ttsngBillingInstructions.isBypassPermittedDestinationVerification()) {
            if (Log.isDebugEnabled())
                Log.debug("{} : bypass permitted destination verification for extension {} ", sessionId, extension);
            return true; // Skip verification
        }

        // Verification of permitted destinations is required
        if (!isAllowedByPermittedDestinationList(account, extension)) {
            Log.error("{} : requested extension {} is not permitted. bypassPermittedDest={}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, extension, ttsngBillingInstructions.isBypassPermittedDestinationVerification(), String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INVALID_DATA);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Destination Not in Permitted List");
            return false; // Verification failed
        }
        if (Log.isDebugEnabled())
            Log.debug("{} : extension {} is included in account {} permitted destinations list", sessionId, extension, account.getSysId());

        return true;
    }

    // Notice: The old TTS use a different MTRouter than the SIPApp.
    // The implementation below is specific for TTSNG-Verify and legacy-tts backward compatibility.
    // The reason it is using a different MTRouter is:
    // Old TTS has its own implementation about the RandomPool cache which is different to the provided implementation
    // in messaging.jar
    // In the future, that should be further investigated and be a generic solution or all products.
    private static Route getRoute(String sessionId, SmppAccount account, String from, String supplierId, String extension,
                                  VoiceProduct requestedVoiceProduct, String outboundMccMnc, Builder voiceCtxBuilder,
                                  HashMap<String, String> inputParams, AgiChannel channel) throws AgiException, ServiceException {

        Route route = null;

        VoiceMTRouter ttsMTRouter = Core.getInstance().getMtRouter();
        if (Objects.isNull(ttsMTRouter)) {
            Log.error("{} : Failed to load the ttsMTRouter for {}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, extension, String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Failed to load the ttsMTRouter");
        }

        RouteData supplierIdRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                .withExc(new RouteNullPointerException("Initialize supplierId Route null")).
                build();

        if (!VoiceProduct.VERIFY.equals(requestedVoiceProduct)) {
            supplierIdRouteData = lookupRoute(sessionId, account, supplierId, extension, requestedVoiceProduct, outboundMccMnc);
        }

        RouteData oaPrefixRouteData = lookupRoute(sessionId, account, from, extension, requestedVoiceProduct, outboundMccMnc);
        RouteData routeData = AsteriskAGIServer.decideRoute(supplierIdRouteData, oaPrefixRouteData);

        if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL) {
            Exception e = routeData.getExc();
            Log.warn("{} : Failed to find a route for account: {} from: {} to: {}  network: {}, due to {} . Call is rejected. About to set APP_REASON to {}",
                    sessionId, account.getSysId(), from, extension, outboundMccMnc, e.getMessage(), String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Route not found");
        } else if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.DROPMESSAGE) {
            Log.info("{} : Route for account: {} from: {} to: {}  network: {} is configurred to drop the call . Call is rejected. About to set APP_REASON to {}",
                    sessionId, account.getSysId(), from, extension, outboundMccMnc, String.valueOf(SIPCode.FORBIDDEN.getCode()));
            DropMessageException e = (DropMessageException) routeData.getExc();
            long droppingRoutingRuleSeq= e.getRoutingRuleSequence();
            voiceCtxBuilder.withSequenceNumber(droppingRoutingRuleSeq);
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_DROPPING_ROUTE);
            voiceCtxBuilder.withNetwork(outboundMccMnc);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Route not found", false);
        } else if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTENULL) {
            Exception e = routeData.getExc();
            Log.info("{} No Route NullPointer Failed to find a route for account: {} from: {} to: {}  network: {}, due to {} . Call is rejected. About to set APP_REASON to {}",
                    sessionId, account.getSysId(), from, extension, outboundMccMnc, e.getMessage(), String.valueOf(SIPCode.FORBIDDEN.getCode()));

            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            voiceCtxBuilder.withNetwork(outboundMccMnc);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Route not found");
        }
        return routeData.getRoute();
    }

    private static RouteData lookupRoute(String sessionId, SmppAccount account, String from, String extension, VoiceProduct requestedVoiceProduct, String outboundMccMnc) {
        VoiceMTRouter ttsMTRouter = Core.getInstance().getMtRouter();

        try {
            Route route  = ttsMTRouter.lookupRoute(Product.PRODUCT_VOICE_CALL,
                    requestedVoiceProduct.getDescriptor(),
                    account,
                    from,
                    extension,
                    outboundMccMnc,
                    null);// parameters map

            Log.info("{}: Route found for account: {} from: {} to: {}  network: {}  will be using route: {}",
                    sessionId, account.getSysId(), from, extension, outboundMccMnc, route);

            if (Objects.isNull(route)) {
                Log.info("{} sessionId, route is null", sessionId);
                return new RouteData.Builder()
                        .withRoute(null)
                        .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                        .withExc(new RouteNullPointerException("Failed to find a route for outbound destination:" + extension))
                        .build();
            }

            MtRoutingRule rule = route.getRule();
            if (Objects.isNull(rule)) {
                Log.info("{} sessionId, rule is null", sessionId);
                return new RouteData.Builder()
                        .withRoute(null)
                        .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                        .withExc(new RouteNullPointerException("Failed to find a route for outbound destination:" + extension))
                        .build();
            }

            return new RouteData.Builder()
                    .withRoute(route)
                    .withExceptionType(RouteData.RouteExceptionType.NONE)
                    .withExc(null).build();

        } catch (RoutingException | UnroutableMTException | RandomPoolNoMatchSoRejectException e) {
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL)
                    .withExc(e).build();
        } catch (DropMessageException e) {
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.DROPMESSAGE)
                    .withExc(e).build();

        } catch (NullPointerException e) {
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                    .withExc(e).build();


        }
    }

    private static String getFromValue(SmppAccount account, String callerId, boolean isOwnedLvn, boolean isAllowedAnyCallerIdValue, String sessionId) {
        // 1. The provided callerId parameter is the requested "from".
        // 2. If the customer has the capability "disable-must-own-lvn" i.e isAllowedAnyCallerIdValue = true - it means the customer is allowed to put in the callerId any value.
        //       - If that is the case, the "from" will be the requested callerId.
        // 3. If the customer must use his LVN in the "from", we verify if the provided callerId is indeed his LVN
        //    3.1 If the provided callerId is the customer's LVN: it will be used.
        //    3.2 If the account is sub-account, and the master-account owns the LVN (in the "from") : it will be used
        //    3.3 If not 3.1 and not 3.2 the "from" is set to "Unknown"
        //

        if (Log.isDebugEnabled())
            Log.debug("{}: About to check if callerId {} is valid for account {} and find the suitable value, isOwnedLvn {}, isAllowedAnyCallerIdValue {}",
                    sessionId, callerId, account.getSysId(), isOwnedLvn, isAllowedAnyCallerIdValue);


        // If the account is allowed to use any callerId, just make sure it is not null
        if (isAllowedAnyCallerIdValue) {
            return Objects.isNull(callerId) ? AsteriskAGIServer.UNKNOWN_CALLER : callerId;
        }

        if (isOwnedLvn) {
            return callerId;
        }

        // The callerId cannot be used, account and his master-account do not own the requested LVN
        Log.warn("{}: Account {} and his master-account do not own the requested lvn {} - it will be ignored",
                sessionId, account.getSysId(), callerId);
        return AsteriskAGIServer.UNKNOWN_CALLER;
    }

    private static EffectivePrice getForcedPrice(String forcedPriceStr, VoiceProduct requestedVoiceProduct,
                                                 String sessionId, Builder voiceCtxBuilder, HashMap<String, String> inputParams, AgiChannel channel,
                                                 boolean isVpricingEnabled, boolean voiceSkipQuota)
            throws AgiException {

        EffectivePrice agiReqForcedPrice = null;
        if (Objects.isNull(forcedPriceStr) || forcedPriceStr.isEmpty())
            return null; // Forced price was not requested

        if (Log.isDebugEnabled())
            Log.debug("{}: About to get the forcedPrice of {} and product {} ", sessionId, forcedPriceStr, requestedVoiceProduct.name());

        if (!VoiceProduct.VERIFY.equals(requestedVoiceProduct)) {
            Log.error("{} Attempt to force price for NON-VERIFY product {}. call is rejected. About to set APP_REASON to  {}",
                    sessionId, requestedVoiceProduct.name(), String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));

            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Unauthorized attempt to use forcedPrice");
        }

        BigDecimal forcedPrice = null;
        try {
            forcedPrice = new BigDecimal(forcedPriceStr);
        } catch (Exception e) {
            Log.error("{} Invalid forced price was requested {}. call is rejected. About to set APP_REASON to  {}",
                    sessionId, forcedPriceStr, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INVALID_DATA);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Invalid force price was requested");
        }

        if (BigDecimal.ZERO.compareTo(forcedPrice) > 0) {
            Log.error("{} Forced price cannot be negative {}. call is rejected. About to set APP_REASON to  {}",
                    sessionId, forcedPriceStr, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INVALID_DATA);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Negative forced price was requested");
        }

        if (isVpricingEnabled && (voiceSkipQuota || BigDecimal.ZERO.compareTo(forcedPrice) < 0)) {
            Log.info("Session ID: {} - Ignoring forced price as vPricing is enabled and either forced price is greater than 0 or voice-skip-quota is enabled.", sessionId);
            return null; // Ignore forced price greater than 0 or if voice-skip-quota is enabled when vPricing is enabled
        }

        agiReqForcedPrice = new EffectivePrice(forcedPrice, "forced-price");
        if (Log.isDebugEnabled())
            Log.debug("{}: agiReqForcedPrice is: {} ", sessionId, agiReqForcedPrice);

        return agiReqForcedPrice;
    }

    private static Network getNetwork(String extension, String sessionId, Builder voiceCtxBuilder,
                                      HashMap<String, String> inputParams, AgiChannel channel) throws AgiException {
        if (Log.isDebugEnabled())
            Log.debug("{}: About to get the network for {} ", sessionId, extension);

        NetworksClient networksClient = Core.getInstance().getNetworksClient();
        if (Objects.isNull(networksClient)) {
            Log.error("{} : Failed to load the networksClient of {}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, extension, String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Failed to load the networksClient");
        }

        Network network = networksClient.lookupNetwork(extension);
        if (Objects.isNull(network)) {
            Log.error("{} : Failed to find the network for {}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, extension, String.valueOf(SIPCode.FORBIDDEN.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.FORBIDDEN, "Failed to find the network");
        } else {
            Log.info("{} : Network: {} ", sessionId, network.getName());
        }

        return network;
    }

    private static PriceMatrixList getOutboundPricesMatrix(String sessionId, Builder voiceCtxBuilder,
                                                           HashMap<String, String> inputParams, AgiChannel channel) throws AgiException {

        PriceMatrixList outboundPriceMatrix = Core.getInstance().getConfig().getMtPriceMatrixList();
        if (Objects.isNull(outboundPriceMatrix)) {
            Log.error("{} : Failed to allocate the outboundPriceMatrix. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to find outbound price matrix");
        }

        return outboundPriceMatrix;
    }

    private static ChargingConfig getChargingConfig(String sessionId, Builder voiceCtxBuilder,
                                                    HashMap<String, String> inputParams, AgiChannel channel) throws AgiException {

        ChargingConfig chargingConfig = Core.getInstance().getConfig().getChargingConfig();
        if (Objects.isNull(chargingConfig)) {
            Log.error("{} : Failed to find the charging config. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to find charging config");
        }

        return chargingConfig;
    }


    private static ShortCode getShortCodeForNumber(String number) {
        if (Log.isDebugEnabled())
            Log.debug("About to look for shortcode for number {} ", number);
        number = number.replace("+", "");

        ShortCodes shortCodes = Core.getInstance().getConfig().getShortCodes();

        if (shortCodes == null) {
            Log.info("Could not check if number {} belongs to ShortCodes. ShortCodes is not initialized!", number);
            return null;
        }

        final long timer = System.nanoTime();
        ShortCode shortCode = null;
        try {
            shortCode = shortCodes.getFirstShortCodeMatchForNumber(number);
            if (Log.isDebugEnabled())
                Log.debug("shortcode {} found for number {} ", shortCode, number);
        } catch (ShortCodeException ex) {
            Log.info("Failed to retrieve shortcode for {} due to {}", number, ex.getMessage());
            Core.getInstance().incrementShortCodesDBErrorCounter(ex);
        }
        Core.getInstance().updateShortCodesDBLatencyMetrics((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        return shortCode;
    }

    // This method role to verify if the account has enough balance to start the call
    // At the moment it is based on the messaging.jar quotaClient which either return the accountBalance object,
    // or throw one of several potential exceptions.
    // Some of the exceptions implies that there is a problem, other will mark not-enough-balance or skip-balance-check
    // In the case of skip-balance-check which is applied to my understanding only to test accounts, we set the price to zero
    // and this is used as forcedPrice overwritting any other forcedPrice.
    private static EffectivePrice checkForBalanceRestrictions(SmppAccount account, BigDecimal pricePerMinute,
                                                              long secondsToCharge, String sessionId, Builder voiceCtxBuilder, HashMap<String, String> inputParams,
                                                              AgiChannel channel, EffectivePrice agiReqForcedPrice, String uniqueId, String pricePrefix, boolean isVpricingEnabled) throws AgiException {

        if (Log.isDebugEnabled())
            Log.debug("{} about to check balance restrictions during AGI request, for account {} pricePerMinute {} secondsToCharge {}",
                    sessionId, account.getSysId(), pricePerMinute.toPlainString(), String.valueOf(secondsToCharge));

        QuotaClient quotaClient = Core.getInstance().getQuotaClient();
        BigDecimal pricePerSecond = pricePerMinute.divide(BigDecimal.valueOf(60), 10, RoundingMode.HALF_UP);
        BigDecimal minBalanceRequired = pricePerSecond.multiply(BigDecimal.valueOf(secondsToCharge)).setScale(8, RoundingMode.HALF_UP);

        AccountBalance balance = null;
        BigDecimal quotaDisabledPrice = null;
        EffectivePrice quotaOverwritePrice = null;
        try {
            if(isVpricingEnabled) {
                VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
                if (Objects.nonNull(agiReqForcedPrice) && agiReqForcedPrice.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                    balance = vQuotaService.getCurrentBalance(account.getSysId(), agiReqForcedPrice.getPrice(), sessionId);
                } else {
                    // If the forced price is not provided, we will use BSS vQuota price-impact API to check balance restrictions.
                    // Temporarily create voiceContext to check balance restrictions using BSS vQuota price-impact API.
                    // This API requires call info to rate the call price for the given duration.
                    VoiceContext vctx = voiceCtxBuilder.build();
                    vctx.setConnectionId(uniqueId);
                    vctx.setSessionId(sessionId);

                    // isVoiceSkipQuota function check to determine if the call is chargeable has already been performed before this function call.
                    boolean isChargeable = !SipAppUtils.isNotChargeablePricePrefix(pricePrefix);
                    vctx.setChargeable(isChargeable);
                    PriceImpactApiSuccessResponse resp = vQuotaService.invokePriceImpactApi(vctx, secondsToCharge, QuotaUpdateDetails.Operation.QUERY);
                    if (resp != null) {
                        balance = resp.getAccountBalance();
                    }
                }
            } else {
                balance = quotaClient.checkAccountBalance(account.getSysId(), minBalanceRequired);
            }
            // This is the real thing; the customer does not have the required balance
            if (balance != null && !balance.isRequiredFreeBalanceAvailable()) {
                // Check if we can ignore this - available only in the testing systems, just so we will block the call on the BridgeEvent
                if (!AsteriskAGIServer.shouldIgnoreMissingBalance(sessionId, account.getSysId(), inputParams.get(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q))) {
                    Log.info("{} : Account {} hasn't enough balance to start the call. About to set APP_REASON to {}",
                            sessionId, account.getSysId(), String.valueOf(SIPCode.PAYMENT_REQUIRED.getCode()));
                    voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);
                    handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.PAYMENT_REQUIRED, "Not enough balance");
                } else {
                    Log.warn("{} {} ignoring the missing balance on the AGI stage. Will be checked again on the BridgeEvent. THIS IS ALLOWED ONLY IN TEST ENV",
                            sessionId, account.getSysId());
                }
            }
            if (balance != null && !balance.isQuotaEnabled()) {
                // This is suspicious.....
                Log.warn("{} Quota is disabled for account {}. Setting forced price to 0.", sessionId, account.getSysId());
                quotaDisabledPrice = BigDecimal.ZERO;
                voiceCtxBuilder.withInternalFlag(CallInternalFlag.ACCOUNT_HAS_QUOTA_DISABLED);
            }
        } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException e) {
            if (Core.getInstance().isAsyncQuotaFlag()) {
                Log.warn("{} : AsyncQuota exception: Failed to check the account balance due to {}. Call will be continued.",
                        sessionId, e.getMessage());
                voiceCtxBuilder.withInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
            } else {
                Log.error("{} : Quota exception: Failed to check the account balabce due to {}. Call cannot be handled. About to set APP_REASON to {}",
                        sessionId, e.getMessage(), String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
                voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_QUOTA_ISSUES);
                handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to check the account balance");
            }
        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException e) {
            Log.error("{} : Account issues: Failed to check the account balabce due to {}. Call cannot be handled. About to set APP_REASON to {}",
                    sessionId, e.getMessage(), String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to check the account balance");
        } catch (QuotaDisabledException e) {
            // This is also suspicious.....
            Log.warn("{} : QuotaDisabled exception for account {}. Setting forced price to 0.", sessionId, account.getSysId());
            quotaDisabledPrice = BigDecimal.ZERO;
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.ACCOUNT_HAS_QUOTA_DISABLED);
        } catch (NotEnoughBalanceException e) {
            // Check if we can ignore this - available only in the testing systems, just so we will block the call on the BridgeEvent
            if (!AsteriskAGIServer.shouldIgnoreMissingBalance(sessionId, account.getSysId(), inputParams.get(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q))) {
                Log.info("{} : Account {} hasn't enough balance to start the call. About to set APP_REASON to {}",
                        sessionId, account.getSysId(), String.valueOf(SIPCode.PAYMENT_REQUIRED.getCode()));
                voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);
                handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.PAYMENT_REQUIRED, "Not enough balance");
            } else {
                Log.warn("{} {} ignoring the missing balance on the AGI stage. Will be checked again on the BridgeEvent. THIS IS ALLOWED ONLY IN TEST ENV",
                        sessionId, account.getSysId());
            }
        }

        if (Objects.nonNull(quotaDisabledPrice))
            quotaOverwritePrice = new EffectivePrice(quotaDisabledPrice, "special-quota-price");

        return quotaOverwritePrice;
    }

    private static void setPDDStart(String uniqueId) {
        try {
            Core.getInstance().getPddCalculator().storeStartTime(uniqueId, System.currentTimeMillis());
        } catch (PDDCalculationException e) {
            Log.warn("PDD can't be calculated for the call with unique id {} due to {}", uniqueId, e.getMessage());
        }
    }

    private static SmppAccount getAccount(String accountId, String sessionId, Builder voiceCtxBuilder,
                                          HashMap<String, String> inputParams, AgiChannel channel) throws AgiException {

        SmppAccount account = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
            if (Log.isDebugEnabled())
                Log.debug("Account found for accountId {} sessionId {}", accountId, sessionId);
        } catch (AccountsException e) {
            Log.error("{} Failed to retrieve account {} due to {}.  About to set APP_REASON to {} ",
                    sessionId, accountId, e.getMessage(), String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Failed to located the requested account");
        }

        if (Objects.isNull(account)) {
            Log.error("{} Account {} not found. About to set APP_REASON to {}", sessionId, accountId, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            handleError(sessionId, voiceCtxBuilder, inputParams, channel, SIPCode.SERVER_INTERNAL_ERROR, "Account not found");
        }
        return account;
    }

    private static boolean isAllowedByPermittedDestinationList(SmppAccount account, String extension) {
        if (account.getPermittedDestinations() == null)
            return true; // No list, allow all destinations

        boolean includedInList = account.getPermittedDestinations().stream().anyMatch(x -> extension.startsWith(x));
        if (includedInList) {
            return true;
        } else {
            return false;
        }
    }

    private static boolean isPrefixGroupNumber(String to) {
        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig();
        if (prefixMapConfig != null) {
            if ((to != null) && to.startsWith(prefixMapConfig.getCountryCode()))
                return true;
            else
                return false;
        } else {
            return false;
        }
    }

    private static void handleError(String sessionId, Builder voiceCtxBuilder, HashMap<String, String> inputParams,
                                    AgiChannel channel, SIPCode sipCode, String message) throws AgiException {

        handleError(sessionId, voiceCtxBuilder, inputParams, channel, sipCode, message, true);
    }

    private static void handleError(String sessionId, Builder voiceCtxBuilder, HashMap<String, String> inputParams,
                                    AgiChannel channel, SIPCode sipCode, String message, boolean isReallyAnError) throws AgiException {

        // If a route is configured to drop a call messaging.jar throws an exception.
        // Logging it creates redundant noise in the error log, hence this amazing "if". 
        //I also want to take advantage of it and mark the rejections due to DROPPING routing rules
        if (isReallyAnError) {
            Log.error("{}:  Internal error during the AGI request processing. Generating error voiceContext  for leg {} with SIPCode {}",
                    sessionId, inputParams.get(AsteriskAGIServer.UNIQUE_ID), sipCode.name());
        } else {
            Log.info("{}:  Internal rejection [{}] during the AGI request processing. Generating error voiceContext  for leg {} with SIPCode {}",
                    sessionId, message, inputParams.get(AsteriskAGIServer.UNIQUE_ID), sipCode.name());
        }

        // Create the error voiceContext for the later CDREvent
        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(inputParams.get(AsteriskAGIServer.CHANNEL_ID),
                inputParams.get(AsteriskAGIServer.CLIENT_CALL_ID),
                null,
                null,
                false,
                0);

        voiceCtxBuilder.withApplicationContext(errorAppContext)
                .withPricePerMinute(BigDecimal.ZERO)
                .withFirstChargedSeconds(0)
                .withQuotaUpdatesInterval(0)
                .withCostPerMinute(BigDecimal.ZERO)
                .withInitialChargingStatus(BillingInfo.Status.ERROR);

        VoiceContext context = voiceCtxBuilder.build();
        context.setSessionId(sessionId);
        context.setConnectionId(inputParams.get(AsteriskAGIServer.UNIQUE_ID));

        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        cache.storeContext(sessionId, inputParams.get(AsteriskAGIServer.UNIQUE_ID), context);

        if (Log.isDebugEnabled())
            Log.debug("{} : Building and storing ERROR context {} ", sessionId, context.getDebugString());

        channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(sipCode.getCode()));
        throw new AgiException(message);
    }

    private static HashMap<String, String> normalizedRequestParams(String nexmoUUID, AgiRequest request, AgiChannel channel) {
        Map<String, String[]> requestParams = request.getParameterMap();

        HashMap<String, String> normalizedParams = new HashMap<String, String>();

        normalizedParams.put(AsteriskAGIServer.DIRECTION, getRequestParamValue(requestParams, AsteriskAGIServer.DIRECTION));
        normalizedParams.put(AsteriskAGIServer.EXTENSION, getRequestParamValue(requestParams, AsteriskAGIServer.EXTENSION)); // This is the TTSNG "to"
        normalizedParams.put(AsteriskAGIServer.UNIQUE_ID, getRequestParamValue(requestParams, AsteriskAGIServer.UNIQUE_ID));
        normalizedParams.put(AsteriskAGIServer.CALLER_ID, getRequestParamValue(requestParams, AsteriskAGIServer.CALLER_ID)); // This is the TTSNG "from"
        normalizedParams.put(AsteriskAGIServer.CHANNEL_ID, getRequestParamValue(requestParams, AsteriskAGIServer.CHANNEL_ID));
        normalizedParams.put(AsteriskAGIServer.CLIENT_CALL_ID, getRequestParamValue(requestParams, AsteriskAGIServer.CLIENT_CALL_ID));
        normalizedParams.put(AsteriskAGIServer.ORIGIN, getRequestParamValue(requestParams, AsteriskAGIServer.ORIGIN));

        //I think this is not supported as a stand-alone parameter, it can arrive only as part of the special billing instructions
        //of verify calls. In order to make sure there is no back-door here to set prices, I removed this line. Keeping it in comment in case
        //I am wrong.
        //normalizedParams.put(AsteriskAGIServer.FORCED_PRICE, getRequestParamValue(requestParams, AsteriskAGIServer.FORCED_PRICE));

        // Fetch the headers
        try {
            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL,
                    AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL)));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL, e.getMessage());
        }
        try {
            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC,
                    AsteriskAGIServer.normalizeValue(StringUtils.trimToNull(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC))));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC, e.getMessage());
        }
        try {
            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID,
                    AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID)));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID, e.getMessage());
        }
        try {

            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS,
                    AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS)));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS, e.getMessage());
        }
        try {
            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q,
                    AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q)));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_X_DELAY_Q, e.getMessage());
        }
        try {
            normalizedParams.put(AsteriskAGIServer.SIP_HEADER_X_NEXMO_Vonage_Product_Path,
                    AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_NEXMO_Vonage_Product_Path)));
        } catch (AgiException e) {
            Log.warn("{}: Failed to fetch header {} due to {} ", nexmoUUID, AsteriskAGIServer.SIP_HEADER_X_NEXMO_Vonage_Product_Path, e.getMessage());
        }

        return normalizedParams;
    }

    private static String getRequestParamValue(Map<String, String[]> requestParams, String paramName) {
        String param = null;
        String[] params = requestParams.get(paramName);
        if (params != null)
            param = params[0];
        Log.trace("Extracted {} : {}", paramName, param);
        return param;
    }

    private static VoiceContext.Builder generateContextBuilder(HashMap<String, String> inputParams, String accountId,
                                                               VoiceProduct requestedVoiceProduct, String sessionId, String callTermination, boolean isVAPIOutboundToVBC) {

        VoiceContext.Builder voiceCtxBuilder = new VoiceContext.Builder()
                .withVoiceProduct(requestedVoiceProduct)
                .withTo(inputParams.get(AsteriskAGIServer.EXTENSION)) // This is the TTSNG "to"
                .withAccountId(accountId)
                .withVoiceDirection(VoiceDirection.OUTBOUND)
                .withProductClass(requestedVoiceProduct.getDescriptor())
                .withProductClassVersion(AsteriskAGIServer.PRODUCT_VERSION) // To clarify this is TTS/Verify-NG
                .withQuotaRef(SipAppUtils.generateQuotaReference(sessionId, false))
                .withRequestIp(inputParams.get(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC))
                .withCallOrigin(inputParams.get(AsteriskAGIServer.ORIGIN))
                .withCallTermination(callTermination)
                .withIsVAPIOutboundToVBC(isVAPIOutboundToVBC)
                .withFrom(AsteriskAGIServer.UNKNOWN_CALLER) // This is temporary until we will get the final "from"
                .withProductPath(inputParams.get(SIP_HEADER_X_NEXMO_Vonage_Product_Path));

        // Add alternative callback_url if provided - this header is added by the NET for calling applications.
        // It let SIPApp know to which ngnix it should return a callback on the call completion.
        // this is not for customers usage.
        String internalNexmoCallbackUrl = inputParams.get(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL);
        if (StringUtils.isNotBlank(internalNexmoCallbackUrl)) {
            voiceCtxBuilder.withInternalCallbackUrl(internalNexmoCallbackUrl);
            voiceCtxBuilder.withInternalCallbackMethod(CallbackMethod.getMatching("GET"));
        }
        return voiceCtxBuilder;
    }


    private static String getSourceCountryCode(String sessionId, String callerId) {
        String sourceCountryCode = null;
        if (Log.isDebugEnabled())
            Log.debug("{}: About to get the sourceCountryCode for {} ", sessionId, callerId);

        NetworksClient networksClient = Core.getInstance().getNetworksClient();
        Network sourceNetwork = networksClient != null ? networksClient.lookupNetwork(callerId): null;
        if (Objects.nonNull(sourceNetwork) && sourceNetwork.getCountryCode() != null) {
                sourceCountryCode = sourceNetwork.getCountryCode();
        }
        return sourceCountryCode;
    }
}
