package com.nexmo.voice.core.types;

import java.io.Serializable;

import static com.nexmo.voice.core.types.CallInternalFlag.*;

public enum AuthenticationSource implements Serializable {

    DOMAIN_ACL("domain-acl", AUTHENTICATED_BY_DOMAIN_ACL),
    DOMAIN_USER("domain-user", AUTHENTICATED_BY_DOMAIN_USER),
    DOMAIN_TRANSITION("domain-transition", AUTHENTICATED_BY_DOMAIN_TRANSITION),
    DOMAIN_TRANSITION_SERVICE_FALLBACK("domain-transition-service-fallback", AUTHENTICATION_DOMAIN_TRANSITION_SERVICE_FALLBACK),
    DOMAIN_TRANSITION_AUTH_FALLBACK("domain-transition-auth-fallback", AUTHENTICATION_DOMAIN_TRANSITION_AUTH_FALLBACK),
    SOURCE_IP("source-ip", AUTHENTICATED_BY_SOURCE_IP),
    APIKE<PERSON>("apikey", AUTHENTICATED_BY_APIKEY),
    UNK<PERSON>OW<PERSON>("unknown", null),
    ;

    private final String headerValue;
    private final CallInternalFlag internalFlag;

    AuthenticationSource(final String headerValue, final CallInternalFlag internalFlag) {
        this.headerValue = headerValue;
        this.internalFlag = internalFlag;
    }

    public String getHeaderValue() {
        return this.headerValue;
    }

    public CallInternalFlag getInternalFlag() {
        return this.internalFlag;
    }

    public static AuthenticationSource getForHeaderValue(String headerValue) {
        for(AuthenticationSource s: values()) {
            if(s.getHeaderValue().equalsIgnoreCase(headerValue)) {
                return s;
            }
        }
        return UNKNOWN;
    }
}
