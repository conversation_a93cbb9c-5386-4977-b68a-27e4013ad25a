package com.nexmo.voice.core.jmx;

/**
 * <AUTHOR>
 */
public interface CoreJMXMBean {

    public String getUptime();

    public String getTimeStarted();

    public String getConfigXmlFileLocation();

    public void reloadConfig();

    public void shutdown(String message);

    public void deleteContext(String sessionId);

    public void flushContextCacheForAccount(String accountId);

    public void dumpXmlToLog();

}
