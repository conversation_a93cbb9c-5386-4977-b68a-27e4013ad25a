package com.nexmo.voice.core.sip;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.WhitelistedNumbersConfig;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.emergency.EmergencyCallProperties;
import com.nexmo.voice.core.emergency.EmergencyCallingException;
import com.nexmo.voice.core.routing.RouteData;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.VoiceDirection;
import com.thepeachbeetle.common.msisdn.PhoneNumberTool;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingTargetGroup;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import com.thepeachbeetle.messaging.hub.core.routing.GenericMTRouter;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.fastagi.AgiChannel;
import org.asteriskjava.fastagi.AgiException;
import org.asteriskjava.fastagi.AgiRequest;
import org.asteriskjava.fastagi.BaseAgiScript;

import com.nexmo.common.api.********.exceptions.ServiceException;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.types.SIPCode;
import com.nexmo.voice.core.types.VoiceProduct;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import static java.util.Objects.nonNull;

/**
 * This class is the AsteriskAGIRequests controller.
 * It receive the AGIRequest and direct it to the relevant AGIRequest handler.
 * AT the moment the one handler we have is SIP, and the implementation is the old AsteriskAGIServer.
 * <p>
 * In the future we will have a TTSNG handler to handle the TTSNG requests.
 * <p>
 * In the far future, the TTSNG handler will be the general modern implementation to handle both SIP and TTSNG.
 *
 * <AUTHOR>
 */

public class AsteriskAGIServer extends BaseAgiScript {

    private final static Logger Log = LogManager.getLogger(AsteriskAGIServer.class);

    //Channel variables:
    //
    //Response variables:
    //These channel variables are set during the request processing in order to
    //return back information to the Asterisk server
    public static final String APP_REASON = "APP_REASON";

    public static final String GW = "GW";
    public static final String GWS = "GWS";

    public static final String IB_DEST = "IB_DEST";
    public static final String IB_TYPE = "IB_TYPE";
    public static final String CALLERID_NAME = "CALLERID(NAME)";
    public static final String CALLERID_NUM = "CALLERID(NUM)";
    public static final String NEXMO_PAY = "********";
    public static final String APP_APIKEY = "APP_APIKEY";
    public static final String CHANNEL_EXTENSION = "EXTENSION";
    public static final String DOMAIN_DEST = "DOMAIN_DEST";
    public static final String DOMAIN_DEST_TYPE = "DOMAIN_DEST_TYPE";


    //Request Headers
    public static final String SIP_HEADER_P_NEXMO_CLASS = "SIP_HEADER(********Class)";
    public static final String SIP_HEADER_X_NEXMO_TRACE_ID = "SIP_HEADER(********Trace-Id)";
    public static final String SIP_HEADER_P_NEXMO_SRC = "SIP_HEADER(********Src)";
    public static final String SIP_HEADER_P_NEXMO_CALLBACK_URL = "SIP_HEADER(********CallbackUrl)";
    public static final String SIP_HEADER_P_NEXMO_BILLING_OPTS = "SIP_HEADER(********Billing-Opts)";

    public static final String SIP_HEADER_P_NEXMO_CALL_DETAILS = "SIP_HEADER(********Call-Details)";
    //public static final String SIP_HEADER_P_NEXMO_TTS_TRACE_ID = "SIP_HEADER(********Trace-Id)"; TALLY check this

    public static final String SIP_HEADER_P_NEXMO_CUSTOMERDOMAIN_TYPE = "SIP_HEADER(********customerdomain-type)";

    public static final String SIP_HEADER_P_NEXMO_AUTHENTICATION_SOURCE = "SIP_HEADER(********authentication-source)";

    //Special header to allow us generate the 402 (out of money) on the BridgeEvent
    //This header take place only in the testing environment.
    //In any case the call will not be able to take place due to not enough money, just not on the AGI req.
    //When this header is provided and its value is "yes", the balance check will be postpone to the BridgeEvent
    public static final String SIP_HEADER_X_DELAY_Q = "SIP_HEADER(X-Delay-Q)";
    public static final String SIP_HEADER_X_NEXMO_Vonage_Product_Path = "SIP_HEADER(********Vonage-Product-Path)"; // RTC-1399

    //Request input parameters
    public static final String NEXMO_UUID = "nexmoCallId";
    public static final String ACCOUNT_ID = "accountId";
    public static final String ORIGIN = "origin";
    public static final String APPLICATION_ID = "application_id";
    public static final String FORCED_GATEWAY = "forceGateway";
    public static final String ATTEMPT = "attempt";
    public static final String CLIENT_CALL_ID = "clientCallId";
    public static final String CHANNEL_ID = "channelId";
    public static final String CALLER_ID = "callerId";
    public static final String UNIQUE_ID = "uniqueId";
    public static final String EXTENSION = "extension";
    public static final String DIRECTION = "direction";
    public static final String CUSTOMER_DOMAIN = "customer_domain";


    //Request sub-parameters - available only for Verify calls
    public static final String FORCED_PRICE = "force-price";
    public static final String RETURN_COST_IN_CALLBACK = "request-cost";
    public static final String BYPASS_PERMITTED_DEST_VERIFICATION = "bypass-permitted-destination";

    //General constants
    public static final String DISABLE_MUST_OWN_LVN_CAPABILITY_NAME = "disable-must-own-lvn";
    public static final String CLI_ALLOWED_TO_ALL_COUNTRIES_TAG = "ZZ";
    public static final String PREFIX_GROUP_CAPABILITY_NAME = "use-prefix-group-pricing";
    public static final String HAS_KYC = "has-kyc"; //to check if kyc process has been completed
    public static final String VOICE_SKIP_QUOTA = "voice-skip-quota"; //ability to start a call without balance check or consuming quota
    public static final String VPRICING_ENABLED_CAPABILITY = "rtc-voice-enable-vpricing"; //will calculate pricing via v********
    public static final String OBFUSCATE_PRICE_IN_CALLBACk = "sms-hide-price-on-api";
    public static final String SIP_ABSOLUTE_TIMEOUT = "SIP-ABSOLUTE-TIMEOUT";
    private static final String CALL_ABSOLUTE_TIMEOUT = "rtc-sip-absolute-timeout"; //allows early termination of a call, hangs up both legs

    private static final String CAP_EMERGENCY_CALLING = "rtc-emergency-calling"; // account capability to allow emergency calling

    public static final String DEMO_WATERMARK = "[Nexmo DEMO]";

    public static final String CALL_ORIGIN_PSTN = "pstn";
    public static final String CALL_ORIGIN_SIP = "sip";
    public static final String CALL_ORIGIN_APPLICATION = "application";
    public static final String PRIVACY_HEADER = "privacy";
    public static final String DIVERSION_HEADER = "DIVERSION"; // RTC-2201
    public static final String PRIVACY_HEADER_ID = "id";
    public static final String ANONYMOUS_CALLER = "anonymous";
    public static final String UNKNOWN_CALLER = "Unknown";
    public static final String DEFAULT_GATEWAY = "default";
    public static final String INBOUND_DIRECTION = "inbound";

    public static final String PRODUCT_VERSION = "NG";


    public static final String VERSTAT = "verstat";
    public static final String ATTESTATION_LEVEL = "ATTESTATION_LEVEL";

    public static final String ASTERISK_VERSION = "VCP_ASTERISK_VERSION";

    //Although this variable is named Inbound, its also used for outbound call, and its used to check if the current connection is SRTP
    //regardless of whether the SIP url has SRTP enabled
    public static final String IB_SRTP_ENABLED_VAR_NAME = "IB_SRTP_ENABLED"; //0 (SRTP not enabled) and 1 (SRTP enabled)
    public static final String NEXMO_PAY_SCENARIO = "1";
    public static final String IB_SRTP_ENABLED = "1";
    public static final String VSIP_TRANSPORT_VAR_NAME = "VSIP_TRANSPORT"; //tls or tcp
    public static final String VSIP_TRANSPORT_TLS = "tls";
    public static final String VSIP_TRANSPORT_TCP = "tcp";

    private static final Histogram AGI_PROCESSING_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_agi_processing_latency").help("Time taken to process AGI requests").labelNames("voiceProduct").register();

    private final static Counter AGI_SHORTCODE_USAGE_COUNT = Counter.build().name("sipapp_shortcode_usage_count").help("Number of successful requests for checking lvn ownership.").labelNames("shortCodeType", "isSubAccountUsingShortCodeOwnedByMaster").register();

    public static final String CUSTOMER_DOMAIN_TYPE_TRUNKING = "TRUNKING";
    public static final String CUSTOMER_DOMAIN_TYPE_APPLICATION = "APPLICATION";
    private static final Pattern DIGIT_SEPARATORS_PATTERN = Pattern.compile("[\\s,_]");

    private static final Counter EMERGENCY_CALLS_INITIATED = Counter.build().name("rtc_emergency_call_initiated").labelNames("locale","number").help("Emergency calls initiated by locale and emergency number").register();

    private static final Counter EMERGENCY_CALLS_CONFIG_ERROR = Counter.build().name("rtc_emergency_call_config_error").labelNames("locale").help("Emergency call failures due to config errors by locale").register();

    private static final Counter EMERGENCY_CALLS_NO_CAPABILITY = Counter.build().name("rtc_emergency_call_no_capability").help("Emergency call failures due to account not having capability").register();

    private static final Counter EMERGENCY_CALLS_UNSUPPORTED_NUMBER_TYPE = Counter.build().name("rtc_emergency_call_unsupported_number_type").labelNames("type").help("Emergency calls failures due to unsupported from number type").register();

    public AsteriskAGIServer() {
        super();
        Log.info("AsteriskAGIServerController constructor " + this.hashCode());
    }

    @Override
    public void service(AgiRequest request, AgiChannel channel) throws AgiException {
        final long timer = System.nanoTime();
        // Verify both input parameters are not null
        validateInputParameters(request, channel);

        String nexmoUUID = StringUtils.trimToNull(request.getParameter(NEXMO_UUID));

        // the content of the SIP_HEADER_P_NEXMO_CALL_DETAILS structure is on the legacy tts
        // at class MediaBridgeCallContext method: getTTSCallDetails
        // the content of the header need to be parsed into an object and added to the
        // VoiceContext, also callbackurl need to be decoded.
        // null values will appear as empty i.e. ;;

        Optional<String> legacyTTSCallDetails = Optional
                .ofNullable(channel.getVariable(SIP_HEADER_P_NEXMO_CALL_DETAILS))
                .map(AsteriskAGIServer::normalizeValue);

        // nexmoUUID is the call unique identifier - it is mandatory.
        if (Objects.isNull(nexmoUUID) || nexmoUUID.trim().isEmpty()) {
            Log.warn("nexmoUUID mandatory parameter is missing - rejecting the call. About to set APP_REASON to "
                    + String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));

            channel.setVariable(APP_REASON, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            throw new AgiException("nexmoUUID mandatory parameter is missing");
        }
        if (Log.isDebugEnabled()) {
            Log.debug("Received AGI request: nexmoUUID: {}", nexmoUUID);
            Log.debug("{} Additional AGI request headers: {}={} ", nexmoUUID, SIP_HEADER_P_NEXMO_CALL_DETAILS, legacyTTSCallDetails);
        }

        String accountId = StringUtils.trimToNull(request.getParameter(ACCOUNT_ID));

        String productClassOverride = normalizeValue(channel.getVariable(SIP_HEADER_P_NEXMO_CLASS));
        //I must say this is a little concerning as it open a door to a problem if accidently this header is added to regular sip calls,
        //as that direct them to be treated as tts calls.. if you are reading this and saying ohh.. shi... I am sorry to see you are having a bad day.
        final VoiceProduct requestedVoiceProduct;
        if (legacyTTSCallDetails.isPresent()) {
            requestedVoiceProduct = VoiceProduct.TTS;
            if (Log.isDebugEnabled()) {
                Log.debug("{} Forced VoiceProduct to TTS because {} is present ", nexmoUUID, legacyTTSCallDetails);
            }
        } else {
            requestedVoiceProduct = SipAppUtils.getVoiceProduct(productClassOverride);
        }

        if (Objects.isNull(requestedVoiceProduct)) {
            Log.error("VoiceProduct is null for {}: {}, accountId: {}, nexmoUUID: {}", SIP_HEADER_P_NEXMO_CLASS,
                    productClassOverride, accountId, nexmoUUID);
            throw new AgiException("Null VoiceProduct for " + nexmoUUID);
        }
        Log.info("Received AGI request nexmoUUID: {}  accountId: {} productClassOverride: {} requestedVoiceProduct: {}  legacyTTSCallDetails.isPresent(): {}",
                nexmoUUID, accountId, productClassOverride, requestedVoiceProduct, legacyTTSCallDetails.isPresent());

        try {
            switch (requestedVoiceProduct) {
                case SIP:
                case CALL_API:
                    AsteriskAGIServerSIPHandler.handleRequest(request, channel, accountId, nexmoUUID,
                            productClassOverride, requestedVoiceProduct);
                    break;
                case TTS:
                    //TTS calls must have the legacyTTSCallDetails
                    AsteriskAGIServerTTSNGHandler.handleRequest(request, channel, accountId, nexmoUUID,
                            productClassOverride, requestedVoiceProduct, legacyTTSCallDetails.get());
                    break;
                case VERIFY:
                    //Verify calls should never have the legacyTTSCallDetails - hence forcing null
                    AsteriskAGIServerTTSNGHandler.handleRequest(request, channel, accountId, nexmoUUID,
                            productClassOverride, requestedVoiceProduct, null);
                    break;
                default:
                    Log.error("{} : Unsupported VoiceProduct requested. Header {}={}, converted to: {}. accountId: {}",
                            nexmoUUID, SIP_HEADER_P_NEXMO_CLASS, productClassOverride, requestedVoiceProduct.name(), accountId);
                    throw new ServiceException("Unsupported VoiceProduct " + requestedVoiceProduct.name());
            }

            // Log the channel's final status before sending the response to Asterisk
            logChannelContent(channel, nexmoUUID);

        } catch (AgiException ex) {
            Log.error("{}: Failed to handle call. accountId: {} due to {}", nexmoUUID, accountId, ex.getMessage());
            cleanCache(nexmoUUID, accountId);
            throw ex;
        } catch (ServiceException ex) {
            Log.error("{}: Failed to handle call due to {}", nexmoUUID, ex.getMessage());
            cleanCache(nexmoUUID, accountId);
            throw new AgiException("Failed to handle call " + nexmoUUID, ex);
        }
        finally {
            AGI_PROCESSING_LATENCY.labels(requestedVoiceProduct.name()).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        }
    }

    private static void logChannelContent(AgiChannel channel, String nexmoUUID) {
        StringBuilder sb = new StringBuilder();
        sb.append("End of AGIServer request. nexmoUUID: ").append(nexmoUUID);
        sb.append(" Channel variables set to: ");
        logChannelVariable(channel, APP_REASON, sb, nexmoUUID);
        logChannelVariable(channel, GW, sb, nexmoUUID);
        logChannelVariable(channel, GWS, sb, nexmoUUID);
        logChannelVariable(channel, IB_DEST, sb, nexmoUUID);
        logChannelVariable(channel, IB_TYPE, sb, nexmoUUID);
        logChannelVariable(channel, CALLERID_NAME, sb, nexmoUUID);
        logChannelVariable(channel, CALLERID_NUM, sb, nexmoUUID);
        logChannelVariable(channel, ATTESTATION_LEVEL, sb, nexmoUUID);
        logChannelVariable(channel, IB_SRTP_ENABLED_VAR_NAME, sb, nexmoUUID);
        logChannelVariable(channel, EXTENSION, sb, nexmoUUID);
        logChannelVariable(channel, APP_APIKEY, sb, nexmoUUID);
        logChannelVariable(channel, VSIP_TRANSPORT_VAR_NAME, sb, nexmoUUID);
        logChannelVariable(channel, SIP_ABSOLUTE_TIMEOUT, sb, nexmoUUID);
        Log.info(sb.toString());
    }

    private static void logChannelVariable(AgiChannel channel, String variableName, StringBuilder sb, String sessionId) {
        final String varValue = getChannelVariable(sessionId, variableName, channel);
        if (Objects.nonNull(varValue)) {
            sb.append(" ").append(variableName).append(":").append(varValue).append(",");
        }
    }

    protected static String normalizeValue(String variableValue) {
        return (Objects.isNull(variableValue)) ? null : variableValue.trim();
    }

    protected static String getAGIReqParamsAndVars(AgiRequest request, AgiChannel channel) {
        if (Objects.isNull(request))
            return "Null request";
        else {
            StringBuilder sb = new StringBuilder();
            Map<String, String[]> p = request.getParameterMap();
            p.keySet().stream().forEach(k -> sb.append("[").append(k.toString()).append("=").append(p.get(k)[0]).append("],"));

            String alternativeCallbackUrl = "";
            String productClassOverride = "";
            String sourceIpHeader = "";
            String nexmoTraceId = "";
            String delayQuotaCheckHeader = "";
            String productPath = "";
            if (Objects.nonNull(channel)) {
                try {
                    alternativeCallbackUrl = channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL);
                    productClassOverride = channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CLASS);
                    sourceIpHeader = channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC);
                    nexmoTraceId = channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID);
                    delayQuotaCheckHeader = channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q);
                    productPath = channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_NEXMO_Vonage_Product_Path);
                } catch (AgiException e) {
                    Log.warn("Failed to fetch AGIServer request headers due to: ", e);
                }

                sb.append(" Headers: ");
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL).append("=").append(alternativeCallbackUrl);
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CLASS).append("=").append(productClassOverride);
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC).append("=").append(sourceIpHeader);
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID).append("=").append(nexmoTraceId);
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q).append("=").append(delayQuotaCheckHeader);
                sb.append(" ").append(AsteriskAGIServer.SIP_HEADER_X_NEXMO_Vonage_Product_Path).append("=").append(productPath);


            } else {
                sb.append("Channel is null");
            }
            return sb.toString();
        }
    }


    private static void validateInputParameters(AgiRequest request, AgiChannel channel) throws AgiException {
        // Verify the incoming objects are not null
        if (Objects.isNull(request) || Objects.isNull(channel)) {
            Log.warn("AGIRequest and/or AgiRequest are null. ");
            throw new AgiException("AGIRequest and/or AgiRequest are null.");
        }
    }

    //Cleanup any already created entries in the cache.
    //That might be the case if the exception had happened after the initial cache entry was created already
    private static void cleanCache(String nexmoUUID, String accountId) {
        if (Log.isDebugEnabled())
            Log.debug("cleanCache for session{} account {}", nexmoUUID, accountId);

        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();

        //Clean the session cache
        try {
            cache.removeAllForSession(nexmoUUID);
        } catch (Exception e) {
            //That might happen if the cache entry was not created yet.. hence it is info and not error
            Log.info("cleanCache session {} failed due to {} ", nexmoUUID, e);
        }

        //Clean the leg-flow cache
        Collection<VoiceContext> sessionContexts = cache.getInnerValues(nexmoUUID);
        for (VoiceContext connection : sessionContexts) {
            try {
                Core.getInstance().getLegFlowCache().removeLegContext(connection.getConnectionId());
            } catch (Exception e) {
                Log.info("cleanCache session {} connectionId {} failed due to {} ", nexmoUUID,
                        connection.getAccountId(), e);
            }
        }

    }

    protected static boolean shouldIgnoreMissingBalance(String sessionId, String accountId, String delayQuotaCheckHeader) {
        boolean isDelayQuotaCheckRequested = isDelayQuotaCheckRequested(delayQuotaCheckHeader);
        if (!isDelayQuotaCheckRequested) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} delayQuotaCheckHeader={} isDelayQuotaCheckRequested={}, shouldIgnoreMissingBalance=false",
                        sessionId, accountId, delayQuotaCheckHeader, isDelayQuotaCheckRequested);
            return false;
        }

        boolean isAllowSkipBalanceCheckOnAGI = Core.getInstance().getConfig().getExtendedQuotaAPIConfig().isAllowSkipBalanceCheckOnAGI();
        if (Log.isDebugEnabled())
            Log.debug("{} {} configuration param isAllowSkipBalanceCheckOnAGI={} isDelayQuotaCheckRequested={} ",
                    sessionId, accountId, isAllowSkipBalanceCheckOnAGI, isDelayQuotaCheckRequested);

        if (isAllowSkipBalanceCheckOnAGI && isDelayQuotaCheckRequested)
            return true;
        else
            return false;
    }

    protected static boolean isDelayQuotaCheckRequested(String delayQuotaCheckHeader) {
        boolean isDelayQuotaCheckRequested = false;
        if ("yes".equalsIgnoreCase(delayQuotaCheckHeader))
            isDelayQuotaCheckRequested = true;

        return isDelayQuotaCheckRequested;
    }

    public static boolean isAllowedAnyCallerIdValue(SmppAccount account, String destination) {
        if (Log.isDebugEnabled()) {
            Log.debug("Checking {} for {} capability among {} capabilities", account.getSysId(), DISABLE_MUST_OWN_LVN_CAPABILITY_NAME, account.getCapabilities());
        }
        if (Objects.nonNull(account) &&
                Objects.nonNull(account.getCapabilities()) &&
                account.getCapabilities().contains(DISABLE_MUST_OWN_LVN_CAPABILITY_NAME)) {
            Set<String> tags = account.getCapabilityTags(DISABLE_MUST_OWN_LVN_CAPABILITY_NAME);
            if (tags == null || tags.isEmpty())
                return false;
            if(tags.stream().map(String::toUpperCase).anyMatch(tag -> CLI_ALLOWED_TO_ALL_COUNTRIES_TAG.equals(tag))) {
                Log.info("account {} has disable-must-own-lvn with ZZ tag", account.getSysId());
                return true;
            }
            if (StringUtils.isEmpty(destination)) {
                return false;
            }
            PhoneNumberTool.NationalNumber destNumber = PhoneNumberTool.formatToNationalNumber(destination);
            String destCountryCode = destNumber.getCountryCode();
            if (StringUtils.isEmpty(destCountryCode)) {
                Log.info("Could not get dest Country code for destination {} for account {}", destination, account.getSysId());
                return false;
            }
            if(tags.stream().map(String::toUpperCase).anyMatch(tag -> destCountryCode.equals(tag))) {
                Log.info("account {} has disable-must-own-lvn and dest country code {} tag", account.getSysId(), destCountryCode);
                return true;
            }
        }
        return false;
    }

    protected static boolean hasKyc(SmppAccount account) {
        if (Log.isDebugEnabled()) {
            Log.debug("Checking {} for {} capability among {} capabilities", account.getSysId(), HAS_KYC, account.getCapabilities());
        }
        return nonNull(account.getCapabilities()) && account.getCapabilities().contains(HAS_KYC);
    }

    public static boolean isVoiceSkipQuota(SmppAccount account) {
        if (Log.isDebugEnabled()) {
            Log.debug("Checking {} for {} capability among {} capabilities", account.getSysId(), VOICE_SKIP_QUOTA, account.getCapabilities());
        }
        return nonNull(account.getCapabilities()) && account.getCapabilities().contains(VOICE_SKIP_QUOTA);
    }

    public static boolean isVoiceSkipQuota(SmppAccount account, boolean isEmergencyCall, boolean isSkipQuotaDuringEmergencyCall) {
        if(isEmergencyCall && isSkipQuotaDuringEmergencyCall) {
            Log.info("Skipping quota for emergency call");
            return true;
        }
        return isVoiceSkipQuota(account);
    }

    protected static boolean isWhitelistedNumber(String to) {
        WhitelistedNumbersConfig whitelistConfig = Core.getInstance().getConfig().getWhitelistedNumbersConfig();
        if (whitelistConfig != null && whitelistConfig.isEnabled() && whitelistConfig.isWhitelisted(to)) {
            if (Log.isDebugEnabled())
                Log.debug("Assuming valid destination for general whitelisted number " + to);
            return true;
        } else {
            return false;
        }
    }

    //Format the number and check its validity.
    //There is a global whitelisted numbers - if the destination's prefix is listed there - they are
    //considered valid and should not use the libphone verification
    protected static boolean isValidNumber(String to) {
        if (StringUtils.isEmpty(to)) {
            return false;
        }
        if (isWhitelistedNumber(to))
            return true;

        String formattedTo;
        try {
            // this will throw an exception if libphonenumber says the number is not possible or not valid
            formattedTo = PhoneNumberTool.formatToInternationalNumberAndValidate(to, null);
        } catch (PhoneNumberTool.BadlyFormattedNumberException e) {
            Log.error("Failed to validate {} due to {}", to, e);
            return false;
        }
        return (formattedTo != null);
    }


    protected static boolean isOwnedLvn(SmppAccount account, ShortCode lvnShortCode, String lvn, String sessionId) {
        if (Objects.isNull(lvnShortCode)) {
            Log.warn("{}: Account {} doesn't use its LVN as callerId, but provided callerId {} is not a recognized lvn",
                    sessionId, account.getSysId(), lvn);
            return false;
        }
        String shortCodeAccountId = lvnShortCode.getAccountId();
        String shortCodeType = String.valueOf(lvnShortCode.getShortCodeType());
        if (Objects.isNull(shortCodeAccountId)) {
            Log.warn("{}: Account {} doesn't use its LVN as callerId, the callerId {} is lvn is not related to any account",
                    sessionId, account.getSysId(), lvn);
            return false;
        }
        Log.info("{}: LVN {} is of type {}", sessionId, lvn, shortCodeType);

        if (shortCodeAccountId.equals(account.getSysId())) {
            // The account own the requested lvn (=callerId) - be happy and use it
            AGI_SHORTCODE_USAGE_COUNT.labels(shortCodeType, "false").inc();
            return true;
        }

        // As the account must own the lvn but it is not - check if the master-account
        // exist and own it
        String masterAccountId = account.getMasterAccountId();
        if (Objects.isNull(masterAccountId)) {
            Log.warn("{}: Account {} has no master account - requested callerId {} might be ignored",
                    sessionId, account.getSysId(), lvn);
            return false;
        }
        // the current account is a sub account, check if its allowed to use its master's lvns
        if (!account.isMasterAccountShareMasterShortCodes()) {
            Log.warn("{}: Account {} has master account but does not share its lvns - requested callerId {} might be ignored",
                    sessionId, account.getSysId(), lvn);
            return false;
        }
        // the account is sharing its master's lvns ; verify if the requested callerId is owned by the master
        if (shortCodeAccountId.equals(masterAccountId)) {
            // The master account own the requested lvn (=callerId) and share it with its sub-account - be happy and use it
            AGI_SHORTCODE_USAGE_COUNT.labels(shortCodeType, "true").inc();
            return true;
        }
        Log.warn("{} Account {} doesnt own LVN {}", sessionId, account.getSysId(), lvn);
        return false;
    }

    static int getCallTimeout(SmppAccount account) {
        if (Log.isDebugEnabled()) {
            Log.debug("Checking account {} for {} capability among {} capabilities", account.getSysId(), CALL_ABSOLUTE_TIMEOUT, account.getCapabilities());
        }
        Set<String> timeoutCapabilityValues = null;
        boolean isTimeoutSet = nonNull(account.getCapabilityTags())
            && !(timeoutCapabilityValues = account.getCapabilityTags().getOrDefault(CALL_ABSOLUTE_TIMEOUT, Collections.emptySet())).isEmpty();
        if (!isTimeoutSet) {
            return 0;
        }

        String timeoutString = timeoutCapabilityValues.iterator().next()
            .replaceAll(DIGIT_SEPARATORS_PATTERN.pattern(), "");
        int timeout = 0;
        try {
            timeout = Math.max(0, Integer.parseInt(timeoutString));
            if (timeout > 0) {
                Log.info("{} set to {} seconds for account {}", CALL_ABSOLUTE_TIMEOUT, timeout, account.getSysId());
            }
        } catch (NumberFormatException e) {
            Log.error("Failed to parse property value for {} as was {}, thus property won't be used for account {}", CALL_ABSOLUTE_TIMEOUT, timeoutString, account.getSysId());
        }
        return timeout;
    }

    protected static String getChannelVariable(String sessionId, String name, AgiChannel agiChannel) {
        try {
            final String result = agiChannel.getVariable(name);
            if (Log.isDebugEnabled())
                Log.debug("{} channel var {} value is {}  ", sessionId, name, result);

            return StringUtils.trimToNull(result);
        } catch (AgiException e) {
            Log.warn("{} Failed to extract {} variable due to {}", sessionId, name, e);
        }
        return null;
    }

    protected static VoiceContext.Builder buildErrorContext(String extension, String sessionId, String uniqueId,
                                                            AgiChannel channel, String callerId, String callTermination, String channelId, String clientCallId,
                                                            String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct, String accountId,
                                                            boolean isVAPIOutboundToVBC, String reason, SIPCode sipCode, CallInternalFlag callInternalFlag,
                                                            VoiceProduct voiceProduct, VoiceDirection voiceDirection) throws AgiException {

        String status = String.valueOf(sipCode.getCode());
        Log.info("Due to {}, About to set APP_REASON to {} sessionId: {}", reason, status, sessionId);
        channel.setVariable(AsteriskAGIServer.APP_REASON, status);

        // Build a context for the CDR event
        String sourceIpHeader = AsteriskAGIServer.getChannelVariable(sessionId, AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC, channel);

        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, requestedAppId, null, false, 0);
        return new VoiceContext.Builder()
                .withVoiceProduct(voiceProduct)
                .withFrom(callerId)
                .withTo(extension)
                .withAccountId(accountId)
                .withVoiceDirection(voiceDirection)
                .withProductClass(requestedVoiceProduct.getDescriptor())
                .withRequestIp(sourceIpHeader)
                .withCallOrigin(callOrigin)
                .withCallTermination(callTermination)
                .withIsVAPIOutboundToVBC(isVAPIOutboundToVBC)
                .withApplicationContext(errorAppContext)
                .withInternalFlag(callInternalFlag);


    }

    protected static void rejectCallInShutdown(String extension, String sessionId, String uniqueId,
                                               AgiChannel channel, String callerId, String callTermination, String channelId, String clientCallId,
                                               String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct, String accountId,
                                               boolean isVAPIOutboundToVBC, String reason, VoiceProduct voiceProduct, VoiceDirection voiceDirection) throws AgiException {

        final VoiceContext.Builder errorCtxBuilder = buildErrorContext(extension, sessionId, uniqueId,
                channel, callerId, callTermination, channelId, clientCallId,
                requestedAppId, callOrigin, requestedVoiceProduct, accountId,
                isVAPIOutboundToVBC, reason,
                SIPCode.FORBIDDEN, CallInternalFlag.BLOCKED_DURING_SHUTDOWN, voiceProduct, voiceDirection);
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();

        errorCtxBuilder.withInitialChargingStatus(BillingInfo.Status.ERROR)
                .withPricePerMinute(BigDecimal.ZERO)
                .withFirstChargedSeconds(0)
                .withQuotaUpdatesInterval(0);

        VoiceContext errorContext = errorCtxBuilder.build();
        errorContext.setSessionId(sessionId);
        errorContext.setConnectionId(uniqueId);

        cache.storeContext(sessionId, uniqueId, errorContext);
        if (Log.isDebugEnabled())
            Log.debug("SESSION-ID ['" + sessionId + "'] :: Stored ERROR context :: " + errorContext.getDebugString());
    }

    protected static Set<String> getAlternativeGateways(GenericMTRouter.Route route) {
        Set<String> gws = null;
        if (route != null && route.getAdditionalGateways() != null) {
            gws = new LinkedHashSet<>();
            List<MtRoutingTargetGroup.Target> list = new ArrayList<MtRoutingTargetGroup.Target>(route.getAdditionalGateways());
            Collections.sort(list, new Comparator<MtRoutingTargetGroup.Target>() {
                public int compare(MtRoutingTargetGroup.Target o1, MtRoutingTargetGroup.Target o2) {
                    if (o1.getWeight() == o2.getWeight())
                        return 0;
                    return o1.getWeight() > o2.getWeight() ? -1 : 1;
                }
            });
            for (MtRoutingTargetGroup.Target alternativeGateway : list) {
                Log.debug("getAlternativeGateways gw:[ " + alternativeGateway.getGatewayId() + " ] gw: [ " + alternativeGateway.getWeight() + " ]");
                gws.add(alternativeGateway.getGatewayId());
            }
        }
        return gws;
    }

    protected static String createOutboundAltGateways(String outboundGatewayId, Set<String> alternativeGateways) {
        final StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(outboundGatewayId);
        if (alternativeGateways != null) {
            stringBuilder.append(",").append(String.join(",", alternativeGateways));
        }
        return stringBuilder.toString();
    }

    /**
     * Identifies whether this is an emergency service call for the provided account, from phone
     * number and to phone number. Applies business logic to determine whether account is able to
     * place emergency service calls, the call is for a supported locale and to a supported
     * emergency service number within that locale.
     *
     * @param account the SmppAccount of the caller's account
     * @param fromPhoneNumber the call's "from" phone number
     * @param toNumber the call's "to" phone number
     * @param sessionId the call's sessionId
     * @return EmergencyCallProperties for this call, if determined to be an emergency call
     */
    protected static Optional<EmergencyCallProperties> isEmergencyCall(SmppAccount account, final String fromPhoneNumber, final String toNumber, final String sessionId) {
        if(account == null) {
            // account is null which means it wasn't found, just exit out of here
            Log.debug("{} account was null. Cannot determine if emergency call.", sessionId);
            return Optional.empty();
        }
        Log.debug("{} Checking if call by account {} from {} to {} is a supported emergency call", sessionId, account.getSysId(), fromPhoneNumber, toNumber);

        if(Core.getInstance().getConfig().getEmergencyCallingConfig() == null) {
            Log.debug("{} Emergency calling is not enabled", sessionId);
            return Optional.empty();
        }

        // check account owns the phone number
        ShortCode scode = AsteriskAGIServerSIPHandler.getShortCodeForNumber(fromPhoneNumber);
        if (Objects.isNull(scode) || !isOwnedLvn(account, scode, fromPhoneNumber, sessionId)) {
            Log.debug("{} Account {} does not own lvn {}", sessionId, account.getSysId(), fromPhoneNumber);
            return Optional.empty();
        }

        // get locale for fromPhoneNumber from the found ShortCode
        String locale = scode.getCountry();

        if(StringUtils.isEmpty(locale)) {
            Log.warn("{} Account {} owned lvn {} does not contain country in ShortCode properties", sessionId, account.getSysId(), fromPhoneNumber);

            // empty locale? try to get it from libphonenumber
            locale = SipAppUtils.phoneNumberIsoCountryCode(fromPhoneNumber);
            if (StringUtils.isEmpty(locale)) {
                Log.warn("{} From phone number {} does not have a locale", sessionId, fromPhoneNumber);
                // no locale for fromPhoneNumber
                return Optional.empty();
            }
        }

        // set upper case
        locale = locale.toUpperCase();

        // check that the toNumber is an emergency number for that locale
        if(!Core.getInstance().getConfig().getEmergencyCallingConfig()
                .isEmergencyServiceNumberForLocale(locale, toNumber)) {
            Log.debug("{} To phone number {} is not a supported emergency number for locale {}", sessionId, toNumber, locale);
            return Optional.empty();
        }

        // check if the number is a type qualified to make calls
        if(!Core.getInstance().getConfig().getEmergencyCallingConfig().isAllowedLvnType(scode.getShortCodeType())) {
            Log.warn("{} Account {} lvn {} is of type {}, which is not allowed for emergency calls", sessionId, account.getSysId(), fromPhoneNumber, scode.getShortCodeType());
            CompletableFuture.runAsync(() -> {
                EMERGENCY_CALLS_UNSUPPORTED_NUMBER_TYPE.labels(scode.getShortCodeType().name()).inc();
            });
            return Optional.empty();
        }

        Log.trace("{} Account {} capabilities: {}", sessionId, account.getSysId(), (account.getCapabilities() == null ? "" : String.join(",", account.getCapabilities())));
        // check that account has capability
        if(Objects.isNull(account.getCapabilities()) || !account.getCapabilities().contains(CAP_EMERGENCY_CALLING)) {
            Log.warn("{} Account {} does not have capability {} to make emergency call to {}", sessionId, account.getSysId(), CAP_EMERGENCY_CALLING, toNumber);
            CompletableFuture.runAsync(EMERGENCY_CALLS_NO_CAPABILITY::inc);
            return Optional.empty();
        }
        // check that account has capability tag for locale
        Log.trace("{} Account {} capability tags: {}", sessionId, account.getSysId(), (account.getCapabilityTags() == null ? "" : account.getCapabilityTagsAsString()));
        if(Core.getInstance().getConfig().getEmergencyCallingConfig().isRequireCapabilityLocale()
                && ((account.getCapabilityTags(CAP_EMERGENCY_CALLING) == null)
                    || !account.getCapabilityTags(CAP_EMERGENCY_CALLING).stream().map(String::toUpperCase).collect(Collectors.toSet()).contains(locale))) {
            Log.warn("{} Account {} does not have capability {} for locale {} to make emergency call to {}", sessionId, account.getSysId(), CAP_EMERGENCY_CALLING, locale, toNumber);
            CompletableFuture.runAsync(EMERGENCY_CALLS_NO_CAPABILITY::inc);
            return Optional.empty();
        }

        Log.debug("{} To phone number {} is supported emergency number for locale {}", sessionId, toNumber, locale);
        String finalLocale = locale;
        try {
            Optional<EmergencyCallProperties> emergencyCallProperties = Optional.of(new EmergencyCallProperties.Builder()
                    .withFromNumber(fromPhoneNumber)
                    .withToNumber(toNumber)
                    .withLocale(locale)
                    .withRoute(Core.getInstance().getConfig().getEmergencyCallingConfig().getEmergencyServiceRoutingForLocale(locale, toNumber))
                    .build());
            CompletableFuture.runAsync(() -> {
                EMERGENCY_CALLS_INITIATED.labels(finalLocale, toNumber).inc();
            });
            return emergencyCallProperties;
        } catch(EmergencyCallingException e) {
            Log.error("{} EmergencyCallingException when building properties: {}", sessionId, e.getMessage());
            CompletableFuture.runAsync(() -> {
                EMERGENCY_CALLS_CONFIG_ERROR.labels(finalLocale).inc();
            });
        }

        return Optional.empty();

    }

    protected static RouteData decideRoute(RouteData supplierIdRouteData, RouteData oaPrefixRouteData) {
        if (supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE &&
                oaPrefixRouteData.getRouteExceptionType() != RouteData.RouteExceptionType.NONE) {
            Log.debug("Returning supplier Id route, oa route is null");
            return supplierIdRouteData;
        }

        if (supplierIdRouteData.getRouteExceptionType() != RouteData.RouteExceptionType.NONE &&
                oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            Log.debug("Returning oaPrefix Id route, supplierId is null");
            return oaPrefixRouteData;
        }

        // Both are exceptions
        if ((supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTENULL) &&
                (oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.DROPMESSAGE ||
                        oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL)) {
            Log.debug("Returning oaPrefix Id route exception, supplierId route is null exception");
            return oaPrefixRouteData;
        }

        if ((oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTENULL) &&
                (supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.DROPMESSAGE ||
                        supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL)) {
            Log.debug("Returning supplier Id route exception, oaPrefix route is null exception");
            return supplierIdRouteData;
        }

        // -- One of them is an exception or both have routes
        // SupplierId Rule with account
        if (supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule supplierIdRule = supplierIdRouteData.getRoute().getRule();
            if (Objects.nonNull(supplierIdRule.getBindId())) {
                Log.debug("Returning supplier Id account route");
                return supplierIdRouteData;
            }
        }
        // OA Prefix with account
        if (oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule oaPrefixRule = oaPrefixRouteData.getRoute().getRule();
            if (Objects.nonNull(oaPrefixRule.getBindId())) {
                Log.debug("Returning OA Prefix account route");
                return oaPrefixRouteData;
            }
        }

        // SupplierId Rule with group rule
        if (supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule supplierIdRule = supplierIdRouteData.getRoute().getRule();
            if (Objects.nonNull(supplierIdRule.getRoutingGroupId())) {
                Log.debug("Returning supplierID group rule");
                return supplierIdRouteData;
            }
        }
        // OA Prefix with group rule
        if (oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule oaPrefixRule = oaPrefixRouteData.getRoute().getRule();
            if (Objects.nonNull(oaPrefixRule.getRoutingGroupId())) {
                Log.debug("Returning OA Prefix group rule");
                return oaPrefixRouteData;
            }
        }

        // SupplierId OA default
        if (supplierIdRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule supplierIdRule = supplierIdRouteData.getRoute().getRule();
            if (Objects.nonNull(supplierIdRule.getOa())) {
                Log.debug("Returning SupplierId OA default rule");
                return supplierIdRouteData;
            }
        }
        // OA Prefix default
        if (oaPrefixRouteData.getRouteExceptionType() == RouteData.RouteExceptionType.NONE) {
            MtRoutingRule oaPrefixRule = oaPrefixRouteData.getRoute().getRule();
            if (Objects.nonNull(oaPrefixRule.getOa())) {
                Log.debug("Returning OA Prefix default rule");
                return oaPrefixRouteData;
            }
        }
        //default
        return oaPrefixRouteData;
    }
}
