package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.Set;

import org.apache.log4j.Logger;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.mappings.LVNMappingsConfig;
import com.nexmo.voice.config.mappings.LVNMappingsSource;
import com.nexmo.voice.config.mappings.LVNMappingsConfigUtils;
import com.nexmo.voice.core.Core;

import com.thepeachbeetle.common.xml.XmlUtil;


/**
 * <AUTHOR>
 */
public class NumbersJMX implements NumbersJMXMBean {

    private static final Logger Log = Logger.getLogger(NumbersJMX.class.getName());

    @Override
    public String viewNumbersMetadata() {
        Log.info("About to fetch LVN metadata from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final Config config = Core.getInstance().getConfig();
        final LVNMappingsConfig gatewayConfig = config.getLVNMappingsConfig();
        final LVNMappingsSource gatewaySource = config.getLVNMappingsSource();

        sb.append("Source: ");
        sb.append(gatewaySource.getSource());
        sb.append("<br><br>");

        return sb.toString();
    }

    @Override
    public String loadNumbersListFromFile(String filename) {
        Log.info("About to load new LVN config from " + filename);

        if (filename == null) {
            return "ERROR: No filename provided.";
        }

        final File f = new File(filename);
        if (!f.exists()) {
            return "ERROR: File \"" + filename + "\" does not exist";
        }

        long timeCalled = System.currentTimeMillis();
        final StringBuilder sb = new StringBuilder();
        sb.append("Loading from file: \"");
        sb.append(filename);
        sb.append("\" => ");

        final LVNMappingsSource newSource = new LVNMappingsSource(filename);
        final LVNMappingsConfig newConfig = LVNMappingsConfigUtils.loadConfigFromXMLFile(filename);
        if (newConfig != null) {
            sb.append("SUCCESS!<br><br>");

            final LVNMappingsConfig oldConfig = Core.getInstance().getConfig().getLVNMappingsConfig();

            if (!newConfig.isEmpty()) {
                // Compare old/new configs and report the difference
                final String diffs = LVNMappingsConfigUtils.compareConfigs(oldConfig, newConfig);
                sb.append(diffs);

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setLVNMappingsConfig(newConfig, newSource);
            } else {
                sb.append("New config contains no LVNs: REJECTED!");
            }
        } else {
            sb.append("FAILED!");
        }

        long timeTaken = System.currentTimeMillis() - timeCalled;
        Log.info("NumbersJMX::loadSuppliersListFromFile call-total-time [ " + timeTaken + "]");
        return sb.toString();
    }

}
