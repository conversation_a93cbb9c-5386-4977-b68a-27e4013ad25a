package com.nexmo.voice.core.cache;

import com.nexmo.voice.core.sip.event.user.CdrUserFieldEvent;
import org.apache.log4j.Logger;

import java.util.concurrent.ConcurrentHashMap;

/**
 * This class is a cache of cdr userfield events to be used when piecing together partial CDRs together
 *
 * Will store a hashmap using the CdrUserFieldEvent Map<linkedID, Map<channelID, CdrUserFieldEvent>>
 *
 * <AUTHOR>
 */
public class CdrUserFieldEventCache {

    private static final Logger Log = Logger.getLogger(CdrUserFieldEventCache.class.getName());

    private ConcurrentHashMap<String, ConcurrentHashMap<String, CdrUserFieldEvent>> cdrUserFieldCache;

    public CdrUserFieldEventCache() {
        cdrUserFieldCache = new ConcurrentHashMap<String, ConcurrentHashMap<String, CdrUserFieldEvent>>();
        if (Log.isDebugEnabled()) {
            Log.debug("CdrUserFieldEvent Cache initialization completed");
        }
    }

    public void addEvent(String linkId, String channelId, CdrUserFieldEvent event) {
        cdrUserFieldCache.computeIfAbsent(linkId, key -> new ConcurrentHashMap<>());
        cdrUserFieldCache.get(linkId).put(channelId, event);
        Log.info("Adding LinkId: " + linkId + "; Channel: " + channelId + "; '[" + event + "'] to CdrUserFieldEvent Cache...");
    }

    public boolean containsEvent(String linkId, String channelId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Hash Map: " + this.cdrUserFieldCache.toString());
            Log.debug("Checking linkId: " + linkId);
        }
        if (cdrUserFieldCache.containsKey(linkId)) {
            if (Log.isDebugEnabled()) {
                Log.debug("Found linkId: " + linkId + "; Checking channelID: " + channelId);
            }
            return cdrUserFieldCache.get(linkId).containsKey(channelId);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("Couldn't find linkId: " + channelId);
        }

        return false;
    }

    public boolean deleteEvent(String linkId, String channelId) {
        if (!cdrUserFieldCache.containsKey(linkId)) {
            return true;
        }

        if (!cdrUserFieldCache.get(linkId).containsKey(channelId)) {
            return true;
        }

        // Remove nested linkId from Hangup Cache
        if (cdrUserFieldCache.get(linkId).remove(channelId) == null) {
            Log.warn("Could not find channel ID ['" + channelId + "'] in for link ID ['" + linkId + "'] in CdrUserFieldEvent Cache...");
            return false;
        };

        Log.info("Removed Link ID: " + linkId + " channel ID: " + channelId + " from CdrUserFieldEvent Cache...");
        if (Log.isDebugEnabled()) {
            this.logHashMap();
        }

        return true;
    }

    public boolean deleteLinkId(String linkId) {
        if (!cdrUserFieldCache.containsKey(linkId)) {
            Log.info("Link ID: " + linkId + " already removed from CdrUserFieldEvent Cache...");
            return true;
        }

        if (cdrUserFieldCache.remove(linkId) == null) {
            Log.warn("Could not find link ID ['" + linkId + "'] in CdrUserFieldEvent Cache...");
            return false;
        }

        Log.info("Removed Link ID: " + linkId + " from CdrUserFieldEvent Cache...");
        if (Log.isDebugEnabled()) {
            this.logHashMap();
        }

        return true;
    }

    public Integer getHangupCause(String linkId, String channelId) {
        return Integer.parseInt(cdrUserFieldCache.get(linkId).get(channelId).getHangupCause());
    }

    public CdrUserFieldEvent getEvent(String linkId, String channelId) {
        return cdrUserFieldCache.get(linkId).get(channelId);
    }

    public void logHashMap() {
        Log.debug("Hash Map: " + this.cdrUserFieldCache.toString());
    }
}

