package com.nexmo.voice.core.cache;

import java.util.HashMap;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.sip.AsteriskAGIServer;

public class TTSNGBillingInstructions {

    private static final Logger Log = LogManager.getLogger(TTSNGBillingInstructions.class);

    private final static String COST_REQUIRED_IN_CALLBACK = "1";
    private final static String BAYPASS_PERMITTED_DEST_VERIFICATION = "1";

    private boolean costRequiredInCallback = false;
    private boolean bypassPermittedDestinationVerification = false;
    private String requestedForcedPrice;

    public static TTSNGBillingInstructions parseBillingInstructions(String sessionId, HashMap<String, String> inputParams) {
        //request-cost: "1" - yes,  If not provided or any other value - no.
        //bypass-permitted-destination: "1" - yes,  If not provided or any other value - no.
        //forced-price: - If provided, keep the value. It is validated by TTS-NG,
        //It is verified again in the AsteriskAGIServerTTSNGHandler and a clear message is written in the log in case of error.

        TTSNGBillingInstructions billingInstructions = new TTSNGBillingInstructions();
        String billingOptHeader = inputParams.get(AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS);

        if (Log.isDebugEnabled())
            Log.debug("{}: About to parse the provided billing instructions: {}", sessionId, billingOptHeader);

        if (Objects.nonNull(billingOptHeader)) {
            String[] billingOptHeaderParts = billingOptHeader.split(";");
            for (String part : billingOptHeaderParts) {
                String[] param = part.split("=");
                if (param.length >= 2) {
                    switch (param[0]) {
                        case AsteriskAGIServer.FORCED_PRICE:
                            billingInstructions.setRequestedForcedPrice(param[1]);
                            break;
                        case AsteriskAGIServer.BYPASS_PERMITTED_DEST_VERIFICATION:
                            billingInstructions.setBypassPermittedDestinationVerification(param[1]);
                            break;
                        case AsteriskAGIServer.RETURN_COST_IN_CALLBACK:
                            billingInstructions.setCostRequiredInCallback(param[1]);
                            break;
                        default:
                            Log.info("{} : unrecognized billing param {} in {}. Will be ignored", sessionId, param[0], billingOptHeader);
                    }
                }
            }
        }
        Log.info("{} : {} = {}. parsed billing instructions: {}",
                sessionId, AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS, billingOptHeader, billingInstructions);

        return billingInstructions;
    }


    public boolean isCostRequiredInCallback() {
        return costRequiredInCallback;
    }

    public void setCostRequiredInCallback(String costRequiredInCallback) {
        this.costRequiredInCallback = COST_REQUIRED_IN_CALLBACK.equals(costRequiredInCallback);
    }

    public boolean isBypassPermittedDestinationVerification() {
        return bypassPermittedDestinationVerification;
    }

    public void setBypassPermittedDestinationVerification(String bypassPermittedDestinationVerification) {
        this.bypassPermittedDestinationVerification = BAYPASS_PERMITTED_DEST_VERIFICATION.equals(bypassPermittedDestinationVerification);
    }

    public String getRequestedForcedPrice() {
        return requestedForcedPrice;
    }

    public void setRequestedForcedPrice(String requestedForcedPrice) {
        this.requestedForcedPrice = requestedForcedPrice;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("TTSNGBillingInstructions [costRequiredInCallback=");
        builder.append(costRequiredInCallback);
        builder.append(", bypassPermittedDestinationVerification=");
        builder.append(bypassPermittedDestinationVerification);
        builder.append(", ");
        builder.append("requestedForcedPrice=");
        builder.append(requestedForcedPrice);
        builder.append("]");
        return builder.toString();
    }


}
