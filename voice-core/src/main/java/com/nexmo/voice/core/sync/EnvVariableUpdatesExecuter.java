package com.nexmo.voice.core.sync;

import com.nexmo.voice.config.charging.ChargingUpdaterConfig;
import org.apache.log4j.Logger;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * This class is initiated during the server startup.
 * It runs every 1 second and update environment variables with latest value
 */

public class EnvVariableUpdatesExecuter {

    private static final Logger Log = Logger.getLogger(EnvVariableUpdatesExecuter.class);
    private final ChargingUpdaterConfig config;
    private final ScheduledExecutorService pool;

    public EnvVariableUpdatesExecuter(ChargingUpdaterConfig config) {
        this.config = config;
        this.pool = Executors.newScheduledThreadPool(1); // 1 thread per product

    }

    public void init() {
        Log.info(">>>> Starting EnvVariableUpdatesExecuter tasks...");
        //scheduleAtFixedRate means that a task will start every second.
        this.pool.scheduleAtFixedRate(new EnvVariableUpdateTask(),
                0, // no initial delay
                1,
                TimeUnit.SECONDS);
        Log.info("EnvVariablesExecuter initialization was successful..");
    }

    //This method is called when we take down the SIPApp. Its goal is to stop the updater threads
    public void shutdown() {
        Log.info(">>>> Shutting down EnvVariableUpdatesExecuter...");

        try {
            this.pool.shutdown();
            boolean successfulShutdown = this.pool.awaitTermination(this.config.getShutdownPeriod(), TimeUnit.MILLISECONDS);
            if (successfulShutdown)
                Log.info("Shutdown all tasks successfully!");
            else
                Log.error("FAILED TO SHUTDOWN ALL TASKS SUCCESSFULLY!");
        } catch (InterruptedException ex) {
            Log.error("Something went terribly wrong!", ex);
        }
    }

}
