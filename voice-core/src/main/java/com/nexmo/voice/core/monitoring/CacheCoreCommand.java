package com.nexmo.voice.core.monitoring;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.thepeachbeetle.common.app.monitoring.AbstractMonitoringServlet.MonitoringCommand;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContextCache;

/**
 * <AUTHOR>
 */
public class CacheCoreCommand extends MonitoringCommand {

    private final Core core;

    public CacheCoreCommand(final Core core) {
        this.core = core;
    }

    @Override
    protected String getMetrics(HttpServletRequest request, Map<String, String> metrics) {

        VoiceContextCache cache = this.core.getVoiceContextCache();

        metrics.put("VOICE-CONTEXT-CACHE-OUTER-KEY-SIZE", "" + cache.outerSize());
        metrics.put("VOICE-CONTEXT-CACHE-DB-FLUSH-QUEUE-SIZE", "" + cache.getDBPersistenceFlusherQueueSize());
        metrics.put("VOICE-CONTEXT-CACHE-DB-FLUSH-QUEUE-TIME-SINCE-LAST-SUCCESS", "" + cache.getDBPersistenceFlusherQueueTimeSinceLastSuccess());
        metrics.put("VOICE-CONTEXT-CACHE-PURGE-ITEM-HANDLER-QUEUE-SIZE", "" + cache.getPurgeItemHandlerQueueSize());
        metrics.put("VOICE-CONTEXT-CACHE-PURGE-ITEM-HANDLER-QUEUE-TIME-SINCE-LAST-SUCCESS", "" + cache.getPurgeItemHandlerQueueTimeSinceLastSuccess());

        return "VOICE-CONTEXT-CACHE";
    }

}
