package com.nexmo.voice.core.sip;

/***
 *  This class is the enhanced version of the original AsteriskAGIServer.
 *  It is handling the SIP AGI requests for the SIP and CALL_API VoiceProducts.
 *
 */

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.amazonaws.********.dynamodbv2.model.GetItemResult;
import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import com.nexmo.voice.core.billing.QuotaUpdateTask;
import com.nexmo.voice.core.billing.QuotaUpdateDetails;
import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse;
import com.nexmo.voice.core.domains.DomainRoutingResponse;
import com.nexmo.voice.core.domains.DomainsServiceClient;
import com.nexmo.voice.core.emergency.EmergencyCallProperties;
import com.nexmo.voice.core.routing.RouteData;
import com.nexmo.voice.core.stirshaken.Attestation;
import com.nexmo.voice.core.stirshaken.AttestationValidationParams;
import com.nexmo.voice.core.callblocking.CallBlockingResponse;
import com.nexmo.voice.core.callblocking.CallBlockingServiceClient;
import com.nexmo.voice.core.stirshaken.EnforcerServiceFactory;
import com.nexmo.voice.core.billing.vquota.VQuotaService;
import com.nexmo.voice.core.types.*;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import io.prometheus.client.Counter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.fastagi.AgiChannel;
import org.asteriskjava.fastagi.AgiException;
import org.asteriskjava.fastagi.AgiRequest;

import com.nexmo.common.api.********.exceptions.ServiceException;
import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.ChargingConfig.CountrySpecificInfo;
import com.nexmo.voice.config.accounts.AccountCapabilitiesConfig;
import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.callinfo.dynamodb.DynamoDB;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.billing.QuotaClient;
import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContext.Builder;
import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.randomize.Randomizer;
import com.nexmo.voice.core.routing.RouteNullPointerException;
import com.thepeachbeetle.common.callback.types.CallbackMethod;
import com.thepeachbeetle.common.msisdn.PhoneNumberTool;
import com.thepeachbeetle.common.msisdn.PhoneNumberTool.BadlyFormattedNumberException;
import com.thepeachbeetle.common.util.StringUtil;
import com.thepeachbeetle.hlr.staticprefixmap.SimpleHlrNetworkPrefixMapLookup.Network;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodes;
import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.CallbackType;
import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.ShortCodeMetaData;
import com.thepeachbeetle.messaging.hub.core.Product;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.exceptions.DropMessageException;
import com.thepeachbeetle.messaging.hub.core.exceptions.PermittedDestinationMisMatchException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RandomPoolNoMatchSoRejectException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RoutingException;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import com.thepeachbeetle.messaging.hub.core.exceptions.UnroutableMTException;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.routing.GenericMTRouter.Route;
import com.thepeachbeetle.********.networks.client.NetworksClient;

import static com.nexmo.voice.core.sip.AsteriskAGIServer.ASTERISK_VERSION;
import static com.nexmo.voice.core.sip.AsteriskAGIServer.getChannelVariable;
import static com.nexmo.voice.core.sip.AsteriskAGIServer.*;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import static java.net.URLDecoder.decode;
import static com.nexmo.voice.core.SipAppUtils.getMappedNumberType;

public class AsteriskAGIServerSIPHandler {

    private final static Logger Log = LogManager.getLogger(AsteriskAGIServerSIPHandler.class);
    private final static BigDecimal ZERO_COST = BigDecimal.ZERO.setScale(8, BigDecimal.ROUND_HALF_UP);
    private final static Counter CALL_SPOOFING_BLOCKS = Counter.build().name("sipapp_call_spoofing_blocks").help("Number of calls that are blocked by call spoofing").register();
    private final static Counter CALLER_UNKNOWN_BLOCKS = Counter.build().name("sipapp_caller_unknown_blocks").help("Number of calls that are blocked due to unknown caller").register();
    public static final String MEDIA_SRTP_PATH_INFIX = "media=srtp";
    public static final String MEDIA_SRTP_PATH_INFIX_ENCODED = "media%3Dsrtp";
    public static final String  TRANSPORT_TLS_PATH_INFIX = "transport=tls";
    public static final String TRANSPORT_TLS_PATH_INFIX_ENCODED = "transport%3Dtls";
    public static final String TRANSPORT_TCP_PATH_INFIX = "transport=tcp";
    public static final String TRANSPORT_TCP_PATH_INFIX_ENCODED = "transport%3Dtcp";
    public static final String TRANSPORT_PATH = "transport=";
    private static final String VOICE_PRODUCT = "voice";
    private static final String BLOCKED = "block";
    private static final Counter CALLBLOCKING_REASON = Counter.build().name("callblocking_reason").labelNames("reason_value").help(" ").register();
    private static final Counter CALLBLOCKING_RESPONSE = Counter.build().name("callblocking_response").labelNames("response_value").help(" ").register();

    private static CallBlockingResponse callBlockingResponse;
    private static final int INITIAL_RETRY_COUNT = 0;
    private static final int INITIAL_RETRY_COUNT_DYNAMODB = 0;
    private static final String DEMO_APP_CLI = "12345678901";
    private static final String VOICE_API_DEMO = "Vonage-APIs-Demo";

    // INBOUND calls with several alternatives of sip destinations are composed of
    // several calls, all of them using the same sessionId.
    // Each attempt creates its own AGI server request.
    // In general the sessionId is used to distinguish between different calls,
    // so we add the attempt number to the sessionId to make it unique

    // OUBOUND calls with several gateway options are composed of one call
    // as the fail over is managed in the call itself by Asterisk.
    // there is only one AGI server request for such call

    protected static void handleRequest(AgiRequest request, AgiChannel channel,
                                        String accountId, String nexmoUUID,
                                        String productClassOverride, VoiceProduct requestedVoiceProduct) throws AgiException, ServiceException {

        Log.info("Received SIP AGI request: {} {} ", request.getRequestURL(), AsteriskAGIServer.getAGIReqParamsAndVars(request, channel));

        // Extract the parameters from the request ...
        Map<String, String[]> requestParams = request.getParameterMap();
        String initialCallerId = null;
        String direction = getRequestParamValue(requestParams, AsteriskAGIServer.DIRECTION);
        String extension = getRequestParamValue(requestParams, AsteriskAGIServer.EXTENSION);
        String uniqueId = getRequestParamValue(requestParams, AsteriskAGIServer.UNIQUE_ID);
        String callerId = getRequestParamValue(requestParams, AsteriskAGIServer.CALLER_ID);
        callerId = callerId.trim();
        initialCallerId = callerId;
        String channelId = getRequestParamValue(requestParams, AsteriskAGIServer.CHANNEL_ID);
        String clientCallId = getRequestParamValue(requestParams, AsteriskAGIServer.CLIENT_CALL_ID);
        String sipDestinationAttemptStr = getRequestParamValue(requestParams, AsteriskAGIServer.ATTEMPT);
        String forcedGateway = getRequestParamValue(requestParams, AsteriskAGIServer.FORCED_GATEWAY);
        //SIP-167: VBC : requestedAppId is an optional parameter. Its presence implies
        //that this is a call to an application. It is available only for INBOUND calls
        String requestedAppId = getRequestParamValue(requestParams, AsteriskAGIServer.APPLICATION_ID);
        //SIP-243: Add an indication for INBOUND call originated from a sip origin.
        String callOrigin = getRequestParamValue(requestParams, AsteriskAGIServer.ORIGIN);
        //SIP-409: Add customer_domain for programmable sip: BYOC
        String customerDomain = getRequestParamValue(requestParams, AsteriskAGIServer.CUSTOMER_DOMAIN);

        String alternativeCallbackUrl = AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CALLBACK_URL));
        String sourceIpHeader = AsteriskAGIServer.normalizeValue(StringUtils.trimToNull(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC)));
        String nexmoTraceId = AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_NEXMO_TRACE_ID));
        String delayQuotaCheckHeader = AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_X_DELAY_Q));
        String customerDomainType = AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_CUSTOMERDOMAIN_TYPE));
        String authenticationSourceHeader = AsteriskAGIServer.normalizeValue(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_AUTHENTICATION_SOURCE));

        if (Objects.isNull(alternativeCallbackUrl) ||
                alternativeCallbackUrl.trim().isEmpty())
            Log.info("{} : Header P-Nexmo-CallbackUrl was not provided", nexmoUUID);

        //attempt is the number of attempts out of the potential sip destinations in the original request.
        //If this is not a sip destination call, the attempt parameter will be empty and we convert it to 0.
        int sipDestinationAttempt = parseAttemptNumber(sipDestinationAttemptStr, nexmoUUID, channel);
        int remainingAttempts = 0;
        boolean isDemoAppCli = callerId.equals(DEMO_APP_CLI);
        boolean isInbound = AsteriskAGIServer.INBOUND_DIRECTION.equalsIgnoreCase(direction);
        callOrigin = findOrigin(callOrigin, requestedAppId, isInbound);

        String callTermination = SipAppUtils.findTerminationType(extension, requestedAppId, isInbound);

        //FF-TALLY: Notice this point: the VoiceContext include both VoiceProduct and String named voiceProductClass which
        // is the name of the voice product as arrived in the AGI request "SIP_HEADER(P-Nexmo-Class)"
        // the VoiceProduct in the VoiceContext is ALWAYS!!!! SIP!!! - the real thing is the String voiceProductClass
        // requestedVoiceProduct

        boolean isExplicitRedirectToApplication = isExplicitRedirectToApplication(callOrigin);
        boolean isVAPIOutboundToVBC = isVAPIOutboundToVBC(isInbound, productClassOverride);
        boolean isSipOriginToLVN = isSipOriginToLVN(callOrigin, customerDomain, isInbound);
        verifyMandatoryInputParams(nexmoUUID, accountId, requestedAppId, customerDomain, customerDomainType, channel);
        boolean isSipOriginToDomain = isSipOriginToDomain(callOrigin, customerDomain, isInbound);


        sipDestinationAttempt = verifySipDestinationAttempt(sipDestinationAttempt, isVAPIOutboundToVBC,
                                                                isSipOriginToDomain, nexmoUUID);

        String sessionId = SipAppUtils.getSessionId(nexmoUUID, sipDestinationAttempt);


        if(Core.getInstance().isShutdownInProgress()){
            Log.warn("{} Shutdown is in progress this call will be rejected", sessionId);
            rejectCallInShutdown(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                    clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, isVAPIOutboundToVBC,
                    "Shutdown In Progress", requestedVoiceProduct, isInbound ? VoiceDirection.INBOUND : VoiceDirection.OUTBOUND);
            return;
        }

        Log.info("SIP request: nexmoCallId param (NEXMOUUID): " + nexmoUUID + " will be handled by sessionId: " + sessionId +
                " nexmoTraceId (********Trace-Id): " + nexmoTraceId);

        SmppAccount account = null;
        String supplierId = ""; //empty string
        DynamoDB dynamoDbClient = Core.getInstance().getDynamoDB();
        boolean isAllowedAnyCallerIdValue = false;
        boolean isAllowedToUseOriginalCLI = false;
        boolean isCallerIdE164 = false;
        boolean isOwnedLvn = false;
        boolean isVerifiedCallerId = false;
        String origCallerId = callerId;
        Optional<String> isOriginalCli = Optional.empty();

        final boolean isOutboundNCCOConnectToDomain = isOutboundNCCOConnectToDomain(sessionId, isInbound, productClassOverride, extension);
        // if the call is NCCO to domain it is programmable SIP
        final boolean isProgrammableSip = isOutboundNCCOConnectToDomain || isProgrammableSip(sessionId, isSipOriginToDomain, isSipOriginToLVN, isInbound, requestedVoiceProduct, extension);
        final boolean isOutboundNCCOConnectToSIPEndpoint = isOutboundNCCOConnectToSIPEndpoint(sessionId, isInbound, requestedVoiceProduct, extension);

        //SIP-409: If it is a call to customerDomain, verify that the provided accountId is matching the application's owner
        //If not, the proper response is set already in the channel, and specific error messages were written to the log.
        // RTC-1868: trunking domains may not use an application but we've already checked for it in verifyMandatoryInputParams
        if (isSipOriginToDomain) {
            if(!Objects.isNull(requestedAppId) && !requestedAppId.isEmpty()) {
                if (!isValidApplicationId(accountId, requestedAppId, customerDomain, sessionId, channel,
                        uniqueId, callerId, extension, isInbound, requestedVoiceProduct,
                        sourceIpHeader, callOrigin, callTermination, isVAPIOutboundToVBC, channelId, clientCallId)) {
                    Log.error("{} provided AccountId {} is not the owner of the requested applicationId {} of customerDomain {}",
                            sessionId, accountId, requestedAppId, customerDomain);
                    return;
                }
            }
        }

        //Get the account
        account = getAccount(accountId, sessionId, channel);

        // properties for an emergency call
        boolean allowCallToEmergencyNumberFromBannedAccount = false;
        boolean allowCallToEmergencyNumberWithoutQuota = false;
        boolean allowCallToEmergencyNumberWithoutParentAccountCheck = false;
        boolean isEmergencyCall = false;
        // check if this is an emergency call
        Optional<EmergencyCallProperties> emergencyCallProperties = isEmergencyCall(account, callerId, extension, sessionId);
        if(emergencyCallProperties.isPresent()) {
            Log.info("{} emergency call: accountId: {} callerId: {} extension: {}",sessionId, accountId, callerId, extension);
            isEmergencyCall = true;
            allowCallToEmergencyNumberFromBannedAccount = Core.getInstance().getConfig().getEmergencyCallingConfig().isAllowFromBannedAccount();
            allowCallToEmergencyNumberWithoutQuota = Core.getInstance().getConfig().getEmergencyCallingConfig().isSkipQuota();
            allowCallToEmergencyNumberWithoutParentAccountCheck = Core.getInstance().getConfig().getEmergencyCallingConfig().isSkipParentAccountLookup();

        } else {
            Log.debug("{} determined not an emergency call.", sessionId);
        }

        if(isEmergencyCall && allowCallToEmergencyNumberFromBannedAccount) {
            Log.info("{} emergency call: accountId: {} callerId: {} extension: {} skipping account status check",sessionId, accountId, callerId, extension);
        } else {
            //SIP-409: Verify the account.
            //If account object is null, it means the Account was not found - the call cannot start. In this case
            //  the proper response is set already in the channel, and specific error messages were written to the log.
            //If the account is banned - the call cannot start as well.
            if (!isAccountAvailable(account, sessionId, uniqueId, channel, callerId, extension, isInbound,
                    requestedVoiceProduct, customerDomain, sourceIpHeader, callOrigin, callTermination, isVAPIOutboundToVBC,
                    channelId, clientCallId, requestedAppId)) {
                Log.warn("{} Call cannot start due to account issues.", sessionId);
                return;
            }
        }

        Log.info("{} SIP request: accountId: {} isInbound: {} callOrigin: {} callTermination: {} productClassHeader: {}  voiceProduct:{} isSipOriginToDomain:{} isSipOriginToLVN:{} isExplicitRedirectToApplication:{} isProgrammableSip:{} isOutboundNCCOConnectToSIPEndpoint:{}",
                sessionId, accountId, isInbound, callOrigin, callTermination, productClassOverride, requestedVoiceProduct.name(), isSipOriginToDomain, isSipOriginToLVN, isExplicitRedirectToApplication, isProgrammableSip, isOutboundNCCOConnectToSIPEndpoint);

        Log.info("{} SIP request: accountId: {} requestedAppId: {}  customerDomain: {}",
                sessionId, accountId, requestedAppId, customerDomain);

        Log.info("{} SIP request: accountId: {} isExplicitRedirectToApplication: {} isSipOriginToDomain: {} isSipOriginToLVN: {} isVAPIOutboundToVBC: {} ",
                sessionId, accountId, isExplicitRedirectToApplication, isSipOriginToDomain, isSipOriginToLVN, isVAPIOutboundToVBC);

        ShortCode callerIdScode = null;
        if (!isInbound) {
            //this is used for outbound calls, this calculation is duplicated for inbound call with callback type = TEL
            isCallerIdE164 = AsteriskAGIServer.isValidNumber(callerId);
            isAllowedAnyCallerIdValue = AsteriskAGIServer.isAllowedAnyCallerIdValue(account, extension);
            callerIdScode = getShortCodeForNumber(callerId);
            supplierId = (Objects.nonNull(callerIdScode) && Objects.nonNull(callerIdScode.getSupplierId())) ? callerIdScode.getSupplierId(): "";
            if (AsteriskAGIServer.isOwnedLvn(account, callerIdScode, callerId, sessionId)) {
                Log.info("{} Account {} owns lvn {} supplierId {}", sessionId, account.getSysId(), callerId, supplierId);
                isOwnedLvn = true;
            }
            if (Objects.nonNull(callerIdScode) && callerIdScode.getShortCodeType() == ShortCodeType.VERIFIED_CLI) {
                isVerifiedCallerId = true;
            }
        }
        // RTC-2201 original cli
        if (isInbound && callOrigin.equalsIgnoreCase(AsteriskAGIServer.CALL_ORIGIN_PSTN)) {
            Log.info(sessionId + " an inbound call from pstn saving in Ddb as account may use original cli");
            dynamoDbClient.putItemAsync(accountId + "_" + callerId, extension, requestedAppId, sessionId);
        }

        //Reject invalid destinations for outbound calls
        // also skip if an emergency call
        if (!isInbound && !isVAPIOutboundToVBC && !isEmergencyCall &&
                !isAcceptableDestination(account, extension, sessionId, uniqueId, channel, callerId, callTermination, channelId, clientCallId, requestedAppId, callOrigin, requestedVoiceProduct)) {
            Log.info("{} Call cannot start due to unacceptable destination", sessionId);
            return;
        }

        // Reject invalid Payments scenario
        if (!isValidPaymentScenario(sessionId, channel, extension, requestedAppId, uniqueId, callerId, callTermination,
                accountId, requestedVoiceProduct, callOrigin, clientCallId, channelId, isVAPIOutboundToVBC)) {
            Log.info("{} Call cannot start due to unacceptable payments scenario", sessionId);
            // build the error context
            set403andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                    clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, isVAPIOutboundToVBC,
                    "Invalid Payments path");
            return;
        }

        //Set some indications regarding demo account (??)
        handleDemoAccountSettings(account, channel);

        //Only for OUTBOUND calls: check if a callerId is owned by the user
        //SIP-282: OUTBOUND calls for vbc-ext of from vbc-app are allowed to set the FROM value (callerId)
        //SIP-2334: OUTBOUND calls of NCCO connect to SIP endpoint are also allowed to set any FROM value
        if (!isInbound && !isVAPIOutboundToVBC) {
            if (!isOutboundNCCOConnectToSIPEndpoint && !isOutboundNCCOConnectToDomain && !isEmergencyCall) {
                callerId = checkIfCallerIdIsValid(callerId, account, isOwnedLvn, isAllowedAnyCallerIdValue, channel, isCallerIdE164);
                Log.info("AccountId: {}  sessionId: {}, will use callerId:{}", accountId, sessionId, callerId);
            }
            // RTC-2201 original cli VOICEN-174
            isOriginalCli = checkIfOriginalCli(isOwnedLvn, sessionId, origCallerId, callerId, account, dynamoDbClient, channel);
            callerId = isOriginalCli.isPresent() ? origCallerId : callerId;
            Log.info("AccountId: {} can use original cli  sessionId: {}, will use callerId:{}", accountId, sessionId, callerId);
            dynamoDbClient.deleteItemAsync(account.getSysId() + "_" + origCallerId);
        }
        else{
            Log.info("SessionId={} Check for caller id validity is ignored, isInbound = {}, isVAPIOutboundToVBC = {}, isOutboundNCCOConnectToSIPEndpoint={}, isEmergencyCall={}, will use callerId={}", sessionId, isInbound, isVAPIOutboundToVBC, isOutboundNCCOConnectToSIPEndpoint, isEmergencyCall, callerId);
        }

        //Verify the number of 'allowed concurrent calls'.
        //It will always return true because of: <check-concurrent-capacity enabled="false" />
        //verifyConcurrentCallsCapacity(accountId, extension, uniqueId, callerId,
        //        channelId, clientCallId, requestedVoiceProduct, sessionId, isInbound, channel);

        forcedGateway = verifyForcedGatewayCapability(forcedGateway, account, isVAPIOutboundToVBC);
        Log.info("SIP Request: sessionId: {} accountId: {} isVAPIOutboundToVBC: {} forcedGateway: {}",
                sessionId, accountId, isVAPIOutboundToVBC, forcedGateway);

        EffectivePrice outboundPrice = null;
        String inboundGatewayId = null;
        String inboundCountryCode = null;
        String inboundMccMnc = null;
        String callbackAddress = null;

        String outboundDestination = extension;

        String applicationId = null;
        String rerouteAddress = null;
        CallbackType callbackType = CallbackType.UNKNOWN;
        ShortCode shortCode = null;
        String outboundCountryCode = null;
        String sourceCountryCode = null;
        String inboundNetworkType = null;
        String inboundNetworkName = null;


        //SIP-167: VBC: This change introduce a new type of call creation:
        //The call can arrive only from VBC (this is controlled in the NET) the INVITE includes
        // a header with the requested application id.
        // the extension (the "dialled" number) is provided as well, but it can be any bogus
        // number and we should not relate to it.
        //
        //If the AGI request is for VBC call it will not include the lvn. Instead applicationId is provided
        //SIP-409 : Programmable sip: additional case of AGI request without lvn. Instead applicationId AND Domain name are provided
        //In such cases the inbound network is null
        // RTC-1868 check requestedAppId has a value instead of isSipOriginToDomain
        if (isInbound && (isExplicitRedirectToApplication || (!Objects.isNull(requestedAppId) && !requestedAppId.isEmpty()))) {
            Log.info("{} Inbound call, using requested application_id {} for account {}, ignoring the extension value. isExplicitRedirectToApplication {} isSipOriginToDomain {}",
                    sessionId, requestedAppId, accountId, isExplicitRedirectToApplication, isSipOriginToDomain);
            applicationId = requestedAppId;
            rerouteAddress = applicationId;
            callbackType = CallbackType.APPLICATION;
            channel.setVariable(AsteriskAGIServer.IB_DEST, rerouteAddress);
            channel.setVariable(AsteriskAGIServer.IB_TYPE, callbackType.getType());
            outboundDestination = rerouteAddress;
            //In this case we also do not relate to the LVN's configured callback URL as we do not
            //have a relevant LVN
            inboundMccMnc = null;
            inboundNetworkType = null;
            inboundNetworkName = null;
            inboundGatewayId = AsteriskAGIServer.DEFAULT_GATEWAY;
        } else if(isOutboundNCCOConnectToDomain) {
            Optional<DomainRoutingResponse> domainRoutingResp = getDomainCallRoutingForNccoCall(extension, sessionId);
            if(domainRoutingResp.isPresent()) {
                if(domainRoutingResp.get().getUris() == null || domainRoutingResp.get().getUris().isEmpty()) {
                    // uh oh, we have a domain without any endpoint URIs
                    Log.info("{} Call cannot start due to domain without SIP endpoints: {}", sessionId, extension);
                    // build the error context
                    String reason = "Domain " + extension + " does not have SIP endpoints";
                    set404andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                            clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, reason);
                    return;
                }
                CallbackType domainDestinationType = domainRoutingResp.get().getForwardsTo();
                String domainDestination = domainRoutingResp.get().getUris().get(0);
                channel.setVariable(AsteriskAGIServer.DOMAIN_DEST, domainDestination);
                channel.setVariable(AsteriskAGIServer.DOMAIN_DEST_TYPE, domainDestinationType.getType());

                // also need to set the customer domain and reroute address fields of the CDR
                // reroute address is URI without scheme
                rerouteAddress = domainRoutingResp.get().getUrisWithoutScheme().get(0);
                // customerDomain is created with the domain name plus suffix added on
                customerDomain = Core.getInstance().getConfig().getDomainsServiceConfig().domainFqdn(domainRoutingResp.get().getDomainName());
                // also override requestedVoiceProduct to CALL_API
                requestedVoiceProduct = VoiceProduct.CALL_API;
                // and override productClassOverride to api
                productClassOverride = VoiceProduct.CALL_API.getDescriptor();
                // and override call termination to sip
                callTermination = "sip";
            } else {
                // uh oh, we didn't find the domain?
                Log.info("{} Call cannot start due to unknown domain: {}", sessionId, extension);
                // build the error context
                String reason = "Invalid domain " + extension + " requested by account " + accountId;
                set404andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                        clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, reason);
                return;
            }
        } else {
            if (isInbound) { //there is no need to get Shortcdoe for extension if not inbound call VOICEN-616
                shortCode = getShortCodeForNumber(extension);
                supplierId = (Objects.nonNull(shortCode) && Objects.nonNull(shortCode.getSupplierId())) ? shortCode.getSupplierId() : "";
            }
        }

        //Regular INBOUND call to an LVN
        if (isInbound && shortCode != null) {
            if (Log.isDebugEnabled())
                Log.debug("{}, This is an Inbound call, Shortcode {} was found for extension {} supplierid {}", sessionId, shortCode, extension, supplierId);

            inboundCountryCode = shortCode.getCountry();
            inboundGatewayId = shortCode.getSupplierId();
            ShortCodeMetaData scMetadata = shortCode.getMetaData();

            //While considering to start the call we need to verify that the customer has enough money.
            //For calculating this, we need to find out the INBOUND leg price and the OUTBOUND leg price.
            //For fetching the price, we need to find out the Network. Hence we are fetching the network
            //for both the INBOUND and OUTBOUND legs.
            //
            //INBOUND  :    If the call origin SIP-Connect or VBC-INBOUND - the Network is null.
            //              On any other case, (PSTN originated) the network is of the LVN.
            //
            //OUTBOUND :    If the LVN -> PSTN : the network is of this PSTN
            //              If the LVN -> SIP : the network is empty
            //              If the LVN -> Application -> PSTN : the network is of this PSTN
            //              If the LVN -> Application -> SIP : the network is empty
            //              If the LVN -> Application -> Stream : there is no outbound side.

            //If the call origin is sip-connect the INBOUND network is empty
            inboundMccMnc = null;
            inboundNetworkType = null;
            inboundNetworkName = null;

            //If the call origin is PSTN (i.e. not sip-connect and not vbc-inbound)
            //the INBOUND network for the dialled LVN:
            if (!isSipOriginToLVN && !isExplicitRedirectToApplication) {
                NetworksClient networksClient = Core.getInstance().getNetworksClient();
                Network network = networksClient != null ? networksClient.lookupNetwork(outboundDestination) : null;
                Network sourceNetwork = networksClient != null ? networksClient.lookupNetwork(callerId): null;

                if (Objects.nonNull(network)) {
                    inboundMccMnc = network.getMccMnc();
                    inboundNetworkType = SipAppUtils.getNetworkType(network);
                    if (sourceNetwork != null && sourceNetwork.getCountryCode() != null)
                        sourceCountryCode = sourceNetwork.getCountryCode();
                    inboundNetworkName = network.getName();
                }
                if (Log.isDebugEnabled())
                    Log.debug("{}, This is an Inbound call. inboundMccMnc network {} found for outboundDestination {}",
                            sessionId, network.toString(), outboundDestination);
            }

            //The following are NOT!!! callback URLs, these are actually the forwarding definitions of the LVN.
            //  It might be a telephone number, sip address or application (VAPI)
            if (hasCallbackType(scMetadata, CallbackType.TEL)) {
                rerouteAddress = scMetadata.getCallbackAddress(CallbackType.TEL);
                callbackType = CallbackType.TEL;

                //calculates details that will be used for 2nd leg outbound call
                isCallerIdE164 = AsteriskAGIServer.isValidNumber(callerId);
                isAllowedAnyCallerIdValue = AsteriskAGIServer.isAllowedAnyCallerIdValue(account, outboundDestination);
                ShortCode scode = getShortCodeForNumber(callerId);
                //TODO confirmation needed from Product in forward pstn if we are to use the supplier id of lvn.
                //supplierId = (Objects.nonNull(scode) && Objects.nonNull(scode.getSupplierId())) ? scode.getSupplierId() : "";
                if (AsteriskAGIServer.isOwnedLvn(account, scode, callerId, sessionId)) {
                    Log.info("{} Account {} owns lvn {} supplier id {}", sessionId, account.getSysId(), callerId, supplierId);
                    isOwnedLvn = true;
                }
                if (Objects.nonNull(scode) && scode.getShortCodeType() == ShortCodeType.VERIFIED_CLI) {
                    isVerifiedCallerId = true;
                }
                // RTC-2201 original cli
                if (extension != null) {
                    boolean validAccountAndCallerId =
                            (Objects.nonNull(account.getSysId()) && !account.getSysId().isEmpty()) &&
                                    (Objects.nonNull(callerId) && !callerId.isEmpty());

                    if (validAccountAndCallerId && !isOwnedLvn) {
                        String diversionHeader = "<sip:" + extension + "@sip.vonage.com>;reason=deflection";
                        Log.info("SESSION-ID ['{}'] OriginalCli for callback type TEL Diversion Header: {}",
                                sessionId, diversionHeader);

                        channel.setVariable(AsteriskAGIServer.DIVERSION_HEADER, diversionHeader);
                        channel.setVariable(AsteriskAGIServer.CALLERID_NAME, origCallerId);
                        channel.setVariable(AsteriskAGIServer.CALLERID_NUM, origCallerId);
                        isOriginalCli = Optional.of(extension);
                    }
                }
                callerId = isOriginalCli.isPresent() ? origCallerId : callerId;
                Log.info("SESSION-ID ['{}'] AccountId: {} will use callerId: {}", sessionId, accountId, callerId);
                dynamoDbClient.deleteItemAsync(account.getSysId() + "_" + origCallerId);

            } else if (hasCallbackType(scMetadata, CallbackType.SIP)) {
                String[] sipCallbackAddresses = scMetadata.getCallbackAddress(CallbackType.SIP).split(",");
                if((sipCallbackAddresses.length == 1)
                        && (AsteriskAGIServerSIPHandler.checkIfPsipDomain(sipCallbackAddresses[0]) != null)) {
                    // if this is a trunking domain, we need to get the URIs for it and figure out the final destination
                    String psipDomainName = AsteriskAGIServerSIPHandler.checkIfPsipDomain(sipCallbackAddresses[0]);
                    DomainRoutingResponse routingResponse = AsteriskAGIServerSIPHandler.getDomainRouting(psipDomainName, extension, sessionId);
                    if(routingResponse.isSuccess()) {
                        // if success, we will get all of the necessary properties from the response
                        callbackType = routingResponse.getForwardsTo();
                        if(CallbackType.SIP.equals(routingResponse.getForwardsTo())) {
                            // if forwardsTo SIP
                            sipCallbackAddresses = routingResponse.getUrisWithoutScheme().toArray(new String[0]);
                            // if we do not set alternatives, we will reject the call
                            rerouteAddress = sipCallbackAddresses[sipDestinationAttempt - 1].trim();
                            remainingAttempts = sipCallbackAddresses.length - sipDestinationAttempt;
                        } else if(CallbackType.APPLICATION.equals(routingResponse.getForwardsTo())) {
                            // if forwards to APPLICATION
                            // forwardAddress will be the application_id and only one
                            rerouteAddress = routingResponse.getUrisWithoutScheme().get(0);
                            // and set the applicationId value
                            applicationId = rerouteAddress;
                        }
                    }
                } else {
                    if (sipCallbackAddresses.length < sipDestinationAttempt) {
                        String msg = "'attempt' value ['" + sipDestinationAttempt + "'] exceeds the number or callback addresses.";
                        Log.error(msg);
                        throw new AgiException(msg);
                    }
                    rerouteAddress = sipCallbackAddresses[sipDestinationAttempt - 1].trim();
                    remainingAttempts = sipCallbackAddresses.length - sipDestinationAttempt;
                    callbackType = CallbackType.SIP;
                }
            } else if (hasCallbackType(scMetadata, CallbackType.APPLICATION)) {
                rerouteAddress = scMetadata.getCallbackAddress(CallbackType.APPLICATION);
                applicationId = rerouteAddress;
                callbackType = CallbackType.APPLICATION;
            }

            if (Log.isDebugEnabled())
                Log.debug("{} rerouteAddress {}  callbackType {}", sessionId, rerouteAddress, callbackType);

            if (rerouteAddress == null)
                throw new AgiException("SessionID ['" + sessionId + "'] :: Shortcode ['" + extension + "'] does not have SIP or TEL callback addresses. Cannot redirect.");

            channel.setVariable(AsteriskAGIServer.IB_DEST, rerouteAddress);
            channel.setVariable(AsteriskAGIServer.IB_TYPE, callbackType.getType());

            outboundDestination = rerouteAddress;
            if (Log.isDebugEnabled())
                Log.debug("sessionId: {} outboundDestination was updated from the rerouteAddress to be {}", sessionId, outboundDestination);


            //This is a REAL callback definition, usually connected to an LVN when the redirect is to a real
            // phone number.
            callbackAddress = scMetadata.getCallbackAddress(CallbackType.VOICE_STATUS);
            if (Log.isDebugEnabled())
                Log.debug("SessionID " + sessionId + " extention " + extension + "voice-status callback address " + callbackAddress);
        } //end of specific section about inbound call to a real number

        //Create the call context
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        
        // also enbable skip quota if emergency call
        final boolean voiceSkipQuota = AsteriskAGIServer.isVoiceSkipQuota(account, isEmergencyCall, allowCallToEmergencyNumberWithoutQuota);
        final boolean isVpricingEnabled = VQuotaService.isVpricingEnabled(account);

        VoiceContext.Builder voiceCtxBuilder = new Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                                                                        .withFrom(callerId)
                                                                        .withTo(extension)
                                                                        .withAccountId(accountId)
                                                                        .withAccountPricingGroup(account.getQuotaPricingGroupId())
                                                                        .withMasterAccountId(account.getMasterAccountId())
                                                                        .withMasterAccountPricingGroup(SipAppUtils.getAccountPricingGroup(account.getMasterAccountId(), sessionId))
                                                                        .withVoiceDirection(isInbound ? VoiceDirection.INBOUND : VoiceDirection.OUTBOUND)
                                                                        .withProductClass(requestedVoiceProduct.getDescriptor())
                                                                        .withGateway(inboundGatewayId)
                                                                        .withCallbackUrl(callbackAddress)
                                                                        .withCallbackMethod(account.getHttpForwardMethod() == null ? CallbackMethod.getMatching("GET") : account.getHttpForwardMethod())
                                                                        .withQuotaRef(SipAppUtils.generateQuotaReference(sessionId, isInbound))
                                                                        .withCustomerDomain(customerDomain)
                                                                        .withCustomerDomainType(customerDomainType)
                                                                        .withRequestIp(sourceIpHeader)
                                                                        .withCallOrigin(callOrigin)
                                                                        .withCallTermination(callTermination)
                                                                        .withIsVAPIOutboundToVBC(isVAPIOutboundToVBC)
                                                                        .withAsteriskVersion(AsteriskVersion.from(sessionId, getChannelVariable(sessionId, ASTERISK_VERSION, channel)))
                                                                        .withVoiceSkipQuota(voiceSkipQuota)
                                                                        .withClientCallId(clientCallId)
                                                                        .withIsOutboundProgrammableSip(isOutboundNCCOConnectToDomain || isOutboundProgrammableSip(sessionId, isInbound, requestedVoiceProduct, extension))
                                                                        .withIsSipOriginToLVNToApplication(isSipOriginToLVN && VoiceProduct.CALL_API.equals(requestedVoiceProduct))
                                                                        .withIsOutboundNCCOConnectToDomain(isOutboundNCCOConnectToDomain)
                                                                        .withVpricingEnabled(isVpricingEnabled);



        //Add alternative callback_url if provided - this header is added by the NET for calling applications.
        //It let SIPApp know to which ngnix it should return a callback on the call completion.
        //this is not for customers usage.
        if (StringUtils.isNotBlank(alternativeCallbackUrl)) {
            if (Log.isTraceEnabled())
                Log.trace("alternativeCallbackUrl property value is: " + alternativeCallbackUrl);

            voiceCtxBuilder.withInternalCallbackUrl(alternativeCallbackUrl);
            // TODO(lpedrosa) make it configurable through the sip header
            voiceCtxBuilder.withInternalCallbackMethod(CallbackMethod.getMatching("GET"));
        }
        if (VoiceProduct.VSPS.getDescriptor().equalsIgnoreCase(productClassOverride)) {
            voiceCtxBuilder.withPaymentRoute(VoiceProduct.VSPS.getDescriptor());
        }


        String outboundMccMnc = null;
        String outboundNetworkType = null;
        String outboundNetworkName = null;
        String outboundGatewayId = forcedGateway;
        String outboundAltGateways = null;
        String forceSender = null;

        // Retrieve network if needed for a call to a real number
        if (isVAPIOutboundToVBC) {
            //SIP-282: Adding the VAPI-To-VBC outbound option. In this case, we have the gateway forced.
            //the forcedGateway is already set as destination, as received in the request. Set the list as well
            //This is required for the CDR creation
            outboundAltGateways = outboundGatewayId;

        } else if (isEmergencyCall && emergencyCallProperties.isPresent()) {
            // set outboundGatewayId and outboundAltGateways from EmergencyCallProperties
            outboundGatewayId = emergencyCallProperties.get().getRouteGatewayIds().get(0);
            outboundAltGateways = String.join(",", emergencyCallProperties.get().getRouteGatewayIds());
            // do we need to set these?
            // outboundMccMnc
            // outboundNetworkName
            outboundNetworkType = EmergencyCallProperties.EMERGENCY_CALLING_DEST_NETWORK_TYPE;
            outboundCountryCode = emergencyCallProperties.get().getDestinationCountryCode();
            sourceCountryCode = emergencyCallProperties.get().getSourceCountryCode();
            // set isEmergencyCall in voice context
            voiceCtxBuilder.withIsEmergencyCall(isEmergencyCall);
        } else {

            //At this point the outboundDestination value depends on the LVN's configuration:
            //It can be one of the following:
            //  real telephone number - that will include only numbers and will create a real call to it
            //  sip address - this includes the @ hence not just numbers
            //  application_id - this includes "-" hence not just a number
            //
            if (Log.isDebugEnabled())
                Log.debug("{} isInbound {} outboundDestination {} isVAPIOutboundToVBC {}",
                        sessionId, isInbound, outboundDestination, isVAPIOutboundToVBC);
            //If the final call destination is PSTN - we need to find its outbound Network
            if (StringUtil.containsOnlyNumbers(outboundDestination)) {
                NetworksClient networksClient = Core.getInstance().getNetworksClient();
                Network network = networksClient != null ? networksClient.lookupNetwork(outboundDestination) : null;
                Network sourceNetwork = networksClient != null ? networksClient.lookupNetwork(callerId): null;
                if (network != null) {
                    outboundMccMnc = network.getMccMnc();
                    outboundNetworkType = SipAppUtils.getNetworkType(network);
                    outboundNetworkName = network.getName();
                    outboundCountryCode = network.getCountryCode();
                    //RTC-3018 write outbound country to CDRs even when rejected for blocking rules.
                    if (StringUtils.isNotBlank(outboundCountryCode) ) {
                        voiceCtxBuilder.withCountryCode(outboundCountryCode);
                    }
                    if (sourceNetwork != null && sourceNetwork.getCountryCode() != null)
                        sourceCountryCode = sourceNetwork.getCountryCode();
                    if (Log.isDebugEnabled())
                        Log.debug("{} isInbound {} found outboundMccMnc network {} for outboundDestination {}",
                                sessionId, isInbound, network.toString(), outboundDestination);
                }

                if (!isInbound && StringUtils.isNotBlank(callTermination) && StringUtils.isNotBlank(outboundCountryCode) && callTermination.equalsIgnoreCase("pstn") && outboundCountryCode.equalsIgnoreCase("SG")
                ) {
                    callerId = callerId.replace("+", "");
                    callerId = callerId.trim();
                    String blockedPrefix = Core.getInstance().checkCallerIdHasBlockedPrefixForDestination(callerId, outboundCountryCode, account.getSysId());

                    if (StringUtils.isNotBlank(blockedPrefix)) {
                        String reason = "Call cannot start as " + callerId + " matches/contains blocked prefix " + blockedPrefix + " in " + outboundDestination + " (" + outboundCountryCode + ") block list for account " + account.getSysId() + " call spoofing.";
                        Log.warn(reason);

                        // Increment call spoofing metric counter
                        CALL_SPOOFING_BLOCKS.inc();

                        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, null, null, false, remainingAttempts);
                        voiceCtxBuilder.withApplicationContext(errorAppContext);
                        voiceCtxBuilder.withVoiceProduct(requestedVoiceProduct);
                        voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_CALLID_TO_SG_SPOOF);
                        voiceCtxBuilder.withCountryCode(outboundCountryCode);
                        voiceCtxBuilder.withSourceCountryCode(sourceCountryCode);
                        storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, reason, SIPCode.FORBIDDEN, channel);
                        return;
                    }
                }

                //CALL-BLOCKING RULES
                if ((!isInbound && StringUtils.isNotBlank(callTermination) && callTermination.equalsIgnoreCase("pstn")) || (isInbound && shortCode != null)) {
                    callBlockingResponse = getCallBlockingResponse(sessionId, uniqueId, channelId, nexmoUUID, clientCallId, callerId, outboundDestination, voiceCtxBuilder, account, requestedVoiceProduct, channel, remainingAttempts);
                    if (callBlockingResponse != null) {
                        String action = callBlockingResponse.getAction();
                        String id = callBlockingResponse.getId();
                        voiceCtxBuilder.withId(id)
                            .withBlockingSubsystem(callBlockingResponse.getSubsystem());
                        CompletableFuture.runAsync(() -> {
                            CALLBLOCKING_REASON.labels(action).inc();
                        });
                        if (StringUtils.isNotBlank(action) && BLOCKED.equalsIgnoreCase(action))
                            return;
                    }
                }

                // ROUTING...
                if (outboundGatewayId == null) {
                    Route route = null;
                    String originationAddress = callerId;

                    Log.info("SESSION-ID ['{}'] Search with key apiKey_callerId {} and originalCLI {}",
                            sessionId, account.getSysId() + "_" + callerId, origCallerId);

                    if (isOriginalCli.isPresent()) {
                        supplierId = "";
                        String inboundFromNumber = isOriginalCli.get();
                        Log.info("SESSION-ID ['{}'] Extension for outbound {} ", sessionId, inboundFromNumber);

                        Network callerIdNetwork = networksClient != null ? networksClient.lookupNetwork(origCallerId) : null;
                        String inboundCallerCountryCode = callerIdNetwork != null ? callerIdNetwork.getCountryCode() : null;

                        Network inboundCalledNetwork = networksClient != null ? networksClient.lookupNetwork(inboundFromNumber) : null;
                        String inboundCalledCountryCode = inboundCalledNetwork != null ? inboundCalledNetwork.getCountryCode() : null;
                        if (inboundCallerCountryCode != null && inboundCallerCountryCode.equals(inboundCalledCountryCode) &&
                                inboundCalledCountryCode.equals(outboundCountryCode)) {
                            originationAddress = inboundFromNumber;
                            ShortCode shortCodeInboundFromNumber = getShortCodeForNumber(inboundFromNumber);
                            supplierId = (Objects.nonNull(shortCodeInboundFromNumber) && Objects.nonNull(shortCodeInboundFromNumber.getSupplierId()))
                                    ? shortCodeInboundFromNumber.getSupplierId(): "";
                            Log.info("SESSION-ID ['{}'] Domestic call routing will be based on diversion: {} supplierId:{}"
                                    , sessionId, originationAddress, supplierId);
                        }
                    }

                    RouteData supplierIdRoute = lookupRoute(sessionId, account, accountId, supplierId, outboundDestination, outboundMccMnc);
                    RouteData oaPrefixRoute = lookupRoute(sessionId, account, accountId, originationAddress, outboundDestination, outboundMccMnc);
                    RouteData routeData = AsteriskAGIServer.decideRoute(supplierIdRoute, oaPrefixRoute);

                    if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL) {
                        Exception e = routeData.getExc();
                        Log.warn("{} Failed to find a route for outbound destination {} on netwrok {} for account {} due to {} {}", sessionId, outboundDestination, outboundMccMnc, accountId, e.getMessage(), e.getClass().getName());
                        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, null, null, false, remainingAttempts);
                        voiceCtxBuilder.withApplicationContext(errorAppContext);
                        voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
                        storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, "UNROUTABLE", SIPCode.FORBIDDEN, channel);
                        throw new ServiceException("Failed to find a route for outbound destination", e);

                    } else if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.DROPMESSAGE) {
                        DropMessageException e = (DropMessageException) routeData.getExc();
                        long droppingRoutingRuleSeq = e.getRoutingRuleSequence();
                        Log.warn("{} Route {} DROPPING the call for outbound destination {} on netwrok {} for account {} due to {} {}",
                                sessionId, droppingRoutingRuleSeq, outboundDestination, outboundMccMnc, accountId, e.getMessage(), e.getClass().getName());

                        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, null, null, false, remainingAttempts);
                        voiceCtxBuilder.withApplicationContext(errorAppContext);
                        voiceCtxBuilder.withSequenceNumber(droppingRoutingRuleSeq);
                        voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_DROPPING_ROUTE);
                        voiceCtxBuilder.withNetwork(outboundMccMnc);
                        storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, "UNROUTABLE", SIPCode.FORBIDDEN, channel);
                        throw new ServiceException("Route DROPPING the call", e);
                    } else if (routeData.getRouteExceptionType() == RouteData.RouteExceptionType.ROUTENULL) {
                        Exception e = routeData.getExc();
                        Log.warn("{} No Route NullPointer DROPPING the call for outbound destination {} on network {} for account {} due to {} {}",
                                sessionId, outboundDestination, outboundMccMnc, accountId, e.getMessage(), e.getClass().getName());

                        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, null, null, false, remainingAttempts);
                        voiceCtxBuilder.withApplicationContext(errorAppContext);
                        voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
                        voiceCtxBuilder.withNetwork(outboundMccMnc);
                        storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, "UNROUTABLE", SIPCode.FORBIDDEN, channel);
                        throw new ServiceException("Failed to find a route for outbound destination", e);
                    }
                    route = routeData.getRoute();
                    outboundGatewayId = route.getGatewayId();
                    outboundAltGateways= createOutboundAltGateways(outboundGatewayId, getAlternativeGateways(route));
                    MtRoutingRule rule = route.getRule();
                    if (rule != null) {
                        voiceCtxBuilder.withSequenceNumber(rule.getSeq());
                        voiceCtxBuilder.withRoutingGroup(rule.getRoutingGroupId());
                        voiceCtxBuilder.withRoutingOa(rule.getOa());
                        voiceCtxBuilder.withRoutingBindId(rule.getBindId());
                    }
                    forceSender = route.getForcedSender();
                    if (forceSender != null) {
                        channel.setVariable(AsteriskAGIServer.CALLERID_NAME, forceSender);
                        channel.setVariable(AsteriskAGIServer.CALLERID_NUM, forceSender);
                        if (isAllowedAnyCallerIdValue && callerId != initialCallerId) {
                            //VOICEN-141 put back the callerid to initial as we use forcesender to make a call
                            // we dont want to write unknown in the CDR if they used any invalid CLI
                            callerId = initialCallerId;
                        }
                    } else if (Core.getInstance().isBlockUnknownCLIFlag()) {
                        //PREQ-3741: Prevent calls with Unknown CLI for outgoing PSTN calls
                        Log.info("{} Call sender is not forced by routing rule, checking for \"Unknown\" CallerId {}", sessionId, callerId);
                        if (!isInbound
                                && null != callerId
                                && (callerId.equalsIgnoreCase("unknown"))
                                && StringUtils.isNotBlank(callTermination)
                                && callTermination.equalsIgnoreCase("pstn")) {
                            //caller id can be unknown in below cases
                            //if it is a demo account;
                            //if disable-must-own-lvn capability is enabled;
                            if (isDemoAppCli && isValidDemoCall(account, extension)) {
                                    Log.info("{} CallerId={} Destination={} accountId={} can be Unknown as it is MakeAVoiceTTS demo call for a valid demo account ", sessionId, callerId, extension, accountId);
                                    String demo_cli = Randomizer.getInstance().randomizeSenderAddressForMakeAVoiceCall(extension);
                                    if (demo_cli != null && !demo_cli.isEmpty()) {
					callerId = VOICE_API_DEMO;
                                        channel.setVariable(AsteriskAGIServer.CALLERID_NAME, VOICE_API_DEMO);
                                        channel.setVariable(AsteriskAGIServer.CALLERID_NUM, demo_cli);
                                    } else {
                                        Log.info("{} Could not get random pool number to use instead of Unknown and Call cannot start due to \"Unknown\" CallerId {}", sessionId, callerId);
                                        CompletableFuture.runAsync(() -> {
                                            CALLER_UNKNOWN_BLOCKS.inc();
                                        });
                                        // callerId cannot be unknown, so rejecting the call
                                        String reason = "\"Unknown\" CLI not allowed";
                                        set403andBuildErrorContextForBlockingUnknownCLI(extension, sessionId, uniqueId, channel,
                                                callerId, callTermination, channelId, clientCallId, requestedAppId, callOrigin,
                                                requestedVoiceProduct, accountId, reason);
                                        return;
                                    }
                            } else if (StringUtils.isNotBlank(outboundCountryCode) && outboundCountryCode.equalsIgnoreCase("DE")) {
                                Log.info("{} CallerId={} can be Unknown as we allow unknowns to Germany destination {} because of DT project outboundCountryCode {}",
                                        sessionId, callerId, extension, outboundCountryCode);
                            }else {
                                Log.info("{} Call cannot start due to \"Unknown\" CallerId {}", sessionId, callerId);
                                CompletableFuture.runAsync(() -> {
                                    CALLER_UNKNOWN_BLOCKS.inc();
                                });
                                // callerId cannot be unknown, so rejecting the call
                                String reason = "\"Unknown\" CLI not allowed";
                                set403andBuildErrorContextForBlockingUnknownCLI(extension, sessionId, uniqueId, channel,
                                        callerId, callTermination, channelId, clientCallId, requestedAppId, callOrigin,
                                        requestedVoiceProduct, accountId, reason);
                                return;
                            }
                        }
                    }
                }
            } else {
                //Here we handle the cases where the final call destination is not PSTN.
                //In such case the gateway is 'default' and the outbound network is null
                outboundGatewayId = AsteriskAGIServer.DEFAULT_GATEWAY;
            }

            int callTimeout = AsteriskAGIServer.getCallTimeout(account);
            if (callTimeout > 0) {
                channel.setVariable(AsteriskAGIServer.SIP_ABSOLUTE_TIMEOUT, String.valueOf(callTimeout));
            }

        }
        if (Log.isDebugEnabled())
            Log.debug("{} call direction: {} outboundDestination {} outboundGatewayId {} outboundAltGateways {} outboundMccMnc {} inboundMccMnc {}",
                    sessionId, direction, outboundDestination, outboundGatewayId, outboundAltGateways, outboundMccMnc, inboundMccMnc);

        logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);

        //This is the FIRST leg setup.
        //auxiliary = !isInbound Meaning: OUTBOUND => auxiliary=true
        //                                INBOUND => auxiliary=false
        //applicationId: the name of the application to route the call to.
        //               it will be null if we do NOT forward this call to an application
        String sipAstContextCallbackAddress = outboundDestination.equals(extension) ? null : outboundDestination;
        if(isOutboundNCCOConnectToDomain) {
            // if this is a NCCO to domain call, use the reroute address
            sipAstContextCallbackAddress = rerouteAddress;
        }
        SIPAsteriskContext applicationContext = new SIPAsteriskContext(channelId,
                                                                       clientCallId,
                                                                       sipAstContextCallbackAddress,
                                                                       outboundGatewayId,
                                                                       !isInbound,
                                                                       remainingAttempts,
                                                                       applicationId); // in OUTBOUND product, first leg is auxiliary.

        Log.debug("{} isInbound {} about to build context with inboundMccMnc {} or outboundMccMnc {}",
                sessionId, isInbound, inboundMccMnc, outboundMccMnc);

        if (isInbound) {
            String numberType = null;
            String callType = null;

            // numberType, callType are for PSTN calls
            if(!AsteriskAGIServer.DEFAULT_GATEWAY.equals(inboundGatewayId)) {
                callType = SipAppUtils.determineCallType(sourceCountryCode, inboundCountryCode);
                if(Objects.nonNull(shortCode)) {
                    numberType = getMappedNumberType(shortCode.getShortCodeType());
                }
            }

            voiceCtxBuilder.withGateway(inboundGatewayId)
                .withNetwork(inboundMccMnc)
                .withNetworkType(inboundNetworkType)
                .withNetworkName(inboundNetworkName)
                .withCountryCode(inboundCountryCode)
                .withStirShaken(getChannelVariable(sessionId, AsteriskAGIServer.VERSTAT, channel))
                .withSourceCountryCode(sourceCountryCode)
                .withCallType(callType)
                .withNumberType(numberType);

            if (Objects.nonNull(applicationId)) {
                Application application = Application.lookup(applicationId);
                if (application != null) {
                    voiceCtxBuilder.withPaymentEnabled(application.getPaymentEnabled());
                    voiceCtxBuilder.withRegion(application.getRegion());
                }
            }
            if (CallbackType.TEL.equals(callbackType)) {
                // isVerifiedCallerId flag is set for BYON, in this case we want the attestation level as B and
                // have to send the isOwnedLvn as false.
                Attestation attestationLevel = calculateOutboundAttestationLevel(sessionId, account, requestedVoiceProduct, forcedGateway, 
				                                                 isVAPIOutboundToVBC, isAllowedAnyCallerIdValue, isCallerIdE164, 
                                                                                 isOwnedLvn, outboundCountryCode, outboundGatewayId, forceSender, 
                                                                                 isVerifiedCallerId);
                Log.info("{} Outbound Stir Shaken is {}, will be stored temporarily", sessionId, attestationLevel);
                channel.setVariable(AsteriskAGIServer.ATTESTATION_LEVEL, attestationLevel.getRepresentation());
                voiceCtxBuilder.withTemporaryOutboundStirShaken(attestationLevel.getRepresentation());
            }

        } else {
            String initialSourceCountryCode = sourceCountryCode;
            ShortCode initialScode = callerIdScode;
            String numberType = null;
            String callType = null;

            // PREQ_4786, PREQ-4911, PREQ-5039
            // numberType, callType are for PSTN calls
            if (!AsteriskAGIServer.DEFAULT_GATEWAY.equals(outboundGatewayId)) {
                if (Objects.nonNull(forceSender)) { // handle force-sender for outbound PSTN call-leg
                    callerIdScode = getShortCodeForNumber(forceSender);
                    NetworksClient networksClient = Core.getInstance().getNetworksClient();
                    Network sourceNetwork = networksClient != null ? networksClient.lookupNetwork(forceSender): null;
                    if (sourceNetwork != null && sourceNetwork.getCountryCode() != null) {
                        sourceCountryCode = sourceNetwork.getCountryCode();
                    }
                }

                callType = SipAppUtils.determineCallType(sourceCountryCode, outboundCountryCode);

                if (Objects.isNull(forceSender) && isOriginalCli.isPresent()) {
                    Log.info("AccountId={} is using the original CLI from the inbound leg; setting numberType to {}", accountId, SipAppUtils.NumberType.CLIVerified);
                    numberType = SipAppUtils.NumberType.CLIVerified.name();
                }

                if (numberType == null && Objects.nonNull(callerIdScode)) {
                    numberType = getMappedNumberType(callerIdScode.getShortCodeType());
                }

                if (numberType == null && (isAllowedAnyCallerIdValue || Objects.nonNull(forceSender))) {
                    numberType = SipAppUtils.NumberType.CLIUnverified.name();
                }

                if (numberType == null) {
                    numberType = SipAppUtils.NumberType.CLIMissing.name();
                }
            }

            voiceCtxBuilder.withGateway(outboundGatewayId)
                .withNetwork(outboundMccMnc)
                .withNetworkType(outboundNetworkType)
                .withNetworkName(outboundNetworkName)
                .withCountryCode(outboundCountryCode)
                .withSourceCountryCode(sourceCountryCode)
                .withCallType(callType)
                .withNumberType(numberType);

            sourceCountryCode = initialSourceCountryCode; // write back the initial souceCountryCode
            callerIdScode = initialScode; // write back the initial callerIdScode

            Attestation attestationLevel = calculateOutboundAttestationLevel(sessionId, account, requestedVoiceProduct, forcedGateway, 
                                                                             isVAPIOutboundToVBC, isAllowedAnyCallerIdValue, isCallerIdE164, 
                                                                             isOwnedLvn, outboundCountryCode, outboundGatewayId, forceSender, 
                                                                             isVerifiedCallerId);
            Log.info("{} Outbound Stir Shaken is {}", sessionId, attestationLevel);
            channel.setVariable(AsteriskAGIServer.ATTESTATION_LEVEL, attestationLevel.getRepresentation());
            voiceCtxBuilder.withStirShaken(attestationLevel.getRepresentation());

        }

        voiceCtxBuilder.withForcedSender(forceSender).withApplicationContext(applicationContext)
                .withSrtpEnabled(checkSrtpEnabled(sessionId, channel, rerouteAddress))
                .withSipTransport(getSipTransport(sessionId, channel, rerouteAddress));


        // CLI to use for Caller ID ("From:"), P-Asserted-Identity and source-prefix pricing
        String cli = callerId;
        if (Objects.nonNull(forceSender))
            cli = forceSender;


        //Retrieve matrixes and configuration ...

        //TODO Tally: All these config verifications can be done once during statrtup - in any case we cannot
        //            do anything without them - if they are not there core should throw the exception and set all
        //            possible alarms
        PriceMatrixList outboundPriceMatrix = Core.getInstance().getConfig().getMtPriceMatrixList();
        if (outboundPriceMatrix == null)
            throw new AgiException("Could not build context. Outbound price matrix was null!");
        PriceMatrixList inboundPriceMatrix = Core.getInstance().getConfig().getMoPriceMatrixList();
        if (inboundPriceMatrix == null)
            throw new AgiException("Could not build context. Inbound price matrix was null!");
        ChargingConfig chargingConfig = Core.getInstance().getConfig().getChargingConfig();
        if (chargingConfig == null)
            throw new AgiException("No Charging Config specified!! Aborting ...");
        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig(); // Can be null
        //If outboundCountryCode is null (this will happen for application or VBC) we get back
        //the configured defaults for increments counters
        final CountrySpecificInfo specificInfo = chargingConfig.getCountrySpecificInfo(outboundCountryCode);
        long firstChargedSeconds = specificInfo.getMinIncrement();
        long quotaUpdatesInterval = specificInfo.getRecurringIncrement();

        // Inbound price
        // Inbound price would be decided based on the LVN or ZERO if there is no LVN (VBC Dial in to application)
        // Inbound price for Sip-originated but not directed to application scenario is the default configure price (sip origin to lvn)
        // Inbound price for programmable sip uses the price configured for the account if set otherwise uses the default set SIP product calls
        EffectivePrice inboundPrice = null;
        if (isInbound) {
            //get price for programmable sip and pstn calls
            if (isProgrammableSip || !isSipOriginToLVN) {
                if (Log.isDebugEnabled())
                    Log.debug("PSTN/PROGRAMMABLE INBOUND CALL for SessionID " + sessionId + " using inboundPriceMatrix" +
                            " extension " + extension + " requestedAppId " + requestedAppId + " inboundMccMnc " + inboundMccMnc);
                inboundPrice = EffectivePrice.getPriceFromMatrixOrDefault(inboundPriceMatrix,
                                                                          account,
                                                                          extension,
                                                                          cli,
                                                                          requestedAppId,
                                                                          inboundMccMnc,
                                                                          defaultPrice(chargingConfig, isProgrammableSip),
                                                                          false, //isVAPIOutboundToVBC always false on INBOUND calls,
                                                                          prefixMapConfig,
                                                                          chargingConfig,
                                                                          requestedVoiceProduct,
                                                                          "inbound-price",
                                                                            isProgrammableSip,
                                                                          isEmergencyCall);
            } else {
                if (Log.isDebugEnabled())
                    Log.debug("{} SIP originated INBOUND CALL", sessionId);
                inboundPrice = new EffectivePrice(chargingConfig.getDefaultSipDialInPrice(), "default-sip-originated-price");
            }
        }


        // Outbound price (we want it for the balance check)

        //For SIP-287 we added price for both inbound and outbound sip legs.
        //The sip leg price is taken from the configured defaults iff not Programmable Sip
        //Otherwise the one set in the account is used
        if (outboundPrice == null) {
            if (Log.isDebugEnabled())
                Log.debug("SessionID " + sessionId + " isInbound " + isInbound + " using outboundPriceMatrix" +
                        " outboundDestination " + outboundDestination + " outboundMccMnc " + outboundMccMnc);

            outboundPrice = EffectivePrice.getPriceFromMatrixOrDefault(outboundPriceMatrix,
                                                                       account,
                                                                       outboundDestination,
                                                                       cli,
                                                                       null,
                                                                       outboundMccMnc, //no REQUESTED appId in case of outbound calls
                                                                       chargingConfig.getDefaultSipPrice(),
                                                                       isVAPIOutboundToVBC,
                                                                       prefixMapConfig,
                                                                       chargingConfig,
                                                                       requestedVoiceProduct,
                                                                       "outbound-price-for-balance-check",
                                                                        isProgrammableSip,
                                                                       isEmergencyCall);
        }
        if (Log.isDebugEnabled()) {
            if (Objects.isNull(inboundPrice))
                Log.debug("sessionId: " + sessionId + " returned inboundPrice is null - converting to zero");
            if (Objects.isNull(outboundPrice))
                Log.debug("sessionId: " + sessionId + " returned outboundPrice is null - converting to zero");
        }
        outboundPrice = outboundPrice != null ? outboundPrice : new EffectivePrice(BigDecimal.ZERO, "missing-outbound-price");
        inboundPrice = inboundPrice != null ? inboundPrice : new EffectivePrice(BigDecimal.ZERO, "missing-inbound-price");

        if (Log.isDebugEnabled())
            Log.debug("Account: {} sessionId: {} outboundPrice: {} inboundPrice: {} isInbound {}",
                    account.getSysId(), sessionId, outboundPrice, inboundPrice, isInbound);

        // Balance check ...
        BigDecimal quotaDisabledPrice = null;

        try {
            if(voiceSkipQuota && Log.isDebugEnabled()){
                Log.debug("sessionId {}, accountId {} Balance Check will be skipped as this was marked as voice skip quota", sessionId, accountId);
            }
            if (isVAPIOutboundToVBC && Log.isDebugEnabled()) {
                Log.debug("sessionId {}, accountId {} Balance Check will be skipped as this was marked as outbound to VBC", sessionId, accountId);
            }
            if(!voiceSkipQuota && !isVAPIOutboundToVBC){
                if (Log.isDebugEnabled())
                    Log.debug("Account: {} sessionId: {} about to check minimum balance", account.getSysId(), sessionId);

                AccountBalance accountBalance = checkBalanceRestrictions(account,
                        outboundPrice.getPrice().add(inboundPrice.getPrice()),
                        2 * firstChargedSeconds, isVpricingEnabled, voiceCtxBuilder, uniqueId, inboundPrice.getPrefix());
                if (accountBalance != null && !accountBalance.isQuotaEnabled()) {
                    if (Log.isDebugEnabled())
                        Log.debug("{} Quota is disabled for account {}. Setting forced price to 0.", sessionId, account.getSysId());
                    quotaDisabledPrice = BigDecimal.ZERO;
                }
            }
        } catch (NotEnoughBalanceException ex) {
            Log.info("{} Could not create a call. Not enough balance for accountId: {}", sessionId, accountId);

            //Check if we can ignore this - available only in the testing systems, just so we will block the call on the BridgeEvent
            if (!AsteriskAGIServer.shouldIgnoreMissingBalance(sessionId, accountId, delayQuotaCheckHeader)) {
                voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);
                storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, "BALANCE CHECK", SIPCode.PAYMENT_REQUIRED, channel);
                return;
            } else {
                Log.warn("{} {} ignoring the missing balance on the AGI stage. WIll be checked again on the BridgeEvent. THIS IS ALLOWED ONLY IN TEST ENV",
                        sessionId, accountId);
            }
        } catch (QuotaDisabledException e) {
            if (Log.isDebugEnabled())
                Log.debug("{} Quota is disabled for account {}. Setting forced price to 0.", sessionId, accountId, e);
            quotaDisabledPrice = BigDecimal.ZERO;
        }

        // COST
        EffectiveCost inboundCost = new EffectiveCost(chargingConfig.getDefaultInboundCost(), "default-inbound-cost");
        if (isEmergencyCall) {
            inboundCost = new EffectiveCost(BigDecimal.ZERO, "emergency-call-cost");
        }
        if (isInbound) {
            if (Log.isDebugEnabled())
                Log.debug("{} handling the INBOUND cost. isExplicitRedirectToApplication {}", sessionId, isExplicitRedirectToApplication);

            // TODO Tally: The below is to keep the old implementation as it was and just add the support for the cases where
            // the call is directed to an application not via an lvn
            // It would be good to investigate it, as I think it is actually needed only for redirection
            // to a real number. There is no need to get the GW if the LVN is redirected to sip or an application
            if (!isExplicitRedirectToApplication) {
                // If the origin of the inbound call is SIP (i.e. calling from a SIP origin and not PSTN) - the cost is zero.
                if (isSipOriginToLVN || isSipOriginToDomain) {
                    if (Log.isDebugEnabled())
                        Log.debug("sessionId: " + sessionId + " SIP originated INBOUND call. Cost is zero. ");

                    inboundCost = new EffectiveCost(BigDecimal.ZERO, "default-sip-originated-cost");
                } else { // Else, find the cost from the gw
                    GatewayInfoMatrixConfig gwInfoMatrixConfig = Core.getInstance().getConfig()
                            .getSIPGatewayInfoMatrixConfig();
                    SupplierMappingConfig gwInfo = gwInfoMatrixConfig == null ? null
                            : gwInfoMatrixConfig.getGatewayInfo(inboundGatewayId);
                    PriceMatrixList inboundCostMatrix = gwInfo == null ? null : gwInfo.getInboundCostMatrix();
                    if (gwInfo == null) {
                        if (AsteriskAGIServer.DEFAULT_GATEWAY.equals(inboundGatewayId))
                            Log.info("Inbound gateway is 'default' - using default cost for account "
                                    + account.getSysId());
                        else {
                            if (!account.getSysId().equals(inboundGatewayId)) // SIP-221
                                Log.error("Could not find gateway info for inbound gateway " + inboundGatewayId
                                        + " WILL USE DEFAULT COST! for account " + account.getSysId());
                        }
                    }
                    inboundCost = EffectiveCost.getCostFromMatrixOrDefault(
                            inboundCostMatrix, account, extension, cli,
                            requestedAppId, // On this case this will always be NULL.
                            inboundMccMnc, chargingConfig.getDefaultInboundCost(), false, // isVAPIOutboundToVBC is
                            "inbound-cost",
                            isEmergencyCall);
                }
            } else { // VBC..
                if (Log.isDebugEnabled())
                    Log.debug(" {}  INBOUND cost while dialing to an application is zero. The requested application_id {} ", sessionId, requestedAppId);

                inboundCost = new EffectiveCost(BigDecimal.ZERO, "default-inbound-to-application-cost");
            }

        }

        if (Log.isDebugEnabled())
            Log.debug("{} More details into the context: isInbound {} inboundPrice: {} inboundCost {} inboundCostPrefix {} forcedPrice {}",
                    sessionId, isInbound, inboundPrice, inboundCost, inboundCost.getPrefix(), quotaDisabledPrice);


        // if direction is 'outbound', this leg is not chargeable
        boolean isChargeable = !voiceSkipQuota
                && !SipAppUtils.isNotChargeablePricePrefix(inboundPrice.getPrefix())
                && isInbound;

        if (isChargeable && isVpricingEnabled && quotaDisabledPrice != null && quotaDisabledPrice.compareTo(BigDecimal.ZERO) == 0) {
            isChargeable = false;
        }

        voiceCtxBuilder.withPricePerMinute(inboundPrice.getPrice())
                       .withPricePrefix(inboundPrice.getPrefix())
                       .withPricePrefixGroup(inboundPrice.getPrefixGroup())
                       .withPriceSenderPrefix(inboundPrice.getSenderPrefix())
                       .withPriceTimestamp(inboundPrice.getTimestamp())
                       .withFirstChargedSeconds(firstChargedSeconds)
                       .withQuotaUpdatesInterval(quotaUpdatesInterval)
                       .withCostPerMinute(inboundCost.getCost())
                       .withCostPrefix(inboundCost.getPrefix())
                       .withCostPrefixGroup(inboundCost.getPrefixGroup())
                       .withCostSenderPrefix(inboundCost.getSenderPrefix())
                       .withCostTimestamp(inboundCost.getTimestamp())
                       .withForcedPrice(quotaDisabledPrice)
                       .withChargeable(isChargeable);


        if (outboundGatewayId != null) {
            if (Log.isTraceEnabled())
                Log.trace("TRACE ::: SESSION-ID ['" + sessionId + "'] :: Setting GW to [" + outboundGatewayId + "]");
            channel.setVariable(AsteriskAGIServer.GW, outboundGatewayId);
            if (outboundAltGateways == null) {
                outboundAltGateways = outboundGatewayId;
            }
        }
        if (outboundAltGateways != null) {
            if (Log.isTraceEnabled())
                Log.trace("TRACE ::: SESSION-ID ['" + sessionId + "'] :: Setting GWS to [" + outboundAltGateways + "]");
            channel.setVariable(AsteriskAGIServer.GWS, outboundAltGateways);
            voiceCtxBuilder.withAlternativeGateways(new LinkedHashSet<String>(Arrays.asList(outboundAltGateways.split(","))));
        }

        // Check to see if isProgrammableSip source/destination apikeys are different
        if (isProgrammableSip) {
            if (Log.isDebugEnabled()) {
                Log.debug("Comparing source and destination apikeys for isProgrammableSip");
            }
            String sourceApiKey = account.getSysId();
            String destinationApiKey = null;

            try {
                Application application = Core.getInstance().getApplication(requestedAppId);
                destinationApiKey = application.getApiKey();
                if (Log.isDebugEnabled()) {
                    Log.debug("source apikey={} - destination apikey={}", sourceApiKey, destinationApiKey);
                }
            } catch (Exception e) {
                Log.warn("Failed to get the application details of appId {} due to {}", requestedAppId, e.getMessage());
            }

            if (sourceApiKey != null && destinationApiKey != null && !sourceApiKey.equals(destinationApiKey)) {
                channel.setVariable(AsteriskAGIServer.APP_APIKEY, destinationApiKey);
                Log.info("Setting APP_APIKEY AGI channel to {}", destinationApiKey);
            }

            if (Log.isDebugEnabled()) {
                Log.debug("End source and destination apikey comparison for isProgrammableSip");
            }
        }

        if(authenticationSourceHeader != null) {
            // if we had an authentication source header, add the internal flag
            AuthenticationSource authenticationSource = AuthenticationSource.getForHeaderValue(authenticationSourceHeader);
            if(authenticationSource != null) {
                voiceCtxBuilder.withInternalFlag(authenticationSource.getInternalFlag());
            }
        }

        VoiceContext voiceContext = voiceCtxBuilder.build();
        voiceContext.setSessionId(sessionId);
        voiceContext.setConnectionId(uniqueId);

        try {
            Core.getInstance().getPddCalculator().storeStartTime(uniqueId, System.currentTimeMillis());
        } catch (PDDCalculationException e) {
            Log.warn("PDD can't be calculated for the call with unique id " + uniqueId, e);
        }
        cache.storeContext(sessionId, uniqueId, voiceContext);

        Log.info("End of AGIServer SIP request handle for SESSION-ID: " + sessionId);

        if (Log.isDebugEnabled()) {
            Log.debug("End of AGIServer SIP request handle for SESSION-ID: " + sessionId +
                    " Building and storing context :: " + voiceContext.getDebugString() +
                    " channel variables: GW: " + channel.getVariable(AsteriskAGIServer.GW) + " GWS: " + channel.getVariable(AsteriskAGIServer.GWS));
        }
    }

    private static CallBlockingResponse getCallBlockingResponse(String sessionId,
                                                                String uniqueId,
                                                                String channelId,
                                                                String nexmoUUID,
                                                                String clientCallId,
                                                                String callerId,
                                                                String outboundDestination,
                                                                VoiceContext.Builder voiceCtxBuilder,
                                                                SmppAccount account,
                                                                VoiceProduct requestedVoiceProduct,
                                                                AgiChannel channel,
                                                                int remainingAttempts) {
        long reqTime = 0;
        try {
            CallBlockingServiceClient callBlockingServiceClient = new CallBlockingServiceClient(Core.getInstance().getConfig().getCallblockingServiceConfig());
            reqTime = System.currentTimeMillis();
            callBlockingResponse = callBlockingServiceClient.getCallBlockingRules(account.getSysId(), VOICE_PRODUCT, callerId, outboundDestination, nexmoUUID, INITIAL_RETRY_COUNT, System.nanoTime());
            if (callBlockingResponse != null) {
                String action = callBlockingResponse.getAction();
                String id = callBlockingResponse.getId();
                if (BLOCKED.equalsIgnoreCase(action)) {
                    String errorReason = "Call cannot start since it's blocked by rule id:" + id;
                    SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, null, null, false, remainingAttempts);
                    voiceCtxBuilder.withApplicationContext(errorAppContext);
                    voiceCtxBuilder.withVoiceProduct(requestedVoiceProduct);
                    voiceCtxBuilder.withId(id)
                        .withBlockingSubsystem(callBlockingResponse.getSubsystem());
                    voiceCtxBuilder.withInternalFlag(CallInternalFlag.BLOCKED_ON_CALLID_ENFORCER); // set flag for reason we blocked
                    storeErrorContext(sessionId, uniqueId, voiceCtxBuilder, errorReason, SIPCode.FORBIDDEN, channel);
                }

            } else {
                Log.warn("Allowing call with nexmoUUID {} and with response time {} to proceed, though an error occurred while invoking callblocking api", nexmoUUID, System.currentTimeMillis() - reqTime);
            }
        } catch (Exception e) {
            Log.warn("Generic exception while finding blocking rule for product voice:{} and response time is: {}", e.getMessage(), System.currentTimeMillis() - reqTime);
            if (e.getMessage().contains("HTTP 401")) {
                CompletableFuture.runAsync(() -> {
                    CALLBLOCKING_RESPONSE.labels("authentication_failure").inc();
                });
            } else {
                CompletableFuture.runAsync(() -> {
                    CALLBLOCKING_RESPONSE.labels("generic_failure").inc();
                });
            }
        }
        return callBlockingResponse;
    }

    private static BigDecimal defaultPrice(ChargingConfig chargingConfig, boolean isProgrammableSip) {
        return isProgrammableSip ? chargingConfig.getDefaultSipDialInPrice() : chargingConfig.getDefaultSipPrice();
    }

    private static boolean isProgrammableSip(String sessionId, boolean isSipOriginToDomain, boolean isSipOriginToLVN, boolean inbound, VoiceProduct requestedVoiceProduct, String extension) {
        final boolean isProgrammableSip = isSipOriginToDomain ||
                //Inbound call from SIP, directed towards a LVN that connects to an Application
                (isSipOriginToLVN && VoiceProduct.CALL_API.equals(requestedVoiceProduct)) ||
                isOutboundProgrammableSip(sessionId, inbound, requestedVoiceProduct, extension);
        Log.info("SessionId {} : Check for Programmable Sip with isSipOriginToDomain = {}, isSipOriginToLVN = {}, inbound = {}, requestedVoiceProduct={},  extension = {} is {}",
                sessionId, isSipOriginToDomain, isSipOriginToLVN, inbound, requestedVoiceProduct, extension, isProgrammableSip);
        return isProgrammableSip;
    }

    public static boolean isOutboundProgrammableSip(String sessionId, boolean inbound, VoiceProduct requestedVoiceProduct, String extension){
        // Outbound call to a SIP destination where the VoiceProduct is VoiceProduct.CALL_API. -
        // Notice that this is NOT SIP trunking. On the SIP trunking scenario, the VoiceProduct is VoiceProduct.SIP
        final boolean isOutboundProgrammableSip =  !inbound && VoiceProduct.CALL_API.equals(requestedVoiceProduct) && StringUtils.trimToEmpty(extension).startsWith("sip:") ;
        Log.info("SessionId {} : Check for Outbound Programmable Sip with inbound = {}, requestedVoiceProduct = {}, extension = {} is {}",
                sessionId, inbound, requestedVoiceProduct, extension, isOutboundProgrammableSip);
        return isOutboundProgrammableSip;
    }

    public static boolean isOutboundNCCOConnectToSIPEndpoint(String sessionId, boolean inbound, VoiceProduct requestedVoiceProduct, String extension) {
        //similar check as {@code isOutboundProgrammableSip}
        final boolean isOutboundNCCOConnectToSIPEndpoint = !inbound && VoiceProduct.CALL_API.equals(requestedVoiceProduct) && StringUtils.trimToEmpty(extension).startsWith("sip:");
        Log.info("SessionId {} : Check for Outbound NCCO Conect To SIP Endpoint with inbound = {}, requestedVoiceProduct = {}, extension = {} is {}",
                sessionId, inbound, requestedVoiceProduct, extension, isOutboundNCCOConnectToSIPEndpoint);
        return isOutboundNCCOConnectToSIPEndpoint;
    }

    private static boolean checkSrtpEnabled(String sessionId, AgiChannel channel, String rerouteAddress) {
        if(AsteriskAGIServer.IB_SRTP_ENABLED.equals(getChannelVariable(sessionId, AsteriskAGIServer.IB_SRTP_ENABLED_VAR_NAME, channel))){
            Log.info("{} SRTP is enabled by checking with variable {}", sessionId, AsteriskAGIServer.IB_SRTP_ENABLED_VAR_NAME);
            return true;
        }
        if(StringUtils.isNotEmpty(rerouteAddress) && rerouteAddress.contains(MEDIA_SRTP_PATH_INFIX)){
            Log.info("{} SRTP is enabled by checking with rerouteAddress {} containing {}", sessionId, rerouteAddress, MEDIA_SRTP_PATH_INFIX);
            return true;
        }

        final String extensionChannelVariable = getChannelVariable(sessionId, AsteriskAGIServer.CHANNEL_EXTENSION, channel);
        if(StringUtils.isNotEmpty(extensionChannelVariable) && extensionChannelVariable.contains(MEDIA_SRTP_PATH_INFIX_ENCODED)){
            Log.info("{} SRTP is enabled by checking with extensionChannelVariable {} containing {}", sessionId, extensionChannelVariable, MEDIA_SRTP_PATH_INFIX_ENCODED);
            return true;
        }
        if(StringUtils.isNotEmpty(extensionChannelVariable) && extensionChannelVariable.contains(MEDIA_SRTP_PATH_INFIX)){
            Log.info("{} SRTP is enabled by checking with extensionChannelVariable {} containing {}", sessionId, extensionChannelVariable, MEDIA_SRTP_PATH_INFIX);
            return true;
        }
        return false;
    }

    private static String getSipTransport(String sessionId, AgiChannel channel, String rerouteAddress) {

        if (VSIP_TRANSPORT_TLS.equals(getChannelVariable(sessionId, AsteriskAGIServer.VSIP_TRANSPORT_VAR_NAME, channel)) ||
                VSIP_TRANSPORT_TCP.equals(getChannelVariable(sessionId, AsteriskAGIServer.VSIP_TRANSPORT_VAR_NAME, channel))) {
            Log.info("{} TLS/TCP is enabled by checking with variable {}", sessionId, AsteriskAGIServer.VSIP_TRANSPORT_VAR_NAME);
            return getChannelVariable(sessionId, AsteriskAGIServer.VSIP_TRANSPORT_VAR_NAME, channel);
        }

        if (StringUtils.isNotEmpty(rerouteAddress) && (rerouteAddress.contains(TRANSPORT_TLS_PATH_INFIX) || rerouteAddress.contains(TRANSPORT_TCP_PATH_INFIX))) {
            Log.info("{} TLS/TCP is enabled by checking with rerouteAddress {} containing {}", sessionId, rerouteAddress, MEDIA_SRTP_PATH_INFIX);
            rerouteAddress = StringUtils.substringBetween(rerouteAddress, TRANSPORT_PATH, ";");
            if ("tcp".equalsIgnoreCase(rerouteAddress) || "tls".equalsIgnoreCase(rerouteAddress))
                return rerouteAddress;
        }

        String extensionChannelVariable = getChannelVariable(sessionId, AsteriskAGIServer.CHANNEL_EXTENSION, channel);
        if (StringUtils.isNotEmpty(extensionChannelVariable) && (extensionChannelVariable.contains(TRANSPORT_TLS_PATH_INFIX_ENCODED) || extensionChannelVariable.contains(TRANSPORT_TCP_PATH_INFIX_ENCODED))) {
            Log.info("{} TLS/TCP is enabled by checking with extensionChannelVariable {}", sessionId, extensionChannelVariable);
            try {
                String decodedExtensionChannelVariable = decode(extensionChannelVariable, StandardCharsets.UTF_8.name());
                if (StringUtils.isNotBlank(decodedExtensionChannelVariable)) {
                    int startIndex = decodedExtensionChannelVariable.indexOf(TRANSPORT_PATH) + TRANSPORT_PATH.length();
                    extensionChannelVariable = StringUtils.substring(decodedExtensionChannelVariable, startIndex, startIndex + 3);
                }

            } catch (UnsupportedEncodingException e) {
                Log.warn("UnsupportedEncodingException occurred while decoding sipTransport variable", e);
                return null;
            } catch (Exception e) {
                Log.warn("Exception occurred while decoding sipTransport variable", e);
                return null;
            }
            if ("tcp".equalsIgnoreCase(extensionChannelVariable) || "tls".equalsIgnoreCase(extensionChannelVariable))
                return extensionChannelVariable;
        }

        if (StringUtils.isNotEmpty(extensionChannelVariable) && (extensionChannelVariable.contains(TRANSPORT_TLS_PATH_INFIX) || extensionChannelVariable.contains(TRANSPORT_TCP_PATH_INFIX))) {
            Log.info("{} TLS/TCP is enabled by checking with extensionChannelVariable {}", sessionId, extensionChannelVariable);
            extensionChannelVariable = StringUtils.substringBetween(extensionChannelVariable, TRANSPORT_PATH, ";");

            if ("tcp".equalsIgnoreCase(extensionChannelVariable) || "tls".equalsIgnoreCase(extensionChannelVariable))
                return extensionChannelVariable;
        }
        return null;
    }

    private static Attestation calculateOutboundAttestationLevel(String sessionId,
                                                                 SmppAccount account,
                                                                 VoiceProduct requestedVoiceProduct,
                                                                 String forcedGateway,
                                                                 boolean isVAPIOutboundToVBC,
                                                                 boolean isAllowedAnyCallerIdValue,
                                                                 boolean isCallerIdE164,
                                                                 boolean isOwnedLvn,
                                                                 String outboundCountryCode,
                                                                 String outboundGatewayId,
                                                                 String forceSender,
                                                                 boolean isBYON) {
        AttestationValidationParams validationParam = AttestationValidationParams.builder()
                .withAccountId(account.getSysId())
                .withHasKyc(AsteriskAGIServer.hasKyc(account))
                .withHasDisableMustOwnLVN(isAllowedAnyCallerIdValue)
                .withIsCallerIdE164(isCallerIdE164)
                .withIsOwnedLvn(isOwnedLvn)
                .withDestinationCountry(outboundCountryCode)
                .withGatewayName(outboundGatewayId)
                .withForcedSender(forceSender)
                .withSessionId(sessionId)
                .withIsBYON(isBYON)
                .build();
        Log.info("{} Calculating  stir shaken: voiceProduct = {}, params = {}", sessionId, requestedVoiceProduct, validationParam);
        VoiceProduct realVoiceProduct = isVBC(sessionId, isVAPIOutboundToVBC, forcedGateway) ? VoiceProduct.VBC : requestedVoiceProduct;
        return EnforcerServiceFactory.byProductClass(realVoiceProduct).attestationLevel(validationParam);

    }


    private static String checkIfCallerIdIsValid(String callerId, SmppAccount account, boolean isOwnedLvn, boolean isAllowedAnyCallerIdValue, AgiChannel channel, boolean isCallerIdE164) throws AgiException {
        if (Log.isDebugEnabled())
            Log.debug("About to check if callerId {} is valid for account {} isOwnedLvn {}, isAllowedAnyCallerIdValue {}, isCallerIdE164 {}",
                    callerId, account, isOwnedLvn, isAllowedAnyCallerIdValue, isCallerIdE164);

        if (isAllowedAnyCallerIdValue) {
	        Log.warn("disable-must-own lvn is set for accountSysId:[ {} ] isCallerIdE164 {} callerId {}", account.getSysId(), isCallerIdE164, callerId);
            // VOICEN-31 VOICEN-141 - if a number has prefix '+' but not e164, it is not considered all digits cli
            if (isCallerIdE164 || StringUtil.containsOnlyNumbers(callerId)) {
	        Log.warn("disable-must-own-lvn is set using passed CLI for accountSysId:[ {} ] isCallerIdE164 {} callerId {}", account.getSysId(), isCallerIdE164, callerId);
                return callerId;
            }
	    Log.warn("disable-must-own-lvn is set but invalid CLI for accountSysId:[ {} ] isCallerIdE164 {} callerId {}", account.getSysId(), isCallerIdE164, callerId);
        }
        if (Log.isDebugEnabled())
            Log.debug("Checking if user owns [ {} ] lvn", callerId);

        String from = AsteriskAGIServer.UNKNOWN_CALLER;

        if (!isOwnedLvn) {
            Log.warn("'callerId' [ {} ] is not recognized, using UNKNOWN_CALLER, for " +
                            "accountSysId:[ {} ]",
                    callerId, account.getSysId());

            channel.setVariable(AsteriskAGIServer.CALLERID_NAME, from);
            channel.setVariable(AsteriskAGIServer.CALLERID_NUM, from);

            return from;
        } else
            return callerId;
    }

    private static Optional<String> checkIfOriginalCli(boolean isOwnedLvn, String sessionId, String origCallerId, String callerId, SmppAccount account, DynamoDB dynamoDbClient, AgiChannel channel) throws AgiException{
        boolean validAccountAndCallerId = (Objects.nonNull(account.getSysId()) && !account.getSysId().isEmpty()) &&
                (Objects.nonNull(callerId) && !callerId.isEmpty());

        // this account can use Original cli came in on the inbound leg
        // and this call is not using account's LVN so check to see if the origCallerId is actually Original cli
        // callerId can either be UNKNOWN or origCallerId depending on the account has disable-must-own capability
        if (validAccountAndCallerId && !isOwnedLvn) {
            Log.debug("checkIfOriginalCli sessionId: {} callerId: {} validAccountAndCallerId: {} apiKey: {}_{}",
                    sessionId, callerId, validAccountAndCallerId, account.getSysId(), origCallerId);

            GetItemResult result = null;
            boolean dynamodbException = false;
            try {
                result = dynamoDbClient.findItem(account.getSysId() + "_" + origCallerId, sessionId);
            } catch (Exception e) {
                dynamodbException = true;
            }

            /*
             If result is nonNull & item isn't empty , means we found the item in dynamodb
             If result is null, check if a dynamodb exception occurred
             */
            boolean foundKey = Objects.nonNull(result) && Objects.nonNull(result.getItem()) && !result.getItem().isEmpty();

            if (foundKey || dynamodbException) {
                // possibly set channel variables.
                Log.info("OriginalCli sessionId: {} using callerId: {} dynamodbRecord: {}", sessionId, origCallerId, result.getItem());
                String orgExt = dynamoDbClient.getExtension(result);
                if (Objects.nonNull(orgExt) && !orgExt.isEmpty()) {
                    String diversionHeader = "<sip:" + orgExt + "@sip.vonage.com>;reason=deflection";
                    channel.setVariable(AsteriskAGIServer.DIVERSION_HEADER, diversionHeader);
                    Log.info("OriginalCli sessionId: {} Diversion Header: {}", sessionId, diversionHeader);
                }
                channel.setVariable(AsteriskAGIServer.CALLERID_NAME, origCallerId);
                channel.setVariable(AsteriskAGIServer.CALLERID_NUM, origCallerId);

                return Optional.of(orgExt);
            }
        }
        return Optional.empty();
    }

    //OUTBOUND call with productClass header : vbc
    //This is the VAPI connect-to-vbc outbound scenario
    private static boolean isVAPIOutboundToVBC(boolean isInbound, String productClassOverride) {
        return !isInbound && VoiceProduct.VBC.getDescriptor().equalsIgnoreCase(productClassOverride);
    }


    //It seems like it is allowed to force a specific Gateway for a customer,
    //   should they have the "force-sip-gateway" capability set.
    //
    //For OUTBOUND calls, which are directed to VBC specific Gateway, the forcedGateway will be populated by Asterisk
    //and should be forced, regardless of the account's capabilities
    private static String verifyForcedGatewayCapability(String forcedGateway, SmppAccount account, boolean isVAPIOutboundToVBC) {
        if (forcedGateway != null)
            forcedGateway = forcedGateway.trim().isEmpty() ? null : forcedGateway.trim();

        //Check OUTBOUND VBC specific case
        if (isVAPIOutboundToVBC) {
            return forcedGateway;
        }

        // Check capability for forcing gateway ...
        AccountCapabilitiesConfig capabilityConfig = Core.getInstance().getConfig().getAccountCapabilitiesConfig();
        if (forcedGateway != null &&
                (capabilityConfig == null || !account.hasCapability(capabilityConfig.getForceGatewayCapability()))
        ) {
            Log.warn("Account " + account.getSysId() + " not allowed to force gateway: " + forcedGateway + " Will use default routing.");
            forcedGateway = null;
        }
        return forcedGateway;
    }


    private static void storeErrorContext(String sessionId,
                                          String uniqueId,
                                          VoiceContext.Builder contextBuilder,
                                          String errorReason,
                                          SIPCode errorSipCode,
                                          AgiChannel channel) throws AgiException {
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();

        contextBuilder.withInitialChargingStatus(BillingInfo.Status.ERROR)
                      .withPricePerMinute(BigDecimal.ZERO)
                      .withFirstChargedSeconds(0)
                      .withQuotaUpdatesInterval(0)
                      .withCostPerMinute(ZERO_COST);

        VoiceContext context = contextBuilder.build();
        context.setSessionId(sessionId);
        context.setConnectionId(uniqueId);

        Log.info("Due to internal error during the AGI request processing, About to set APP_REASON to " +
                String.valueOf(errorSipCode.getCode()) + " sessionId: " + context.getSessionId());
        channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(errorSipCode.getCode()));
        cache.storeContext(sessionId, uniqueId, context);
        if (Log.isDebugEnabled())
            Log.debug("SESSION-ID ['" + sessionId + "'] :: Building and storing ERROR context :: " + context.getDebugString());
    }

    private static void storeAccountErrorContext(String sessionId, String uniqueId, Builder errorCtxBuilder) {
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();

        errorCtxBuilder.withInitialChargingStatus(BillingInfo.Status.ERROR)
                      .withPricePerMinute(BigDecimal.ZERO)
                      .withFirstChargedSeconds(0)
                      .withQuotaUpdatesInterval(0)
                      .withCostPerMinute(ZERO_COST);

        VoiceContext errorContext = errorCtxBuilder.build();
        errorContext.setSessionId(sessionId);
        errorContext.setConnectionId(uniqueId);
        errorContext.setAccountNotAvailableErrorContext();

        cache.storeContext(sessionId, uniqueId, errorContext);
        if (Log.isDebugEnabled())
            Log.debug("SESSION-ID ['" + sessionId + "'] :: Building and storing ERROR context :: " + errorContext.getDebugString());
    }


    private static String getRequestParamValue(Map<String, String[]> requestParams,
                                               String paramName) {
        String param = null;
        String[] params = requestParams.get(paramName);
        if (params != null)
            param = params[0];
        Log.trace("Extracted " + paramName + " ['" + param + "']");
        return param;
    }

    protected static ShortCode getShortCodeForNumber(String number) {
        if (Log.isDebugEnabled())
            Log.debug("About to look for shortcode for number {} ", number);
        if (StringUtils.isEmpty(number)) {
            return null;
        }
        number = number.replace("+", "");
        number = number.trim();
        ShortCodes shortCodes = Core.getInstance().getConfig().getShortCodes();

        if (shortCodes == null) {
            Log.info("Could not check if number {} belongs to ShortCodes. ShortCodes is not initialized!", number);
            return null;
        }

        final long timer = System.nanoTime();
        ShortCode shortCode = null;
        try {
            shortCode = shortCodes.getFirstShortCodeMatchForNumber(number);
            if (Log.isDebugEnabled())
                Log.debug("shortcode {} found for number {} ", shortCode, number);
        } catch (ShortCodeException ex) {
            Log.info("Failed to retrieve shortcode for [shortcode: " + number + "]", ex);
            Core.getInstance().incrementShortCodesDBErrorCounter(ex);
        }
        Core.getInstance().updateShortCodesDBLatencyMetrics((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        return shortCode;
    }

    private static AccountBalance checkBalanceRestrictions(SmppAccount account, BigDecimal pricePerMinute, long secondsToCharge,
                                                           final boolean isVpricingEnabled, VoiceContext.Builder vctxBuilder, String uniqueId, String pricePrefix)
            throws AgiException, NotEnoughBalanceException, QuotaDisabledException {
        String accountId = account.getSysId();
        AccountBalance balance = null;

        QuotaClient quotaClient = Core.getInstance().getQuotaClient();
        if (quotaClient == null) {
            final String errorMsg = "Could not check for balance restrictions. QuotaAPIService was null!";
            throw new AgiException(errorMsg);
        }
        //Get the general minimum balance to start a call
        BigDecimal generalMinBalance = Core.getInstance().getConfig().getInternalApiConfig().getMinBalance();
        if (Log.isDebugEnabled())
            Log.debug("{} generalMinBalance: {} ", accountId, generalMinBalance.toPlainString());

        //Get the required minimum balance based on the call price and minimum seconds to charge
        BigDecimal pricePerSecond = pricePerMinute.divide(BigDecimal.valueOf(60), 10, RoundingMode.HALF_UP);
        BigDecimal perPriceMinBalance = pricePerSecond.multiply(BigDecimal.valueOf(secondsToCharge)).setScale(8, RoundingMode.HALF_UP);
        if (Log.isDebugEnabled())
            Log.debug("{} perPriceMinBalance: {} ", accountId, perPriceMinBalance.toPlainString());

        //Use the higher minimum for the balance verification
        BigDecimal finalMinimum = generalMinBalance.max(perPriceMinBalance);
        if (Log.isDebugEnabled())
            Log.debug("{} finalMinimum: {} ", accountId, finalMinimum.toPlainString());

        try {
            if(isVpricingEnabled) {
                VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
                // Temporarily create voiceContext to check balance restrictions using BSS vQuota price-impact API.
                // This API requires call info to rate the call price for the given duration.
                VoiceContext vctx = vctxBuilder.build();
                vctx.setConnectionId(uniqueId);

                // isVoiceSkipQuota function check to determine if the call is chargeable has already been performed before this function call.
                boolean isChargeable = !SipAppUtils.isNotChargeablePricePrefix(pricePrefix);
                vctx.setChargeable(isChargeable);
                PriceImpactApiSuccessResponse resp = vQuotaService.invokePriceImpactApi(vctx, secondsToCharge, QuotaUpdateDetails.Operation.QUERY);
                if (resp != null) {
                    balance = resp.getAccountBalance();
                }
            } else {
                balance = quotaClient.checkAccountBalance(accountId, finalMinimum);
            }
        } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException e) {
            if (Core.getInstance().isAsyncQuotaFlag()) {
                Log.warn("AsyncQuota Exception:Forced price will be set later", e);
                balance = null;
            } else {
                throw new AgiException("Failed to check account balance for [accountId:" + accountId + "]", e);
            }

        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException e) {
            throw new AgiException("Failed to check account balance for [accountId:" + accountId + "]", e);
        }

        if (balance != null && !balance.isRequiredFreeBalanceAvailable())
            throw new NotEnoughBalanceException("Account [accountId:" + accountId + "] does not have enough balance!");

        if (Log.isDebugEnabled() && balance != null)
            Log.debug("{} has the required balance {} ", accountId, balance.getBalance().toPlainString());

        return balance;
    }


    private static SmppAccount getAccount(String accountId, String sessionId, AgiChannel channel) throws AgiException {
        SmppAccount account = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
            if (Log.isDebugEnabled())
                Log.debug("{} Account found for accountId {} account's master {} account's pricing group {}",
                        sessionId, accountId, account.getMasterAccountId(), account.getQuotaPricingGroupId());
        } catch (AccountsException e) {
            Log.error("{} Could not retrieve account {} due to {} about to set APP_REASON to {} ",
                    sessionId, accountId, e.getMessage(), String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            return null;
        }
        return account;
    }

    //This method is used to verify the provided accountId is matching the provided applicationId owner.
    private static boolean isValidApplicationId(String accountId, String requestedAppId, String customerDomain,
                                                String sessionId, AgiChannel channel,
                                                String uniqueId, String callerId, String extension, boolean isInbound, VoiceProduct requestedVoiceProduct,
                                                String sourceIpHeader, String callOrigin, String callTermination,
                                                boolean isVAPIOutboundToVBC, String channelId, String clientCallId) throws AgiException {

        if (Log.isDebugEnabled())
            Log.debug("{} About to look for the application's owner using applicationId {} for Domain {} ",
                    sessionId, requestedAppId, customerDomain);

        boolean isValidApplicationId = true;
        Application application = null;
        String applicationOwner = null;

        try {
            long reqTime = System.currentTimeMillis();
            application = Core.getInstance().getApplication(requestedAppId);
            Log.info("{} Provisioning getApplication {} took {} ms", sessionId, requestedAppId, System.currentTimeMillis() - reqTime);
        } catch (Exception e) {
            Log.error("{} Failed to retrieve application {} due to {}. about to set APP_REASON to {} ",
                    sessionId, requestedAppId, e.getMessage(), String.valueOf(SIPCode.NOT_FOUND.getCode()));
            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.NOT_FOUND.getCode()));
            isValidApplicationId = false;
        }

        if (isValidApplicationId && Objects.isNull(application)) {
            Log.error("{} application {} not found, about to set APP_REASON to {} ",
                    sessionId, requestedAppId, String.valueOf(SIPCode.NOT_FOUND.getCode()));
            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.NOT_FOUND.getCode()));
            isValidApplicationId = false;
        }

        if (isValidApplicationId) {
            applicationOwner = application.getApiKey();
            Log.info("{} applicationId {} owner is {}", sessionId, requestedAppId, applicationOwner);
            if (Objects.isNull(applicationOwner)) {
                Log.error("{} application {} owner not found, about to set APP_REASON to {} ",
                        sessionId, requestedAppId, String.valueOf(SIPCode.NOT_FOUND.getCode()));
                channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.NOT_FOUND.getCode()));
                isValidApplicationId = false;
            }
        }

        if (isValidApplicationId && !applicationOwner.equals(accountId)) {
            Log.error("{} application {} owner {} is mismatching the provided accountid {}, about to set APP_REASON to {} ",
                    sessionId, requestedAppId, applicationOwner, accountId, String.valueOf(SIPCode.NOT_FOUND.getCode()));
            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.NOT_FOUND.getCode()));
            isValidApplicationId = false;
        }

        if (Log.isDebugEnabled())
            Log.debug("{} isValidApplicationId: {} ", sessionId, isValidApplicationId);

        if (!isValidApplicationId) {
            //Generate error context to handle the later CDR event
            if (Log.isDebugEnabled())
                Log.debug("{} isValidApplicationId: {} about to build the error context", sessionId, isValidApplicationId);

            SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, requestedAppId, null, false, 0);

            VoiceContext.Builder errorCtxBuilder = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                    .withFrom(callerId)
                    .withTo(extension)
                    .withAccountId(accountId)
                    .withVoiceDirection(isInbound ? VoiceDirection.INBOUND : VoiceDirection.OUTBOUND)
                    .withProductClass(requestedVoiceProduct.getDescriptor())
                    .withCustomerDomain(customerDomain)
                    .withRequestIp(sourceIpHeader)
                    .withCallOrigin(callOrigin)
                    .withCallTermination(callTermination)
                    .withIsVAPIOutboundToVBC(isVAPIOutboundToVBC)
                    .withApplicationContext(errorAppContext);
            storeAccountErrorContext(sessionId, uniqueId, errorCtxBuilder);
        }


        return isValidApplicationId;
    }

    //TODO: Tally: Check if and why we need this
    private static void handleDemoAccountSettings(SmppAccount account, AgiChannel channel) throws AgiException {
        //check if it is a demo account; in this case, set the caller id as anonymous
        if (account.getMessageWatermark() != null && account.getMessageWatermark().equals(AsteriskAGIServer.DEMO_WATERMARK)) {
            channel.setVariable(AsteriskAGIServer.PRIVACY_HEADER, AsteriskAGIServer.PRIVACY_HEADER_ID);
            channel.setVariable(AsteriskAGIServer.CALLERID_NAME, AsteriskAGIServer.ANONYMOUS_CALLER);
            channel.setVariable(AsteriskAGIServer.CALLERID_NUM, AsteriskAGIServer.ANONYMOUS_CALLER);
        }
    }

    private static int parseAttemptNumber(String requestAttemptStr, String nexmoUUID, AgiChannel channel) throws AgiException {
        int attempt = 0;
        if (Objects.isNull(requestAttemptStr) || requestAttemptStr.trim().isEmpty())
            return attempt;

        try {
            attempt = Integer.parseInt(requestAttemptStr.trim());
        } catch (NumberFormatException e) {
            Log.error("attempt parameter is not a valid Integer: " + requestAttemptStr +
                    " about to set APP_REASON to " + String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()) +
                    " nexmoUUID " + nexmoUUID);

            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
            throw new AgiException("Invalid attempt parameter");
        }
        return attempt;
    }

    private static boolean isExplicitRedirectToApplication(String callOrigin) {
        //If an applicationId was requested explicitly (VBC)
        return AsteriskAGIServer.CALL_ORIGIN_APPLICATION.equalsIgnoreCase(callOrigin);
    }

    private static boolean isSipOriginToLVN(String callOrigin, String customerDomain, boolean isInbound) {
        return isInbound &&
                AsteriskAGIServer.CALL_ORIGIN_SIP.equalsIgnoreCase(callOrigin) &&
                Objects.isNull(customerDomain);
    }

    private static boolean isSipOriginToDomain(String callOrigin, String customerDomain, boolean isInbound) {
        return isInbound &&
                AsteriskAGIServer.CALL_ORIGIN_SIP.equalsIgnoreCase(callOrigin) &&
                Objects.nonNull(customerDomain);
    }

    //SIP destination might be a regular sip destination out of a list of sip destinations to fail over to.
    //In case of the regular sip destination, and the sip destination file over list, the attempt number is
    //used to identify the session and all the consecutive fail over attempts.
    //
    //As of SIP-282 VAPI outbound to VBC, the sip destination might also be the VBC sip address. In such case
    //there is no meaning to fail over and no meaning to the attempt number.
    private static int verifySipDestinationAttempt(int sipDestinationAttempt, boolean isVAPIOutboundToVBC, boolean isSipOriginToDomain, String nexmoUUID) {
        if (isVAPIOutboundToVBC) {
            if (Log.isDebugEnabled())
                Log.debug("nexmoUUID=" + nexmoUUID + " isVAPIOutboundToVBC=" + isVAPIOutboundToVBC + " reset sip destination attempts counter ");
            return 0;
        }
        return sipDestinationAttempt;
    }


    //TEMP SOLUTION!!! - We need to add origin=application(or other value) to what now we call VBC Inbound.
    //                   That will be further investigated as part of VBC Outbound
    //
    //Today the provided INBOUND details are:
    //PSTN:   requestedAppId=null,          callOrigin=null
    //VBC:    requestedAppId=applicationId, callOrigin=null
    //SIP:    requestedAppId=applicationId, callOrigin=sip
    //
    //I prefer it to be:
    //PSTN:   requestedAppId=null,          callOrigin=pstn
    //VBC:    requestedAppId=applicationId, callOrigin=application(or other value)
    //                                       (that cannot be sip - as we need to differentiate it from sip-connect)
    //SIP:    requestedAppId=applicationId, callOrigin=sip

    private static String findOrigin(String callOrigin, String requestedAppId, boolean isInbound) {
        if (!isInbound)
            return callOrigin;

        if (StringUtils.isEmpty(requestedAppId) && StringUtils.isEmpty(callOrigin))
            return AsteriskAGIServer.CALL_ORIGIN_PSTN;
        if (Objects.nonNull(requestedAppId) && StringUtils.isEmpty(callOrigin))
            return AsteriskAGIServer.CALL_ORIGIN_APPLICATION;

        //there should be a validation here that callOrigin != application
        return callOrigin;
    }

    //SIP-409 : Programmable sip: If customer_domain is provided, the application_id is mandatory as well.
    //          That will probably change and will need enhancements when we will extend the Domains Service ******** and info
    private static void verifyMandatoryInputParams(String nexmoUUID, String accountId,
                                                   String requestedAppId, String customerDomain,
                                                   String customerDomainType, AgiChannel channel) throws AgiException {

        if ((Objects.nonNull(customerDomain) && !customerDomain.trim().isEmpty()) &&
                (Objects.isNull(requestedAppId) || requestedAppId.trim().isEmpty())) {
            if (Objects.nonNull(customerDomainType) && !customerDomainType.trim().isEmpty() &&
                    AsteriskAGIServer.CUSTOMER_DOMAIN_TYPE_TRUNKING.equalsIgnoreCase(customerDomainType)) {
                if(Log.isDebugEnabled()) {
                    Log.debug("{} Customer's Domain is provided but applicationId is missing. Domain type is {}",
                            nexmoUUID, customerDomainType);
                }
            } else {
                Log.error("{} Customer's Domain is provided but applicationId is missing. Cannot handle the call. " +
                        " about to set APP_REASON to {}", nexmoUUID, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));

                channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.SERVER_INTERNAL_ERROR.getCode()));
                throw new AgiException("Missing AGI applicationId related to requested customerDomain ");
            }
        }

    }


    private static boolean isAccountAvailable(SmppAccount account, String sessionId, String uniqueId, AgiChannel channel,
                                              String callerId, String extension, boolean isInbound, VoiceProduct requestedVoiceProduct,
                                              String customerDomain, String sourceIpHeader, String callOrigin, String callTermination,
                                              boolean isVAPIOutboundToVBC, String channelId, String clientCallId, String requestedAppId) throws AgiException {

        if (Log.isDebugEnabled())
            Log.debug("{} Verify the account {} availability for customerDomain {} ", sessionId,
                    Objects.isNull(account) ? "null" : account.getSysId(), customerDomain);

        //Account is null if not found
        //channel variables and log messages are already set
        if (Objects.nonNull(account) && !account.isBanned()) {
            if (Log.isDebugEnabled())
                Log.debug("{} account {} is ready ", sessionId, account.getSysId());
            return true;
        }

        CallInternalFlag internalFlag=null;
        if (Objects.isNull(account)) {
            Log.error("{} Unable to handle the call due to problems locating the account.", sessionId);
            internalFlag = CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES;
        } else if (account.isBanned()) {
            Log.warn("{} Account {} is banned. The call will be rejected - about to set APP_REASON to {} ",
                    sessionId, account.getSysId(), String.valueOf(SIPCode.FORBIDDEN.getCode()));
            internalFlag = CallInternalFlag.BLOCKED_ON_BANNED_ACCOUNT;
            channel.setVariable(AsteriskAGIServer.APP_REASON, String.valueOf(SIPCode.FORBIDDEN.getCode()));
        }

        //Generate error context to handle the later CDR event
        String accountId = Objects.isNull(account) ? "null" : account.getSysId();
        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, requestedAppId, null, false, 0);

        VoiceContext.Builder errorCtxBuilder = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(callerId)
                .withTo(extension)
                .withAccountId(accountId)
                .withVoiceDirection(isInbound ? VoiceDirection.INBOUND : VoiceDirection.OUTBOUND)
                .withProductClass(requestedVoiceProduct.getDescriptor())
                .withCustomerDomain(customerDomain)
                .withRequestIp(sourceIpHeader)
                .withCallOrigin(callOrigin)
                .withCallTermination(callTermination)
                .withIsVAPIOutboundToVBC(isVAPIOutboundToVBC)
                .withApplicationContext(errorAppContext)
                .withInternalFlag(internalFlag);
        storeAccountErrorContext(sessionId, uniqueId, errorCtxBuilder);
        return false;
    }

    private static boolean hasCallbackType(ShortCodeMetaData scMetadata, CallbackType type) {
        return scMetadata.getCallbackAddresses() != null
                && scMetadata.getCallbackAddresses().containsKey(type)
                && StringUtils.isNotBlank(scMetadata.getCallbackAddress(type));
    }


    //The goal here is to verify the OUTBOUND destination validation and authorization. If the destination is invalid
    // SIPApp will return 404 to Asterisk
    //
    //It is relevant only for PSTN destinations:
    //1. For customers which has the capability "voice-enable-phonenumber-validation" : verify that the PSTN destination
    //   is a valid number. (For example Tiktok)
    //2. For customers which have the permitted-destination-list set in their account : verify that the PSTN destination
    //   is a valid number AND that it is in their account's permitted destination - this is specifically
    //   to block the fraud of VAPI outbound calls to premium destinations. for other scenarios such verification
    //   is done in the InternalApiServlet while called by Kamailio
    private static boolean isAcceptableDestination(SmppAccount account, String extension, String sessionId, String uniqueId,
                                                   AgiChannel channel, String callerId, String callTermination, String channelId,
                                                   String clientCallId, String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct) throws AgiException {

        if (!"pstn".equalsIgnoreCase(callTermination))
            return true; // Only applies to PSTN calls

        String accountId = account.getSysId();

        String reason;

        //If the account has the capability "voice-enable-phonenumber-validation"... (like Tiktok)
        //Check the destination is valid.
        if (shouldVerifyDestinationNumbers(account)) {
            if (!AsteriskAGIServer.isValidNumber(extension)) {
                reason = "Invalid PSTN destination number " + extension + " requested by account " + accountId;
                set404andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                        clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, reason);

                return false;
            }
        }

        //For all accounts - check if the destination is in the permitted destination list - if such is
        //configured for the account
        if (!isAllowedByPermittedDestinationList(account, extension)) {
            reason = "PSTN destination number " + extension + " not in account " + accountId + " permitted list";
            set404andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                    clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, reason);

            return false;
        }

        // For all accounts. Check if the destination is to the Prefix Group virtual country code (+806)
        // and reject the call if it is
        if (isPrefixGroupNumber(extension)) {
            reason = "PSTN destination number " + extension + " is in Vonagistan (cannot directly call a Prefix Group virtual number)";
            set404andBuildErrorContext(extension, sessionId, uniqueId, channel, callerId, callTermination, channelId,
                    clientCallId, requestedAppId, callOrigin, requestedVoiceProduct, accountId, reason);

            return false;
        }

        return true; //Passed all verifications - good to go
    }

    private static boolean shouldVerifyDestinationNumbers(SmppAccount account) {
        return (account.getCapabilities() != null &&
                account.getCapabilities().contains("voice-enable-phonenumber-validation"));
    }

    private static boolean isPrefixGroupNumber(String to) {
        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig();
        if (prefixMapConfig != null) {
            if ((to != null) && to.startsWith(prefixMapConfig.getCountryCode()))
                return true;
            else
                return false;
        } else {
            return false;
        }
    }


    //Format the number, check its validity and verify it is matching the account's permitted destinations list
    private static boolean isAllowedByPermittedDestinationList(SmppAccount account, String to) {
        //Check if the account has the permitted-destinations limit. This will be set for new accounts
        //which hasn't yet top-up their account. The destination list then will include the number they used
        //in-order to register.
        //It might be set to veteran accounts as well - if they wish so.
        Set<String> permittedDestinations = account.getPermittedDestinations();
        if (Objects.isNull(permittedDestinations) || permittedDestinations.isEmpty())
            return true;

        String formattedTo = null;
        try {
            formattedTo = PhoneNumberTool.formatToInternationalNumber(to, null);
        } catch (BadlyFormattedNumberException e1) {
            return false;
        }
        if (Objects.isNull(formattedTo))
            return false; // Invalid number cannot be in permitted destinations list

        try {
            account.destinationMatchesPermittedDestinationsWhitelist(formattedTo);
        } catch (PermittedDestinationMisMatchException e) {
            Log.warn("Error while checking if number is in account " + account.getSysId() + " permitted destinations", e.getMessage());
            return false;
        }

        return true;
    }


    private static void set404andBuildErrorContext(String extension, String sessionId, String uniqueId,
                                                   AgiChannel channel, String callerId, String callTermination, String channelId, String clientCallId,
                                                   String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct, String accountId,
                                                   String reason) throws AgiException {

        // Tell Asterisk that this number can't be reached - error 404!
        String status = String.valueOf(SIPCode.NOT_FOUND.getCode());
        Log.info("Due to {}, About to set APP_REASON to {} sessionId: {}", reason, status, sessionId);
        channel.setVariable(AsteriskAGIServer.APP_REASON, status);

        // Build a context for the CDR event
        String sourceIpHeader = AsteriskAGIServer.normalizeValue(StringUtils.trimToNull(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC)));

        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, requestedAppId, null, false, 0);
        VoiceContext.Builder errorCtxBuilder = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(callerId)
                .withTo(extension)
                .withAccountId(accountId)
                .withVoiceDirection(VoiceDirection.OUTBOUND) // This check is only for outbound calls
                .withProductClass(requestedVoiceProduct.getDescriptor())
                .withRequestIp(sourceIpHeader)
                .withCallOrigin(callOrigin)
                .withCallTermination(callTermination) // Always PSTN
                .withIsVAPIOutboundToVBC(false) // This check is not for VBC calls
                .withApplicationContext(errorAppContext);

        storeAccountErrorContext(sessionId, uniqueId, errorCtxBuilder);
    }

    private static void set403andBuildErrorContextForBlockingUnknownCLI(String extension, String sessionId, String uniqueId,
                                                                        AgiChannel channel, String callerId, String callTermination, String channelId, String clientCallId,
                                                                        String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct, String accountId,
                                                                        String reason) throws AgiException {

        // Tell Asterisk that this call is rejected with error 403
        SIPCode status = SIPCode.FORBIDDEN_UNKNOWN_CLI;

        // Build a context for the CDR event
        String sourceIpHeader = AsteriskAGIServer.normalizeValue(StringUtils.trimToNull(channel.getVariable(AsteriskAGIServer.SIP_HEADER_P_NEXMO_SRC)));

        SIPAsteriskContext errorAppContext = new SIPAsteriskContext(channelId, clientCallId, requestedAppId, null, false, 0);
        VoiceContext.Builder errorCtxBuilder = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(callerId)
                .withTo(extension)
                .withAccountId(accountId)
                .withVoiceDirection(VoiceDirection.OUTBOUND) // This check is only for outbound calls
                .withProductClass(requestedVoiceProduct.getDescriptor())
                .withRequestIp(sourceIpHeader)
                .withCallOrigin(callOrigin)
                .withCallTermination(callTermination) // Always PSTN
                .withIsVAPIOutboundToVBC(false) // This check is not for VBC calls
                .withApplicationContext(errorAppContext)
                .withInternalFlag(CallInternalFlag.BLOCKED_ON_CALLID_UNKNOWN); // set flag for reason we blocked

        storeErrorContext(sessionId, uniqueId, errorCtxBuilder, reason, status, channel);
    }

    private static boolean isVBC(String sessionId, boolean isVAPIOutboundToVBC, String forcedGateway) {
        final boolean result = isVAPIOutboundToVBC || VoiceProduct.VBC.getDescriptor().equalsIgnoreCase(forcedGateway);
        Log.info("{} Depending on VAPIOutboundToVBC {} and forcedGateway {}, calculated its a VBC call {}", sessionId, isVAPIOutboundToVBC, forcedGateway, result);
        return result;
    }

    //SIP-2118:
    //Payments scenarios are allowed only if the channel variable NEXMOPAY=1 AND the application at hand has the attribute payment-enabled.
    //There are 3 cases of handling applications:
    //1. VBC inbound call - the requestedAppId is provided in the AGI input params
    //2. CustomerDomain call - the requestedAppId is provided in the AGI input params
    //3. LVN configured to forward to an application - the requestedAppId is NOT provided
    //
    //If the application is marked as Payments enabled but the AGI request does not include the NEXMOPAY=1 channel variable
    //reject the call with 403
    private static boolean isValidPaymentScenario(String sessionId, AgiChannel channel, String extension,
            String requestedAppId, String uniqueId, String callerId, String callTermination, String accountId,
            VoiceProduct requestedVoiceProduct, String callOrigin, String clientCallId, String channelId, boolean isVAPIOutboundToVBC) throws AgiException {

        if (Log.isDebugEnabled())
            Log.debug("{} starting  isValidPaymentScenario.  extension={} requestedAppId={}", sessionId, extension, requestedAppId);

        String appId = null;
        //Check if an application is involved in the scenario.
        //If not, then it is not a Payment scenario, as this can happen only via application.
        //Application can be asked explicitly (VBC or DomainService), or by being connected to an LVN.
        if (Objects.nonNull(requestedAppId) && !requestedAppId.isEmpty())
            appId = requestedAppId;
        else
            appId = fetchApplicationId(sessionId, extension);

        if (Objects.isNull(appId))
            return true; //No application involved

        // Now that we have an applicationId - lets check if it is payment-enabled
        Application application = Application.lookup(appId);
        if (Objects.isNull(application)) {
            Log.warn("{} Something fishy.. application not found for configured applicationId {} ", sessionId, appId);
            return true; //No application involved
        }

        Boolean isPaymentEnabled = application.getPaymentEnabled();
        if (Log.isDebugEnabled())
            Log.debug("{} application {} Payment enabled: {}.", sessionId, appId, isPaymentEnabled);

        if (Objects.isNull(isPaymentEnabled) || !isPaymentEnabled)
            return true; //The application is not a Payment one.

        //It is a Payment application - check if it is payment scenario
        if (isNexmoPaymentScenario(sessionId, channel))
            return true;

        //It is not a Payment scenario, while the application has Payments-enabled=true
        //this is an invalid scenario
        return false;
    }

    private static String fetchApplicationId(String sessionId, String extension) {
        ShortCode lvn = getShortCodeForNumber(extension);
        if (Objects.isNull(lvn)) // the extension is not an LVN
            return null;

        // check if the LVN is configured with an app
        ShortCodeMetaData scMetadata = lvn.getMetaData();
        if (Objects.isNull(scMetadata)) {
            Log.warn("{} 'extension' {} is an LVN which does not have scMetadata definition", sessionId, extension);
            return null;
        }
        String confApp = scMetadata.getCallbackAddress(CallbackType.APPLICATION);
        if (Log.isDebugEnabled())
            Log.debug("{} confApp={} ", sessionId, confApp);

        return confApp;
    }

    private static boolean isNexmoPaymentScenario(String sessionId, AgiChannel channel) {
        String nexmoPayScenarioVar = AsteriskAGIServer.getChannelVariable(sessionId, AsteriskAGIServer.NEXMO_PAY, channel);
        return AsteriskAGIServer.NEXMO_PAY_SCENARIO.equals(nexmoPayScenarioVar);
    }

    private static void set403andBuildErrorContext(String extension, String sessionId, String uniqueId,
                                                   AgiChannel channel, String callerId, String callTermination, String channelId, String clientCallId,
                                                   String requestedAppId, String callOrigin, VoiceProduct requestedVoiceProduct, String accountId,
                                                   boolean isVAPIOutboundToVBC, String reason) throws AgiException {

        final Builder errorCtxBuilder = buildErrorContext(extension, sessionId, uniqueId,
                channel, callerId, callTermination, channelId, clientCallId,
                requestedAppId, callOrigin, requestedVoiceProduct, accountId,
                isVAPIOutboundToVBC, reason,
                SIPCode.FORBIDDEN, CallInternalFlag.BLOCKED_ON_INVALID_PAYMENT_SCENARIO,
                QuotaUpdateTask.VoiceApplicationType.SIP_ASTERISK.getVoiceProduct(), VoiceDirection.INBOUND);
        storeAccountErrorContext(sessionId, uniqueId, errorCtxBuilder);

        Log.info("{} error context stored", sessionId);
    }

    protected static void logCallingNumberValidation(String sessionId, String direction, String callerId, String forceSender, String outboundDestination, String accountId) {
        // we will validate the calling number (to ensure it is a valid phone number) and log the disposition here
        String callingNumber = callerId;
        if(forceSender != null) {
            callingNumber = forceSender;
        }
        boolean isWhitelisted = false;
        boolean isValid = false;

        if (!StringUtils.isEmpty(callingNumber)) {
            // check if it was whitelisted
            try {
                isWhitelisted = AsteriskAGIServer.isWhitelistedNumber(callingNumber);
            } catch (Exception e) {
                // catch any exceptions here
                Log.debug("{} exception when checking isWhitelistedNumber for {}: {}", sessionId, callingNumber, e.getMessage());
            }
            // check if it is valid
            try {
                //
                isValid = (PhoneNumberTool.formatToInternationalNumberAndValidate(callingNumber, null) != null);
            } catch (Exception e) {
                // catch all exceptions here
                isValid = false;
            }
        }

        Log.info("{} calling number validation status isValid={}, isWhitelisted={}, accountId={}, callingNumber={}, direction={}, destination={}", sessionId, isValid, isWhitelisted, accountId, callingNumber, direction, outboundDestination);
    }

    public static String checkIfPsipDomain(String sipUri) {
        // compare the sipUri to the list of domain suffixes that are PSIP domains and return the domain name if matches
        if((Core.getInstance().getConfig().getDomainsServiceConfig() != null)
                && (Core.getInstance().getConfig().getDomainsServiceConfig().getDomainSuffixes() != null)) {
            return Core.getInstance().getConfig().getDomainsServiceConfig().getDomainFromSipUri(sipUri);
        }
        return null;
    }

    public static List<String> getDomainTrunkCallRouting(String domainName, String destinationNumber, String sessionId) {
        DomainRoutingResponse response = getDomainRouting(domainName, destinationNumber, sessionId);
        if(response.isSuccess() && (response.getUris() != null)) {
            Log.info("{} domain routing found for domainName={}, destinationNumber={}, uris={}", sessionId, domainName, destinationNumber, response.getUris());
            // URIs need to be cleaned to remove the sip or sips scheme identifier
            return response.getUrisWithoutScheme();
        }
        Log.info("{} domain routing not found for domainName={}, destinationNumber={}, returnCode={}, codeDescription={}", sessionId, domainName, destinationNumber, response.getReturnCode(), response.getCodeDescription());
        return Collections.emptyList();
    }

    public static Optional<DomainRoutingResponse> getDomainCallRoutingForNccoCall(String extension, String sessionId) {
        // the extension value here will be <user>@<psip_domain>, not a full SIP URI
        String[] extensionParts = extension.split("@");
        if(extensionParts.length != 2) {
            // malformed?
            Log.warn("{} domain routing could not be determined due to unparsable extension extension={}", sessionId, extension);
            return Optional.empty();
        }
        // First, take the domain name
        String domainName = extensionParts[1];
        // Then, take the user part
        String ruriUser = extensionParts[0];
        // look up routing
        DomainsServiceClient dsClient = new DomainsServiceClient(Core.getInstance().getConfig().getDomainsServiceConfig());
        // TODO -- need to tell domain service this call is from an NCCO so we don't get an application back
        DomainRoutingResponse response = dsClient.getDomainRouting(domainName, ruriUser, sessionId, true);
        if(response.isSuccess() && (response.getUris() != null)) {
            Log.info("{} domain routing found for domainName={}, destinationNumber={}, uris={}", sessionId, domainName, ruriUser, response.getUris());
            return Optional.of(response);
        }
        Log.info("{} domain routing not found for domainName={}, ruriUser={}, returnCode={}, codeDescription={}", sessionId, domainName, ruriUser, response.getReturnCode(), response.getCodeDescription());
        return Optional.empty();
    }

    public static DomainRoutingResponse getDomainRouting(String domainName, String destinationNumber, String sessionId) {
        DomainsServiceClient dsClient = new DomainsServiceClient(Core.getInstance().getConfig().getDomainsServiceConfig());
        return dsClient.getDomainRouting(domainName, destinationNumber, sessionId);
    }

    private static boolean isValidDemoCall(SmppAccount account, String extension) {
        // check if the account has a watermark and has permitted destination,
        // permitted destination matches with extension
        // and this call was initiated with isDemoAppCli
        if (account != null && account.getMessageWatermark() != null && account.getPermittedDestinations() != null) {
            return(isAllowedByPermittedDestinationList(account, extension));
        }
        return false;
    }

    /**
     * Determines if the call is from an NCCO to a domain
     * @param sessionId
     * @param isInbound
     * @param productClassOverride
     * @param extension
     * @return true if the call is from an NCCO to a domain
     */
    public static boolean isOutboundNCCOConnectToDomain(String sessionId, boolean isInbound, String productClassOverride, String extension) {
        // unlike isOutboundNCCOConnectToSIPEndpoint the extension is formatted as <user>@<psip_domain_name>, not a full SIP URI
        final boolean isOutboundNCCOConnectToDomain = !isInbound
                && VoiceProduct.DOMAIN.getDescriptor().equalsIgnoreCase(productClassOverride);
        Log.info("SessionId {} : Check for Outbound NCCO Connect To domain with inbound = {}, productClassOverride = {}, extension = {} is {}",
                sessionId, isInbound, productClassOverride, extension, isOutboundNCCOConnectToDomain);
        return isOutboundNCCOConnectToDomain;
    }

    private static boolean isDefaultRoute(MtRoutingRule rule) {
        return (Objects.isNull(rule.getBindId()) && Objects.isNull(rule.getRoutingGroupId()) &&
                Objects.isNull(rule.getOa()));

    }

    private static RouteData lookupRoute(String sessionId,
                                         SmppAccount account,
                                         String accountId,
                                         String oa,
                                         String outboundDestination,
                                         String outboundMccMnc) {
        try {
            Route route = Core.getInstance().getMtRouter().lookupRoute(Product.PRODUCT_VOICE_CALL,
                    VoiceProduct.SIP.getDescriptor(),
                    account,
                    oa,
                    outboundDestination,
                    outboundMccMnc,
                    null);

            if (Log.isDebugEnabled())
                Log.debug("SESSION-ID ['{}'] :: Lookup route for account: {} from: {} to: {}  network: {} => {}", sessionId, account, oa, outboundDestination, outboundMccMnc, route);

            if (Objects.isNull(route)) {
                Log.debug("{} sessionId, route is null", sessionId);
                return new RouteData.Builder()
                        .withRoute(null)
                        .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                        .withExc(new RouteNullPointerException("Failed to find a route for outbound destination:" + outboundDestination))
                        .build();
            }

            MtRoutingRule rule = route.getRule();
            if (Objects.isNull(rule)) {
                Log.debug("{} sessionId, rule is null", sessionId);
                return new RouteData.Builder()
                        .withRoute(null)
                        .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                        .withExc(new RouteNullPointerException("Failed to find a route for outbound destination:" + outboundDestination))
                        .build();
            }

            return new RouteData.Builder()
                    .withRoute(route)
                    .withExceptionType(RouteData.RouteExceptionType.NONE)
                    .withExc(null).build();
        } catch (RoutingException | UnroutableMTException | RandomPoolNoMatchSoRejectException e) {
            Log.debug("{} Failed to find a route for outbound destination {} on network {} for account {} oa {} due to {} {}",
                    sessionId, outboundDestination, outboundMccMnc, accountId, oa, e.getMessage(), e.getClass().getName());
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL)
                    .withExc(e).build();
        } catch (DropMessageException e) {
            Log.debug("{} Route {} DROPPING the call for outbound destination {} on network {} for account {} oa {} due to {} {}",
                    sessionId, e.getRoutingRuleSequence(), outboundDestination, outboundMccMnc, accountId, oa, e.getMessage(), e.getClass().getName());
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.DROPMESSAGE)
                    .withExc(e).build();
        } catch (NullPointerException e) {
            Log.debug("{} No Route NullPointer DROPPING the call for outbound destination {} on network {} for account {} oa {} due to {} {}",
                    sessionId, outboundDestination, outboundMccMnc, accountId, oa, e.getMessage(), e.getClass().getName());
            return new RouteData.Builder()
                    .withRoute(null)
                    .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                    .withExc(e).build();
        }
    }
}