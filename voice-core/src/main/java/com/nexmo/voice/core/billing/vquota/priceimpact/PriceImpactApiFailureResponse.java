package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceImpactApiFailureResponse {
    private final static Logger Log = LogManager.getLogger(PriceImpactApiFailureResponse.class);
    private String status;
    private String message;
    private ErrorDetails error;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() { return message; }

    public void setMessage(String message) { this.message = message; }

    public ErrorDetails getError() {
        return error;
    }

    public void setError(ErrorDetails error) {
        this.error = error;
    }

    public static class ErrorDetails {
        private PriceResponse priceResponse;
        private QuotaResponse quotaResponse;

        public PriceResponse getPriceResponse() {
            return priceResponse;
        }

        public void setPriceResponse(PriceResponse priceResponse) {
            this.priceResponse = priceResponse;
        }

        public QuotaResponse getQuotaResponse() {
            return quotaResponse;
        }

        public void setQuotaResponse(QuotaResponse quotaResponse) {
            this.quotaResponse = quotaResponse;
        }
    }

    public static class PriceResponse {
        private String code;
        private String description;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public static class QuotaResponse {
        private String code;
        private String description;
        private String exceptionName;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getExceptionName() {
            return exceptionName;
        }

        public void setExceptionName(String exceptionName) {
            this.exceptionName = exceptionName;
        }
    }

    public void handleResponse(String apiKey) throws QuotaException,
            NotEnoughBalanceException, QuotaDisabledException, QuotaUnderMaintenanceException,
            AccountNotFoundException, IllegalOperationOnSubAccountException, NegativeBalanceRefundException {
        if (this.message != null && !this.message.isEmpty()) {
            Log.error("Error message: {} received for price-impact API for apiKey: {} ", this.message, apiKey);
        }
        if (this.getError() != null) {
            ErrorDetails errorDetails = this.getError();

            // Handle priceResponse if present
            if (errorDetails.getPriceResponse() != null) {
                PriceResponse priceResponse = errorDetails.getPriceResponse();
                String priceCode = priceResponse.getCode();
                String priceDescription = priceResponse.getDescription();
                String priceErrorMessage = String.format("Price Error code: %s, Description: %s", priceCode, priceDescription);
                throw new QuotaException(priceErrorMessage);
            }

            // Handle quotaResponse if present
            if (errorDetails.getQuotaResponse() != null) {
                QuotaResponse quotaResponse = errorDetails.getQuotaResponse();
                String quotaCode = quotaResponse.getCode();
                String quotaDescription = quotaResponse.getDescription();
                String exceptionName = this.getError().getQuotaResponse().getExceptionName();
                String quotaErrorMessage = String.format("Quota Error code %s, Description: %s", quotaCode, quotaDescription);
                if (exceptionName == null) {
                    throw new QuotaException(quotaErrorMessage);
                } else {
                    switch (exceptionName) {
                        case "QuotaDisabledException":
                            throw new QuotaDisabledException(quotaErrorMessage);
                        case "QuotaUnderMaintenanceException":
                            throw new QuotaUnderMaintenanceException(quotaErrorMessage);
                        case "AccountNotFoundException":
                            throw new AccountNotFoundException(quotaErrorMessage);
                        case "IllegalOperationOnSubAccountException":
                            throw new IllegalOperationOnSubAccountException(quotaErrorMessage);
                        case "NotEnoughBalanceException":
                            throw new NotEnoughBalanceException(quotaErrorMessage);
                        case "NegativeBalanceRefundException":
                            throw new NegativeBalanceRefundException(quotaErrorMessage);
                        default:
                            throw new QuotaException(quotaErrorMessage);
                    }
                }
            }
        } else {
            throw new QuotaException("Error details are missing in the response");
        }
    }
}