package com.nexmo.voice.core.cache;

import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;


import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse.MediationCdrField;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.TTSContext;
import com.nexmo.voice.core.types.VoiceDirection;
import com.nexmo.voice.core.types.VoiceProduct;
import com.nexmo.voice.core.types.AsteriskVersion;
import com.nexmo.voice.core.types.SIPCode;
import com.thepeachbeetle.common.callback.types.CallbackMethod;
import com.thepeachbeetle.common.persistentclusteredcache.PersistentCacheEntry;
import com.thepeachbeetle.common.util.DateUtil;


public class VoiceContext extends PersistentCacheEntry<String, String> implements Cloneable, Serializable {

    private static final Logger Log = LogManager.getLogger(VoiceContext.class);

    private static final long serialVersionUID = -5288018871686341657L;

    private final VoiceProduct voiceProduct;
    private final String productClass;
    private final String productClassVersion;

    private final String accountId;
    private final String masterAccountId;

    private final String accountPricingGroup;
    private final String masterAccountPricingGroup;

    private final String ringingTime;

    private final String from;
    private final String forcedSender;
    private final String to;

    private final String countryCode;
    private final String network;
    private final String networkType;
    private final String networkName;
    private final String costPrefix;
    private final String costPrefixGroup;
    private final String costSenderPrefix;
    private final long costTimestamp;
    private final String pricePrefix;
    private final String pricePrefixGroup;
    private final String priceSenderPrefix;
    private final long priceTimestamp;

    private final String gateway;
    private final Long sequenceNumber;
    private Set<String> alternativeGateways;

    private String callbackUrl;
    private CallbackMethod callbackMethod;

    private URI eventUrl;
    private CallbackMethod eventMethod;

    private String internalCallbackUrl;
    private CallbackMethod internalCallbackMethod;

    //TODO Tally: Investigate these two fields - should take part in the gateway fallback impl, but
    //TODO        at the moment it seems like they are not populated correctly - 
    //TODO        and there are no locks around their updates.
    //TODO        Seems like redundant to keep it here, the accurate information arrives from the
    //TODO        CDREvent.
    private volatile String currentGateway;
    private AtomicInteger noRetries = new AtomicInteger(0);

    private final int repeat;
    private final int machineTimeout;

    private final VoiceDirection voiceDirection;

    //For inbound calls - this is the AGI request arrival time.
    //For outbound calls - this is the BrdigeEvent arrival time.
    //In any case it is not really useful, and should not take part in the CDR as the "call date"
    private final long contextCreationDate;

    private String sessionId = null;
    private String connectionId = null;

    private final BillingInfo billingInfo;

    private String voiceInstanceId;

    private ApplicationContext context = null;

    private final String requestIp;

    //callOrigin is set for INBOUND calls. 
    //It will be null for PSTN originated calls, 
    //"sip" for sip originated calls (any sip dial-in), 
    //"application" for direct connection to applicationId (For example: VBC)
    private String callOrigin;

    //callTermination is set for OUTBOUND calls. 
    //Its potential values:
    //"sip" for sip destination
    //"pstn" for regular pstn destination
    private String callTermination;

    //SIP-282 : An indication for the VAPI outbound call to VBC gateway. This type of
    // outbound call impose several special rules, this flag is required while setting the OUTBOUND CDR.
    private boolean isVAPIOutboundToVBC;

    //SIP-230: Add a quota update reference
    private String quotaRef;

    private BillingInfo.Status initialChargingStatus;

    private AGIRequestContext agiRequestContext;

    //SIP-409: Programmable sip: the customer's Domain name
    private String customerDomain;
    private String customerDomainType;
    private boolean accountNotAvailableErrorContext = false;

    //SIP-1389 VSPS
    private final Boolean paymentEnabled;
    private final String paymentRoute;
    private String region;

    private final TTSContext ttsContext;

    private final String stirShaken;

    private final String temporaryOutboundStirShaken;

    private Set<CallInternalFlag> internalFlags;

    private final boolean voiceSkipQuota;
    private final boolean vPricingEnabled;

    private final boolean srtpEnabled;

    private final AsteriskVersion asteriskVersion;

    private final String sipTransport;

    private final LocalDateTime contextCreationTime;

    private final String clientCallId;
    private final boolean isOutboundProgrammableSip;
    private final boolean isSipOriginToLVNToApplication;
    private final String sourceCountryCode;
    private final String id;
    private final String blockingSubsystem;

    private String channelId;
    private long pdd;
    private boolean cdrEventArrived;
    private String applicationId;
    private boolean allowNegativeBalance;
    private boolean isQuotaDown;

    private final String productPath;
    private final String callType;
    private final String numberType;
    private boolean isOutboundNCCOConnectToDomain;

    private final String routingGroup;
    private final String routingOa;
    private final String routingBindId;
    private boolean chargeable;
    private MediationCdrField mediationCdrField;
    private boolean isEmergencyCall;
    private Map<String, SIPCode> emergencyCallFailoverReason;


    private VoiceContext(final String ringingTime,
                         final String productClass,
                         final String productClassVersion,
                         final String accountId,
                         final String masterAccountId,
                         final String accountPricingGroup,
                         final String masterAccountPricingGroup,
                         final String from,
                         final String forcedSender,
                         final String to,
                         final String countryCode,
                         final String network,
                         final String networkType,
                         final String networkName,
                         final String costPrefix,
                         final String costPrefixGroup,
                         final String costSenderPrefix,
                         final long costTimestamp,
                         final String pricePrefix,
                         final String pricePrefixGroup,
                         final String priceSenderPrefix,
                         final long priceTimestamp,
                         final long firstChargedSeconds,
                         final long quotaUpdatesInterval,
                         final BigDecimal pricePerMinute,
                         final BigDecimal forcedPrice,
                         final BigDecimal costPerMinute,
                         final String gateway,
                         final Long sequenceNumber,
                         final Set<String> alternativeGateway,
                         final int repeat,
                         final int machineTimeout,
                         final VoiceDirection voiceDirection,
                         final VoiceProduct voiceProduct,
                         final String callbackUrl,
                         final CallbackMethod callbackMethod,
                         final URI eventUrl,
                         final CallbackMethod eventMethod,
                         final String internalCallbackUrl,
                         final CallbackMethod internalCallbackMethod,
                         final ApplicationContext context,
                         final String requestIp,
                         final String callOrigin,
                         final String callTermination,
                         final boolean isVAPIOutboundToVBC,
                         final String quotaRef,
                         final BillingInfo.Status initialChargingStatus,
                         final AGIRequestContext agiRequestContext,
                         final String customerDomain,
                         final Boolean paymentEnabled,
                         final String paymentRoute,
                         final String region,
                         final TTSContext ttsContext,
                         final String stirShaken,
                         final String temporaryOutboundStirShaken,
                         final Set<CallInternalFlag> internalFlags,
                         final boolean voiceSkipQuota,
                         final boolean srtpEnabled,
                         final AsteriskVersion asteriskVersion,
                         final String sipTransport,
                         final String clientCallId,
                         final boolean isOutboundProgrammableSip,
                         final boolean isSipOriginToLVNToApplication,
                         final String sourceCountryCode,
                         final String id,
                         final String blockingSubsystem,
                         final String productPath,
                         final String customerDomainType,
                         final String callType,
                         final String numberType,
                         final boolean isOutboundNCCOConnectToDomain,
                         final String routingGroup,
                         final String routingOa,
                         final String routingBindId,
                         final boolean chargeable,
                         final boolean vPricingEnabled,
                         final boolean isEmergencyCall,
                         final Map<String, SIPCode> emergencyCallFailoverReason) {
        super();
        if (Log.isDebugEnabled()) {
            Log.debug("About to create VoiceContext for account " + accountId + " in direction " + Objects.toString(voiceDirection, "null"));
        }
        this.ringingTime = ringingTime;
        this.productClass = productClass;
        this.productClassVersion = productClassVersion;
        this.accountId = accountId;
        this.masterAccountId = masterAccountId;
        this.accountPricingGroup = accountPricingGroup;
        this.masterAccountPricingGroup = masterAccountPricingGroup;
        this.from = from;
        this.forcedSender = forcedSender;
        this.to = to;

        this.countryCode = countryCode;
        this.network = network;
        this.networkType = networkType;
        this.networkName = networkName;
        this.costPrefix = costPrefix;
        this.costPrefixGroup = costPrefixGroup;
        this.costSenderPrefix = costSenderPrefix;
        this.costTimestamp = costTimestamp;
        this.pricePrefix = pricePrefix;
        this.pricePrefixGroup = pricePrefixGroup;
        this.priceSenderPrefix = priceSenderPrefix;
        this.priceTimestamp = priceTimestamp;
        this.gateway = gateway;
        this.sequenceNumber = sequenceNumber;
        this.alternativeGateways = alternativeGateway;

        this.currentGateway = this.gateway;
        this.noRetries = new AtomicInteger(0);

        this.repeat = repeat;
        this.machineTimeout = machineTimeout;
        this.voiceDirection = voiceDirection;
        this.contextCreationDate = System.currentTimeMillis();

        this.ttsContext = ttsContext;


        BillingInfo.Status requestedInitStatus;
        if (Objects.isNull(this.initialChargingStatus))
            requestedInitStatus = BillingInfo.Status.NOT_STARTED;
        else
            requestedInitStatus = initialChargingStatus;

        this.billingInfo = new BillingInfo(pricePerMinute,
                                            forcedPrice,
                                            costPerMinute,
                                            firstChargedSeconds,
                                            quotaUpdatesInterval,
                                            requestedInitStatus,
                                            chargeable,
                                            vPricingEnabled);


        this.voiceProduct = voiceProduct;

        this.callbackUrl = callbackUrl;
        this.callbackMethod = callbackMethod;

        this.eventUrl = eventUrl;
        this.eventMethod = eventMethod;

        this.internalCallbackUrl = internalCallbackUrl;
        this.internalCallbackMethod = internalCallbackMethod;

        this.context = context;

        this.requestIp = requestIp;
        this.callOrigin = callOrigin;
        this.callTermination = callTermination;
        this.isVAPIOutboundToVBC = isVAPIOutboundToVBC;
        this.quotaRef = quotaRef;
        this.agiRequestContext = agiRequestContext;
        this.customerDomain = customerDomain;

        this.paymentEnabled = paymentEnabled;
        this.paymentRoute = paymentRoute;
        this.region = region;
        this.stirShaken = stirShaken;
        this.temporaryOutboundStirShaken = temporaryOutboundStirShaken;
        this.internalFlags = internalFlags;
        this.voiceSkipQuota = voiceSkipQuota;
        this.srtpEnabled = srtpEnabled;
        this.asteriskVersion = asteriskVersion;
        this.sipTransport = sipTransport;
        this.contextCreationTime = LocalDateTime.now();
        this.clientCallId = clientCallId;
        this.isOutboundProgrammableSip = isOutboundProgrammableSip;
        this.isSipOriginToLVNToApplication = isSipOriginToLVNToApplication;
        this.sourceCountryCode = sourceCountryCode;
        this.id = id;
        this.blockingSubsystem = blockingSubsystem;
        this.productPath = productPath;
        this.customerDomainType = customerDomainType;
        this.callType = callType;
        this.numberType = numberType;
        this.isOutboundNCCOConnectToDomain = isOutboundNCCOConnectToDomain;
        this.routingGroup = routingGroup;
        this.routingOa = routingOa;
        this.routingBindId = routingBindId;
        this.chargeable = chargeable;
        this.vPricingEnabled = vPricingEnabled;
        this.isEmergencyCall = isEmergencyCall;
        this.emergencyCallFailoverReason = emergencyCallFailoverReason;
    }


    public synchronized void updateGatewayFields(final Set<String> alternativeGateways, final String currentGateway,
            final int noRetriesInc) {
        Log.info("Updating the gateway fields of  sessionId: " + this.sessionId + " used gateway: " + currentGateway);

        if (alternativeGateways != null && currentGateway != null) {
            this.alternativeGateways = alternativeGateways;
            this.currentGateway = currentGateway;
            this.noRetries.addAndGet(noRetriesInc);
        } else {
            Log.warn("An attempt to update the gateway fields of  sessionId: " + this.sessionId
                    + " while  alternativeGateways or currentGateway is null");
        }

        Log.info("Finished updating the gateway fields of sessionId: " + this.sessionId);
    }

    public synchronized void addInternalFlag(final CallInternalFlag internalFlag) {
        Log.info("SessionId: {} - adding internalFlag {} ", this.sessionId, internalFlag);

        if (Objects.isNull(internalFlag))
            return;

        if (Objects.isNull(this.internalFlags)) {
            this.internalFlags = new HashSet<CallInternalFlag>(1);
        }

        internalFlags.add(internalFlag);
    }

    //This method is used for debugging, but should NOT be used for any calculation regarding the
    //final call duration or billing 
    public long getDurationSoFar() {
        long endTime = 0;
        long startTime = 0;
        synchronized (this) {
            endTime = this.getBillingInfo().getCallEndTime();
            startTime = this.getBillingInfo().getCallStartTime();
        }
        if (endTime > 0)
            return endTime - startTime;
        else if (startTime > 0)
            return System.currentTimeMillis() - startTime;
        return 0;
    }

    public BillingInfo getBillingInfo() {
        return this.billingInfo;
    }

    public VoiceProduct getVoiceProduct() {
        return this.voiceProduct;
    }

    public String getProductClass() {
        return this.productClass;
    }

    public String getProductClassVersion() {
        return this.productClassVersion;
    }

    public String getAccountId() {
        return this.accountId;
    }

    public String getMasterAccountId() {
        return this.masterAccountId;
    }

    public String getAccountPricingGroup() {
        return this.accountPricingGroup;
    }

    public String getMasterAccountPricingGroup() {
        return this.masterAccountPricingGroup;
    }

    public String getRingingTime() {
        return this.ringingTime;
    }

    public String getFrom() {
        return this.from;
    }

    public String getForcedSender() {
        return this.forcedSender;
    }

    public String getSender() {
        return this.forcedSender != null ? this.forcedSender : this.from;
    }

    public String getTo() {
        return this.to;
    }

    public String getCountryCode() {
        return this.countryCode;
    }

    public String getNetwork() {
        return this.network;
    }

    public String getNetworkType() {
        return this.networkType;
    }

    public String getNetworkName() {
        return this.networkName;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public void setCallbackUrl(String value) {
        this.callbackUrl = value;
    }

    public void setCallbackMethod(CallbackMethod value) {
        this.callbackMethod = value;
    }

    public void setEventUrl(URI value) {
        this.eventUrl = value;
    }

    public void setEventMethod(CallbackMethod value) {
        this.eventMethod = value;
    }

    public void setInternalCallbackUrl(String value) {
        this.internalCallbackUrl = value;
    }

    public void setInternalCallbackMethod(CallbackMethod value) {
        this.internalCallbackMethod = value;
    }

    public CallbackMethod getCallbackMethod() {
        return this.callbackMethod;
    }

    public URI getEventUrl() {
        return eventUrl;
    }

    public CallbackMethod getEventMethod() {
        return eventMethod;
    }

    public String getInternalCallbackUrl() {
        return this.internalCallbackUrl;
    }

    public CallbackMethod getInternalCallbackMethod() {
        return this.internalCallbackMethod;
    }

    public String getCostPrefix() {
        return this.costPrefix;
    }

    public String getCostPrefixGroup() {
        return this.costPrefixGroup;
    }

    public String getCostSenderPrefix() {
        return this.costSenderPrefix;
    }

    public long getCostTimestamp() {
        return this.costTimestamp;
    }

    public String getPricePrefix() {
        return this.pricePrefix;
    }

    public String getPricePrefixGroup() {
        return this.pricePrefixGroup;
    }

    public String getPriceSenderPrefix() {
        return this.priceSenderPrefix;
    }

    public long getPriceTimestamp() {
        return this.priceTimestamp;
    }

    public String getCurrentGateway() {
        return this.currentGateway;
    }

    public Set<String> getAlternativeGateways() {
        return alternativeGateways;
    }

    public boolean hasAlternativeGateways() {
        return (alternativeGateways != null && alternativeGateways.size() > 1);
    }

    public Long getSequenceNumber() {
        return this.sequenceNumber;
    }

    public int getNoRetries() {
        return this.noRetries.get();
    }

    public int getRepeat() {
        return this.repeat;
    }

    public int getMachineTimeout() {
        return this.machineTimeout;
    }

    public VoiceDirection getVoiceDirection() {
        return this.voiceDirection;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
        if (this.context != null)
            this.context.setParentSessionId(sessionId);
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setAccountNotAvailableErrorContext() {
        accountNotAvailableErrorContext = true;
    }

    public boolean isAccountNotAvailableErrorContext() {
        return accountNotAvailableErrorContext;
    }


    public void setConnectionId(String connectionId) {
        this.connectionId = connectionId;
        if (this.context != null)
            this.context.setParentConnectionId(connectionId);
    }

    public String getConnectionId() {
        return this.connectionId;
    }

    public String getRequestIp() {
        return this.requestIp;
    }

    public String getCallOrigin() {
        return this.callOrigin;
    }

    public String getCallTermination() {
        return this.callTermination;
    }

    public boolean isVAPIOutboundToVBC() {
        return isVAPIOutboundToVBC;
    }

    public String getQuotaRef() {
        return quotaRef;
    }

    public AGIRequestContext getAGIRequestContext() {
        return this.agiRequestContext;
    }

    public String getCustomerDomain() {
        return this.customerDomain;
    }

    public String getCustomerDomainType() {
        return this.customerDomainType;
    }

    public long getContextCreationDate() {
        return this.contextCreationDate;
    }

    public ApplicationContext getApplicationContext() {
        return this.context;
    }

    public Boolean getPaymentEnabled() {
        return paymentEnabled;
    }

    public String getPaymentRoute() {
        return paymentRoute;
    }

    public String getRegion() {
        return region;
    }

    public TTSContext getTtsContext() {
        return ttsContext;
    }

    public String getStirShaken() {
        return stirShaken;
    }

    public String getTemporaryOutboundStirShaken() {
        return temporaryOutboundStirShaken;
    }

    public Set<CallInternalFlag> getInternalFlags() {
        return internalFlags;
    }

    public boolean isVoiceSkipQuota() {
        return voiceSkipQuota;
    }

    public boolean isVpricingEnabled() {
        return vPricingEnabled;
    }

    public boolean isSrtpEnabled() {
        return srtpEnabled;
    }

    public AsteriskVersion getAsteriskVersion() {
        return asteriskVersion;
    }

    public String getSipTransport() {
        return sipTransport;
    }

    public LocalDateTime getContextCreationTime() {
        return contextCreationTime;
    }

    public boolean isOutboundProgrammableSip(){
        return isOutboundProgrammableSip;
    }

    public boolean isSipOriginToLVNToApplication(){
        return isSipOriginToLVNToApplication;
    }

    public String getSourceCountryCode() {
        return sourceCountryCode;
    }

    public String getId() {
        return id;
    }

    public String getBlockingSubsystem() {
        return this.blockingSubsystem;
    }

    public String getProductPath() { return productPath; }

    public String getCallType() { return callType; }
    public String getNumberType() { return numberType; }

    public boolean isOutboundNCCOConnectToDomain() {
        return isOutboundNCCOConnectToDomain;
    }
    public String getRoutingGroup() { return routingGroup; }
    public String getRoutingOa() { return routingOa; }
    public String getRoutingBindId() { return routingBindId; }
    public boolean isChargeable() { return chargeable; }
    public boolean isEmergencyCall() {
        return isEmergencyCall;
    }
    public Map<String, SIPCode> getEmergencyCallFailoverReason() {
        return emergencyCallFailoverReason;
    }

    public void addEmergencyCallFailoverReason(String gateway, SIPCode status) {
        if(emergencyCallFailoverReason == null) {
            emergencyCallFailoverReason = new HashMap<>();
        }
        emergencyCallFailoverReason.put(gateway, status);
    }

    public String getDebugString() {
        StringBuilder sb = new StringBuilder("VoiceContext: NEXMOUUID=");
        sb.append(SipAppUtils.getNexmoUUID(this.sessionId));
        sb.append(", SESSION-ID=");
        sb.append(this.sessionId);
        sb.append(", CONNECTION-ID=");
        sb.append(this.connectionId);
        sb.append(", PRODUCT-CLASS-NAME=");
        sb.append(this.productClass);
        sb.append(", VOICE-PRODUCT=");
        sb.append(this.voiceProduct.name());
        sb.append(", PRODUCT-CLASS-VERSION=");
        sb.append(this.productClassVersion);
        sb.append(", ACCOUNT-ID=");
        sb.append(this.accountId);
        sb.append(", SUPERHUB-ACC=");
        sb.append(this.masterAccountId);
        sb.append(", ACCOUNT-PRICE-GRP=");
        sb.append(this.accountPricingGroup);
        sb.append(", MASTER-ACC-PRICE-GRP=");
        sb.append(this.getMasterAccountPricingGroup());
        sb.append(", FROM=");
        sb.append(this.from);
        sb.append(", TO=");
        sb.append(this.to);
        sb.append(", GW=");
        sb.append(this.currentGateway);
        if (this.alternativeGateways != null && !this.alternativeGateways.isEmpty()) {
            sb.append(", ALT-GW=");
            Iterator<String> it = this.alternativeGateways.iterator();
            while (it.hasNext()) {
                sb.append(it.next());
                if (it.hasNext()) sb.append(", ");
            }
        }
        sb.append(", FORCE-SENDER=");
        sb.append(this.getForcedSender());
        sb.append(", DURATION=");
        sb.append(this.getDurationSoFar());
        sb.append(", DIRECTION=");
        sb.append(this.voiceDirection);
        sb.append(", START=");
        sb.append(DateUtil.createCommonTimestampString(this.billingInfo.getCallStartTime()));
        if (this.context != null)
            sb.append(", ").append(this.context.getDebugString());
        if (this.billingInfo != null)
            sb.append(", ").append(this.billingInfo.toString());
        sb.append(", CALLORIGIN=");
        sb.append(this.getCallOrigin());
        sb.append(", CALLTERMINATION=");
        sb.append(this.getCallTermination());
        sb.append(", isVAPIOutboundToVBC=");
        sb.append(this.isVAPIOutboundToVBC());
        sb.append(", quotaRef=");
        sb.append(this.quotaRef);
        sb.append(", AGIReqCtx=");
        sb.append(this.agiRequestContext);
        sb.append(", CUSTOMER_DOMAIN=");
        sb.append(this.customerDomain);
        sb.append(", CUSTOMER_DOMAIN_TYPE=");
        sb.append(this.customerDomainType);
        sb.append(", isAccountNotAvailableErrorContext=");
        sb.append(this.isAccountNotAvailableErrorContext());
        sb.append(", PAYMENT-ENABLED-APP=");
        sb.append(this.paymentEnabled);
        sb.append(", PAYMENT-ROUTE=");
        sb.append(this.getPaymentRoute());
        sb.append(", REGION=");
        sb.append(this.getRegion());
        if (this.ttsContext != null) {
            sb.append(", ttsContext=");
            sb.append(this.ttsContext);
        }
        sb.append(", STIR-SHAKEN=");
        sb.append(this.getStirShaken());
        sb.append(", TEMPORARY-OUTBOUND-STIR-SHAKEN=");
        sb.append(this.getTemporaryOutboundStirShaken());
        sb.append(", INTERNAL-FLAGS=");
        sb.append(this.getInternalFlags());
        sb.append(",VOICE-SKIP-QUOTA=");
        sb.append(this.isVoiceSkipQuota());
        sb.append(",SRTP_ENABLED=");
        sb.append(this.isSrtpEnabled());
        sb.append(",ASTERISK-VERSION=");
        sb.append(this.getAsteriskVersion());
        sb.append(",VSIP_TRANSPORT=");
        sb.append(this.getSipTransport());
        sb.append(",CONTEXT_CREATION_TIME=");
        sb.append(this.contextCreationTime);
        sb.append(", CLIENT_CALL_ID=");
        sb.append(this.clientCallId);
        sb.append(", OUTBOUND-PROGRAMMABLE-SIP=");
        sb.append(this.isOutboundProgrammableSip);
        sb.append(", SOURCE_COUNTRYCODE=");
        sb.append(this.sourceCountryCode);
        sb.append(", ID=");
        sb.append(this.id);
        sb.append(", SUBSYSTEM=" + this.blockingSubsystem);
        sb.append(", SIP-ORIGIN-TO-LVN-TO-APPLICATION=");
        sb.append(this.isSipOriginToLVNToApplication);
        sb.append(", PRODUCT-PATH=");
        sb.append(this.productPath);
        sb.append(", CALL-TYPE=");
        sb.append(this.callType);
        sb.append(", NUMBER-TYPE=");
        sb.append(this.numberType);
        sb.append(", isOutboundNCCOConnectToDomain=");
        sb.append(this.isOutboundNCCOConnectToDomain);
        sb.append(", routingGroup=");
        sb.append(this.routingGroup);
        sb.append(", routingOa=");
        sb.append(this.routingOa);
        sb.append(", routingBindId=");
        sb.append(this.routingBindId);
        sb.append(", CHARGEABLE=");
        sb.append(this.chargeable);
        sb.append(", VPRICING-ENABLED=");
        sb.append(this.vPricingEnabled);
        if (this.mediationCdrField != null) {
            sb.append(", ");
            sb.append(this.mediationCdrField);
        }
        sb.append(", isEmergencyCall=");
        sb.append(this.isEmergencyCall);
        return sb.toString();
    }

    public void dumpToLog() {
        Log.info("Dumping context =| " + getDebugString());
    }

    @Override
    public String getOuterKey() {
        return this.sessionId;
    }

    @Override
    public String getInnerKey() {
        return this.connectionId;
    }

    public void setVoiceInstanceId(String voiceInstanceId) {
        this.voiceInstanceId = voiceInstanceId;
    }

    /**
     * This should not be used once the VoiceContext is cached. It could cause inconsistencies in a
     * clustered scenario
     *
     * @param context
     */
    public void setApplicationContext(ApplicationContext context) {
        if (this.context != null)
            Log.warn("Potential unsafe overwriting of a VoiceContext's ApplicationContext :: sessionId['" + this.sessionId + "'] connectionId ['" + this.connectionId + "']");
        context.setParentConnectionId(this.connectionId);
        context.setParentSessionId(this.sessionId);
        this.context = context;
    }

    public String getVoiceInstanceId() {
        return this.voiceInstanceId;
    }

    public String getClientCallId() {
        return this.clientCallId;
    }

    public boolean isCdrEventArrived() {
        return this.cdrEventArrived;
    }

    public boolean allowNegativeBalance() {
        return this.allowNegativeBalance;
    }

    public boolean isQuotaDown() {
        return this.isQuotaDown;
    }

    public void setCdrEventArrived(boolean cdrEventArrived) {
        this.cdrEventArrived = cdrEventArrived;
    }

    public void setNegativeBalance(boolean allowNegativeBalance) {
        this.allowNegativeBalance = allowNegativeBalance;
    }

    public void setQuotaDown(boolean isQuotaDown) {
        this.isQuotaDown = isQuotaDown;
    }

    public void setMediationCdrField(MediationCdrField mediationCdrField) {
        this.mediationCdrField = mediationCdrField;
    }

    public void setChargeable(boolean chargeable) {
        this.chargeable = chargeable;
    }

    public MediationCdrField getMediationCdrField() {
        return this.mediationCdrField;
    }


    public static class Builder {
        private VoiceProduct voiceProduct;
        // this is different from the voice product attr
        // might be forced by a different app
        private String productClass;
        private String productClassVersion;

        private String accountId;
        private String masterAccountId;

        private String accountPricingGroup;
        private String masterAccountPricingGroup;

        private String ringingTime;

        private String from;
        private String forcedSender;
        private String to;

        private String countryCode;
        private String network;
        private String networkType;
        private String networkName;
        private String costPrefix;
        private String costPrefixGroup;
        private String costSenderPrefix;
        private long costTimestamp;
        private String pricePrefix;
        private String pricePrefixGroup;
        private String priceSenderPrefix;
        private long priceTimestamp;

        private long firstChargedSeconds; // minimum amount of units charged
        private long quotaUpdatesInterval; // how many units need to elapse to charge

        private BigDecimal pricePerMinute;
        private BigDecimal forcedPrice;
        private BigDecimal costPerMinute;

        private String gateway;
        private Long sequenceNumber;
        private Set<String> alternativeGateways;

        private String callbackUrl;
        private CallbackMethod callbackMethod;

        private URI eventUrl;
        private CallbackMethod eventMethod;

        private String internalCallbackUrl;
        private CallbackMethod internalCallbackMethod;

        private int repeat;
        private int machineTimeout;

        private VoiceDirection voiceDirection;

        private ApplicationContext context;

        private String requestIp;

        private String callOrigin;

        private String callTermination;

        private boolean isVAPIOutboundToVBC;

        private String quotaRef;

        private BillingInfo.Status initialChargingStatus;

        private AGIRequestContext agiRequestContext;

        private String customerDomain;
        private String customerDomainType;

        private Boolean paymentEnabled;
        private String paymentRoute;
        private String region;

        private final Set<String> tiedConnectionIds = new HashSet<>();
        private TTSContext ttsContext;

        private String stirShaken;
        private String temporaryOutboundStirShaken;

        private Set<CallInternalFlag> internalFlags;

        private boolean voiceSkipQuota;
        private boolean vPricingEnabled;
        private boolean srtpEnabled;

        private AsteriskVersion asteriskVersion;

        private String sipTransport;
        private String clientCallId;
        private boolean isOutboundProgrammableSip;
        private boolean isSipOriginToLVNToApplication;
        private String sourceCountryCode;
        private String id;
        private String blockingSubsystem;

        private String productPath;
        private String callType;
        private String numberType;

        private boolean isOutboundNCCOConnectToDomain;

        private String routingGroup;
        private String routingOa;
        private String routingBindId;
        private boolean chargeable;
        private boolean isEmergencyCall;
        private Map<String, SIPCode> emergencyCallFailoverReason;

        public Builder withVoiceProduct(VoiceProduct voiceProduct) {
            this.voiceProduct = voiceProduct;
            return this;
        }

        public Builder withProductClass(String productClass) {
            this.productClass = productClass;
            return this;
        }

        public Builder withProductClassVersion(String productClassVersion) {
            this.productClassVersion = productClassVersion;
            return this;
        }

        public Builder withAccountId(String accountId) {
            this.accountId = accountId;
            return this;
        }

        public Builder withMasterAccountId(String masterAccountId) {
            this.masterAccountId = masterAccountId;
            return this;
        }

        public Builder withAccountPricingGroup(String accountPricingGroup) {
            this.accountPricingGroup = accountPricingGroup;
            return this;
        }

        public Builder withMasterAccountPricingGroup(String masterAccountPricingGroup) {
            this.masterAccountPricingGroup = masterAccountPricingGroup;
            return this;
        }

        public Builder withRingingTime(String ringingTime) {
            this.ringingTime = ringingTime;
            return this;
        }

        public Builder withFrom(String from) {
            this.from = from;
            return this;
        }

        public Builder withForcedSender(String forcedSender) {
            this.forcedSender = forcedSender;
            return this;
        }

        public Builder withTo(String to) {
            this.to = to;
            return this;
        }

        public Builder withCountryCode(String countryCode) {
            this.countryCode = countryCode;
            return this;
        }

        public Builder withNetwork(String network) {
            this.network = network;
            return this;
        }

        public Builder withNetworkType(String networkType) {
            this.networkType = networkType;
            return this;
        }

        public Builder withNetworkName(String networkName) {
            this.networkName = networkName;
            return this;
        }

        public Builder withCostPrefix(String costPrefix) {
            this.costPrefix = costPrefix;
            return this;
        }

        public Builder withCostPrefixGroup(String costPrefixGroup) {
            this.costPrefixGroup = costPrefixGroup;
            return this;
        }

        public Builder withCostSenderPrefix(String costSenderPrefix) {
            this.costSenderPrefix = costSenderPrefix;
            return this;
        }

        public Builder withCostTimestamp(long costTimestamp) {
            this.costTimestamp = costTimestamp;
            return this;
        }

        public Builder withPricePrefix(String pricePrefix) {
            this.pricePrefix = pricePrefix;
            return this;
        }

        public Builder withPricePrefixGroup(String pricePrefixGroup) {
            this.pricePrefixGroup = pricePrefixGroup;
            return this;
        }

        public Builder withPriceSenderPrefix(String priceSenderPrefix) {
            this.priceSenderPrefix = priceSenderPrefix;
            return this;
        }

        public Builder withPriceTimestamp(long priceTimestamp) {
            this.priceTimestamp = priceTimestamp;
            return this;
        }

        public Builder withFirstChargedSeconds(long firstChargedSeconds) {
            this.firstChargedSeconds = firstChargedSeconds;
            return this;
        }

        public Builder withQuotaUpdatesInterval(long quotaUpdatesInterval) {
            this.quotaUpdatesInterval = quotaUpdatesInterval;
            return this;
        }

        public Builder withPricePerMinute(BigDecimal pricePerMinute) {
            this.pricePerMinute = pricePerMinute;
            return this;
        }

        public Builder withCostPerMinute(BigDecimal costPerMinute) {
            this.costPerMinute = costPerMinute;
            return this;
        }

        public Builder withForcedPrice(BigDecimal forcedPrice) {
            this.forcedPrice = forcedPrice;
            return this;
        }

        public Builder withGateway(String gateway) {
            this.gateway = gateway;
            return this;
        }

        public Builder withAlternativeGateways(Set<String> alternativeGateways) {
            this.alternativeGateways = alternativeGateways;
            return this;
        }

        public Builder withSequenceNumber(Long sequenceNumber) {
            this.sequenceNumber = sequenceNumber;
            return this;
        }

        public Builder withCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
            return this;
        }

        public Builder withCallbackMethod(CallbackMethod callbackMethod) {
            this.callbackMethod = callbackMethod;
            return this;
        }

        public Builder withEventUrl(URI eventUrl) {
            this.eventUrl = eventUrl;
            return this;
        }

        public Builder withEventMethod(CallbackMethod eventMethod) {
            this.eventMethod = eventMethod;
            return this;
        }

        public Builder withInternalCallbackUrl(String internalCallbackUrl) {
            this.internalCallbackUrl = internalCallbackUrl;
            return this;
        }

        public Builder withInternalCallbackMethod(CallbackMethod internalCallbackMethod) {
            this.internalCallbackMethod = internalCallbackMethod;
            return this;
        }

        public Builder withRepeat(int repeat) {
            this.repeat = repeat;
            return this;
        }

        public Builder withMachineTimeout(int machineTimeout) {
            this.machineTimeout = machineTimeout;
            return this;
        }

        public Builder withVoiceDirection(VoiceDirection voiceDirection) {
            this.voiceDirection = voiceDirection;
            return this;
        }

        public Builder withApplicationContext(ApplicationContext context) {
            this.context = context;
            return this;
        }

        public Builder withTiedConnectionIds(Set<String> tiedConnectionIds) {
            if (tiedConnectionIds != null)
                this.tiedConnectionIds.addAll(tiedConnectionIds);
            return this;
        }

        public Builder withRequestIp(String requestIp) {
            this.requestIp = requestIp;
            return this;
        }

        public Builder withCallOrigin(String callOrigin) {
            this.callOrigin = callOrigin;
            return this;
        }

        public Builder withCallTermination(String callTermination) {
            this.callTermination = callTermination;
            return this;
        }

        public Builder withIsVAPIOutboundToVBC(boolean isVAPIOutboundToVBC) {
            this.isVAPIOutboundToVBC = isVAPIOutboundToVBC;
            return this;
        }

        public Builder withQuotaRef(String quotaRef) {
            this.quotaRef = quotaRef;
            return this;
        }

        public Builder withInitialChargingStatus(BillingInfo.Status initialChargingStatus) {
            this.initialChargingStatus = initialChargingStatus;
            return this;
        }

        public Builder withAGIRequestContext(AGIRequestContext agiRequestContext) {
            this.agiRequestContext = agiRequestContext;
            return this;
        }

        public Builder withCustomerDomain(String customerDomain) {
            this.customerDomain = customerDomain;
            return this;
        }

        public Builder withCustomerDomainType(String customerDomainType) {
            this.customerDomainType = customerDomainType;
            return this;
        }

        public Builder withPaymentEnabled(Boolean paymentEnabled) {
            this.paymentEnabled = paymentEnabled;
            return this;
        }

        public Builder withPaymentRoute(String paymentRoute) {
            this.paymentRoute = paymentRoute;
            return this;
        }

        public Builder withRegion(String region) {
            this.region = region;
            return this;
        }

        public Builder withTTSContext(TTSContext ttsContext) {
            this.ttsContext = ttsContext;
            return this;
        }

        public Builder withStirShaken(String stirShaken) {
            this.stirShaken = stirShaken;
            return this;
        }

        public Builder withTemporaryOutboundStirShaken(String temporaryOutboundStirShaken) {
            this.temporaryOutboundStirShaken = temporaryOutboundStirShaken;
            return this;
        }

        public synchronized Builder withInternalFlags(Set<CallInternalFlag> internalFlags) {
            if (Objects.isNull(internalFlags))
                return this;

            if (Objects.isNull(this.internalFlags)) {
                this.internalFlags = new HashSet<CallInternalFlag>();
            }
            this.internalFlags.addAll(internalFlags);

            return this;
        }

        public synchronized Builder withInternalFlag(CallInternalFlag internalFlag) {
            if (Objects.isNull(internalFlag))
                return this;

            if (Objects.isNull(this.internalFlags)) {
                this.internalFlags = new HashSet<CallInternalFlag>();
            }
            this.internalFlags.add(internalFlag);

            return this;
        }


        public Builder withVoiceSkipQuota(boolean voiceSkipQuota){
            this.voiceSkipQuota = voiceSkipQuota;
            return this;
        }

        public Builder withVpricingEnabled(boolean vPricingEnabled){
            this.vPricingEnabled = vPricingEnabled;
            return this;
        }

        public Builder withSrtpEnabled(boolean srtpEnabled) {
            this.srtpEnabled = srtpEnabled;
            return this;
        }

        public Builder withAsteriskVersion(AsteriskVersion asteriskVersion) {
            this.asteriskVersion = asteriskVersion;
            return this;
        }

        public Builder withSipTransport(String sipTransport) {
            this.sipTransport = sipTransport;
            return this;
        }

        public Builder withClientCallId(String clientCallId){
            this.clientCallId = clientCallId;
            return this;
        }

        public Builder withIsOutboundProgrammableSip(boolean isOutboundProgrammableSip){
            this.isOutboundProgrammableSip = isOutboundProgrammableSip;
            return this;
        }

        public Builder withIsSipOriginToLVNToApplication(boolean isSipOriginToLVNToApplication){
            this.isSipOriginToLVNToApplication = isSipOriginToLVNToApplication;
            return this;
        }

        public Builder withSourceCountryCode(String sourceCountryCode) {
            this.sourceCountryCode = sourceCountryCode;
            return this;
        }

        public Builder withId(String id) {
            this.id = id;
            return this;
        }

        public Builder withBlockingSubsystem(String blockingSubsystem) {
            this.blockingSubsystem = blockingSubsystem;
            return this;
        }

        public Builder withProductPath(String productPath) {
            this.productPath = productPath;
            return this;
        }

        public Builder withCallType(String callType) {
            this.callType = callType;
            return this;
        }

        public Builder withNumberType(String numberType) {
            this.numberType = numberType;
            return this;
        }

        public Builder withIsOutboundNCCOConnectToDomain(boolean isOutboundNCCOConnectToDomain) {
            this.isOutboundNCCOConnectToDomain = isOutboundNCCOConnectToDomain;
            return this;
        }

        public Builder withRoutingGroup(String routingGroup) {
            this.routingGroup = routingGroup;
            return this;
        }
        public Builder withRoutingOa(String routingOa) {
            this.routingOa = routingOa;
            return this;
        }
        public Builder withRoutingBindId(String routingBindId) {
            this.routingBindId = routingBindId;
            return this;
        }

        public Builder withChargeable(boolean chargeable) {
            this.chargeable = chargeable;
            return this;
        }

        public Builder withIsEmergencyCall(boolean isEmergencyCall) {
            this.isEmergencyCall = isEmergencyCall;
            return this;
        }

        public Builder withEmergencyCallFailoverReason(Map<String, SIPCode> emergencyCallFailoverReason) {
            this.emergencyCallFailoverReason = emergencyCallFailoverReason;
            return this;
        }

        public ApplicationContext getApplicationContext() {
            return this.context;
        }

        public VoiceContext build() {
            return new VoiceContext(this.ringingTime,
                                    this.productClass,
                                    this.productClassVersion,
                                    this.accountId,
                                    this.masterAccountId,
                                    this.accountPricingGroup,
                                    this.masterAccountPricingGroup,
                                    this.from,
                                    this.forcedSender,
                                    this.to,
                                    this.countryCode,
                                    this.network,
                                    this.networkType,
                                    this.networkName,
                                    this.costPrefix,
                                    this.costPrefixGroup,
                                    this.costSenderPrefix,
                                    this.costTimestamp,
                                    this.pricePrefix,
                                    this.pricePrefixGroup,
                                    this.priceSenderPrefix,
                                    this.priceTimestamp,
                                    this.firstChargedSeconds,
                                    this.quotaUpdatesInterval,
                                    this.pricePerMinute,
                                    this.forcedPrice,
                                    this.costPerMinute,
                                    this.gateway,
                                    this.sequenceNumber,
                                    this.alternativeGateways,
                                    this.repeat,
                                    this.machineTimeout,
                                    this.voiceDirection,
                                    this.voiceProduct,
                                    this.callbackUrl,
                                    this.callbackMethod,
                                    this.eventUrl,
                                    this.eventMethod,
                                    this.internalCallbackUrl,
                                    this.internalCallbackMethod,
                                    this.context,
                                    this.requestIp,
                                    this.callOrigin,
                                    this.callTermination,
                                    this.isVAPIOutboundToVBC,
                                    this.quotaRef,
                                    this.initialChargingStatus,
                                    this.agiRequestContext,
                                    this.customerDomain,
                                    this.paymentEnabled,
                                    this.paymentRoute,
                                    this.region,
                                    this.ttsContext,
                                    this.stirShaken,
                                    this.temporaryOutboundStirShaken,
                                    this.internalFlags,
                                    this.voiceSkipQuota,
                                    this.srtpEnabled,
                                    this.asteriskVersion,
                                    this.sipTransport,
                                    this.clientCallId,
                                    this.isOutboundProgrammableSip,
                                    this.isSipOriginToLVNToApplication,
                                    this.sourceCountryCode,
                                    this.id,
                                    this.blockingSubsystem,
                                    this.productPath,
                                    this.customerDomainType,
                                    this.callType,
                                    this.numberType,
                                    this.isOutboundNCCOConnectToDomain,
                                    this.routingGroup,
                                    this.routingOa,
                                    this.routingBindId,
                                    this.chargeable,
                                    this.vPricingEnabled,
                                    this.isEmergencyCall,
                                    this.emergencyCallFailoverReason);
        }
    }

    //I couldn't find any reference to this, but it is an abstract that must be implemented.
    @Override
    public void handlePurge() throws Exception {
        Log.info("Ignoring  handlePurge for sessionId {}", this.sessionId);
    }

    //messaging.jar handling the cache, is verifying if it is allowed to evict old entries - so "NO" :
    //we do not want any VoiceContext to be removed not as part of our logic.
    @Override
    public boolean forcePurge() {
        Log.info("Ignoring  forcePurge for sessionId {}", this.sessionId);
        return false;
    }

    //messaging.jar handling the cache, is verifying if it is allowed to evict old entries - so "NO" :
    //we do not want any VoiceContext to be removed not as part of our logic.
    @Override
    public boolean allowPurge() {
        Log.info("Ignoring  forcePurge for sessionId {}", this.sessionId);
        return false;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        final VoiceContext clone = (VoiceContext) super.clone();
        return clone;
    }

}
