package com.nexmo.voice.core.types;


import java.io.Serializable;

public enum SIPCode implements Serializable {

    UNKNOWN                     (-1, "Unknown sip code"),
    UNDEFINED                   (0, "Undefined sip code"),
    RINGING                     (180, "The call is ringing"),
    SESSION_IN_PROGRESS         (183, "The call is in progress"),
    OK                          (200, "The call was successful."),
    UNAUTHORIZED                (401, "This request requires user authentication"),
    PAYMENT_REQUIRED            (402, "Not enough credit."),
    BANNED_CALL_ENDED           (412, "Conditional Request Failed"),
    FORBIDDEN                   (403, "The call was rejected"),
    FORBIDDEN_UNKNOWN_CLI       (403, "The call was rejected due to unknown cli rules"),
    NOT_FOUND                   (404, "Recipient does not exist in the specified domain"),
    REQUEST_TIMEOUT             (408, "Could not find the recipient in time"),
    GONE                        (410, "The recipient existed once, but not anymore"),
    BAD_EXTENSION               (420, "Bad SIP Protocol Extension used"),
    FLOW_FAILED                 (430, "A specific flow to a user agent has failed, although other flows may succeed."),
    TEMPORARILY_UNAVAILABLE     (480, "Callee currently unavailable"),
    ADDRESS_INCOMPLETE          (484, "Request URI incomplete"),
    BUSY_HERE                   (486, "Callee is busy"),
    REQUEST_TERMINATED          (487, "Request terminated by BYE or CANCEL"),
    NOT_ACCEPTABLE              (488, "Some aspect of the session description or the request-URI is not acceptable"),
    SERVER_INTERNAL_ERROR       (500, "Internal error occurred."),
    NOT_IMPLEMENTED             (501, "The server does not have the ability to fulfill the request"),
    BAD_GATEWAY                 (502, "Invalid response received when attempting to fulfill the request"),
    SERVICE_UNAVAILABLE         (503, "The server is undergoing maintenance or is temporarily overloaded and so cannot process the request"),
    SERVER_TIMEOUT              (504, "The server attempted to access another server in attempting to process the request, and did not receive a prompt response."),
    VERSION_NOT_SUPPORTED       (505, "The SIP protocol version in the request is not supported by the server."),
    MESSAGE_TOO_LARGE           (513, "The request message length is longer than the server can process."),
    PRECONDITION_FAILURE        (580, "The server is unable or unwilling to meet some constraints specified in the offer."),
    BUSY_EVERYWHERE             (600, "All possible destinations are busy."),
    DECLINED                    (603, "The request was declined by the destination.");

    private final int code;
    private final String message;

    private SIPCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static SIPCode getFromCode(int code) {
        for (SIPCode sip : values()) {
            if (sip.getCode() == code)
                return sip;
        }
        return UNKNOWN;
    }

}
