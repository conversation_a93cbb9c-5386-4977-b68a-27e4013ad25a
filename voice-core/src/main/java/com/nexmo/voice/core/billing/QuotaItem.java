package com.nexmo.voice.core.billing;

import com.nexmo.voice.core.cache.VoiceContext;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;

import java.io.*;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

/**
 * This class encapsulates all the data that is needed for making request to Quota API
 * This class stores both the VoiceContext that refers to data in actual VoiceContext cache, as well as hold a clone of the same.
 * cloned VC will be snapshot of VC data at that particular moment, a request to Quota API is made.
 * Amount is the amount we will be charging the consumer
 * Operation can be consume/refund
 * Event type will be one of the ENUM as mentioned below
 * CDREvent is an indication of whether CDREvent has arrived for this particular call
 * VoiceContextCount indicates number of voiceContexts available for a particular call
 * Duration is the duration we will be charging the consumer
 */

public class QuotaItem implements Cloneable, Serializable {

    private static final Logger Log = LogManager.getLogger(QuotaItem.class);
    private static final long serialVersionUID = -5129259777257872418L;
    private String id;
    public VoiceContext voiceContext;
    public VoiceContext clonedContext;
    public BigDecimal amount;
    public QuotaUpdateDetails.Operation operation;
    public EventType eventType;
    public CdrEvent cdrEvent;
    public int voiceContextCount;
    public int quotaExceptionCounter;
    public int bridgeExceptionCounter;
    private Long duration;
    private boolean markedForSkip;

    public QuotaItem(VoiceContext vctx, EventType eventType, QuotaUpdateDetails.Operation
            operation, BigDecimal amount, CdrEvent cdrEvent, Long duration) {
        this.id = UUID.randomUUID().toString();
        this.voiceContext = vctx;
        try {
            VoiceContext clonedCtx = (VoiceContext) vctx.clone();
            this.clonedContext = clonedCtx;
        } catch (CloneNotSupportedException e) {
            Log.warn("An error occurred while cloning voicecontext for :{}", this.voiceContext.getSessionId());
        }
        this.eventType = eventType;
        this.operation = operation;
        this.amount = amount;
        this.cdrEvent = cdrEvent;
        this.duration = duration;
        this.markedForSkip = false;

    }

    public String getId() {
        return id;
    }

    public VoiceContext getClonedContext() {
        return clonedContext;
    }

    public VoiceContext getVoiceContext() {
        return voiceContext;
    }

    public void setVoiceContext(VoiceContext voiceContext) {
        this.voiceContext = voiceContext;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOperation() {
        return String.valueOf(operation);
    }

    public void setOperation(QuotaUpdateDetails.Operation operation) {
        this.operation = operation;
    }

    public EventType getEventType() {
        return eventType;
    }

    public CdrEvent getCdrEvent() {
        return cdrEvent;
    }

    public void setCdrEvent(CdrEvent cdrEvent) {
        this.cdrEvent = cdrEvent;
    }

    public int getVoiceContextCount() {
        return voiceContextCount;
    }

    public void setVoiceContextSize(int voiceContextCount) {
        this.voiceContextCount = voiceContextCount;
    }

    public int getQuotaExceptionCounter() {
        return quotaExceptionCounter;
    }

    public void setQuotaExceptionCounter(int quotaExceptionCounter) {
        this.quotaExceptionCounter = quotaExceptionCounter;
    }

    public int getBridgeExceptionCounter() {
        return bridgeExceptionCounter;
    }

    public void setBridgeExceptionCounter(int bridgeExceptionCounter) {
        this.bridgeExceptionCounter = bridgeExceptionCounter;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public static byte[] serializeToByteArray(QuotaItem object) throws IOException, CloneNotSupportedException {
        // clone item
        QuotaItem item = (QuotaItem) object.clone();
        VoiceContext vctx = item.voiceContext;

        // remove unwanted properties during initialization
        if (vctx != null) {
            if (StringUtils.isBlank(vctx.getCallbackUrl()))
                vctx.setCallbackUrl("");
            if (ObjectUtils.isEmpty(item.voiceContext.getCallbackMethod()))
                vctx.setCallbackMethod(null);
            if (ObjectUtils.isEmpty(vctx.getEventUrl()))
                vctx.setEventUrl(null);
            if (ObjectUtils.isEmpty(vctx.getEventMethod()))
                vctx.setEventMethod(null);
            if (StringUtils.isBlank(vctx.getInternalCallbackUrl()))
                vctx.setInternalCallbackUrl("");
            if (ObjectUtils.isEmpty(vctx.getInternalCallbackMethod()))
                vctx.setInternalCallbackMethod(null);

            if (vctx.getTtsContext() != null) {
                if (StringUtils.isBlank(vctx.getTtsContext().getCallbackUrl()))
                    vctx.getTtsContext().setCallbackUrl("");
                if (ObjectUtils.isEmpty(vctx.getTtsContext().getCallbackMethod()))
                    vctx.getTtsContext().setCallbackMethod(null);
            }
        }

        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        ObjectOutputStream os = new ObjectOutputStream(bout);
        os.writeObject(item);
        os.close();

        return bout.toByteArray();
    }

    public static QuotaItem deSerializeObject(byte[] bytes) throws IOException, ClassNotFoundException {
        ByteArrayInputStream bin = new ByteArrayInputStream(bytes);
        ObjectInputStream ois = new ObjectInputStream(bin);
        return (QuotaItem) ois.readObject();
    }


    public static enum EventType {
        BRIDGE,
        DELTA,
        CDR,
        SKIP_QUOTA,
        HANDLE_FINAL_CALL
    }

    public boolean isMarkedForSkip() {
        return markedForSkip;
    }

    public void setMarkedForSkip(boolean markedForSkip) {
        this.markedForSkip = markedForSkip;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("QuotaItem :: ");
        sb.append("eventType: ");
        sb.append(eventType);
        sb.append(" :: amount :: ");
        sb.append(Objects.nonNull(amount) ? amount.toPlainString() : "null");
        sb.append(" :: operation :: ");
        sb.append(ObjectUtils.isNotEmpty(operation) ? operation : "null");
        sb.append(" :: duration :: ");
        sb.append(Objects.nonNull(duration) ? duration : "null");
        sb.append(" :: isMarkedForSkip :: ");
        sb.append(markedForSkip);
        sb.append(" :: voiceContext :: ");
        sb.append(voiceContext.getDebugString());
        return sb.toString();
    }

}
