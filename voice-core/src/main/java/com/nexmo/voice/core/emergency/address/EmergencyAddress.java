package com.nexmo.voice.core.emergency.address;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmergencyAddress {

    @JsonProperty("id")
    private String id;
    @JsonProperty("address_name")
    private String addressName;
    @JsonProperty("address_line_1")
    private String addressLine1;
    @JsonProperty("address_line_2")
    private String addressLine2;
    @JsonProperty("city")
    private String city;
    @JsonProperty("region")
    private String region;
    @JsonProperty("type")
    private AddressType addressType;
    @JsonProperty("address_location_type")
    private LocationType addressLocationType;
    @JsonProperty("postal_code")
    private String postalCode;
    @JsonProperty("country")
    private String country;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAddressName() {
        return addressName;
    }

    public void setAddressName(String addressName) {
        this.addressName = addressName;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public AddressType getAddressType() {
        return addressType;
    }

    public void setAddressType(AddressType addressType) {
        this.addressType = addressType;
    }

    public LocationType getAddressLocationType() {
        return addressLocationType;
    }

    public void setAddressLocationType(LocationType addressLocationType) {
        this.addressLocationType = addressLocationType;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "EmergencyAddress{" +
                "id='" + id + '\'' +
                ", addressName='" + addressName + '\'' +
                ", addressLine1='" + addressLine1 + '\'' +
                ", addressLine2='" + addressLine2 + '\'' +
                ", city='" + city + '\'' +
                ", region='" + region + '\'' +
                ", addressType=" + addressType +
                ", addressLocationType=" + addressLocationType +
                ", postalCode='" + postalCode + '\'' +
                ", country='" + country + '\'' +
                '}';
    }

    public enum AddressType {
        EMERGENCY;

        @JsonCreator
        public static AddressType fromString(String value) {
            return value == null ? null : AddressType.valueOf(value.toUpperCase());
        }
    }

    public enum LocationType {
        BUSINESS,
        RESIDENTIAL;

        @JsonCreator
        public static LocationType fromString(String value) {
            return value == null ? null : LocationType.valueOf(value.toUpperCase());
        }

    }

}
