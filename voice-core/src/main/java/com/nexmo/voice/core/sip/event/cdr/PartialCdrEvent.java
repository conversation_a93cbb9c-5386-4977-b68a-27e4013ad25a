package com.nexmo.voice.core.sip.event.cdr;

import org.apache.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;

/**
 * This class will store partial CDR event data
 *
 * It's a combination of a CdrEvent and AbstractCdrEventUserData
 *
 * <AUTHOR>
 */
public class PartialCdrEvent {

    private static final Logger Log = Logger.getLogger(PartialCdrEvent.class.getName());

    private CdrEvent cdrEvent;
    private AbstractCdrEventUserData cdrEventUserData;

    public PartialCdrEvent(CdrEvent cdrEvent, AbstractCdrEventUserData cdrEventUserData) {
        this.cdrEvent = cdrEvent;
        this.cdrEventUserData = cdrEventUserData;
        Log.info("PartialCdrEvent initialization completed");
    }

    public CdrEvent getCdrEvent() {
        return cdrEvent;
    }

    public AbstractCdrEventUserData getCdrEventUserData() {
        return cdrEventUserData;
    }
}
