package com.nexmo.voice.core.billing;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Objects;

import com.nexmo.voice.core.billing.exceptions.AccountBannedException;
import com.nexmo.voice.core.logger.SIPAppLogger;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import io.prometheus.client.Gauge;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;

import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.ApplicationContext;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.gateway.asterisk.AsteriskActionIssuer;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.SIPCode;
import com.nexmo.voice.core.types.VoiceDirection;
import com.nexmo.voice.core.types.VoiceProduct;

import static java.time.temporal.ChronoUnit.SECONDS;

/**
 * This class is activated as a task which runs every 1 second and calculate the call length so far.
 * Based on the call length it updates the customer's quota.
 * (The 1 second interval is configurable)
 * <p>
 * It is scanning all the existing VoiceContexts from the moment they were created, hence it is important to control
 * the BillingInfo (part of VoiceContext) status before any operation.
 * <p>
 * The UpdateQuotaTask instance is specific per product-type:
 *     In SIPApp: sip_asterisk, api
 *
 *     The task is handling all the legs of a specific product.
 *
 */

public class QuotaUpdateTask implements Runnable {


    private static final Logger Log = LogManager.getLogger(QuotaUpdateTask.class);
    private static final Gauge SCAN_SIZE_MONITOR = Gauge.build().name("sipapp_current_calls").labelNames("applicationType").help("Number of Concurrent Calls.").register();
    private static final Gauge SCAN_LENGTH_MONITOR = Gauge.build().name("sipapp_scan_length_monitor_ms").labelNames("applicationType").help("How long it took to scan the context").register();


    public final VoiceApplicationType voiceApplicationType;

    //This is called once, during the server initiation. ATM, there are two tasks: one for API and one SIP.
    public QuotaUpdateTask(final VoiceApplicationType voiceApplicationType) {
        this.voiceApplicationType = voiceApplicationType;
        if (Log.isDebugEnabled())
            Log.debug("QuotaUpdateTask created with VoiceApplicationType: {}", voiceApplicationType.toString());
    }

    @Override
    public void run() {
        long startScanTime = System.currentTimeMillis();

        final VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        if (voiceContextCache == null) {
            Log.error("FAILED TO PERFORM UPDATE CHARGE TASK [productClass: " + this.voiceApplicationType + "]:: VoiceContextCache is null!");
            return;
        }

        //Concurrency notes:
        //At the end of the call, in CDREventHandler, the VoiceContext is removed from the Cache. At that point the BillingInfo status
        //was changed already to ENDED or ERROR or OUT_OF_FUNDS.
        //If the removal is happening while this thread is working, the VoiceContext is still part of the below iterator, but its status
        //would indicate that it is an ended call, hence the quota update will be skipped. 

        //While fetching the list of VoiceContext to handle, skip all those which are passed the status of suitable for quota updates.
        final Collection<VoiceContext> allContextsOfType =
                voiceContextCache.getAllContextsForQuotaUpdate(this.voiceApplicationType.getVoiceProduct().getDescriptor());

        int scanSize = allContextsOfType.size();
        SCAN_SIZE_MONITOR.labels(voiceApplicationType.name()).set(scanSize);
        //Some build-in trap to check what is going on
        if (scanSize > 24000) {
            Log.error("QuotaUpdateTask need to scan more than 24000 VoiceContexts of type {} ", this.voiceApplicationType.getVoiceProduct().getDescriptor());
        } else if (scanSize > 16000) {
            Log.warn("QuotaUpdateTask need to scan more than 16000 VoiceContexts of type {} ", this.voiceApplicationType.getVoiceProduct().getDescriptor());
        }

        final BillingManager billingManager = Core.getInstance().getBillingManager();

        for (VoiceContext ctx : allContextsOfType)
            try {

                if (Objects.isNull(ctx)) {
                    Log.error("!!! Some bug popped in - the context should not be null ");
                } else {
                        billingManager.delta(ctx); //In case of failure, this will set the BillingInfo status to be the correct one
                        //Log.info("Added item from delta for:{}",ctx.getSessionId());
                }

            } catch (QuotaInternalError e) {
                //In case of Quota exception set QuotaErrorStatus for BillingInfo, add internal flags and let call go through
                if (Core.getInstance().isAsyncQuotaFlag()) {
                    //ctx.getBillingInfo().setQuotaErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
                    Log.warn("AsyncQuota Failed to charge context sessionId {} channelId {} due to {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                    ctx.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
                } else {
                    Log.error("Failed to charge context sessionId {} channelId {} due to {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                    ctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_QUOTA_ISSUES);
                    handleError(ctx, SIPCode.SERVER_INTERNAL_ERROR);
                }
            } catch (NotEnoughBalanceException e) {
                Log.warn("Failed to charge context sessionId {} channelId {} due to {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                ctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_OUT_OF_MONEY);
                handleError(ctx, SIPCode.PAYMENT_REQUIRED);
            } catch (AccountBannedException e) {
                Log.warn("SessionId {}, channelId {}. Account was banned during call exception is {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                ctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_BANNED_ACCOUNT);
                handleError(ctx, SIPCode.BANNED_CALL_ENDED);
            } catch (AccountsException e) {
                Log.warn("SessionId {}, channelId {}. Failed to retrieve user account, exception is {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                ctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_ACCOUNT_ISSUES);
                handleError(ctx, SIPCode.SERVER_INTERNAL_ERROR);
            } catch (QuotaDisabledException e) { //This is theoretical situation. If account has this feature, their price is set to zero and they never arrive to this point.
                Log.warn("SessionId {}, channelId {}. QuotaDisabled, exception is {} ", ctx.getSessionId(), ctx.getConnectionId(), e);
                ctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_QUOTA_DISABLED);
                handleError(ctx, SIPCode.SERVER_INTERNAL_ERROR);
            }

        long scanTime = System.currentTimeMillis() - startScanTime;
        SCAN_LENGTH_MONITOR.labels(voiceApplicationType.name()).set(scanTime);

        if (scanTime > 999) {
            Log.error("QuotaUpdateTask scan {} elements of VoiceContexts type {} took {} millisecs",
                    scanSize, this.voiceApplicationType.getVoiceProduct().getDescriptor(), scanTime);
        } else if (scanTime > 500) {
            Log.warn("QuotaUpdateTask scan {} elements of VoiceContexts type {} took {} millisecs",
                    scanSize, this.voiceApplicationType.getVoiceProduct().getDescriptor(), scanTime);
        }

        if (Log.isDebugEnabled()) {
            if (scanSize > 0)
                Log.debug("QuotaUpdateTask scan {} elements of VoiceContexts type {} took {} millisecs",
                        scanSize, this.voiceApplicationType.getVoiceProduct().getDescriptor(), scanTime);
        }
    }

    private void handleError(VoiceContext ctx, SIPCode sipCode) {
        if (Log.isDebugEnabled())
            Log.debug("Processing QuotaUpdateTask error in context: {} SIPCode: {}", ctx.getDebugString(), sipCode);
        
        ApplicationContext applicationContext = ctx.getApplicationContext();
        String channelId = null;
        //Yaaaaaaaaaaaaaaakkkkkkkkk 
        //ok, to fix this we need to move the ChannelId from the specific ApplicationContext to the regular VoiceContext
        try {
            SIPAsteriskContext asteriskAppContext = SIPAsteriskContext.class.cast(applicationContext);
            channelId = asteriskAppContext.getSipChannelId();
        } catch (ClassCastException e) {
            Log.error("Failed to cancel context's session in Asterisk. Non Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']", e);
        }
        if (channelId == null || channelId.isEmpty()) {
            Log.error("Failed to cancel context's session in Asterisk. Empty or null channelId in Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']");
            return;
        }

        // Just in case we don't find the first leg (happens when the first leg has error already)
        String channelToStop = channelId;
        // Get first leg ..
        VoiceContext firstLegContext = null;
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextsInSession = cache.getInnerValues(ctx.getSessionId());
        for (VoiceContext context : contextsInSession) {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) context.getApplicationContext();
            if (context.getVoiceDirection() == VoiceDirection.INBOUND || asteriskContext.isAuxiliary())
                firstLegContext = context;
        }
        if (firstLegContext == null)
            Log.warn("1st leg not found in session " + ctx.getSessionId() + " Might've been processed already...");
        else {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) firstLegContext.getApplicationContext();
            if (asteriskContext != null)
                channelToStop = asteriskContext.getSipChannelId();
        }

        HangupCause hangupCause = SIPCode.BANNED_CALL_ENDED.equals(sipCode) ? HangupCause.AST_CAUSE_WRONG_CALL_STATE : HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED;
        if (channelToStop != null) {
            try {
                Log.info("About to set APP_REASON to " + SIPCode.REQUEST_TERMINATED.getCode() +
                        " and finish the call with HangupCause: " + hangupCause +
                        " for sessionId: " + ctx.getSessionId());
                // We kill the call via the first leg's channel, or Asterisk will overwrite our HangupCause
                AsteriskActionIssuer.setVariable(channelToStop, "APP_REASON", String.valueOf(SIPCode.REQUEST_TERMINATED.getCode()));
                AsteriskActionIssuer.finishCall(channelToStop, hangupCause);
            } catch (Exception e1) {
                Log.error("Failed to finish the call", e1);
            }
        } else {
            Log.warn("2nd leg error was dealt with already.");
        }

    }

    public enum VoiceApplicationType {
        SIP_ASTERISK    ("sip_asterisk", VoiceProduct.SIP),
        API             ("api", VoiceProduct.CALL_API),
        VERIFY          ("verify", VoiceProduct.VERIFY),
        TTS             ("tts", VoiceProduct.TTS);

        private final String key;
        private final VoiceProduct voiceProduct;


        private VoiceApplicationType(final String key,
                                     final VoiceProduct voiceProduct) {
            this.key = key;
            this.voiceProduct = voiceProduct;
        }

        public String getKey() {
            return this.key;
        }

        public static VoiceApplicationType getMatching(String key) {
            VoiceApplicationType value = SIP_ASTERISK; //The default
            if (key != null)
                for (VoiceApplicationType val : VoiceApplicationType.values())
                    if (val.getKey().equals(key))
                        value = val;
            return value;
        }

        public static boolean isValidKey(String key) {
            for (VoiceApplicationType val : VoiceApplicationType.values())
                if (val.getKey().equals(key))
                    return true;
            return false;
        }

        public VoiceProduct getVoiceProduct() {
            return this.voiceProduct;
        }

        @Override
        public String toString() {
            return this.key;
        }

    }

}
