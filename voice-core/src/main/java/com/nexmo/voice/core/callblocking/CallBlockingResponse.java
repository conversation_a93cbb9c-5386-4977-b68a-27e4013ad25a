package com.nexmo.voice.core.callblocking;

public class CallBlockingResponse {

    private final String id;
    private final String action;
    private final String subsystem;

    public CallBlockingResponse(String id, String action, String subsystem) {
        this.id = id;
        this.action = action;
        this.subsystem = subsystem;
    }

    public String getId() {
        return id;
    }

    public String getAction() {
        return action;
    }

    public String getSubsystem() {
        return subsystem;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CALLBLOCK SERVICE:: ");
        sb.append(" ID: " + id);
        sb.append(":: ACTION:: " + action);
        sb.append(":: SUBSYSTEM:: " + subsystem);
        return sb.toString();
    }

}
