package com.nexmo.voice.core.emergency;

import org.jdom.Element;

import java.util.List;
import java.util.Objects;
import java.util.Set;

public class EmergencyCallingLocaleConfig {

    public static final String ROOT_NODE = "locale";
    public static final String COUNTRY_ATTR = "country";
    public static final String EMERGENCY_NUMBERS_ATTR = "emergency-numbers";
    public static final String ROUTE_ATTR = "route";

    private String country;

    private Set<String> emergencyNumbers;

    private List<String> route;

    public EmergencyCallingLocaleConfig() {

    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Set<String> getEmergencyNumbers() {
        return emergencyNumbers;
    }

    public void setEmergencyNumbers(Set<String> emergencyNumbers) {
        this.emergencyNumbers = emergencyNumbers;
    }

    public List<String> getRoute() {
        return route;
    }

    public void setRoute(List<String> route) {
        this.route = route;
    }

    public Element toXML() {
        Element config = new Element(ROOT_NODE);
        config.setAttribute(COUNTRY_ATTR, country);

        String emergencyNumbersCsv = "";
        if(this.emergencyNumbers != null) {
            emergencyNumbersCsv = String.join(",", this.emergencyNumbers);
        }
        config.setAttribute(EMERGENCY_NUMBERS_ATTR, emergencyNumbersCsv);

        String routeCsv = "";
        if(this.route != null) {
            routeCsv = String.join(",", this.route);
        }
        config.setAttribute(ROUTE_ATTR, routeCsv);

        return config;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("EmergencyCallingLocaleConfig [");
        sb.append("country=").append(this.country);
        String emergencyNumbersCsv = "";
        if(this.emergencyNumbers != null) {
            emergencyNumbersCsv = String.join(",", this.emergencyNumbers);
        }
        sb.append("; ").append("emergencyNumbers=").append(emergencyNumbersCsv);
        String routeCsv = "";
        if(this.route != null) {
            routeCsv = String.join(",", this.route);
        }
        sb.append("; ").append("route=").append(routeCsv);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EmergencyCallingLocaleConfig that = (EmergencyCallingLocaleConfig) o;
        return Objects.equals(country, that.country) && Objects.equals(emergencyNumbers, that.emergencyNumbers) && Objects.equals(route, that.route);
    }

    @Override
    public int hashCode() {
        return Objects.hash(country, emergencyNumbers, route);
    }
}
