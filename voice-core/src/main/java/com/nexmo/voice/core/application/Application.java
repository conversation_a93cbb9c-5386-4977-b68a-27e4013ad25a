package com.nexmo.voice.core.application;

import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.Core;

public class Application {
    private final static Logger Log = LogManager.getLogger(Application.class);

    private final String id;
    private final String name;
    private final String apiKey;
    private final Boolean paymentsEnabled; // null, true or false
    private final String region;

    public Application(String id,
                       String name,
                       String apiKey,
                       Boolean paymentsEnabled) {
        this(id, name, apiKey, null, paymentsEnabled);
    }

    public Application(String id,
                       String name,
                       String apiKey,
                       String region,
                       Boolean paymentsEnabled) {
        this.id = id;
        this.name = name;
        this.apiKey = apiKey;
        this.region = region;
        this.paymentsEnabled = paymentsEnabled;
    }

    public String getApiKey() {
        return this.apiKey;
    }

    public Boolean getPaymentEnabled() {
        return paymentsEnabled;
    }

    public String getRegion() {
        return this.region;
    }

    public static Application lookup(String applicationId) {
        if (Core.getInstance().isInitialised()) {
            try {
                long reqTime = System.currentTimeMillis();
                Application application = Core.getInstance().getApplication(applicationId);
                Log.info("Application.lookup({}) took {} ms", applicationId, System.currentTimeMillis() - reqTime);
                return application;
            } catch (ApplicationLookupException e) {
                Log.warn("Failed to get the application details of appId {} due to {}", applicationId, e.getMessage());
            }
        }
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof Application))
            return false;
        Application other = (Application) o;

        // Mandatory parameters
        if (!this.id.equals(other.id))
            return false;
        if (!this.name.equals(other.name))
            return false;
        if (!this.apiKey.equals(other.apiKey))
            return false;
        if (!Objects.equals(this.region, other.region))
            return false;
        //if (this.enabled != other.enabled)
        //    return false;

        if (!Objects.equals(this.paymentsEnabled, other.paymentsEnabled))
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + id.hashCode();
        result = prime * result + name.hashCode();
        result = prime * result + apiKey.hashCode();
        //result = prime * result + (enabled ? 1231 : 1237);

        if (paymentsEnabled != null) {
            result = prime * result + (paymentsEnabled.booleanValue() ? 1231 : 1237);
        }
        if (region != null) {
            result = prime * result + region.hashCode();
        }

        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("APP :: ");
        sb.append(id);
        sb.append(" :: ");
        sb.append(name);
/*
        sb.append(" (enabled:");
        sb.append(enabled);
        sb.append(")");
*/
        sb.append(" :: API-KEY: ");
        sb.append(apiKey);
        if (paymentsEnabled != null) {
            sb.append(" Payments: ");
            sb.append(paymentsEnabled);
        }
        if (region != null) {
            sb.append(" REGION : ");
            sb.append(region);
        }
        return sb.toString();
    }

}
