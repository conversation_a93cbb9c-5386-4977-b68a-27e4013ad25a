package com.nexmo.voice.core.logger;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.cdr.CDRKeysConverter;
import com.nexmo.voice.core.cdr.CDRValuesConverter;
import com.nexmo.voice.core.types.CDRType;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.SIPCode;
import com.thepeachbeetle.common.logging.DailyStatsLogger;

public class SIPAppLogger extends DailyStatsLogger {

    private final static Logger Log = LogManager.getLogger(SIPAppLogger.class);
    private final String logPrefix;
    private final CDRType cdrType;
    
    private final static String RANDOM_DEFAULT_VALUE = UUID.randomUUID().toString();
    
    public SIPAppLogger(String logDir, String pfx, CDRType cdrType) throws Exception {
        super(logDir, pfx);
        this.logPrefix = pfx;
        this.cdrType = cdrType;
        Log.info("CDRs logger was created in folder {} with prefix {} for cdrType {}", logDir, pfx, cdrType);
    }
    
    public void logCDR(CDRData cdrData, String[] CDROrder) {
        
        if(Log.isDebugEnabled())
            Log.debug("logging CDR on prefix={}, cdrType={}, cdrData={}", this.logPrefix, this.cdrType, cdrData);
        
        if (CDRType.JSON.equals(this.cdrType)) {
            log(null, buildJsonCDR(cdrData, CDROrder));
            if(Log.isDebugEnabled())
                Log.debug("logging JSON CDR completed {}", cdrData.getKeyValuePairs().get("ID"));
        } else {
            log(null, buildKeyValueCDR(cdrData, CDROrder));
            if(Log.isDebugEnabled())
                Log.debug("logging Key-Value CDR completed {}", cdrData.getKeyValuePairs().get("ID"));
        }
   }

    
    // The CDR header will always be before all the key-values
    public static String buildKeyValueCDR(CDRData cdrData, String[] CDROrder) {

        StringBuilder sb = new StringBuilder();
        long timeStamp = cdrData.getCdrCreationTimestamp();
        
        sb.append(timeStamp).append(" :: ");
        sb.append(formatToCdrDate(timeStamp)).append(" :: ");
        sb.append(CDRData.CDR_CLASS_NAME).append(" :: ");

        for (int i=0; i<CDROrder.length; i++) {
            String key = CDROrder[i];
            //While building the CDRData, there are several approaches for null value which are identical to the way the sb was built in the past:
            //In some cases we simply do not add the attribute to the map (i.e. not write it at all)
            //In other cases we write the key and as value we put the word "null" (as String)
            //In other cases we put the value NULL 
            
            //While building the CDR according to the CDROrder, we need to handle it as well, so if the ordered key is not in the map
            //we skip it. It is not enough to get the value and check it is not null, so we return a default random UUID value.
            String value = cdrData.getKeyValuePairs().getOrDefault(key, RANDOM_DEFAULT_VALUE); 
            
            if (! RANDOM_DEFAULT_VALUE.equals(value))
                sb.append("\"").append(key).append("=").append(cdrData.getKeyValuePairs().get(key)).append("\","); 
        }
        
        String kvCDR = sb.toString();
        
        if (Log.isDebugEnabled())
            Log.debug("Generated Key-Value CDR: "+kvCDR);
        return kvCDR;

    }

    public static String buildJsonCDR(CDRData cdrData, String[] CDROrder) {

        StringBuilder sb = new StringBuilder();
        sb.append("{");

        for (int i=0; i<CDROrder.length; i++) {
            String key = CDROrder[i];
            //While building the CDRData, there are several approaches for null value which are identical to the way the sb was built in the past:
            //In some cases we simply do not add the attribute to the map (i.e. not write it at all)
            //In other cases we write the key and as value we put the word "null" (as String)
            //In other cases we put the value NULL 
            
            //While building the CDR according to the CDROrder, we need to handle it as well, so if the ordered key is not in the map
            //we skip it. It is not enough to get the value and check it is not null, so we return a default random UUID value.
            String value = cdrData.getKeyValuePairs().getOrDefault(key, RANDOM_DEFAULT_VALUE); 
            
            if (! RANDOM_DEFAULT_VALUE.equals(value))
                sb.append("\"")
                .append(CDRKeysConverter.getJsonKey(key))
                .append("\":\"")
                .append(CDRValuesConverter.convertValue(key, value))
                .append("\",");
        }
        
        //Notice the @timestamp
        //While using LogConverter:
        //The key-value CDR includes 3 "headers": (example from prod)
        //  1638947747688 :: 12/08/2021 07:15:47 (688) :: com.nexmo.voice.core.cache.VoiceContext ::
        //the timestamp (1638947747688) is matching the date (12/08/2021 07:15:47 (688))
        
        //The LogConverter output translate this to:
        // "@timestamp":"1638947740186","@date":"2021-12-08T07:15:47.688+0000","@class":"com.nexmo.voice.core.cache.VoiceContext"
        // the value of @timestamp is wrong - it is the time the logconverter record was created and it is not matching the @date
        // the @date is matching the original date as provided in the CDR header - just using different date format.
        
        //After the LogConverter removal:
        //the @timestamp and @date will match, and will be the time of the json cdr creation.
        //It means that the transition testing junits should expect the difference for the @timestamp
        
        long timeStamp = cdrData.getCdrCreationTimestamp();
        String cdrClassicCreationDate = formatToCdrDate(timeStamp);

        sb.append("\"@timestamp\"").append(":").append("\"").append(timeStamp).append("\",");
        sb.append("\"@date\"").append(":").append("\"").append(CDRValuesConverter.formatCDRCreationTimeForJson(cdrClassicCreationDate)).append("\",");
        sb.append("\"@class\"").append(":").append("\"").append(CDRData.CDR_CLASS_NAME).append("\"");

        sb.append("}");
        String jsonCDR = sb.toString();
        
        if (Log.isDebugEnabled())
            Log.debug("Generated JSON CDR: "+jsonCDR);
        return jsonCDR;
    }
    
    //Copied method 'deQuote' from messaging.jar "as-is" so can be used in the controller rather than the logger itself
    public static String handleQuote(final String in) {
        if (in == null)
            return null;
        final char[] chars = in.toCharArray();
        final StringBuilder sb = new StringBuilder();
        for (final char ch: chars)
            if (ch == '\"')
                sb.append("\"\"");
            else
                sb.append(ch);
        return sb.toString();
    }
   
    public static String formatToCdrDate(long timeDate) {
        return formatToCdrDate(new Date(timeDate));
    }

    // FIXME: This behavior should replace formatToCdrDate above (SIP-1897)
    public static String formatToCdrDateOrNull(long timeDate) {
        if (timeDate > 0L)
            return formatToCdrDate(new Date(timeDate));
        else
            return "null";
    }

    public static String formatToCdrDate(Date timeDate) {
        if (Objects.isNull(timeDate))
            return "null";

        SimpleDateFormat cdrDateFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss (SSS)");
        return cdrDateFormat.format(timeDate);
    }

    public static String formatToCdrBigDecimal(BigDecimal bd) {
        return Objects.nonNull(bd) ? bd.toPlainString() : "null";
    }

    public static String concatInternalFlags(Set<CallInternalFlag> internalFlags) {
        if (Objects.isNull(internalFlags) || internalFlags.isEmpty())
            return "";

        return internalFlags.stream().map(flag -> flag.getFlagValue()).collect(Collectors.joining(","));
    }
    
    public static String getJsonLogPrefix(String keyValueLogPrefix) {
        String jsonLogPrefix;
        if (Objects.isNull(keyValueLogPrefix) || keyValueLogPrefix.trim().isEmpty()) {
           Log.error("Missing cdrs logger name. Check the sip.xml log tag.");
           return null;
        }
        
        if (keyValueLogPrefix.startsWith("sip-") ) {
            jsonLogPrefix = keyValueLogPrefix.replaceFirst("sip-", "sip-json-"); 
        } else if (keyValueLogPrefix.startsWith("tts-") ) {
            jsonLogPrefix = keyValueLogPrefix.replaceFirst("tts-", "tts-json-"); 
        } else {
            jsonLogPrefix = "json-"+keyValueLogPrefix;
            Log.error("Unexpected CDRs log file name {}, will generate {}", keyValueLogPrefix, jsonLogPrefix);
        }
        return jsonLogPrefix;
    }
    
    public static String getBackupLogPrefix(String keyValueLogPrefix) {
        String backupLogPrefix;
        if (Objects.isNull(keyValueLogPrefix) || keyValueLogPrefix.trim().isEmpty()) {
           Log.error("Missing cdrs logger name. Check the sip.xml log tag.");
           return null;
        }
        
        if (keyValueLogPrefix.startsWith("sip-") ) {
            backupLogPrefix = keyValueLogPrefix.replaceFirst("sip-", "sip-backup-"); 
        } else if (keyValueLogPrefix.startsWith("tts-") ) {
            backupLogPrefix = keyValueLogPrefix.replaceFirst("tts-", "tts-backup-"); 
        } else {
            backupLogPrefix = "backup-"+keyValueLogPrefix;
            Log.error("Unexpected CDRs log file name {}, will generate {}", keyValueLogPrefix, backupLogPrefix);
        }
        return backupLogPrefix;
    }
    
    @Override
    public String toString() {
        return "SIPAppLogger [logPrefix=" + logPrefix + ", cdrType=" + cdrType + "]";
    }

    public static String getValueOrEmpty(Object value) {
        return Objects.toString(value, "");
    }

    public static String getValueOrNullStr(Object value) {
        return Objects.toString(value, "null");
    }
    
    public CDRType getCDRType() {
        return this.cdrType;
    }
    
    public String getlogPrefix() {
        return this.logPrefix;
    }

    public static String formatSipCodeMap(Map<String, SIPCode> sipCodeMap) {
        if ((sipCodeMap == null) || sipCodeMap.isEmpty()) {
            return "";
        }
        
        return sipCodeMap.entrySet().stream().map(
                entry -> entry.getKey()+":"+(Objects.nonNull(entry.getValue()) ? entry.getValue().getCode() :  "null")
        ).collect(Collectors.joining(","));
    }

}
