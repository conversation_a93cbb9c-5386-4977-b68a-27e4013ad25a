package com.nexmo.voice.core.logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import io.prometheus.client.Counter;
import java.util.LinkedHashMap;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.types.SIPCode;
import com.nexmo.voice.core.types.VoiceDirection;

/**
 * This class route the in/out CDRs writing to the configured cdrs file.
 * Key-value CDRs should be written the XXX-inbound XXX-outbound XXX=> sip or tts json
 * Json CDRs should be written to the XXX-json-inbound XXX-json-outbound XXX=> sip or tts
 * 
 * 
 * <AUTHOR>
 *
 */


public class CallLoggerController extends SIPAppLoggerController {
    private final static Logger Log = LogManager.getLogger(CallLoggerController.class);
    private static final List<String> VONAGE_GATEWAYS = Arrays.asList("vonage-prem", "vonage", "vonage-cnam");

    private static final Counter INBOUND_CDRS = Counter.build().name("inbound_cdr").labelNames("inbound").help(" ").register();
    private static final Counter INBOUND_CDRS_VONAGE_PREM = Counter.build().name("inbound_cdr_vonage_prem").labelNames("inbound_vonage_prem").help(" ").register();
    private static final Counter INBOUND_CDRS_VONAGE = Counter.build().name("inbound_cdr_vonage").labelNames("inbound_vonage").help(" ").register();
    private static final Counter INBOUND_CDRS_VONAGE_CNAM = Counter.build().name("inbound_cdr_vonage_cnam").labelNames("inbound_vonage_cnam").help(" ").register();

    private static final Counter OUTBOUND_CDRS = Counter.build().name("outbound_cdr").labelNames("outbound").help(" ").register();
    private static final Counter OUTBOUND_CDRS_VONAGE_PREM = Counter.build().name("outbound_cdr_vonage_prem").labelNames("outbound_vonage_prem").help(" ").register();
    private static final Counter OUTBOUND_CDRS_VONAGE = Counter.build().name("outbound_cdr_vonage").labelNames("outbound_vonage").help(" ").register();
    private static final Counter OUTBOUND_CDRS_VONAGE_CNAM = Counter.build().name("outbound_cdr_vonage_cnam").labelNames("outbound_vonage_cnam").help(" ").register();

    // Define a metric to track price differences
    private static final Counter CHARGING_PRICE_DISPARITY_COUNTER = Counter.build().name("price_differences_count").help("Counts how many times the prices are different with vPricing enabled.").register();

    public static final String[] CallCDROrder = {
            "PRODUCT",
            "PRODUCT-CLASS",
            "PRODUCT-VERSION",
            "DIRECTION",
            "HOST",
            "ID",
            "SESSION-ID",
            "LEG1-ID",
            "LEG2-ID",
            "SUPERHUB-ACC",
            "MASTER-ACCOUNT-PRICING-GROUP",
            "ACC",
            "ACCOUNT-PRICING-GROUP",
            "FROM",
            "TO",
            "PREFIX-FROM",
            "PREFIX-TO",
            "FORCED_SENDER",
            "PREFIX-FORCED_SENDER",
            "SIP-DEST-ATTEMPT",
            "TEXT",
            "COUNTRY",
            "NET",
            "NETWORK-NAME",
            "NETWORK-TYPE",
            "GW",
            "GWS",
            "GW_ATTEMPT",
            "CALL_RETRIES",
            "VOICE",
            "REROUTE-ADDRESS",
            "UNIT",
            "MIN_UNIT",
            "RECURRING_UNIT",
            "PRICE",
            "OVERRIDE_PRICE",
            "TOTAL_PRICE",
            "PRICE_PREFIX",
            "PRICE_PREFIX_GROUP",
            "PRICE_SENDER_PREFIX",
            "PRICE_TIMESTAMP",
            "COST",
            "TOTAL_COST",
            "CALL_MARGIN",
            "COST_PREFIX",
            "COST_PREFIX_GROUP",
            "COST_SENDER_PREFIX",
            "COST_TIMESTAMP",
            "START",
            "END",
            "DURATION",
            "CALL_DURATION",
            "STATUS",
            "REASON",
            "REASON_DESC",
            "CALL_DATE",
            "ROUTING_SEQ",
            "BACKEND",
            "PDD",
            "REQUEST_IP",
            "CALL_ORIGIN",
            "SCENARIO",
            "CALL_TERMINATION",
            "CUSTOMER_DOMAIN",
            "PAYMENT-ENABLED-APP",
            "PAYMENT-ROUTE",
            "REGION",
            "TTS_XTRACE_ID",
            "CALL_BACK_URL",
            "CALL_BACK_METHOD",
            "CLIENT_REFERENCE",
            "REPEAT",
            "MACHINE_DETECTION_TYPE",
            "MACHINE_TIMEOUT",
            "LANGUAGE_NAME",
            "MB_STYLE",
            "INTERNAL",
            "CDR_UUID",
            "STIR_SHAKEN",
            "CARRIER_PLATFORM",
            "INTERNAL_FLAG",
            "CHARGEABLE",
            "SRTP",
            "SIP_TRANSPORT",
            "SOURCE_COUNTRY",
            "HANGUP_CAUSE",
            "RULE_ID",
            "BLOCKING_SUBSYSTEM",
            "ASTERISK_VERSION",
            "PRODUCT-PATH",
            "NUMBER_TYPE",
            "CALL_TYPE",
            "ROUTING_GROUP",
            "ROUTING_OA",
            "ROUTING_BIND_ID",
            "VPRICING-ENABLED",
            "ORIGIN_PREFIX_GROUP",
            "ORIGIN_COUNTRY_REGION",
            "EMERGENCY_CALL",
            "EMERGENCY_CALL_FAILOVER",
            "EMERGENCY_CALL_LOCATION_ID",
    };


    public CallLoggerController(String logDir, String pfx, CdrsConfig cdrsConfig) throws Exception {
        super(logDir, pfx, cdrsConfig);
        }


    public synchronized void logCall(
            VoiceContext ctx,
            String status,
            SIPCode sipCode,
            String carrierCallId,
            String idSuffix,
            int truncationLength,
            CdrEventUserData eventUserData) {
        logCall(ctx, status, sipCode, carrierCallId, idSuffix, Core.PHONE_NUMBER_TRUNCATION_LENGTH, eventUserData, false);
    }

    public synchronized void logEmergencyStoppedCall(
            VoiceContext ctx,
            String status,
            SIPCode sipCode) {
        logCall(ctx, status, sipCode, null, null, Core.PHONE_NUMBER_TRUNCATION_LENGTH, null, true);
    }

    private synchronized void logCall(
            VoiceContext ctx,
            String status,
            SIPCode sipCode,
            String carrierCallId,
            String idSuffix,
            int truncationLength,
            CdrEventUserData eventUserData,
            boolean emergencyTermination) {

        if (Log.isDebugEnabled())
            Log.debug("We are logging the CDR for sessionId: " + ctx.getSessionId() +
                    " status: " + status +
                    " carrierCallId: " + carrierCallId +
                    " idSuffix: " + idSuffix +
                    " eventUserData: " + eventUserData +
                    " product: " + ctx.getVoiceProduct().name());

        CDRData cdrData = buildCDRData(ctx, status,
                sipCode, carrierCallId,
                idSuffix, truncationLength,
                eventUserData,
                emergencyTermination);
        
        logCDR(cdrData, ctx.getSessionId(), CallCDROrder, "Finished logging the INBOUND/OUTBOUND CDR ");

        if (ctx.getVoiceDirection() != null && ctx.getVoiceDirection() == VoiceDirection.INBOUND) {
            CompletableFuture.runAsync(() -> {
                INBOUND_CDRS.labels("success").inc();
            });
            if (ctx.getCurrentGateway() != null && VONAGE_GATEWAYS.contains(ctx.getCurrentGateway())) {
                switch (ctx.getCurrentGateway()) {
                    case "vonage":
                        CompletableFuture.runAsync(() -> {
                            INBOUND_CDRS_VONAGE.labels("success").inc();
                        });
                        break;
                    case "vonage-cnam":
                        CompletableFuture.runAsync(() -> {
                            INBOUND_CDRS_VONAGE_CNAM.labels("success").inc();
                        });
                        break;
                    case "vonage-prem":
                        CompletableFuture.runAsync(() -> {
                            INBOUND_CDRS_VONAGE_PREM.labels("success").inc();
                        });
                        break;

                }
            }
        }

        if (ctx.getVoiceDirection() != null && ctx.getVoiceDirection() == VoiceDirection.OUTBOUND) {
            CompletableFuture.runAsync(() -> {
                OUTBOUND_CDRS.labels("success").inc();
            });

            if (ctx.getCurrentGateway() != null && VONAGE_GATEWAYS.contains(ctx.getCurrentGateway())) {
                switch (ctx.getCurrentGateway()) {
                    case "vonage":
                        CompletableFuture.runAsync(() -> {
                            OUTBOUND_CDRS_VONAGE.labels("success").inc();
                        });
                        break;
                    case "vonage-cnam":
                        CompletableFuture.runAsync(() -> {
                            OUTBOUND_CDRS_VONAGE_CNAM.labels("success").inc();
                        });
                        break;
                    case "vonage-prem":
                        CompletableFuture.runAsync(() -> {
                            OUTBOUND_CDRS_VONAGE_PREM.labels("success").inc();
                        });
                        break;

                }
            }
        }
    }


    protected CDRData buildCDRData(
            final VoiceContext ctx,
            final String status,
            final SIPCode sipCode,
            final String carrierCallId,
            final String idSuffix,
            final int truncationLength,
            final CdrEventUserData eventUserData,
            final boolean emergencyTermination) {

        LinkedHashMap<String, String> cdrDetails = buildBaseCDRData(ctx, carrierCallId, idSuffix, truncationLength, eventUserData);

        //Override "inbound and outbound CDR" special values
        cdrDetails.put("LEG1-ID", "");
        cdrDetails.put("REROUTE-ADDRESS", "");

        ctx.getApplicationContext().populateParams(cdrDetails);

        if (Objects.nonNull(ctx.getTtsContext())) {
            ctx.getTtsContext().populateParams(cdrDetails);
        }
        
        //Special handling of the ID:
        //Original code:  
        //  String id = SipAppUtils.getNexmoUUID(ctx.getSessionId());
        //  id = idSuffix != null ? id + idSuffix : id;
        //  sb.append("\"ID=").append(id).append("\",");
        String id = SipAppUtils.getNexmoUUID(ctx.getSessionId());
        id = idSuffix != null ? id + idSuffix : id;
        cdrDetails.put("ID", id);

        cdrDetails.put("SESSION-ID", SipAppUtils.getNexmoUUID(ctx.getSessionId()));
        cdrDetails.put("MASTER-ACCOUNT-PRICING-GROUP", SIPAppLogger.getValueOrEmpty(ctx.getMasterAccountPricingGroup()));
        cdrDetails.put("ACCOUNT-PRICING-GROUP", SIPAppLogger.getValueOrEmpty(ctx.getAccountPricingGroup()));

        BillingInfo billingInfo = ctx.getBillingInfo();

        cdrDetails.put("UNIT", String.valueOf(billingInfo.getQuotaUpdatesInterval()));
        cdrDetails.put("MIN_UNIT", String.valueOf(billingInfo.getFirstChargedSeconds()));
        cdrDetails.put("RECURRING_UNIT", String.valueOf(billingInfo.getQuotaUpdatesInterval()));

        cdrDetails.put("OVERRIDE_PRICE", SIPAppLogger.formatToCdrBigDecimal(billingInfo.getForcedPricePerMinute()));

        BigDecimal totalPrice;
        BigDecimal totalPriceWithoutVpricing = billingInfo.getTotalCallPrice(ctx.getSessionId(), ctx.getConnectionId());
        if(ctx.isVpricingEnabled()) {
            totalPrice = billingInfo.getTotalEstimatedPriceImpact();
            totalPrice = (totalPrice != null) ? totalPrice.setScale(8, RoundingMode.HALF_UP) : null;
            cdrDetails.put("PRICE", SIPAppLogger.formatToCdrBigDecimal(billingInfo.getEffectivePricingRate(ctx.getSessionId())));
            cdrDetails.put("TOTAL_PRICE", SIPAppLogger.formatToCdrBigDecimal(totalPrice));
            comparePrices(id, totalPrice, totalPriceWithoutVpricing, ctx.getAccountId());
            cdrDetails.put("PRICE_PREFIX", "");
            cdrDetails.put("PRICE_PREFIX_GROUP", "");
            cdrDetails.put("PRICE_SENDER_PREFIX", "");
            cdrDetails.put("PRICE_TIMESTAMP", "");
        } else {
            totalPrice = totalPriceWithoutVpricing;
            cdrDetails.put("TOTAL_PRICE", SIPAppLogger.formatToCdrBigDecimal(totalPrice));
            cdrDetails.put("PRICE", SIPAppLogger.formatToCdrBigDecimal(billingInfo.getPricePerMinute()));
            cdrDetails.put("PRICE_PREFIX", ctx.getPricePrefix());
            cdrDetails.put("PRICE_PREFIX_GROUP", ctx.getPricePrefixGroup());
            cdrDetails.put("PRICE_SENDER_PREFIX", ctx.getPriceSenderPrefix());
            cdrDetails.put("PRICE_TIMESTAMP", SIPAppLogger.formatToCdrDateOrNull(ctx.getPriceTimestamp()));
        }

        cdrDetails.put("COST", SIPAppLogger.formatToCdrBigDecimal(billingInfo.getCostPerMinute()));
        BigDecimal totalCost = billingInfo.getTotalCallCost(ctx.getSessionId(), ctx.getConnectionId());
        cdrDetails.put("TOTAL_COST", SIPAppLogger.formatToCdrBigDecimal(totalCost));

        cdrDetails.put("COST_PREFIX", ctx.getCostPrefix());
        cdrDetails.put("COST_PREFIX_GROUP", ctx.getCostPrefixGroup());
        cdrDetails.put("COST_SENDER_PREFIX", ctx.getCostSenderPrefix());
        cdrDetails.put("COST_TIMESTAMP", SIPAppLogger.formatToCdrDateOrNull(ctx.getCostTimestamp()));

        if (Objects.nonNull(totalPrice) && Objects.nonNull(totalCost))
           cdrDetails.put("CALL_MARGIN", SIPAppLogger.formatToCdrBigDecimal(totalPrice.subtract(totalCost)));

        cdrDetails.put("STATUS", status);
        if (sipCode != null) {
            cdrDetails.put("REASON", String.valueOf(sipCode.getCode()));
            cdrDetails.put("REASON_DESC", SIPAppLogger.handleQuote(sipCode.getMessage()));
        }
        
        cdrDetails.put("START", SIPAppLogger.formatToCdrDate(billingInfo.getCallStartTime()));
        cdrDetails.put("END", SIPAppLogger.formatToCdrDate(billingInfo.getCallEndTime()));
        cdrDetails.put("CALL_DATE", SIPAppLogger.formatToCdrDate(billingInfo.getCallStartTime()));

        cdrDetails.put("DURATION", String.valueOf(billingInfo.getCallDurationInSeconds()));
        cdrDetails.put("CALL_DURATION", String.valueOf(billingInfo.getCallDurationInMillis()));

        if (Objects.nonNull(ctx.getPaymentEnabled())) {
            cdrDetails.put("PAYMENT-ENABLED-APP", String.valueOf(ctx.getPaymentEnabled()));
        }
        if (Objects.nonNull(ctx.getPaymentRoute())) {
            cdrDetails.put("PAYMENT-ROUTE", ctx.getPaymentRoute());
        }

        if (Objects.nonNull(ctx.getRegion())) {
            cdrDetails.put("REGION", ctx.getRegion());
        }
        
        if (ctx.isVAPIOutboundToVBC()) {
            cdrDetails.put("SCENARIO", "API-TO-VBC");
        }
        
        //PDD is required only for outbound calls, but it is kept inside the specific SIPAsteriskContext which extends ApplicationContext
        //In the past, it used to be put in the map and later ignored
        //Now we put it in the map only when needed, but the implementation is yak :-(
        if (ctx.getVoiceDirection() == VoiceDirection.OUTBOUND) {
            long pddValue = 0;
            if (Objects.nonNull(eventUserData)) {
                pddValue = eventUserData.getPddTimeMillis();
            }
            if (Objects.nonNull(ctx.getApplicationContext()) && ( ctx.getApplicationContext() instanceof SIPAsteriskContext ) )
                if (pddValue > 0) {
                    // If available get PDD value from CDR instead of calculating it
                    cdrDetails.put("PDD", String.valueOf(pddValue));
                } else {
                    cdrDetails.put("PDD", String.valueOf(((SIPAsteriskContext) (ctx.getApplicationContext())).getPdd()));
                }
        }

        //Mark that this CDR was created as part of the emergency stop and not a regular cdr event.
        if (emergencyTermination) {
            cdrDetails.put("INTERNAL", "1");

            //Amend some edge cases of when the emergency stop is happening before the call had
            //started, and so the start time and end time are still zero.
            if (billingInfo.getCallDurationInSeconds() == 0) {
                if (billingInfo.getCallStartTime() == 0) {
                    long now = System.currentTimeMillis();
                    cdrDetails.put("START", SIPAppLogger.formatToCdrDate(now));
                    cdrDetails.put("END", SIPAppLogger.formatToCdrDate(now));
                }
            }
        }

        cdrDetails.put("CHARGEABLE", String.valueOf(ctx.isChargeable()));

        if (Objects.nonNull(ctx.getMediationCdrField())) {
            if(Objects.nonNull(ctx.getMediationCdrField().getOriginPrefixGroup())) {
                cdrDetails.put("ORIGIN_PREFIX_GROUP", ctx.getMediationCdrField().getOriginPrefixGroup());
            } else if(Objects.nonNull(ctx.getMediationCdrField().getOriginCountryRegion())) {
                cdrDetails.put("ORIGIN_COUNTRY_REGION", ctx.getMediationCdrField().getOriginCountryRegion());
            }
        }

        if (Objects.nonNull(ctx.getAsteriskVersion())) {
            cdrDetails.put("ASTERISK_VERSION", ctx.getAsteriskVersion().getCdrVersion());
        }

        CDRData cdrData = new CDRData(cdrDetails);
        return cdrData;
    }

    private void comparePrices(String id, BigDecimal priceWithVpricing, BigDecimal priceWithoutVpricing, String accountId) {
        Log.info("For ID: [{}] and api-key: [{}], totalPrice as per vPricing: [{}] and totalPrice without vPricing: [{}]",
                id, accountId, priceWithVpricing, priceWithoutVpricing);
        // Check if the prices are different
        if (priceWithoutVpricing == null || priceWithVpricing == null) {
            Log.error("Price comparison failed for ID: [{}] due to null value(s)", id);
            return; // Avoid null pointer exceptions
        }

        if (priceWithoutVpricing.compareTo(priceWithVpricing) != 0) {
            Log.warn("Price mismatch detected for ID: {}, api-key: {}", id, accountId);
            CompletableFuture.runAsync(() -> {
                CHARGING_PRICE_DISPARITY_COUNTER.inc(); // Increment the counter when prices are different
            });
        }
    }
}
