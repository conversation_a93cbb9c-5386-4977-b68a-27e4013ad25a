package com.nexmo.voice.core.emergency.address;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexmo.voice.core.viam.*;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Base64;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;

public class EmergencyAddressServiceClient extends ViamAuthProtectedService {

    private static final Logger LOG = LogManager.getLogger(EmergencyAddressServiceClient.class);

    private EmergencyAddressServiceConfig serviceConfig;

    private HttpClient client;

    private HttpClientContext context;

    private static final String X_VONAGE_EC_HEADER = "X-Vonage-EC-Header";

    private static final String X_VONAGE_ACCESS_TOKEN_HEADER = "X-Vonage-Access-Token";

    private static final Histogram EMERGENCY_ADDRESS_SERVICE_REQUESTS_LATENCY = Histogram.build()
            .buckets(DEFAULT_DURATION_BUCKETS)
            .name("sipapp_emergency_address_service_requests_latency")
            .help("Http Requests Latency to Fetch Emergency Service Address")
            .labelNames("status")
            .register();

    private static final Counter EMERGENCY_ADDRESS_SERVICE_ERRORS = Counter.build()
            .name("sipapp_emergency_address_service_error")
            .labelNames("reason")
            .help("Emergency Address Service error count").register();


    public EmergencyAddressServiceClient(EmergencyAddressServiceConfig serviceConfig, ViamAuthConfig authConfig) {
        super(authConfig);
        this.serviceConfig = serviceConfig;

        context = HttpClientContext.create();
        client = HttpClientBuilder.create()
                .setDefaultRequestConfig(
                        RequestConfig.custom()
                                .setConnectTimeout(serviceConfig.getServiceTimeout())
                                .setConnectionRequestTimeout(serviceConfig.getServiceTimeout())
                                .setSocketTimeout(serviceConfig.getServiceTimeout())
                                .build()
                )
                .build();
    }


    public EmergencyNumberDetail getEmergencyNumberDetail(String number, String apiKey) throws EmergencyAddressServiceException {
        String extendedToken = null;
        try {
            ViamAccessTokenResponse token = super.getAccessToken(new ViamScope().withAnyApiKey().withAnyApplicationId(), ViamAudience.NEXMO_API);
            extendedToken = super.getExtendedToken(token, apiKey);
        } catch (ViamAuthenticationException e) {
            LOG.error("Could not complete VIAM authentication: " + e.getMessage(), e);
            throw new EmergencyAddressServiceException("Could not complete VIAM authentication", e);
        }

        try {
            return doGetDetail(number, apiKey, serviceConfig.getPrimaryServiceUrl(), extendedToken, serviceConfig.isPrimaryUseBearerToken());
        } catch(EmergencyAddressServiceException e) {
            LOG.warn("Exception from primary emergency number service: "+e.getMessage());
            if(serviceConfig.getSecondaryServiceUrl() == null) {
                LOG.error("No secondary emergency number service URL");
                throw e;
            }
        }
        return doGetDetail(number, apiKey, serviceConfig.getSecondaryServiceUrl(), extendedToken, serviceConfig.isSecondaryUseBearerToken());
    }

    private EmergencyNumberDetail doGetDetail(String number, String apiKey, URI serviceUrl, String extendedToken, boolean useBearerToken) throws EmergencyAddressServiceException {
        URI numberUrl = null;

        try {
            numberUrl = new URIBuilder(serviceUrl)
                    .setPath(serviceUrl.getPath() + "/numbers/"+number)
                    .build();
        } catch (URISyntaxException e) {
            LOG.error("Could not build URL for emergency address service: "+e.getMessage(), e);
            throw new EmergencyAddressServiceException("Could not build URL for emergency address service", e);
        }
        LOG.debug("Getting emergency number detail for number="+number+", apiKey="+apiKey+", url="+numberUrl);
        HttpGet get = new HttpGet(numberUrl);
        long currentTimestamp = System.currentTimeMillis()/1000;
        get.setHeader(X_VONAGE_EC_HEADER, Base64.getEncoder().encodeToString((apiKey+":"+currentTimestamp+":rtc-emergency-calling").getBytes()));
        if(useBearerToken) {
            get.setHeader(HttpHeaders.AUTHORIZATION, "Bearer " + extendedToken);
        } else {
            get.setHeader(X_VONAGE_ACCESS_TOKEN_HEADER, extendedToken);
        }

        HttpResponse response = null;
        long httpRequestStartTime = System.nanoTime();
        try {
            response = this.client.execute(get, this.context);
        } catch(IOException e) {
            EMERGENCY_ADDRESS_SERVICE_ERRORS.labels("io_error").inc();
            LOG.error("Could not execute HTTP POST to emergency address service: "+e.getMessage(), e);
            throw new EmergencyAddressServiceException("Could not execute HTTP POST to emergency address service", e);
        }

        if ((response == null) || (response.getStatusLine() == null)) {
            EMERGENCY_ADDRESS_SERVICE_ERRORS.labels("unreadable").inc();
            LOG.error("Could not read emergency address service response");
            throw new EmergencyAddressServiceException("Could not read emergency address service response");
        }
        EMERGENCY_ADDRESS_SERVICE_REQUESTS_LATENCY
                .labels(Integer.toString(response.getStatusLine().getStatusCode()))
                .observe(System.nanoTime() - httpRequestStartTime);

        if(response.getStatusLine().getStatusCode() != 200) {
            LOG.error("Emergency address service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
            throw new EmergencyAddressServiceException("Emergency address service returned HTTP status "+response.getStatusLine().getStatusCode()+" "+response.getStatusLine().getReasonPhrase());
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(response.getEntity().getContent(), EmergencyNumberDetail.class);
        } catch(IOException e) {
            LOG.error("Could not parse emergency address service response body: "+e.getMessage(), e);
            throw new EmergencyAddressServiceException("Could not parse emergency address service response body", e);
        }
    }

}
