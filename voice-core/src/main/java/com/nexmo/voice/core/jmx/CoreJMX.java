package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.Collection;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.thepeachbeetle.common.util.DateUtil;
import com.thepeachbeetle.common.xml.LoaderException;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.Shutdown;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;

/**
 * <AUTHOR>
 */
public class CoreJMX implements CoreJMXMBean {

    private static final Logger Log = LogManager.getLogger(CoreJMX.class);


    public CoreJMX() {
    }

    @Override
    public String getUptime() {
        return "" + (System.currentTimeMillis() - Core.getInstance().getConfig().getTimeLoaded());
    }

    @Override
    public String getTimeStarted() {
        return DateUtil.createCommonTimestampString(new Date(Core.getInstance().getConfig().getTimeLoaded()));
    }

    @Override
    public String getConfigXmlFileLocation() {
        return Core.getInstance().getConfig().getConfigXmlFileLocation().getAbsolutePath();
    }

    @Override
    public void dumpXmlToLog() {
        try {
            Config config = Core.getInstance().getConfig();
            config.dumpXmlToLog();
        } catch (Throwable t) {
            Log.error("xxxxxx", t);
        }
    }

    @Override
    public void reloadConfig() {
        long timeCalled = System.currentTimeMillis();
        Config config = Core.getInstance().getConfig();
        File configXmlFileLocation = config.getConfigXmlFileLocation();
        ConfigReader configReader = new ConfigReader();
        try {
            configReader.read(configXmlFileLocation);
        } catch (LoaderException e) {
            String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
            Log.error(err, e);
            throw new RuntimeException(err, e);
        }
        Config newConfig = configReader.getConfig();
        try {
            Core.getInstance().init(newConfig);
        } catch (Exception e) {
            Log.error("....... failed to re-load configs ....", e);
            throw new RuntimeException("....... failed to re-load configs ....", e);
        }
        long timeTaken = System.currentTimeMillis() - timeCalled;
        Log.info("CoreJMX::reloadConfig time-taken [ " + timeTaken + "]");
        Log.info(">>>>>>>>>>>>>>>>>>>> CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void deleteContext(String sessionId) {
        Log.warn("Ignoring attempt to delete context for sessionId {}", sessionId);
    }

    @Override
    public void flushContextCacheForAccount(String accountId) {
        Log.warn("Ignoring attempt to flush context for account {}", accountId);
    }

    @Override
    public void shutdown(String message) {
        Shutdown.getInstance().shutdown(message);
    }

}
