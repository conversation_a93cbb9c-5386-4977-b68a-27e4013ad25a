package com.nexmo.voice.core.sip.api.metrics;

import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.exporter.common.TextFormat;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class PrometheusScrapeServlet extends HttpServlet {


    private final CollectorRegistry registry;
    public static final String PROMETHEUS_SCRAPE_PATH = "/prometheus";

    public PrometheusScrapeServlet(CollectorRegistry registry){
        this.registry = registry;
    }

    @Override
    protected void doGet(final HttpServletRequest request, final HttpServletResponse response) throws IOException {
        handleRequest(request, response);
    }

    @Override
    protected void doPost(final HttpServletRequest request, final HttpServletResponse response) throws IOException {
        handleRequest(request, response);
    }

    public void handleRequest(final HttpServletRequest request, final HttpServletResponse response) throws IOException {
        response.setContentType("text/plain");
        TextFormat.write004(response.getWriter(), registry.metricFamilySamples());
    }

    public String clientPath(){
        return PROMETHEUS_SCRAPE_PATH;
    }


}
