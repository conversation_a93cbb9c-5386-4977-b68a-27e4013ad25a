package com.nexmo.voice.core.cache;

import java.io.Serializable;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import io.prometheus.client.cache.caffeine.CacheMetricsCollector;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.Policy;
import com.nexmo.voice.config.applications.ApplicationsServiceConfig;
import com.nexmo.voice.config.caches.CacheConfig;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.application.ApplicationLookupException;
import com.nexmo.voice.core.application.ApplicationsServiceClient;
import com.nexmo.voice.core.application.ProvisioningClient;
import com.thepeachbeetle.messaging.hub.core.provisioning.client.config.ProvisioningApiClientConfig;


public class ApplicationCache implements Serializable {
    private static final long serialVersionUID = 203875511609777309L;
    private final static Logger Log = LogManager.getLogger(ApplicationCache.class);

    private final ProvisioningClient provisioningClient;
    private final ApplicationsServiceClient applicationsServiceClient;

    private ModeType mode;

    private int size;
    private Duration expiry;
    private Duration refresh;
    private LoadingCache<String, Application> applicationsCache;

    private static final CacheMetricsCollector CACHE_METRICS_COLLECTOR = new CacheMetricsCollector().register();

    public ApplicationCache(CacheConfig config, ProvisioningApiClientConfig pconfig, ApplicationsServiceConfig asconfig) {
        Log.info("========= Initializing Provisioning Applications Client ===========");
        this.provisioningClient = new ProvisioningClient(pconfig);
        Log.info("========= Initializing Provisioning Applications Client [done] ===========");

        Log.info("========= Initializing Applications Service Client ===========");
        this.applicationsServiceClient = new ApplicationsServiceClient(asconfig);
        boolean applicationsServiceClientEnabled = asconfig.isEnabled();
        Log.info("========= Initializing Applications Service Client [done] ===========");

        this.mode = ModeType.from(!applicationsServiceClientEnabled, applicationsServiceClientEnabled);
        Log.info("Setting application look-up mode to " + this.mode);

        this.size = config.getSize();
        this.expiry = config.getExpiry();
        this.refresh = config.getRefresh();
        applicationsCache = Caffeine.newBuilder()
                                    .maximumSize(this.size)
                                    .expireAfterWrite(this.expiry)
                                    .refreshAfterWrite(this.refresh)
                                    .build(key -> fetchApplication(key));
        CACHE_METRICS_COLLECTOR.addCache("sipapp_application_cache", applicationsCache);
    }


    public Application getApplication(String applicationId) throws ApplicationLookupException {
        return applicationsCache.get(applicationId);
    }

    public Map<String, Application> getApplicationsCache() {
        return applicationsCache.asMap();
    }

    public void flushCache() {
        synchronized (this) {
            applicationsCache.invalidateAll();
            applicationsCache.cleanUp();
        }
    }

    public void updateSize(int newSize) {
        if (newSize <= 0)
            throw new IllegalArgumentException("Invalid cache size: " + newSize);

        synchronized (this) {
            Policy<String, Application> policy = this.applicationsCache.policy();
            Policy.Eviction<String, Application> evictionPolicy = policy.eviction().get(); // Will throw NoSuchElementException if not present
            evictionPolicy.setMaximum(newSize);
            this.applicationsCache.cleanUp();
            this.size = newSize;
        }
    }

    public void updateExpiry(String period) {
        final Duration newExpiry = Duration.parse(period);

        synchronized (this) {
            Policy<String, Application> policy = this.applicationsCache.policy();
            // FIXME: Will become Policy.FixedExpiration in Caffeine 3.0.1
            Policy.Expiration<String, Application> expirationPolicy = policy.expireAfterWrite().get(); // Will throw NoSuchElementException if not present
            expirationPolicy.setExpiresAfter(newExpiry);
            this.applicationsCache.cleanUp();
            this.expiry = newExpiry;
        }
    }

    public void updateRefresh(String period) {
        final Duration newRefresh = Duration.parse(period);

        synchronized (this) {
            Policy<String, Application> policy = this.applicationsCache.policy();
            // FIXME: Will become Policy.FixedRefresh and setRefreshesAfter() in Caffeine 3.0.1
            Policy.Expiration<String, Application> refreshPolicy = policy.refreshAfterWrite().get(); // Will throw NoSuchElementException if not present
            refreshPolicy.setExpiresAfter(newRefresh);
            this.applicationsCache.cleanUp();
            this.refresh = newRefresh;
        }
    }

    public String getMode() {
        return mode.toString();
    }

    public void setMode(String modeName) {
        ModeType newMode = ModeType.from(modeName);
        Log.info("Changing lookup mode from {} to {}", this.mode, newMode);
        this.mode = newMode;
    }


    //
    // Internal methods
    //
    private Application fetchApplication(String applicationId) throws ApplicationLookupException {
        Log.info("Fetching Application {}", applicationId);

        Application app1 = null;
        if (mode.isProvisioningClientEnabled()) {
            long reqTime = System.currentTimeMillis();
            app1 = provisioningClient.getApplication(applicationId);
            Log.info("Provisioning application client took {} ms", System.currentTimeMillis() - reqTime);
        }
        Application app2 = null;
        if (mode.isApplicationsServiceClientEnabled()) {
            long reqTime = System.currentTimeMillis();
            app2 = applicationsServiceClient.getApplication(applicationId);
            Log.info("Application Service client took {} ms", System.currentTimeMillis() - reqTime);
        }
        // Compare if we retrieved both
        if (mode.isProvisioningClientEnabled() && mode.isApplicationsServiceClientEnabled() && !Objects.equals(app1, app2)) {
            Log.warn("Different application responses from PHUB ({}) vs Applications Service({})", app1, app2);
        }

        if (app1 != null)
            return app1;
        else if (app2 != null)
            return app2;
        else
            return null; // Null here means the application has been deleted, and should be removed from the cache
    }

    //
    // Internal enum for lookup mode
    //
    public static enum ModeType {
        DISABLED(false, false),
        PROVISIONING(true, false),
        APPLICATIONS_SERVICE(false, true),
        DUAL(true, true);

        private final boolean provisioningClientEnabled;
        private final boolean applicationsServiceClientEnabled;

        ModeType(final boolean provisioningClientEnabled, final boolean applicationsServiceClientEnabled) {
            this.provisioningClientEnabled = provisioningClientEnabled;
            this.applicationsServiceClientEnabled = applicationsServiceClientEnabled;
        }

        public boolean isProvisioningClientEnabled() {
            return this.provisioningClientEnabled;
        }

        public boolean isApplicationsServiceClientEnabled() {
            return this.applicationsServiceClientEnabled;
        }

        public static ModeType from(final String key) {
            if (key != null)
                for (final ModeType type : ModeType.values())
                    if (key.equalsIgnoreCase(type.toString()))
                        return type;
            throw new IllegalArgumentException("Cannot determine ModeType from (" + key + ")");
        }

        public static ModeType from(boolean provisioningClientEnabled, boolean applicationsServiceClientEnabled) {
            for (final ModeType type : ModeType.values())
                if ((provisioningClientEnabled == type.isProvisioningClientEnabled()) &&
                        (applicationsServiceClientEnabled == type.isApplicationsServiceClientEnabled()))
                    return type;
            throw new IllegalArgumentException("Cannot determine ModeType from (" + provisioningClientEnabled + "," + applicationsServiceClientEnabled + ")");
        }
    }

}
