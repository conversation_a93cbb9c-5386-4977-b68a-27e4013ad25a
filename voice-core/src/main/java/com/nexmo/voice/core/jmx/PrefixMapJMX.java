package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.Set;

import org.apache.log4j.Logger;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.prefix.MappingConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.config.prefix.PrefixMapSource;
import com.nexmo.voice.config.prefix.PrefixMapConfigUtils;
import com.nexmo.voice.core.Core;

import com.thepeachbeetle.common.xml.XmlUtil;


/**
 * <AUTHOR>
 */
public class PrefixMapJMX implements PrefixMapJMXMBean {

    private static final Logger Log = Logger.getLogger(PrefixMapJMX.class.getName());

    @Override
    public int getSize() {
        Log.info("About to report size of Prefix Map");

        final PrefixMapConfig config = Core.getInstance().getConfig().getPrefixMapConfig();
        if (config == null)
            return 0;
        else
            return config.size();
    }

    @Override
    public boolean getEnabled() {
        Log.info("About to report if Prefix Group pricing is enabled");

        final PrefixMapConfig config = Core.getInstance().getConfig().getPrefixMapConfig();
        if (config == null)
            return false;
        else
            return config.isEnabled();
    }

    @Override
    public void setEnabled(boolean value) {
        final String newState = (value) ? "enable" : "disable";
        Log.info("About to " + newState + " Prefix Group pricing");

        final PrefixMapConfig config = Core.getInstance().getConfig().getPrefixMapConfig();
        final PrefixMapSource source = Core.getInstance().getConfig().getPrefixMapSource();

        if (config == null)
            throw new IllegalStateException("No Prefix Map config present");

        PrefixMapConfig newConfig = config.cloneWithEnabled(value);
        Core.getInstance().getConfig().setPrefixMapConfig(newConfig, source);

        Log.info("Prefix config changed to " + newState + "d");
    }

    @Override
    public String viewPrefixMapMetadata() {
        Log.info("About to fetch Prefix Map metadata from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final PrefixMapConfig config = Core.getInstance().getConfig().getPrefixMapConfig();
        final PrefixMapSource source = Core.getInstance().getConfig().getPrefixMapSource();

        if (config != null) {
            sb.append("Source: ");
            sb.append(source.getSource());
            sb.append("<br><br>");
        } else {
            sb.append("ERROR: No Prefix Map config present");
        }

        return sb.toString();
    }

    @Override
    public String loadPrefixMapFromFile(String filename) {
        Log.info("About to load new Prefix Map config from " + filename);

        if (filename == null) {
            return "ERROR: No filename provided.";
        }

        final File f = new File(filename);
        if (!f.exists()) {
            return "ERROR: File \"" + filename + "\" does not exist";
        }

        long timeCalled = System.currentTimeMillis();
        final StringBuilder sb = new StringBuilder();
        sb.append("Loading from file: \"");
        sb.append(filename);
        sb.append("\" => ");

        final PrefixMapSource newSource = new PrefixMapSource(filename);
        final PrefixMapConfig newConfig = PrefixMapConfigUtils.loadConfigFromXMLFile(filename);
        if (newConfig != null) {
            sb.append("SUCCESS!<br><br>");

            final PrefixMapConfig oldConfig = Core.getInstance().getConfig().getPrefixMapConfig();

            if ((oldConfig == null) || oldConfig.isEmpty()) {
                sb.append("No previous config. New config contains ");
                sb.append(Integer.toString(newConfig.size()));
                sb.append(" prefixes.");

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setPrefixMapConfig(newConfig, newSource);
            } else {
                // Compare old/new configs and report the difference
                final String diffs = PrefixMapConfigUtils.compareConfigs(oldConfig, newConfig);
                sb.append(diffs);

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setPrefixMapConfig(newConfig, newSource);
            }
        } else {
            sb.append("FAILED!");
        }
        long timeTaken = System.currentTimeMillis() - timeCalled;
        Log.info("PrefixMapJMX::loadSuppliersListFromFile call-total-time [ " + timeTaken + "]");
        return sb.toString();
    }

    @Override
    public String prefixMapLookup(String number) {
        Log.info("About to use Prefix Map to look up \"" + number + "\"");

        if ((number == null) || number.trim().isEmpty()) {
            return "ERROR: No number provided.";
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("Looking up: \"");
        sb.append(number);
        sb.append("\" => ");

        final PrefixMapConfig config = Core.getInstance().getConfig().getPrefixMapConfig();
        if (config != null) {
            MappingConfig mapping = config.lookup(number);
            sb.append(mapping);
        } else {
            sb.append("FAILED! No Prefix Map config present");
        }

        return sb.toString();
    }

}
