package com.nexmo.voice.core.gateway.asterisk.task;

import java.util.concurrent.ThreadFactory;

public class CustomThreadFactoryBuilder {
    private String namePrefix = null;

    public CustomThreadFactoryBuilder setNamePrefix(String namePrefix) {
        this.namePrefix = namePrefix;
        return this;
    }

    public ThreadFactory build() {
        return build(this);
    }

    private static ThreadFactory build(CustomThreadFactoryBuilder builder) {
        final String namePrefix = builder.namePrefix;

        return new ThreadFactory() {
            @Override
            public Thread newThread(Runnable runnable) {
                Thread thread = new Thread(runnable);

                thread.setName(thread.getName() + "-" + namePrefix);
                thread.setDaemon(false);
                thread.setPriority(Thread.NORM_PRIORITY);
                return thread;
            }
        };
    }

}
