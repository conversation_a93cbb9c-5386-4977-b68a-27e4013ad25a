package com.nexmo.voice.core.billing.vquota;

import com.nexmo.voice.core.billing.vquota.currentbalance.CurrentBalanceApiConfig;
import com.nexmo.voice.core.billing.vquota.currentbalance.CurrentBalanceApiConfigLoader;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiConfig;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiConfigLoader;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class VQuotaServiceConfigLoader extends NestedXmlHandler {
    private VQuotaServiceConfig config;
    private boolean enabled;
    private String secretName;
    private CurrentBalanceApiConfig currentBalanceApiConfig;
    private PriceImpactApiConfig priceImpactApiConfig;


    public VQuotaServiceConfigLoader(final String nodeName) {
        super(nodeName);
        CurrentBalanceApiConfigLoader currentBalanceApiConfigLoader = new CurrentBalanceApiConfigLoader(getSubNodeName(nodeName, CurrentBalanceApiConfig.ROOT_NODE));
        addHandler(currentBalanceApiConfigLoader);
        PriceImpactApiConfigLoader priceImpactApiConfigLoader = new PriceImpactApiConfigLoader(getSubNodeName(nodeName, PriceImpactApiConfig.ROOT_NODE));
        addHandler(priceImpactApiConfigLoader);
    }

    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.enabled = parseBoolean(content.getAttribute(VQuotaServiceConfig.ENABLED_ATTR, true));
            this.secretName = content.getAttribute(VQuotaServiceConfig.API_SECRET_NAME_ATTR, true);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.config = new VQuotaServiceConfig(this.enabled, this.secretName, currentBalanceApiConfig, priceImpactApiConfig);
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof CurrentBalanceApiConfigLoader) {
            currentBalanceApiConfig = ((CurrentBalanceApiConfigLoader) childHandler).getConfig();
        } else if (childHandler instanceof PriceImpactApiConfigLoader) {
            priceImpactApiConfig = ((PriceImpactApiConfigLoader) childHandler).getConfig();
        }
    }

    public VQuotaServiceConfig getConfig() {
        return config;
    }
}