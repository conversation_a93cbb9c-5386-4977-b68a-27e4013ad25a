package com.nexmo.voice.core.sip.event.cdr;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.sip.event.CdrEventHandler;
import com.nexmo.voice.core.sip.event.IgnoredVoiceEventHandlerException;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.types.CarrierPlatform;
import com.nexmo.voice.core.types.SIPCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;

import java.util.Map;

import static com.nexmo.voice.core.sip.event.CdrEventUserData.roundUpTime;
import static com.nexmo.voice.core.sip.event.CdrEventUserData.toLong;

public class Asterisk16CdrEventUserData extends AbstractCdrEventUserData {

    private static final Logger Log = LogManager.getLogger(Asterisk16CdrEventUserData.class.getName());

    // Additional userField elements
    private static final String ANSWERED_TIME_MS = "ANSWEREDTIME_MS";
    private static final String PDD_PROGRESS_TIME_MS = "PROGRESSTIME_MS";

    private static final String PARTIAL_CDR_HANGUP_CAUSE = "777";

    public Asterisk16CdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        super(event, channelUniqueId);
    }


    protected void handleMissingUserFieldEvent(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        if (Log.isDebugEnabled()) {
            Log.debug("Starting handleMissingUserFieldEvent for event: " + event + "; channelUniqueID:" + channelUniqueId);
        }

        if ("Dial".equals(event.getLastApplication())) {
            Log.info("Intentionally ignoring CdrEvent channelUniqueId = {} as it has missing user field",  channelUniqueId);
            throw new IgnoredVoiceEventHandlerException("Ignoring missing userfield");
        }
        // Try to create the userField data for CANCEL event. If unsuccessful - throw an
        // exception
        if (Log.isDebugEnabled()) {
            Log.debug("Attempt to fix user field data if not a 777 partial CDR...");
        }
        Map<String, String> userFieldsMap = extractUserField(event.getUserField(), channelUniqueId);
        String userFieldHangupCause = userFieldsMap.get(HANGUP_CAUSE);
        if (!PARTIAL_CDR_HANGUP_CAUSE.equals(userFieldHangupCause) && !attemptFixingUserFieldData(event, channelUniqueId)) {
            Log.error("Failed to fix the missing userField, missing CANCEL indicators. channelUniqueId = {}, LastApplication {}, Disposition {}, Duration  {}", channelUniqueId, event.getLastApplication(), event.getDisposition(), event.getDuration());
            throw new VoiceEventHandlerException("Error handling CdrEventUserData. userField not found. channelUniqueId: " + event.getUniqueId());
        }

        if (Log.isDebugEnabled()) {
            Log.debug("Completed handleMissingUserFieldEvent for event: " + event + "; channelUniqueID:" + channelUniqueId);
        }
    }

    protected void parseUserField(String userField, String channelUniqueId) throws VoiceEventHandlerException {
        if (Log.isDebugEnabled()) {
            Log.debug("Parsing userField {} with channelUniqueId {} ", userField, channelUniqueId);
        }
        Map<String, String> userFieldsMap = extractUserField(userField, channelUniqueId);

        // Fetch the HANGUPCAUSE and DIALSTATUS - these are mandatory in any scenario
        // If their values are wrong or missing, an exception is thrown
        preProcessHangupDialStatus(userFieldsMap, channelUniqueId);
        setHangupCause(userFieldsMap.get(HANGUP_CAUSE), channelUniqueId);
        setDialStatus(userFieldsMap.get(DIAL_STATUS), channelUniqueId);

        for (String userFieldKey : userFieldsMap.keySet()) {
            String userFieldValue = userFieldsMap.get(userFieldKey);

            switch (userFieldKey) {
                case GATEWAYS_LIST:
                    gatewaysList = userFieldValue;
                    continue;
                case CURRENT_GATEWAY:
                    currentGateway = userFieldValue;
                    continue;
                case GATEWAY_FAIL_OVER_ATTEMPT:
                    setGatewayAttemptDetails(userFieldValue, channelUniqueId);
                    continue;
                case CHANNEL_2_ID:
                    channel2Id = userFieldValue;
                    continue;
                case SIP_TARGET_FALLBACK_ATTEMPT_NO:
                    setTargetFallbackAttemptNo(userFieldValue, channelUniqueId);
                    continue;
                case SIP_TARGET_FALLBACK_ALTERNATIVES:
                    setTargetFallbackAlternatives(userFieldValue, channelUniqueId);
                    continue;
                case NEXMO_UUID:
                    nexmoUUID = userFieldValue;
                    continue;

                case DIAL_STATUS: // already fetched
                case HANGUP_CAUSE:
                    continue;

                case TO_INPS:
                    carrierPlatform = CarrierPlatform.from(userFieldValue);
                    continue;

                case ANSWERED_TIME_MS:
                    answeredDurationMillis = toLong(userFieldValue, channelUniqueId);
                    answeredDuration = roundUpTime(answeredDurationMillis);
                    continue;

                case PDD_PROGRESS_TIME_MS:
                    pddTimeMillis = toLong(userFieldValue, channelUniqueId);
                    continue;

                default: // unexpected elements
                    additionalParams.put(userFieldKey, userFieldValue);
                    continue;
            }
        }
        if(cdrEventBillableSeconds != null && cdrEventBillableSeconds > answeredDuration){
            Log.error("{} RECEIVED A CALL THAT HAD CDREVENTBILLABLESECONDS {} MORE THAN ANSWEREDDURATION {}", getNexmoUUID(), cdrEventBillableSeconds, answeredDuration);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("Completed parsing userField {} with channelUniqueId {} ", userField, channelUniqueId);
        }
    }

    protected void preProcessHangupDialStatus(Map<String, String> userFieldsMap, String channelUniqueId) {
        processHangupCause(userFieldsMap, channelUniqueId);
        processDialStatus(userFieldsMap, userFieldsMap.get(HANGUP_CAUSE), channelUniqueId);

        return;
    }

    @Override
    public long getReportedBillableSeconds() {
        final long result;
        if(cdrEventBillableSeconds != null){
            result = Math.max(cdrEventBillableSeconds.longValue(), answeredDuration);
        }
        else {
            result = Math.max(0, answeredDuration);
        }
        Log.info("{} calculated {} billable seconds for this call", nexmoUUID, result);
        return result;
    }

    @Override
    public SIPCode calculateCallSIPCodeAndDialStatus(String channelUniqueId, Integer billableSeconds, VoiceContext channelContext) {
        if (Log.isDebugEnabled()) {
            Log.debug("Starting calculateCallSIPCodeAndDialStatus in asterisk 16...");
        }

        SIPCode sipCode = super.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, channelContext);

        //In Asterisk 16: HANGUPCAUSE=44 w/ DIAL_STATUS_ANSWER => AST_CAUSE_FAILURE to match HANGUPCAUSE=38; DIALSTATUS=ANSWER; billableseconds=271 that happens in Asterisk 1.8
        if ((this.getHangupCause() == HangupCause.AST_CAUSE_REQUESTED_CHAN_UNAVAIL) &&
                (CdrEventHandler.DIAL_STATUS_ANSWER.equals(this.getDialStatus())) &&
                this.getReportedBillableSeconds() > 0)
            sipCode = SIPCode.FLOW_FAILED;

        setDialStatus(channelUniqueId, sipCode);

        if (Log.isDebugEnabled()) {
            Log.debug("Completed calculateCallSIPCodeAndDialStatus in asterisk 16...");
        }

        return sipCode;
    }

    private void processDialStatus(Map<String, String> userFieldsMap, String userFieldHangupCause, String channelUniqueId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Starting processDialStatus for userFieldHangupCause: " + userFieldHangupCause + "; uniqueID: " + channelUniqueId);
        }

        String userFieldDialStatus = userFieldsMap.get(DIAL_STATUS);
        if (Log.isDebugEnabled()) {
            Log.debug("dialstatus=" + userFieldDialStatus);
        }

        if (userFieldDialStatus == null || userFieldDialStatus.trim().isEmpty() || userFieldDialStatus.equals("null")) {
            // Try grabbing dial status from user event
            Log.info("Attempt dialstatus lookup in cdr userfield event cache for channel unique ID: " + channelUniqueId + "; channel ID:" + this.event.getDestinationChannel());
            if (Core.getInstance().getCdrUserFieldEventCache().containsEvent(channelUniqueId, this.event.getDestinationChannel())) {
                // Extract out dialstatus from cdr userfield event cache
                userFieldDialStatus = Core.getInstance().getCdrUserFieldEventCache().getEvent(channelUniqueId, this.event.getDestinationChannel()).getDialStatus();
                userFieldsMap.put(DIAL_STATUS, userFieldDialStatus);
            }
        }

        // Set Dial status
        if (String.valueOf(HangupCause.AST_CAUSE_UNALLOCATED.getCode()).equals(userFieldHangupCause) && (userFieldDialStatus == null || userFieldDialStatus.isEmpty())) {
            // Seed the dialstatus before updating based on sip code
            userFieldsMap.put(DIAL_STATUS, CdrEventHandler.DIAL_STATUS_CHANUNAVAIL);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("Completed processDialStatus - set dialstatus=" + userFieldsMap.get(DIAL_STATUS));
        }
    }

    private void processHangupCause(Map<String, String> userFieldsMap, String channelUniqueId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Starting processHangupCause for channelUniqueId: " + channelUniqueId);
        }

        String userFieldHangupCause = userFieldsMap.get(HANGUP_CAUSE);
        if (!PARTIAL_CDR_HANGUP_CAUSE.equals(userFieldHangupCause)) {
            // It's not a 777 hangup cause so don't process
            if (Log.isDebugEnabled()) {
                Log.debug("Userfield Hangup Cause is not 777 so skipping...");
            }

            return;
        }

        Log.info("Processing " + PARTIAL_CDR_HANGUP_CAUSE + " hangup cause for channel unique ID: " + channelUniqueId + "; channel ID:" + this.event.getDestinationChannel());
        if (!Core.getInstance().getCdrUserFieldEventCache().containsEvent(channelUniqueId, this.event.getDestinationChannel())) {
            if (Log.isDebugEnabled()) {
                Log.debug("Not found in hangup cache so skipping...");
            }

            return;
        }

        // Extract out hangup cause from cdr userfield event cache
        Integer code = Core.getInstance().getCdrUserFieldEventCache().getHangupCause(channelUniqueId, this.event.getDestinationChannel());
        userFieldHangupCause = String.valueOf(code);
        userFieldsMap.put(HANGUP_CAUSE, userFieldHangupCause);

        if (Log.isDebugEnabled()) {
            Log.info("Completed processHangupCause for channelUniqueId: " + channelUniqueId + "; set hangupCause=" + userFieldsMap.get(HANGUP_CAUSE));
        }

        return;
    }

    // SIP-222 ; When CANCEL is sent before the INVITE processing is completed,
    // there are cases
    // where the CDR Event userField is missing or include only partial details.
    // If we can verify that the call was probably cancelled we will reconstruct the
    // proper userField
    @Override
    public boolean attemptFixingUserFieldData(CdrEvent event, String channelUniqueId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Attempting to fix user field data for unique ID: " + channelUniqueId);
        }

        boolean userFieldRestored = false;

        // Depending on the INVITE handling and the CANCEL handling race condition, we
        // might be able to identify the
        // sessionId and the callId. Lets try and log it if possible:
        String origSessionDetails = getOriginSessionDetails(channelUniqueId);

        Map<String, String> userFieldsMap = extractUserField(event.getUserField(), channelUniqueId);
        String userFieldHangupCause = userFieldsMap.get(HANGUP_CAUSE);

        // Verify that indeed the call hasn't started and that there is no potential for
        // missing on chargeable call
        if ("AGI".equals(event.getLastApplication()) && "NO ANSWER".equals(event.getDisposition())
                && (0 == event.getDuration())) {

            Log.warn("CdrEvent is missing the userField or userField is partialy available. Assuming CANCEL. "
                    + origSessionDetails + " event: " + event);

            this.hangupCause = HangupCause.AST_CAUSE_NOANSWER;
            this.dialStatus = CdrEventHandler.DIAL_STATUS_CANCEL;
            this.channel2Id = null;
            this.nexmoUUID = null;

            this.gatewaysList = "Unknown-due-to-fast cancel";
            this.currentGateway = gatewaysList;
            this.gwsAttemptStr = "1#1";
            this.currentGWAttempt = 1;
            this.availableGWAttempts = 1;

            this.fallbackAttemptStr = "1";
            this.fallbackAlternativesStr = "1";
            this.currentFallbackAttempt = 1;
            this.availableFallbackAlternatives = 1;

            userFieldRestored = true;
        } else if ("Congestion".equals(event.getLastApplication()) && "FAILED".equals(event.getDisposition())
                && (0 == event.getDuration())) {

            // we only send calls to the Congestion() application if AGISTATUS/AJ_AGISTATUS isn't SUCCESS or GW is empty
            // this should therefore be a final event for a failed call

            Log.warn("CdrEvent is missing the userField or userField is partial/y available and Congestion() was invoked. "
                    + origSessionDetails + " event: " + event);

            this.hangupCause = HangupCause.AST_CAUSE_CONGESTION;
            this.dialStatus = CdrEventHandler.DIAL_STATUS_CONGESTION;
            this.channel2Id = null;
            this.nexmoUUID = null;

            this.gatewaysList = "Unknown-due-to-congestion";
            this.currentGateway = gatewaysList;
            this.gwsAttemptStr = "1#1";
            this.currentGWAttempt = 1;
            this.availableGWAttempts = 1;

            this.fallbackAttemptStr = "1";
            this.fallbackAlternativesStr = "1";
            this.currentFallbackAttempt = 1;
            this.availableFallbackAlternatives = 1;

            userFieldRestored = true;
        } else if (PARTIAL_CDR_HANGUP_CAUSE.equals(userFieldHangupCause)) {
            if (Log.isDebugEnabled()) {
                Log.debug("Partial CDR event with hangup cause=777 so store in PartialCdrEventCache and ignore...");
            }
            Core.getInstance().getPartialCdrEventCache().addEvent(channelUniqueId, event.getDestinationChannel(), new PartialCdrEvent(event, this));
            this.nexmoUUID = userFieldsMap.get(NEXMO_UUID);
            this.ignoreEvent = true;
            userFieldRestored = true;
        } else {
            Log.warn(
                    "CdrEvent is missing the userField or userField is partialy available. The event does not include the specific CANCEL-during-INVITE indicators. "
                            + origSessionDetails + " event: " + event
                            + " PLEASE VERIFY IN HEPIC WHAT IS GOING ON WITH THIS CALL.");
        }

        return userFieldRestored;
    }
}
