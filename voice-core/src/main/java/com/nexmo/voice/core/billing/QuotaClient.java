package com.nexmo.voice.core.billing;

import java.math.BigDecimal;
import java.util.Objects;

import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.logger.SIPAppLogger;
import io.prometheus.client.Histogram;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.quota.ExtendedQuotaApiClientConfig;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.client.config.QuotaApiClientConfig;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

/**
 * This class is used as a facade of the messaging.jar QuotaClient. It is used
 * in order to log the quota requests as generated by SIPApp
 * <p>
 * The logging feature is enabled or disabled in the sip.xml
 * file
 *
 * <AUTHOR>
 */
public class QuotaClient extends com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient {

    private final static Logger Log = LogManager.getLogger(QuotaClient.class);
    private final static Logger Quota_updates_Log = LogManager.getLogger("quota.updates.log");

    private static final Histogram QUOTA_UPDATES_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_quota_latency").help("Processing time for quota updates").labelNames("type").register();
    private static final Histogram QUOTA_UPDATES_ERROR_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_quota_error_latency").help("Processing time for error quota updates").labelNames("type", "error").register();



    private final boolean logEnabled;

    public QuotaClient(QuotaApiClientConfig config, ExtendedQuotaApiClientConfig extendedConfig) {
        super(config);
        this.logEnabled = extendedConfig.isLogEnabled();
        Log.info("SIPApp ExtendedQuotaClient logEnabled = {} ", logEnabled);
    }

    public AccountBalance consume(
            final VoiceContext ctx,
            final BigDecimal amount,
            String ref,
            final String messageId)
            throws QuotaException, NotEnoughBalanceException, QuotaDisabledException, QuotaUnderMaintenanceException, AccountNotFoundException, IllegalOperationOnSubAccountException {
        if (ctx.isVoiceSkipQuota()) {
            if (Log.isDebugEnabled())
                Log.debug("sessionId {} channelId {}, accountId {} , Quota consume  was skipped as this was marked as voice skip quota", ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId());
            return null;
        }
        if (ctx.allowNegativeBalance()) {
            ref = "allowNegative" + ref;
        }
        return consume(ctx.getAccountId(), amount, ref, messageId);
    }

    @Override
    public AccountBalance consume(
            final String accountId,
            final BigDecimal amount,
            String ref,
            final String messageId)
            throws QuotaException, NotEnoughBalanceException,
            QuotaDisabledException, QuotaUnderMaintenanceException,
            AccountNotFoundException, IllegalOperationOnSubAccountException {

        final long timer = System.nanoTime();
        if (Log.isDebugEnabled())
            Log.debug("In ExtendedQuotaClient about to super.consume for account {} amount {} ref {} ",
                    accountId, amount.toPlainString(), ref);

        boolean allowNegativeBalance = false;
        if (ref.startsWith("allowNegative")) {
            ref = ref.replace("allowNegative", "");
            allowNegativeBalance = true;
        }

        if (logEnabled) {
            StringBuffer sb = new StringBuffer();
            appendCmd(sb, "CONSUME");
            appendAccountId(sb, accountId);
            appendAmount(sb, amount);
            appendRef(sb, ref);
            appendAllowNegative(sb, allowNegativeBalance);
            Quota_updates_Log.info(sb.toString());
        }
        try {
            AccountBalance accountBalance = super.consume(accountId, amount, ref, null, allowNegativeBalance);
            if (Log.isDebugEnabled())
                Log.debug("Consume returned accountBalance: {}", accountBalance);
            QUOTA_UPDATES_LATENCY.labels("consume").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            return accountBalance;
        } catch (Exception e) {
            Log.warn("Consume from Account {} amount {} with ref {} allowNegativeBalance {} has been rejected due to {} : {} ",
                    accountId, Objects.isNull(amount) ? "null" : amount.toPlainString(), ref, allowNegativeBalance, e.getMessage(), e);
            if (Objects.nonNull(e.getCause())) {
                Log.warn("Further details of consume exception for account {} with ref {} is {}", accountId, ref, e.getCause());
            }
            if (logEnabled) {
                StringBuffer sb = new StringBuffer();
                appendCmd(sb, "CONSUME-REJECTED");
                appendAccountId(sb, accountId);
                appendAmount(sb, amount);
                appendRef(sb, ref);
                appendAllowNegative(sb, allowNegativeBalance);
                appendRejection(sb, e);
                Quota_updates_Log.info(sb.toString());
            }
            QUOTA_UPDATES_ERROR_LATENCY.labels("consume", e.getClass().getSimpleName()).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            throw e;
        }
    }

    public AccountBalance refund(final VoiceContext ctx,
                                 final BigDecimal amount,
                                 final String ref,
                                 final String connectionId)
            throws QuotaException, QuotaDisabledException, QuotaUnderMaintenanceException, AccountNotFoundException, IllegalOperationOnSubAccountException {
        if (ctx.isVoiceSkipQuota()) {
            if (Log.isDebugEnabled())
                Log.debug("sessionId {} channelId {}, accountId {} , Quota refund  was skipped as this was marked as voice skip quota", ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId());
            return null;
        }
        return refund(ctx.getAccountId(), amount, ref, connectionId);
    }

    @Override
    public AccountBalance refund(final String accountId, final BigDecimal amount, final String ref, final String connectionId)
            throws QuotaException, QuotaDisabledException,
            QuotaUnderMaintenanceException, AccountNotFoundException, IllegalOperationOnSubAccountException {
        final long timer = System.nanoTime();
        if (logEnabled) {
            StringBuffer sb = new StringBuffer();
            appendCmd(sb, "REFUND");
            appendAccountId(sb, accountId);
            appendAmount(sb, amount);
            appendRef(sb, ref);
            appendConnectionId(sb, connectionId);
            Quota_updates_Log.info(sb.toString());
        }

        try {
            final AccountBalance accountBalance = super.refund(accountId, amount, ref, connectionId);
            QUOTA_UPDATES_LATENCY.labels("refund").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            return accountBalance;
        } catch (Exception e) {
            Log.warn("Refund to Account {} amount {} with ref {} has been rejected due to {} : {} ", accountId,
                    Objects.isNull(amount) ? "null" : amount.toPlainString(), ref, e.getMessage(), e);
            if (Objects.nonNull(e.getCause())) {
                Log.warn("Further details of refund exception for account {} with ref {} is {}", accountId, ref, e.getCause());
            }
            if (logEnabled) {
                StringBuffer sb = new StringBuffer();
                appendCmd(sb, "REFUND-REJECTED");
                appendAccountId(sb, accountId);
                appendAmount(sb, amount);
                appendRef(sb, ref);
                appendConnectionId(sb, connectionId);
                appendRejection(sb, e);
                Quota_updates_Log.info(sb.toString());
            }
            QUOTA_UPDATES_ERROR_LATENCY.labels("refund", e.getClass().getCanonicalName()).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            throw e;
        }
    }


    private void appendCmd(StringBuffer sb, String cmd) {
        sb.append("\"CMD=").append(cmd).append("\",");
    }

    private void appendAccountId(StringBuffer sb, String accountId) {
        sb.append("\"ACC=");
        if (Objects.nonNull(accountId))
            sb.append(accountId);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendAmount(StringBuffer sb, BigDecimal amount) {
        sb.append("\"AMOUNT=");
        if (Objects.nonNull(amount))
            sb.append(amount.toPlainString());
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendRef(StringBuffer sb, String ref) {
        sb.append("\"REF=");
        if (Objects.nonNull(ref))
            sb.append(ref);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendConnectionId(StringBuffer sb, String connectionId) {
        sb.append("\"CONNECTION-ID=");
        if (Objects.nonNull(connectionId))
            sb.append(connectionId);
        else
            sb.append("null");
        sb.append("\",");
    }


    private void appendAllowNegative(StringBuffer sb, boolean allowNegativeBalance) {
        sb.append("\"ALLOW-NEGATIVE=").append(allowNegativeBalance).append("\",");
    }

    private void appendRejection(StringBuffer sb, Exception e) {
        sb.append("\"REJECTED-REASON=").append(e.getMessage()).append("\",");
    }


}
