package com.nexmo.voice.core.jmx;

import com.thepeachbeetle.common.app.jmx.AbstractReloadJMXMBean;

/**
 * <AUTHOR>
 */
public interface ReloadJMXMBean extends AbstractReloadJMXMBean {

    public String getUptime();

    public String getTimeStarted();

    public String getConfigXmlFileLocation();

    public void reloadConfig();

    public void reloadChargingConfig();

    public void reloadGatewayInfoMatrixConfig();

    public void reloadMTRoutingTargetGroups() throws Exception;

    public void reloadMTRoutingTargetGroup(String groupId) throws Exception;

    public void reloadInternalApiConfig();

    public void reloadSenderRandomizerRandomPool(final String poolId) throws Exception;

}
