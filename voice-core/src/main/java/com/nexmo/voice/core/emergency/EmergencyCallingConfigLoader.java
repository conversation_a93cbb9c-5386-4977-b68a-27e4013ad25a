package com.nexmo.voice.core.emergency;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import java.util.HashSet;
import java.util.Set;

public class EmergencyCallingConfigLoader extends NestedXmlHandler {

    private boolean allowFromBannedAccount;

    private boolean skipQuota;

    private boolean skipParentAccountLookup;

    private boolean requireCapabilityLocale;

    private boolean allowFromByon;

    private Set<String> emergencyServiceNumbers;

    private EmergencyCallingConfig config;

    private EmergencyCallingLocaleConfigLoader localeConfigLoader;

    private Set<EmergencyCallingLocaleConfig> localeConfigs;

    public EmergencyCallingConfigLoader(final String nodeName) {
        super(nodeName);
        this.localeConfigs = new HashSet<>();
        this.localeConfigLoader = new EmergencyCallingLocaleConfigLoader(getSubNodeName(nodeName, EmergencyCallingLocaleConfigLoader.ROOT_NODE));
        addHandler(this.localeConfigLoader);
    }

    public EmergencyCallingConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) { // <emergency-calling>
            this.allowFromBannedAccount = parseBoolean(xmlContent.getAttribute(EmergencyCallingConfig.ALLOW_FROM_BANNED_ACCT_ATTR, false, Boolean.toString(EmergencyCallingConfig.DEFAULT_ALLOW_FROM_BANNED_ACCOUNT_VALUE)));
            this.skipQuota = parseBoolean(xmlContent.getAttribute(EmergencyCallingConfig.SKIP_QUOTA_ATTR, false, Boolean.toString(EmergencyCallingConfig.DEFAULT_SKIP_QUOTA_VALUE)));
            this.skipParentAccountLookup = parseBoolean(xmlContent.getAttribute(EmergencyCallingConfig.SKIP_PARENT_ACCOUNT_LOOKUP_ATTR, false, Boolean.toString(EmergencyCallingConfig.DEFAULT_SKIP_PARENT_ACCOUNT_LOOKUP_VALUE)));
            this.requireCapabilityLocale = parseBoolean(xmlContent.getAttribute(EmergencyCallingConfig.REQUIRE_CAPABILITY_LOCALE_TAG_ATTR, false, Boolean.toString(EmergencyCallingConfig.DEFAULT_REQUIRE_CAPABILITY_LOCALE_TAG)));
            this.allowFromByon = parseBoolean(xmlContent.getAttribute(EmergencyCallingConfig.ALLOW_FROM_BYON_ATTR, false, Boolean.toString(EmergencyCallingConfig.DEFAULT_ALLOW_FROM_BYON)));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new EmergencyCallingConfig();
            config.setAllowFromBannedAccount(this.allowFromBannedAccount);
            config.setSkipQuota(this.skipQuota);
            config.setSkipParentAccountLookup(this.skipParentAccountLookup);
            config.setLocaleConfig(this.localeConfigs);
            config.setRequireCapabilityLocale(this.requireCapabilityLocale);
            config.setAllowFromByon(this.allowFromByon);
            notifyComplete();
        }
    }

    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {
        if (childHandler instanceof EmergencyCallingLocaleConfigLoader) {
            EmergencyCallingLocaleConfig lc = ((EmergencyCallingLocaleConfigLoader) childHandler).getConfig();

            boolean alreadyContainsSimilarConfig = !this.localeConfigs.add(lc);
            if (alreadyContainsSimilarConfig)
                throw new LoaderException("Duplicated EmergencyCallingLocaleConfig node for country: " + lc.getCountry());
        }
    }


}
