package com.nexmo.voice.core.billing;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.nexmo.voice.core.sip.event.CdrEventUserData;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;


public final class BillingInfo implements Serializable {

    private static final long serialVersionUID = -975145347884842250L;
    private final static Logger Log = LogManager.getLogger(BillingInfo.class);

    /**
     * These values are set once during instantiation of the BillingInfo.
     **/
    private final BigDecimal pricePerMinute;  //Price per minute as configured in the pricing system
    private final BigDecimal forcedPricePerMinute; //Forced price per minute or null

    private BigDecimal costPerMinute; //Cost per minute
    private final long firstChargedSeconds; //This is the half of how many seconds we charge when the call begins.
                                            //Meaning, ATM it's configured as 6, and so we charge 12 when the call is starting.
    private final long quotaUpdatesInterval; //How often we update the quota in seconds: In the future, might be specific per call
                                             //ATM it's 6.


    /**
     * These values are calculated once during instantiation
     **/
    private final BigDecimal actualPricePerMinute;  //The final price to use per minute.
                                                    //That consider the configured price and the forced-price.

    private final BigDecimal pricePerSecond;  //Price per second
    private final BigDecimal forcedPricePerSecond; //Forced price per second or null
    private final BigDecimal actualPricePerSecond;  //The final price to use per second

    private BigDecimal costPerSecond;  //Cost per second

    private final BigDecimal amountToChargePerInterval; //The amount to charge every interval of x seconds

    /**
     * These values are changing during the call life cycle
     **/

    private Status status; //The call Billing status
    private long callStartTime; //in millis
    private long callEndTime; //in millis

    /**
     * These values are the internal counting of seconds, these are used to calculate the
     * interval's seconds and charge the quota accordingly.
     **/
    private long lastUpdated; //The last time this BillingInfo was updated - in millis.
                              //This is now used for debugging and not to actually control the number of
                              //Quota updates. It is recording the time of the quota updates requests.
    private long secondsChargedSoFar; //Number of seconds that were charged already since the beginning of the call

    /**
     * Temporary counters TALLY - verify which is better - counting seconds or iterations
     */
    //That should run from 1 to quotaUpdatesInterval and when equal to quotaUpdatesInterval - call delta.
    //If this works better - that should replace the lastupdated usage 
    //When the call is ended we set it to negative value, to mark it should not be used.
    private long iterationsCounter = 1;
    /**
     * Only used during the tests, just an aggregate of iterationsCounter
     */
    private long totalNumberOfIterations = 0;

    /**
     * These values are taken from the CDREvent
     **/
    private long reportedBillableSeconds; //The billable seconds as reported from the CDREvent

    //Make this configurable 
    private final static int FIRST_INTERVAL_MULTIPLY = 2;
    private BigDecimal estimatedCallPricePerSecond; // estimated call price per second as reported in the BRIDGE event, used for sending callbacks.
    private BigDecimal totalEstimatedPriceImpact; // total estimated price impact for 'CONSUME' and 'REFUND' vQuota commands.
    private boolean isVpricingEnabled;

    public BillingInfo(final BigDecimal pricePerMinute,
                       final BigDecimal forcedPricePerMinute,
                       final BigDecimal costPerMinute,
                       long firstChargedSeconds,
                       long quotaUpdatesInterval,
                       Status initialChargingStatus,
                       boolean chargeable,
                       boolean isVpricingEnabled) {

        this.pricePerMinute = pricePerMinute;
        this.forcedPricePerMinute = forcedPricePerMinute;
        this.costPerMinute = costPerMinute;
        this.firstChargedSeconds = firstChargedSeconds;
        this.quotaUpdatesInterval = quotaUpdatesInterval;

        this.status = analyzeInitialStatus(initialChargingStatus, pricePerMinute, forcedPricePerMinute, chargeable, isVpricingEnabled);

        this.secondsChargedSoFar = 0;

        BigDecimal priceToUsePerMinute = Objects.isNull(forcedPricePerMinute) ? pricePerMinute : forcedPricePerMinute;
        this.actualPricePerMinute = priceToUsePerMinute;

        this.pricePerSecond = convertToAmountPerSecond(pricePerMinute);
        this.forcedPricePerSecond = convertToAmountPerSecond(forcedPricePerMinute);
        BigDecimal priceToUsePerSecond = convertToAmountPerSecond(priceToUsePerMinute);
        this.actualPricePerSecond = priceToUsePerSecond;

        this.costPerSecond = convertToAmountPerSecond(costPerMinute);

        this.amountToChargePerInterval = calculateAmountPerPeriod(priceToUsePerSecond, quotaUpdatesInterval);
        this.estimatedCallPricePerSecond = BigDecimal.ZERO;
        this.totalEstimatedPriceImpact = BigDecimal.ZERO;
        this.isVpricingEnabled = isVpricingEnabled;
    }


    public QuotaUpdateDetails startCharging(String sessionId, String connectionId, long reportedStartTime) {
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();

        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} Start charging BillingInfo with status {} hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            // If the call has started already, in error, or in the process of ending, this is unexpected situation - do not start charging again
            // If the call price is zero (NO_CHARGES) - that is fine - no need for charges
            if (!Status.NOT_STARTED.equals(this.status)) {
                if (Status.NO_CHARGES.equals(this.status))
                    handleCallStartedOnNoCharges();
                else
                    Log.error("{} {} An attempt to start charging call with unexpected status {} hashCode {}",
                            sessionId, connectionId, this.status.name(), this.hashCode());
                return result;
            }

            this.secondsChargedSoFar = FIRST_INTERVAL_MULTIPLY * this.firstChargedSeconds;

            this.lastUpdated = reportedStartTime;
            this.callStartTime = reportedStartTime;
            this.iterationsCounter = 0;

            BigDecimal firstIntervalCharge = this.amountToChargePerInterval.multiply(BigDecimal.valueOf(FIRST_INTERVAL_MULTIPLY))
                    .setScale(8, RoundingMode.HALF_UP);

            result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, firstIntervalCharge, this.secondsChargedSoFar);

            if (Log.isDebugEnabled())
                Log.debug("{} {} startCharging secondsChargedSoFar={} lastUpdated={} callStartTime={} iterationsCounter={} result={} hashcode={}",
                        sessionId, connectionId, secondsChargedSoFar, lastUpdated, callStartTime, iterationsCounter, result, this.hashCode());

            //The call charges are on NOT_STARTED status - lets start it!
            this.status = Status.STARTED;
        }

        return result;
    }


    public QuotaUpdateDetails stopCharging(String sessionId, String connectionId, CdrEvent event, CdrEventUserData eventUserData) {

        QuotaUpdateDetails result = null;

        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} stopCharging on CDR event. status={} hashcode={}",
                        sessionId, connectionId, this.status, this.hashCode());


            //Set the stopped charging details:
            long currentMillis = System.currentTimeMillis();
            updateEndOfCallDetails(eventUserData);


            //The old impl used to calculate the call length by (endTime - startTime) - then convert it to seconds and round it always up.
            //For example, if endTime - startTime = 7100 milliseconds - the call length was calculated to be 8 seconds. That usually
            //matched the call length as reported in the CDREvent.
            //Problem: It is not always matching with Asterisk 16 CDREvent. (this is future concern)


            //At this point the expected status value is one of: 
            //ENDED - the call has ended, and quota was updated during the call
            //NO_CHARGES - the call has ended, but price is zero, and no reconciliation is needed
            //ERROR - the call flow encountered an ERROR - verify if the call was charged from the CDR event.
            //NOT_STARTED - the call never started - double check with the CDR data, but no reconciliation is needed
            //OUT_OF_FUNDS - the call was stopped in the middle, or not started by the BridgeEvent - verify if the call was charged from the CDR event.

            switch (this.status) {
                case NO_CHARGES:
                    result = handleNoChargesCall(sessionId, connectionId, event, currentMillis);
                    break;
                case ENDED:
                    result = handleEndedCall(sessionId, connectionId, event, currentMillis);
                    break;
                case ERROR:
                    result = handleErrorCall(sessionId, connectionId, event, currentMillis);
                    break;
                case NOT_STARTED:
                    result = handleNotStartedCall(sessionId, connectionId, event, currentMillis);
                    break;
                case OUT_OF_FUNDS_DURING_CALL:
                    result = handleOutOfFundsDuringCall(sessionId, connectionId, event, currentMillis);
                    break;
                case OUT_OF_FUNDS_DURING_START:
                    result = handleOutOfFundsDuringStart(sessionId, connectionId, event, currentMillis);
                    break;
                default:
                    Log.error("{} {} Attempt to handle stopCharging for a call with status {}. hashCode={}",
                            sessionId, connectionId, this.status, this.hashCode());
                    result = QuotaUpdateDetails.skipQuotaUpdate();
            }

            if (Log.isDebugEnabled())
                Log.debug("{} {} result of stopCharging on CDR event. status={} result={} BillingInfo={}. hashcode={}",
                        sessionId, connectionId, this.status, result, this.toString(), this.hashCode());
        }
        return result;
    }

    public QuotaUpdateDetails emergencyStopCharging(String sessionId, String connectionId) {
        QuotaUpdateDetails result = null;

        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} EMERGENCY stopCharging status={} hashcode={}",
                        sessionId, connectionId, this.status, this.hashCode());


            //Set the stopped charging details:
            long currentMillis = System.currentTimeMillis();
            //In case we do not know the start time, i.e. the call hasnt really started when the emergency stop was decided:
            //we set the call length to zero.
            //Call length is decided by the billalbe seconds.
            long emergencyBillableSeconds = 0;
            if (this.callStartTime > 0)
                emergencyBillableSeconds = currentMillis - this.callStartTime;

            updateEmergencyEndOfCallDetails(roundUpToSeconds(emergencyBillableSeconds));


            //At this point we are handling only calls with status EMERGENCY_ENDED: 
            if (Status.EMERGENCY_ENDED.equals(this.status))
                result = handleEmergencyEndedCall(sessionId, connectionId, currentMillis);
            else {
                Log.info("{} {} EMERGENCY stopCharging ignoring call with status {}. hashCode={}",
                        sessionId, connectionId, this.status, this.hashCode());
                result = QuotaUpdateDetails.skipQuotaUpdate();
            }

            if (Log.isDebugEnabled())
                Log.debug("{} {} result of EMERGENCY stopCharging . status={} result={} BillingInfo={}. hashcode={}",
                        sessionId, connectionId, this.status, result, this.toString(), this.hashCode());
        }
        return result;
    }


    private void updateEndOfCallDetails(CdrEventUserData eventUserData) {
        this.reportedBillableSeconds = eventUserData.getReportedBillableSeconds();
        this.callEndTime = this.callStartTime + TimeUnit.SECONDS.toMillis(this.reportedBillableSeconds);
    }



    private void updateEmergencyEndOfCallDetails(long emergencyBillableSeconds) {
        //This is important - we round up the millis to seconds, and then we need to add the rounded time to the start time
        //rather then using the millis of the emergency-end-call
        //If the call hasn't started yet, this.callStart time would be zero and the length of the call 
        //would be zero as well. 
        this.reportedBillableSeconds = emergencyBillableSeconds;
        this.callEndTime = this.callStartTime + TimeUnit.SECONDS.toMillis(this.reportedBillableSeconds);
    }

    private QuotaUpdateDetails handleNoChargesCall(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        return QuotaUpdateDetails.skipQuotaUpdate();
    }


    private QuotaUpdateDetails handleEndedCall(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        this.lastUpdated = currentMillis;
        this.iterationsCounter = -1;
        QuotaUpdateDetails result = getReconciliationResult(sessionId, connectionId);
        return result;
    }

    private QuotaUpdateDetails handleEmergencyEndedCall(String sessionId, String connectionId, long currentMillis) {
        this.lastUpdated = currentMillis;
        this.iterationsCounter = -1;

        Log.info("{} {} EMERGENCY Stop - about to reconsile the call using calculated time of {}", sessionId, connectionId, currentMillis);
        QuotaUpdateDetails result = getReconciliationResult(sessionId, connectionId);
        return result;
    }


    private QuotaUpdateDetails handleErrorCall(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        //The call is on ERROR status. That might be due to any error which had happened during the call life-cycle.
        //The CDR Event billableSeconds might be => 0
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();

        if (this.reportedBillableSeconds == 0)
            return result;

        Log.info("{} {} handling stop charging for a call in ERROR, with CDREvent: {}",
                sessionId, connectionId, event);

        result = getReconciliationResult(sessionId, connectionId);
        return result;
    }

    private QuotaUpdateDetails handleNotStartedCall(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        //As the call in NOT_STARTED status, it means the BridgeEvent never arrived, and the CDREvent is expected to include ZERO billableSeconds.
        //If there are billable seconds - there is a billing problem, we should log it.

        if (this.reportedBillableSeconds != 0) {
            Log.error("{} {} stopCharging handleNotStartedCall : the status is NOT_STARTED, but the CDREvent includes billable seconds!. "
                    + "CDREvent {}  BillingInfo {}", sessionId, connectionId, event, this.toString());
        }
        //This is to make sure we are not over charging. If we under-charging, the above log details will give the information
        //for the manual reconciliation. - This is theoretical error situation.
        this.reportedBillableSeconds = 0;
        return QuotaUpdateDetails.skipQuotaUpdate();
    }


    private QuotaUpdateDetails handleOutOfFundsDuringCall(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        this.lastUpdated = currentMillis;
        this.iterationsCounter = -1;

        if (Log.isDebugEnabled())
            Log.debug("{} {} handling stop charging for a call which run out of funds during the call, with CDREvent: {}",
                    sessionId, connectionId, event);

        QuotaUpdateDetails result = getOutOfFundsCallReconciliationResult(sessionId, connectionId);
        return result;
    }

    private QuotaUpdateDetails handleOutOfFundsDuringStart(String sessionId, String connectionId, CdrEvent event, long currentMillis) {
        this.lastUpdated = currentMillis;
        this.iterationsCounter = -1;

        if (Log.isDebugEnabled())
            Log.debug("{} {} handling stop charging for a call which run out of funds during BridgEvent, with CDREvent: {}",
                    sessionId, connectionId, event);
        // It the call was ended because of out-of-funds during the BridgeEvent - it means that the first consume request did not took place: 
        //   instead an exception was thrown and the requested amount to consume was NOT deducted. That means the refund is not required, 
        //   nor additional consume. The call is rejected
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();
        return result;
    }


    //When the call status is NO_CHARGES (i.e. the actual price is zero) we should only record the 
    //start time of the call 
    private void handleCallStartedOnNoCharges() {
        this.callStartTime = System.currentTimeMillis();
    }


    //This method calculate the last amount that needed to reconcile the quota updates.
    //  If it is negative - we need to refund the customer -  that the usual scenario
    //  If it is positive - we need to charge the customer - that can happen for long calls
    //                      when there is small delats of millisecs between the 6 seconds Quota updates
    //                      at some point they accumulate and become more than the extra 6 seconds we charged at
    //                      the beginning of the call. This would be enhanced when the system is loaded.
    private QuotaUpdateDetails getReconciliationResult(String sessionId, String connectionId) {
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();

        if (Log.isDebugEnabled())
            Log.debug("{} {} getReconciliationResult: secondsChargedSoFar={} reportedBillableSeconds={}",
                    sessionId, connectionId, this.secondsChargedSoFar, this.reportedBillableSeconds);

        if ((!this.isVpricingEnabled && BigDecimal.ZERO.compareTo(this.actualPricePerSecond) == 0) ||
                (this.isVpricingEnabled && Objects.nonNull(forcedPricePerMinute) && BigDecimal.ZERO.compareTo(this.forcedPricePerMinute) == 0)) {
            Log.info("{} {} getReconciliationResult: - call of zero price - skip reconsileation ", sessionId, connectionId);
            return result;
        }

        long overChargedSeconds = this.secondsChargedSoFar - this.reportedBillableSeconds;

        if (overChargedSeconds == 0) { //very rarely to happen because we start by charging double-the-interval.
            Log.info("{} {} getReconciliationResult: the final overChargedSeconds are ZERO "
                            + " BillingInfo: {} ",
                    sessionId, connectionId, this.toString());
            return result;
        } else if (overChargedSeconds < 0) { //under-charging - we need to consume again
            Log.info("{} {} getReconciliationResult: the final overChargedSeconds is negative - we need to consume again. "
                            + " BillingInfo: {} ",
                    sessionId, connectionId, this.toString());
            //The logic continues and we will attempt to consume the missing charges. In that case there wont be any refund.
        }

        //In case the call was overCharged: The overChargedAmount amount is positive: we need to refund.
        //In case the call was underCharged: The overChargedAmount amount is negative: we need to negate and indicate additional consume.
        //
        BigDecimal overChargedAmount = calculateAmountPerPeriod(this.actualPricePerSecond, overChargedSeconds);
        if(this.isVpricingEnabled) {
            if (overChargedSeconds > 0) {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.REFUND, overChargedAmount, overChargedSeconds);
            } else {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, overChargedAmount != null ? overChargedAmount.negate() : null, overChargedSeconds);
            }
        } else {
            if (overChargedAmount == null) {
                Log.warn("Overcharged amount is null. Treating it as zero for the sessionID: {}", sessionId);
                overChargedAmount = BigDecimal.ZERO;
            }

            if (overChargedAmount.compareTo(BigDecimal.ZERO) > 0) {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.REFUND, overChargedAmount, overChargedSeconds);
            } else {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, overChargedAmount.negate(), overChargedSeconds);
            }
        }
        return result;
    }

    private QuotaUpdateDetails getOutOfFundsCallReconciliationResult(String sessionId, String connectionId) {
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();

        // It the call was ended because of out-of-funds - it means that the last consume request did not took place: 
        //   instead an exception was thrown and the requested amount to consume was NOT deducted. That means the refund 
        //   should consider that the last consume didnt took place.
        // The this.secondsChargedSoFar is updated before we send the consume.
        long overChargedSeconds = this.secondsChargedSoFar - this.reportedBillableSeconds - this.quotaUpdatesInterval;

        if (overChargedSeconds == 0) { //cannot really happen because we start by charging double-the-interval.
            Log.error("{} {} stopCharging getOutOfFundsReconciliationResult: the final overChargedSeconds are ZERO - that is suspicious. "
                            + " BillingInfo: {} ",
                    sessionId, connectionId, this.toString());
            return result;
        } else if (overChargedSeconds < 0) { //under-charging - if it happen - there is a bug in the UpdateCharge
            Log.error("{} {} stopCharging getOutOfFundsReconciliationResult: the final overChargedSeconds is negative - that is suspicious. "
                            + " BillingInfo: {} ",
                    sessionId, connectionId, this.toString());
            //The logic continues and we will attempt to consume the missing charges. In that case there wont be any refund.
        }

        //In case the call was overCharged: (That the correct case, as we charge more on the beginning...
        //   The overChargedAmount amount is positive: we need to refund.
        //In case the call was underCharged:
        //   The overChargedAmount amount is negative (overChargedSeconds < 0): we need to negate and indicate additional consume.
        //   Such case is an indication for a problem in SIPApp, as Astersik is the source of truth whether the call
        //   had happened or not and how long it lasted. So if SIPApp do not charge enough during the call - we find it here.. 
        //   and reported above as ERROR in the log.
        //
        BigDecimal overChargedAmount = calculateAmountPerPeriod(this.actualPricePerSecond, overChargedSeconds);
        if(this.isVpricingEnabled) {
            if (overChargedSeconds > 0) {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.REFUND, overChargedAmount, overChargedSeconds);
            } else {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, overChargedAmount != null ? overChargedAmount.negate() : null, overChargedSeconds);
            }
        } else {
            if (overChargedAmount == null) {
                Log.warn("Overcharged amount is null. Treating it as zero for the sessionID: {}", sessionId);
                overChargedAmount = BigDecimal.ZERO;
            }

            if (overChargedAmount.compareTo(BigDecimal.ZERO) > 0) {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.REFUND, overChargedAmount, overChargedSeconds);
            } else {
                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, overChargedAmount.negate(), overChargedSeconds);
            }
        }
        return result;
    }


    //This method is gathering the data required to do the quota update if needed.
    //It will be needed every X seconds, yet it is called every second by the QuotaUpdateTask.
    public QuotaUpdateDetails delta(String sessionId, String connectionId) {
        QuotaUpdateDetails result = QuotaUpdateDetails.skipQuotaUpdate();

        synchronized (this) {

            if (Log.isDebugEnabled())
                Log.debug("{} {} Delta: Calculating the delta for BillingInfo with status {} hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            //Quota updates are required only for on-going calls with price > 0, 
            //The UpdateChargeTask is working every second, so might detect the BillingInfo before the call started.
            if (!isSuitableForQuotaUpdates(sessionId, connectionId)) {
                if (Log.isDebugEnabled())
                    Log.debug("{} {} ChargingContext status={} - ignoring quota update request.",
                            sessionId, connectionId, this.status);
                return result;
            }

            //The call has started already - verify if its the time to update the quota: every x configurable time (6 atm)
            long currentMillis = System.currentTimeMillis();
            long secondsElapsedSinceLastUpd = TimeUnit.MILLISECONDS.toSeconds(currentMillis - this.lastUpdated);

            boolean timeToCharge = secondsElapsedSinceLastUpd >= this.quotaUpdatesInterval;

            //Handle the delta updates via the counter and not by the time
            if (this.iterationsCounter < 0) {
                System.out.println("delta something is really wrong the iterations counter is negative, whil...." + this.toString());
                Log.error("{} {} delta something is really wrong the iterations counter is negative, while the call status is still an indication for an on-going call: "
                                + "status={} callStartTime={} secondsElapsedSinceLastUpd={} timeToCharge={} secondsChargedSoFar={} iterationsCounter={} hashcode={}",
                        sessionId, connectionId, this.status, this.callStartTime, secondsElapsedSinceLastUpd, timeToCharge,
                        this.secondsChargedSoFar, this.iterationsCounter, this.hashCode());
                return result;
            }
            this.iterationsCounter++;
            this.totalNumberOfIterations++;
            boolean timeToChargeByCounter = false;
            if (iterationsCounter >= this.quotaUpdatesInterval) {
                timeToChargeByCounter = true;
                iterationsCounter = 0;
            }

            if (Log.isDebugEnabled())
                Log.debug("{} {} delta after calculations before updating values: status={} callStartTime={} secondsElapsedSinceLastUpd={} timeToCharge={}"
                                + " secondsChargedSoFar={} iterationsCounter={} timeToChargeByCounter={} hashcode={}",
                        sessionId, connectionId, this.status, this.callStartTime, secondsElapsedSinceLastUpd, timeToCharge, this.secondsChargedSoFar,
                        this.iterationsCounter, timeToChargeByCounter, this.hashCode());

            if (timeToChargeByCounter) {

                this.secondsChargedSoFar += this.quotaUpdatesInterval;
                this.lastUpdated = currentMillis;

                if (Log.isDebugEnabled())
                    Log.debug("{} {} delta - this is time to charge (by counter) - after some calculations: "
                                    + "status={} secondsElapsedSinceLastUpd={} timeToCharge={} "
                                    + "secondsChargedSoFar={} lastUpdated={} hashcode={}",
                            sessionId, connectionId,
                            this.status, secondsElapsedSinceLastUpd, timeToCharge,
                            this.secondsChargedSoFar, this.lastUpdated, this.hashCode());

                result = new QuotaUpdateDetails(QuotaUpdateDetails.Operation.CONSUME, this.amountToChargePerInterval, quotaUpdatesInterval);

            } else {
                if (Log.isDebugEnabled())
                    Log.debug("{} {} delta - this is not the time to charge hashcode={}", sessionId, connectionId, this.hashCode());
            }

            if (Log.isDebugEnabled())
                Log.debug("Delta: result={} hashCode={}", result, this.hashCode());
        }

        return result;
    }


    public boolean isSuitableForQuotaUpdates(String sessionId, String connectionId) {
        synchronized (this) {
            boolean isSuitableForQuotaUpdates = false;
            if (Status.STARTED.equals(this.status))
                isSuitableForQuotaUpdates = true;

            if (Log.isDebugEnabled())
                Log.debug("{} {} status={} isSuitableForQuotaUpdates={} hashcode={}", sessionId, connectionId,
                        this.status.name(), isSuitableForQuotaUpdates, this.hashCode());

            return isSuitableForQuotaUpdates;
        }
    }


    public BigDecimal getPricePerMinute() {
        synchronized (this) {
            return this.pricePerMinute;
        }
    }

    public BigDecimal getPricePerSecond() {
        return this.pricePerSecond;
    }

    public BigDecimal getForcedPricePerMinute() {
        synchronized (this) {
            return this.forcedPricePerMinute;
        }
    }

    public BigDecimal getActualPricePerMinute() {
        synchronized (this) {
            return this.actualPricePerMinute;
        }
    }

    public long getCallDurationInSeconds() {
        synchronized (this) {
            return this.reportedBillableSeconds;
        }
    }

    public long getCallDurationInMillis() {
        synchronized (this) {
            return TimeUnit.SECONDS.toMillis(this.reportedBillableSeconds);
        }
    }

    public BigDecimal getTotalCallPrice(String sessionId, String connectionId) {
        synchronized (this) {
            BigDecimal totalCallPrice = calculateAmountPerPeriod(this.actualPricePerSecond, this.reportedBillableSeconds);
            if (Log.isDebugEnabled()) {
                Log.debug("{} {} actual price per second: {}, billable seconds: {} total price: {}",
                        sessionId, connectionId, this.actualPricePerSecond.toPlainString(), this.reportedBillableSeconds,
                        totalCallPrice.toPlainString());
            }
            return totalCallPrice;
        }
    }

    public BigDecimal getCostPerMinute() {
        synchronized (this) {
            return this.costPerMinute;
        }
    }

    public BigDecimal getTotalCallCost(String sessionId, String connectionId) {
        synchronized (this) {
            BigDecimal totalCallCost = calculateAmountPerPeriod(this.costPerSecond, this.reportedBillableSeconds);
            if (Log.isDebugEnabled()) {
                Log.debug("{} {} actual cost per second: {}, billable seconds: {} total cost: {}",
                        sessionId, connectionId, this.costPerSecond.toPlainString(), this.reportedBillableSeconds,
                        totalCallCost.toPlainString());
            }
            return totalCallCost;
        }
    }


    public void setCostPerMinute(BigDecimal costPerMinute) {
        this.costPerMinute = costPerMinute;
    }

    public long getFirstChargedSeconds() {
        return this.firstChargedSeconds;
    }

    public long getQuotaUpdatesInterval() {
        return this.quotaUpdatesInterval;
    }

    public void setSecondsChargedSoFar(long secondsChargedSoFar) {
        this.secondsChargedSoFar = secondsChargedSoFar;
    }

    public void setStatus(Status status) {
        this.status = status;
    }



    public long getCallStartTime() {
        synchronized (this) {
            return this.callStartTime;
        }
    }

    public long getCallEndTime() {
        synchronized (this) {
            return this.callEndTime;
        }
    }

    public Status getStatus() {
        synchronized (this) {
            return this.status;
        }
    }

    public boolean isOnEmergencyStop() {
        synchronized (this) {
            return Status.EMERGENCY_ENDED.equals(this.status);
        }
    }


    private BigDecimal convertToAmountPerSecond(BigDecimal amountPerMinute) {
        if (Objects.isNull(amountPerMinute))
            return null;

        return amountPerMinute.divide(BigDecimal.valueOf(60), 10, RoundingMode.HALF_UP);
    }

    //This is utility method for local and public usage.
    //Make sure NOT to include in it any usages of state-full data as it is not locked.
    public static BigDecimal calculateAmountPerPeriod(BigDecimal amountPerSecond, long periodSeconds) {
        if (Objects.isNull(amountPerSecond))
            return null;

        return amountPerSecond.multiply(BigDecimal.valueOf(periodSeconds)).setScale(8, RoundingMode.HALF_UP);
    }

    public static long roundUpToSeconds(long millis) {
        if (millis == 0)
            return 0;

        long remainderMillis = millis % 1000;
        long trunckatedSeconds = TimeUnit.MILLISECONDS.toSeconds(millis);
        long roundUpSeconds = trunckatedSeconds += (remainderMillis > 0 ? 1 : 0);
        return roundUpSeconds;
    }

    public void setErrorStatus(String sessionId, String connectionId) {
        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} setErrorStatus in BillingInfo from {} to ERROR. hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            this.status = Status.ERROR;
        }
    }


    public void setOutOfFundsStatusDuringCall(String sessionId, String connectionId) {
        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} setOutOfFundsStatusDuringCall in BillingInfo from {} to OUT_OF_FUNDS_DURING_CALL. hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            this.status = Status.OUT_OF_FUNDS_DURING_CALL;
        }
    }

    public void setOutOfFundsStatusDuringStart(String sessionId, String connectionId) {
        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} setOutOfFundsStatusDuringStart in BillingInfo from {} to OUT_OF_FUNDS_DURING_START. hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            this.status = Status.OUT_OF_FUNDS_DURING_START;
        }
    }


    public void setEndedStatus(String sessionId, String connectionId) {
        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} Attempt to setEndedStatus in BillingInfo from {} to ENDED. hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            if (Status.STARTED.equals(this.status))
                this.status = Status.ENDED;

            if (Log.isDebugEnabled())
                Log.debug("{} {} setEndedStatus final status set to {} . hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());
        }
    }

    public boolean shouldHandleEmergencyStopCharging(String sessionId, String connectionId) {
        synchronized (this) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} Attempt to setEmergencyEndedStatus in BillingInfo from {} to EMERGENCY_ENDED. hashCode {}",
                        sessionId, connectionId, this.status.name(), this.hashCode());

            if (!isEmergencyStopChargingRequired(this.status)) {
                Log.info("{} {} emergencyStopCharging is not needed for already {} call", sessionId, connectionId, this.status);
                return false;
            }

            this.status = Status.EMERGENCY_ENDED;

            if (Log.isDebugEnabled())
                Log.debug("{} {} setEmergencyEndedStatus final status set to {} . hashCode {}", sessionId,
                        connectionId, this.status.name(), this.hashCode());
            return true;
        }
    }


    public void updateCostPerMinute(BigDecimal updatedCostPerMinute, String sessionId, String connectionId) {
        synchronized (this) {

            if (Log.isDebugEnabled())
                Log.debug("{} {} about to updateCostPerMinute from {} to {} . hashCode {}",
                        sessionId, connectionId,
                        Objects.nonNull(this.costPerMinute) ? this.costPerMinute.toPlainString() : "null",
                        Objects.nonNull(updatedCostPerMinute) ? updatedCostPerMinute.toPlainString() : "null",
                        this.hashCode());

            if (Objects.isNull(updatedCostPerMinute)) {
                Log.warn("{} {} try to updateCostPerMinute from {} to null - update rejected . hashCode {}",
                        sessionId, connectionId,
                        Objects.nonNull(this.costPerMinute) ? this.costPerMinute.toPlainString() : "null",
                        this.hashCode());
                return;
            }

            this.costPerMinute = updatedCostPerMinute;
            this.costPerSecond = convertToAmountPerSecond(costPerMinute);
        }
    }


    private static Status analyzeInitialStatus(Status requestedStatus, BigDecimal configuredPricePerMinute, BigDecimal forcedPricePerMinute, boolean chargeable, boolean isVpricingEnabled) {
        //The initial status might be: 
        //NOT_STARTED : The default, the call has not started yet, and when it will start charges will take place
        //NO_CHARGES : The call has not started yet, but since the price is zero, there will be no charges
        //ERROR : Either specifically requested, or price and forcedPrice were not provided

        if (Status.ERROR.equals(requestedStatus))
            return requestedStatus;

        if (isVpricingEnabled) {
            if (!chargeable || (Objects.nonNull(forcedPricePerMinute) && BigDecimal.ZERO.compareTo(forcedPricePerMinute) == 0)) {
                return Status.NO_CHARGES;
            }
            return Status.NOT_STARTED;
        }

        if (Objects.isNull(configuredPricePerMinute) && Objects.isNull(forcedPricePerMinute))
            return Status.ERROR;

        BigDecimal priceToUse = Objects.isNull(forcedPricePerMinute) ? configuredPricePerMinute : forcedPricePerMinute;
        if (BigDecimal.ZERO.compareTo(priceToUse) == 0)
            return Status.NO_CHARGES;
        else
            return Status.NOT_STARTED;
    }

    protected static boolean isEmergencyStopChargingRequired(Status currentStatus) {
        //EmergencyStopCharging should handle calls which are not started or on-going (chargeable or not) 
        //i.e ignore all those which are in the process of ending or already in error of any kind
        return (Status.NOT_STARTED.equals(currentStatus) ||
                Status.STARTED.equals(currentStatus) ||
                Status.NO_CHARGES.equals(currentStatus));
    }


    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("BillingInfo [pricePerMinute=");
        builder.append(Objects.nonNull(pricePerMinute) ? pricePerMinute.toPlainString() : "null");
        builder.append(", forcedPricePerMinute=");
        builder.append(Objects.nonNull(forcedPricePerMinute) ? forcedPricePerMinute.toPlainString() : "null");
        builder.append(", costPerMinute=");
        builder.append(Objects.nonNull(costPerMinute) ? costPerMinute.toPlainString() : "null");
        builder.append(", firstChargedSeconds=");
        builder.append(firstChargedSeconds);
        builder.append(", quotaUpdatesInterval=");
        builder.append(quotaUpdatesInterval);
        builder.append(", actualPricePerMinute=");
        builder.append(Objects.nonNull(actualPricePerMinute) ? actualPricePerMinute.toPlainString() : "null");
        builder.append(", pricePerSecond=");
        builder.append(Objects.nonNull(pricePerSecond) ? pricePerSecond.toPlainString() : "null");
        builder.append(", forcedPricePerSecond=");
        builder.append(Objects.nonNull(forcedPricePerSecond) ? forcedPricePerSecond.toPlainString() : "null");
        builder.append(", actualPricePerSecond=");
        builder.append(Objects.nonNull(actualPricePerSecond) ? actualPricePerSecond.toPlainString() : "null");
        builder.append(", amountToChargePerInterval=");
        builder.append(Objects.nonNull(amountToChargePerInterval) ? amountToChargePerInterval.toPlainString() : "null");
        builder.append(", estimatedCallPricePerSecond=");
        builder.append(Objects.nonNull(estimatedCallPricePerSecond) ? estimatedCallPricePerSecond.toPlainString() : "null");
        builder.append(", totalEstimatedPriceImpact=");
        builder.append(Objects.nonNull(totalEstimatedPriceImpact) ? totalEstimatedPriceImpact.toPlainString() : "null");
        builder.append(", status=");
        builder.append(status);
        builder.append(", startTime=");
        builder.append(callStartTime);
        builder.append(", endTime=");
        builder.append(callEndTime);
        builder.append(", lastUpdated=");
        builder.append(lastUpdated);
        builder.append(", secondsChargedSoFar=");
        builder.append(secondsChargedSoFar);
        builder.append(", iterationsCounter=");
        builder.append(iterationsCounter);
        builder.append(", totalNumberOfIterations=");
        builder.append(totalNumberOfIterations);
        builder.append("]");
        return builder.toString();
    }

    /***
     * ChargingContext status
     */

    public static enum Status implements Serializable {
        NOT_STARTED, //The BillingInfo is ready, the call hasn't started yet
        STARTED, //The call started - set by startCharging
        ENDED, //The call completed with no errors during the call. set by stopCharging
        EMERGENCY_ENDED, //The call was stopped due to disconnect from Asterisk. set by emergencyStopCharging
        ERROR, //There were errors while using the Quota Service
        OUT_OF_FUNDS_DURING_CALL,//The call should be stopped, or stopped already due to not enough money
        OUT_OF_FUNDS_DURING_START,//The call should be stopped, or stopped already due to not enough money
        NO_CHARGES; //The leg price is zero - no need to send quota updates 

        public static boolean isValidTransition(Status from, Status to) {
            if (from == STARTED && to == NOT_STARTED)
                return false;
            if ((from == ENDED || from == ERROR || from == OUT_OF_FUNDS_DURING_CALL || from == OUT_OF_FUNDS_DURING_START) && (to == STARTED || to == NOT_STARTED))
                return false;
            if ((from == NO_CHARGES) && (to == STARTED || to == NOT_STARTED || to == OUT_OF_FUNDS_DURING_CALL || to == OUT_OF_FUNDS_DURING_START))
                return false;
            if (from == EMERGENCY_ENDED)
                return false;
            return true;
        }
    }


    //This method is needed for testing.
    //As the BillingInfo is changing during the life cycle of the call, some junit tests are verifying
    //those changes, and so the test need to keep a snapshot copy of the data on a specific point in time.
    protected BillingInfo takeSnapshot() {
        synchronized (this) {
            return new BillingInfo(
                    this.pricePerMinute,
                    this.forcedPricePerMinute,
                    this.costPerMinute,
                    this.firstChargedSeconds,
                    this.quotaUpdatesInterval,
                    this.actualPricePerMinute,
                    this.pricePerSecond,
                    this.forcedPricePerSecond,
                    this.actualPricePerSecond,
                    this.costPerSecond,
                    this.amountToChargePerInterval,
                    this.estimatedCallPricePerSecond,
                    this.totalEstimatedPriceImpact,
                    this.status,
                    this.callStartTime,
                    this.callEndTime,
                    this.lastUpdated,
                    this.secondsChargedSoFar,
                    this.reportedBillableSeconds,
                    this.iterationsCounter,
                    this.totalNumberOfIterations);
        }

    }

    //This constructor is used only for the snapshot above for testing only
    private BillingInfo(BigDecimal pricePerMinute, BigDecimal forcedPricePerMinute, BigDecimal costPerMinute,
                        long firstChargedSeconds, long quotaUpdatesInterval, BigDecimal actualPricePerMinute,
                        BigDecimal pricePerSecond, BigDecimal forcedPricePerSecond, BigDecimal actualPricePerSecond,
                        BigDecimal costPerSecond, BigDecimal amountToChargePerInterval, BigDecimal estimatedCallPricePerSecond, BigDecimal totalEstimatedPriceImpact,
                        Status status, long callStartTime,
                        long callEndTime, long lastUpdated, long secondsChargedSoFar,
                        long reportedBillableSeconds,
                        long iterationsCounter,
                        long totalNumberOfIterations) {

        this.pricePerMinute = pricePerMinute;
        this.forcedPricePerMinute = forcedPricePerMinute;
        this.costPerMinute = costPerMinute;
        this.firstChargedSeconds = firstChargedSeconds;
        this.quotaUpdatesInterval = quotaUpdatesInterval;
        this.actualPricePerMinute = actualPricePerMinute;
        this.pricePerSecond = pricePerSecond;
        this.forcedPricePerSecond = forcedPricePerSecond;
        this.actualPricePerSecond = actualPricePerSecond;
        this.costPerSecond = costPerSecond;
        this.amountToChargePerInterval = amountToChargePerInterval;
        this.estimatedCallPricePerSecond = estimatedCallPricePerSecond;
        this.totalEstimatedPriceImpact = totalEstimatedPriceImpact;
        this.status = status;
        this.callStartTime = callStartTime;
        this.callEndTime = callEndTime;
        this.lastUpdated = lastUpdated;
        this.secondsChargedSoFar = secondsChargedSoFar;
        this.reportedBillableSeconds = reportedBillableSeconds;
        this.iterationsCounter = iterationsCounter;
        this.totalNumberOfIterations = totalNumberOfIterations;
    }

    //These methods are only for testing, if you are tempted to use it for any other reason - I will find you!!! 
    protected long getSecondsChargedSoFar() {
        return this.secondsChargedSoFar;
    }

    protected long getLastUpdated() {
        return this.lastUpdated;
    }

    protected long getIterationsCounter() {
        return this.iterationsCounter;
    }

    protected long getTotalNumberOfIterations(){
        return this.totalNumberOfIterations;
    }
    //I am serious about it..

    // voiceph-628: callback is sent before waiting for the final vQuota transaction to complete.
    public BigDecimal getEstimatedTotalCallPrice() {
        return calculateAmountPerPeriod(this.estimatedCallPricePerSecond, this.reportedBillableSeconds);
    }

    public BigDecimal getEstimatedCallPricePerMinute() {
        return calculateAmountPerPeriod(estimatedCallPricePerSecond, 60);
    }

    public void setEstimatedCallPricePerSecond(BigDecimal chargedPrice, Long duration, String sessionId) {
        if (duration == null || duration == 0) {
            this.estimatedCallPricePerSecond = BigDecimal.ZERO;
            Log.error("Invalid duration (null or zero) for sessionID: {}. Duration: {}", sessionId, duration);
            return;
        }

        this.estimatedCallPricePerSecond = chargedPrice.divide(BigDecimal.valueOf(duration), 10, RoundingMode.HALF_UP);
    }

    // this method is to handle clonedCtx in async-quota implementation in QuotaQueueTask
    public void setEstimatedCallPricePerSecond(BigDecimal price) {
        this.estimatedCallPricePerSecond = price;
    }

    public BigDecimal getEstimatedCallPricePerSecond() {
        return this.estimatedCallPricePerSecond;
    }

    // voiceph-628: CDR is sent after the final vQuota transaction is complete.
    public void updateTotalEstimatedPriceImpact(BigDecimal price) {
        synchronized (this) {
            this.totalEstimatedPriceImpact = price;
        }
    }

    public BigDecimal getTotalEstimatedPriceImpact() {
        synchronized (this) {
            return this.totalEstimatedPriceImpact;
        }
    }

    public BigDecimal getEffectivePricingRate(String sessionId) {
        if (this.reportedBillableSeconds == 0) {
            Log.warn("Reported billable seconds is zero for sessionID: {}. Returning effective pricing rate as 0.", sessionId);
            return BigDecimal.ZERO;
        }

        BigDecimal pricePerSecond = this.totalEstimatedPriceImpact.divide(BigDecimal.valueOf(this.reportedBillableSeconds), 10, RoundingMode.HALF_UP);
        return calculateAmountPerPeriod(pricePerSecond, 60); // effective rate is calculated as per minute
    }
}
