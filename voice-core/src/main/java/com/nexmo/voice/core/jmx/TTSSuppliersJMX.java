package com.nexmo.voice.core.jmx;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.gateway.*;
import com.nexmo.voice.core.Core;
import com.thepeachbeetle.common.xml.XmlUtil;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.Set;


/**
 * <AUTHOR>
 */
public class TTSSuppliersJMX implements TTSSuppliersJMXMBean {

    private static final Logger Log = Logger.getLogger(TTSSuppliersJMX.class.getName());


    @Override
    public String viewSuppliersMetadata() {
        Log.info("About to fetch TTS  suppliers metadata from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final Config config = Core.getInstance().getConfig();
        final GatewayInfoMatrixConfig gatewayConfig = config.getTTSGatewayInfoMatrixConfig();
        final GatewayInfoMatrixSource gatewaySource = config.getTTSGatewayInfoMatrixSource();

        sb.append("Source: ");
        sb.append(gatewaySource.getSource());
        sb.append("<br><br>");

        sb.append("Number of suppliers: ");
        sb.append(gatewayConfig.getGatewayCount());
        sb.append("<br><br>");

        return sb.toString();
    }

    @Override
    public String listSuppliers() {
        Log.info("About to fetch all TTS suppliers from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final Config config = Core.getInstance().getConfig();

        final Set<String> suppliers = config.getTTSGatewayInfoMatrixConfig().getGatewayNames();
        for (String key : suppliers) {
            sb.append(key);
            sb.append("<br><br>");
        }

        return sb.toString();
    }

    @Override
    public String viewSupplier(String supplier) {
        Log.info("About to fetch TTS supplier details from config for supplier " + supplier);

        if (supplier == null) {
            return "ERROR: No supplier name provided";
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("Supplier: \"");
        sb.append(supplier);
        sb.append("\" => ");

        final Config config = Core.getInstance().getConfig();
        final SupplierMappingConfig supplierConfig = config.getTTSGatewayInfoMatrixConfig().getGatewayInfo(supplier);

        if (supplierConfig == null) {
            sb.append("NOT FOUND");
        } else {
            sb.append("<br><br>\n<pre>\n");
            String xml = supplierConfig.toString();
            String escaped = XmlUtil.XMLClean(xml);
            sb.append(escaped);
            sb.append("</pre>\n");
        }

        return sb.toString();
    }

    @Override
    public String loadSuppliersListFromFile(String filename) {
        Log.info("About to load new TTS suppliers config from " + filename);

        if (filename == null) {
            return "ERROR: No filename provided.";
        }

        final File f = new File(filename);
        if (!f.exists()) {
            return "ERROR: File \"" + filename + "\" does not exist";
        }

        long timeCalled = System.currentTimeMillis();
        final StringBuilder sb = new StringBuilder();
        sb.append("Loading from file: \"");
        sb.append(filename);
        sb.append("\" => ");

        final GatewayInfoMatrixSource newSource = new GatewayInfoMatrixSource(filename);
        final TTSGatewayInfoMatrixConfig newConfig = GatewayInfoMatrixConfigUtils.loadTTSConfigInfoFromXMLFile(filename);
        if (newConfig != null) {
            sb.append("SUCCESS!<br><br>");

            final GatewayInfoMatrixConfig oldConfig = Core.getInstance().getConfig().getTTSGatewayInfoMatrixConfig();

            if (newConfig.hasGateways()) {
                // Compare old/new configs and report the difference
                final String diffs = GatewayInfoMatrixConfigUtils.compareConfigs(oldConfig, newConfig);
                sb.append(diffs);

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setTTSGatewayInfoMatrixConfig(newConfig, newSource);
            } else {
                sb.append("New config contains no suppliers: REJECTED!");
            }
        } else {
            sb.append("FAILED!");
        }
        long timeTaken = System.currentTimeMillis() - timeCalled;
        Log.info("TTSSuppliersJMX::loadSuppliersListFromFile call-total-time [ " + timeTaken + "]");
        return sb.toString();
    }

}
