package com.nexmo.voice.core.domains;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import java.util.Set;

public class DomainsServiceConfigLoader extends NestedXmlHandler {

    private String url;
    private int timeout;
    private boolean authEnabled;
    private String authPrincipalType;
    private String authPrincipalValue;
    private boolean authUseTimestamp;
    private String authPrivateKey;
    private Set<String> supportedDomains;
    private String secondaryUrl;

    private DomainsServiceConfig config;

    public DomainsServiceConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public DomainsServiceConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) { // <domains-service>
            this.url = xmlContent.getAttribute(DomainsServiceConfig.SERVICE_URL_ATTR, true);
            this.timeout = parseInt(xmlContent.getAttribute(DomainsServiceConfig.SERVICE_TIMEOUT_ATTR, false, DomainsServiceConfig.DEFAULT_TIMEOUT));
            this.authEnabled = parseBoolean(xmlContent.getAttribute(DomainsServiceConfig.AUTH_ENABLED_ATTR, false, Boolean.toString(DomainsServiceConfig.DEFAULT_NEXMO_AUTH_ENABLED)));
            this.authPrincipalType = xmlContent.getAttribute(DomainsServiceConfig.AUTH_PRINCIPAL_TYPE_ATTR, false, DomainsServiceConfig.DEFAULT_NEXMO_AUTH_PRINCIPAL_TYPE);
            this.authPrincipalValue = xmlContent.getAttribute(DomainsServiceConfig.AUTH_PRINCIPAL_VALUE_ATTR, false, DomainsServiceConfig.DEFAULT_NEXMO_AUTH_PRINCIPAL_VALUE);
            this.authUseTimestamp = parseBoolean(xmlContent.getAttribute(DomainsServiceConfig.AUTH_USE_TIMESTAMP_ATTR, false, Boolean.toString(DomainsServiceConfig.DEFAULT_NEXMO_AUTH_USE_TIMESTAMP)));
            this.authPrivateKey = xmlContent.getAttribute(DomainsServiceConfig.AUTH_PRIVATE_KEY_ATTR, false);
            this.supportedDomains = parseCommaSeparatedStringSet(xmlContent.getAttribute(DomainsServiceConfig.SUPPORTED_DOMAIN_SUFFIXES, false, DomainsServiceConfig.DEFAULT_SUPPORTED_DOMAIN_SUFFIXES));
            this.secondaryUrl = xmlContent.getAttribute(DomainsServiceConfig.SECONDARY_SERVICE_URL_ATTR, false);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new DomainsServiceConfig();
            config.setUrl(this.url);
            config.setTimeout(this.timeout);
            config.setAuthEnabled(this.authEnabled);
            config.setAuthPrincipalType(this.authPrincipalType);
            config.setAuthPrincipalValue(this.authPrincipalValue);
            config.setAuthUseTimestamp(this.authUseTimestamp);
            config.setAuthPrivateKey(this.authPrivateKey);
            config.setDomainSuffixes(this.supportedDomains);
            config.setSecondaryUrl(this.secondaryUrl);
            notifyComplete();
        }
    }
}
