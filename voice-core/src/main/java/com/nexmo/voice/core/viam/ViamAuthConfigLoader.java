package com.nexmo.voice.core.viam;

import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceConfig;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceException;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class ViamAuthConfigLoader extends NestedXmlHandler {

    private String primaryHydraServiceUrl;

    private String secondaryHydraServiceUrl;

    private int hydraServiceTimeout = 2000;

    private String clientId;

    private String clientSecret;

    private String primaryPortunusServiceUrl;

    private String secondaryPortunusServiceUrl;

    private int portunusServiceTimeout = 2000;


    private ViamAuthConfig config;

    public ViamAuthConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public ViamAuthConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.clientId = xmlContent.getAttribute(ViamAuthConfig.ATTR_CLIENT_ID, true);
            this.clientSecret = xmlContent.getAttribute(ViamAuthConfig.ATTR_CLIENT_SECRET, false);
            this.primaryHydraServiceUrl = xmlContent.getAttribute(ViamAuthConfig.ATTR_PRIMARY_HYDRA_URL, true);
            this.secondaryHydraServiceUrl = xmlContent.getAttribute(ViamAuthConfig.ATTR_SECONDARY_HYDRA_URL, false);
            this.hydraServiceTimeout = Integer.parseInt(xmlContent.getAttribute(ViamAuthConfig.ATTR_HYDRA_TIMEOUT, false, ViamAuthConfig.HYDRA_TIMEOUT_DEFAULT_VALUE));
            this.primaryPortunusServiceUrl = xmlContent.getAttribute(ViamAuthConfig.ATTR_PRIMARY_PORTUNUS_URL, true);
            this.secondaryPortunusServiceUrl = xmlContent.getAttribute(ViamAuthConfig.ATTR_SECONDARY_PORTUNUS_URL, false);
            this.portunusServiceTimeout = Integer.parseInt(xmlContent.getAttribute(ViamAuthConfig.ATTR_PORTUNUS_TIMEOUT, false, ViamAuthConfig.PORTUNUS_TIMEOUT_DEFAULT_VALUE));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            try {
                config = new ViamAuthConfig();
                config.setClientId(this.clientId);
                config.setClientSecret(this.clientSecret);
                config.setPrimaryHydraServiceUrl(this.primaryHydraServiceUrl);
                config.setHydraServiceTimeout(this.hydraServiceTimeout);
                if ((this.secondaryHydraServiceUrl != null) && !this.secondaryHydraServiceUrl.isEmpty()) {
                    config.setSecondaryHydraServiceUrl(this.secondaryHydraServiceUrl);
                }
                config.setPrimaryPortunusServiceUrl(this.primaryPortunusServiceUrl);
                config.setPortunusServiceTimeout(this.portunusServiceTimeout);
                if ((this.secondaryPortunusServiceUrl != null) && !this.secondaryPortunusServiceUrl.isEmpty()) {
                    config.setSecondaryPortunusServiceUrl(this.secondaryPortunusServiceUrl);
                }
            } catch(ViamAuthenticationException e) {
                throw new LoaderException(e.getMessage(), e);
            }
            notifyComplete();
        }
    }
}
