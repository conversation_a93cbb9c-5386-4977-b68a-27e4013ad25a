package com.nexmo.voice.core.cache;

import com.nexmo.voice.core.types.EffectivePrice;

import java.io.Serializable;

/**
 * This class is a container for various parameters received in the AGIRequest
 * and needed later in the process during the BridgeEvent and CDREvent processing.
 * <p>
 * It also include data which is fetched during the AGI request processing
 * and can be used later in the process avoiding duplicate calculations etc.
 *
 * <AUTHOR>
 */
public class AGIRequestContext implements Serializable {

    private boolean isRealPriceRequiredInCallback = false;
    private EffectivePrice forcedPrice; //Will be null if not provided
    private EffectivePrice outboundPrice; //No need to calculate this again for leg2

    public AGIRequestContext(boolean isRealPriceRequiredInCallback, EffectivePrice forcedPrice,
                             EffectivePrice outboundPrice) {

        this.isRealPriceRequiredInCallback = isRealPriceRequiredInCallback;
        this.forcedPrice = forcedPrice;
        this.outboundPrice = outboundPrice;
    }


    public boolean isRealPriceRequiredInCallback() {
        return isRealPriceRequiredInCallback;
    }

    public void setRealPriceRequiredInCallback(boolean isRealPriceRequiredInCallback) {
        this.isRealPriceRequiredInCallback = isRealPriceRequiredInCallback;
    }

    public EffectivePrice getForcedPrice() {
        return forcedPrice;
    }

    public void setForcedPrice(EffectivePrice forcedPrice) {
        this.forcedPrice = forcedPrice;
    }

    public EffectivePrice getOutboundPrice() {
        return outboundPrice;
    }

    public void setOutboundPrice(EffectivePrice outboundPrice) {
        this.outboundPrice = outboundPrice;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("AGIRequestContext: [isRealPriceRequiredInCallback=");
        builder.append(isRealPriceRequiredInCallback);
        builder.append(", forcedPrice=");
        builder.append(forcedPrice);
        builder.append(", outboundPrice=");
        builder.append(outboundPrice);
        builder.append("]");
        return builder.toString();
    }
}
