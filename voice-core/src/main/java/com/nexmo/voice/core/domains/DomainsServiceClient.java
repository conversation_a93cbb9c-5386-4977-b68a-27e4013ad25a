package com.nexmo.voice.core.domains;

import com.nexmo.voice.core.Core;
import com.thepeachbeetle.common.http.HttpRequester;
import io.prometheus.client.Histogram;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.EncodedKeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

public class DomainsServiceClient {

    public final static String HEADER_NEXMO_AUTHORIZATION = "Nexmo-Authorization";
    public final static String HEADER_X_NEXMO_TRACE_ID = "********Trace-Id";

    private static final Histogram DOMAINS_SERVICE_ROUTING_REQUESTS_LATENCY = Histogram.build()
            .buckets(DEFAULT_DURATION_BUCKETS)
            .name("sipapp_domainsservice_routing_requests_latency")
            .help("Http Requests Latency to Fetch Domains Service routing rules")
            .labelNames("status")
            .register();


    private DomainsServiceConfig config;

    private HttpClient client;

    private final static Logger Log = LogManager.getLogger(DomainsServiceClient.class);

    public DomainsServiceClient(DomainsServiceConfig config) {
        this.config = config;
        int timeout = Integer.parseInt(String.valueOf(config.getTimeout()));
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        HttpClientBuilder builder = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig);
        this.client = builder.build();

    }

    private String buildFullRoutingUrl(String baseUrl, String domain, String destination, boolean fromApplication) {
        // path is /routing/{domainName}/destination/{destinationNumber}
        StringBuilder sb = new StringBuilder(baseUrl);
        if (!baseUrl.endsWith("/"))
            sb.append("/");
        sb.append("routing/");
        sb.append(domain);
        sb.append("/destination/");
        sb.append(destination);
        // add parameter to tell domain service to return applicationId for application domains
        sb.append("?supportApp=true");
        if(fromApplication) {
            sb.append("&fromApp=true");
        }
        return sb.toString();
    }

    public DomainRoutingResponse getDomainRouting(String domain, String destination, String traceId) {
        return getDomainRouting(domain, destination, traceId, false);
    }
    
    public DomainRoutingResponse getDomainRouting(String domain, String destination, String traceId, boolean fromApplication) {
        String url = buildFullRoutingUrl(this.config.getUrl(), domain, destination, fromApplication);

        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_X_NEXMO_TRACE_ID, traceId);
        headers.put(HEADER_NEXMO_AUTHORIZATION, buildNexmoAuthorizationHeader());

        String response = null;

        try {
            response = doGet(url, headers);
        } catch(Exception e) {
            Log.warn("{} failed to get a valid response", traceId, e);
        }

        if((response == null) && (this.config.getSecondaryUrl() != null)) {
            Log.info("{} trying secondary URL", traceId);
            url = buildFullRoutingUrl(this.config.getSecondaryUrl(), domain, destination, fromApplication);

            // we should be OK to use the same headers; the timestamp shouldn't have expired
            // will need to change this if we have different security token for failover
            try {
                response = doGet(url, headers);
            } catch(Exception e) {
                Log.warn("{} failed to get a valid response", traceId, e);
            }
        }

        if(response == null) {
            // we didn't get a valid response
            return DomainRoutingResponse.unspecifiedFailure();
        }

        DomainRoutingResponse result = null;
        try {
            JSONObject json = new JSONObject(response);
            result = DomainRoutingResponse.fromJSON(json);
            result = DomainRoutingResponse.fromJSON(json, domain);
            Log.info("{} domain routing json:{}", traceId, result.toString());
        } catch (JSONException e) {
            Log.warn("{} Failed to parse json domain routing response", traceId, e);
        }
        return result;
    }

    protected String buildNexmoAuthorizationHeader() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.config.getAuthPrincipalType()).append("=").append(this.config.getAuthPrincipalValue());
        if(this.config.isAuthUseTimestamp()) {
            sb.append(", Timestamp=").append(System.currentTimeMillis()/1000);
        }
        String toSign = sb.toString();
        return toSign + ", Signature=" + calculateSignature(sb.toString());
    }

    private String calculateSignature(String toSign) {
        try {
            byte[] toSignBytes = toSign.getBytes(StandardCharsets.UTF_8);
            byte[] encoded = Base64.getDecoder().decode(this.config.getAuthPrivateKey());
            KeyFactory kf = KeyFactory.getInstance("EC");
            EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);

            PrivateKey privateKey = kf.generatePrivate(keySpec);

            Signature ecdsaSignature = Signature.getInstance("SHA256withECDSA");
            ecdsaSignature.initSign(privateKey);
            ecdsaSignature.update(toSignBytes);
            byte[] signed = ecdsaSignature.sign();
            return new String(Base64.getEncoder().encode(signed), StandardCharsets.UTF_8);
        } catch(Exception e) {
            Log.warn("failed to calculate signature for nexmo-authentication header: ", e);
            return null;
        }
    }


    private String doGet(String url, Map<String, String> headers) throws DomainsServiceException, HttpRequester.ConnectionRefusedException, HttpRequester.HttpException, HttpRequester.TimeoutException, HttpRequester.ConnectionTimeoutException {
        final long timer = System.nanoTime();
        HttpGet get = new HttpGet(url);
        int timeout = Integer.parseInt(String.valueOf(this.config.getTimeout()));
        RequestConfig requestConf = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        get.setConfig(requestConf);

        for (Map.Entry<String, String> header : headers.entrySet()) {
            get.addHeader(header.getKey(), header.getValue());
        }

        final HttpResponse resp;
        try {
            resp = this.client.execute(get);
            CompletableFuture.runAsync(() -> {
                Core.getInstance().updateDomainsServiceLatencyMetrics((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            });
        } catch (IOException e) {
            CompletableFuture.runAsync(() -> {
                Core.getInstance().incrementDomainsServiceErrorCounter();
            });
            String err = "Failed to execute the request";
            throw new DomainsServiceException(err, e);
        }

        int statusCode = resp.getStatusLine().getStatusCode();
        String responseBody = null;
        try {
            responseBody = EntityUtils.toString(resp.getEntity());
        } catch (Exception e) {
            // It is expected that not all bodies are readable
        }

        if (statusCode != 200) {
            CompletableFuture.runAsync(() -> {
                Core.getInstance().incrementDomainsServiceErrorCounter();
            });
            throw new DomainsServiceException("HTTP " + Integer.valueOf(statusCode) + ": " + responseBody);
        }

        CompletableFuture.runAsync(() -> {
            Core.getInstance().incrementDomainsServiceSuccessCounter();
        });
        // if successful return the response
        return responseBody;
    }

}
