package com.nexmo.voice.core.emergency;

import java.util.List;

public class EmergencyCallProperties {

    private String fromNumber;

    private String toEmergencyNumber;

    private String locale;

    private List<String> routeGatewayIds;

    public static final String EMERGENCY_CALLING_DEST_NETWORK_TYPE = "SPECIAL_SERVICE";

    public String getFromNumber() {
        return fromNumber;
    }

    public void setFromNumber(String fromNumber) {
        this.fromNumber = fromNumber;
    }

    public String getToEmergencyNumber() {
        return toEmergencyNumber;
    }

    public void setToEmergencyNumber(String toEmergencyNumber) {
        this.toEmergencyNumber = toEmergencyNumber;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public List<String> getRouteGatewayIds() {
        return routeGatewayIds;
    }

    public void setRouteGatewayIds(List<String> routeGatewayIds) {
        this.routeGatewayIds = routeGatewayIds;
    }

    public String getSourceCountryCode() {
        return this.locale;
    }

    public String getDestinationCountryCode() {
        return this.locale;
    }

    public static class Builder {
        private String fromNumber;
        private String toNumber;
        private String locale;
        private List<String> route;

        public Builder() {
        }

        public Builder withFromNumber(String fromNumber) {
            this.fromNumber = fromNumber;
            return this;
        }

        public Builder withToNumber(String toNumber) {
            this.toNumber = toNumber;
            return this;
        }

        public Builder withLocale(String locale) {
            this.locale = locale;
            return this;
        }

        public Builder withRoute(List<String> route) {
            this.route = route;
            return this;
        }

        public EmergencyCallProperties build() {
            EmergencyCallProperties p = new EmergencyCallProperties();
            p.setFromNumber(fromNumber);
            p.setToEmergencyNumber(toNumber);
            p.setLocale(locale);
            p.setRouteGatewayIds(route);
            return p;
        }
    }
}


