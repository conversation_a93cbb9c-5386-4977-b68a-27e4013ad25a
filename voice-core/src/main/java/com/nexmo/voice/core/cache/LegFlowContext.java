package com.nexmo.voice.core.cache;

import java.time.LocalDateTime;

import org.asteriskjava.manager.event.CdrEvent;

/**
 * This class hold the leg flow status and pending event should there is such.
 *
 * <AUTHOR>
 */
public class LegFlowContext {

    public enum LEG_STATUS {
        START_S, //Processing of BridgeEvent (start) has started
        START_E, //Processing of BridgeEvent (start) has ended
        STOP_P,  //CDREvent (stop) has arrived; waiting to be processed
        UNKNOWN  //Unknown status - really bad situation, not yet supported  
    }

    private LEG_STATUS legStatus;
    private CdrEvent cdrEvent;
    private LocalDateTime creationTime;

    public LegFlowContext(LEG_STATUS legStatus, CdrEvent cdrEvent) {
        this.legStatus = legStatus;
        this.cdrEvent = cdrEvent;
        this.creationTime = LocalDateTime.now();
    }

    public LEG_STATUS getLegStatus() {
        return legStatus;
    }

    public CdrEvent getCdrEvent() {
        return cdrEvent;
    }

    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    @Override
    public String toString() {
        return "LegFlowContext [legStatus=" + legStatus + ", cdrEvent=" + cdrEvent + ", creationTime=" + creationTime
                + "]";
    }

    public String toReportString() {
        return "legStatus=" + legStatus + ", creationTime=" + creationTime;
    }


}
 