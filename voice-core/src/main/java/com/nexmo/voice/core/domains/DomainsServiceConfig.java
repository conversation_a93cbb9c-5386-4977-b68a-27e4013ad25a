package com.nexmo.voice.core.domains;

/*
 *<domains-service url="http://domains-service.main0.api.rtc.dev.euw1.vonagenetworks.net/domainsapi" timeout="250"></domains-service>
 */

import com.google.common.base.Strings;
import org.jdom.Element;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

public class DomainsServiceConfig implements Serializable {

    public static final String ROOT_NODE = "domains-service";
    public static final String SERVICE_URL_ATTR = "url";
    public static final String SERVICE_TIMEOUT_ATTR = "timeout";
    public static final String AUTH_ENABLED_ATTR = "nexmo-auth-enabled";
    public static final String AUTH_PRINCIPAL_TYPE_ATTR = "nexmo-auth-principal-type";
    public static final String AUTH_PRINCIPAL_VALUE_ATTR = "nexmo-auth-principal-value";
    public static final String AUTH_USE_TIMESTAMP_ATTR = "nexmo-auth-use-timestamp";
    public static final String AUTH_PRIVATE_KEY_ATTR = "nexmo-auth-private-key";
    public static final String SUPPORTED_DOMAIN_SUFFIXES = "supported-domain-suffixes";
    public static final String SECONDARY_SERVICE_URL_ATTR = "secondary-url";

    public static final String DEFAULT_TIMEOUT = "1000";
    public final static boolean DEFAULT_NEXMO_AUTH_ENABLED = true;
    public final static String DEFAULT_NEXMO_AUTH_PRINCIPAL_TYPE = "System";
    public final static String DEFAULT_NEXMO_AUTH_PRINCIPAL_VALUE = "SIPApp";
    public final static boolean DEFAULT_NEXMO_AUTH_USE_TIMESTAMP = true;
    public final static String DEFAULT_SUPPORTED_DOMAIN_SUFFIXES = "sip.vonage.com";

    private String url;
    private int timeout;
    private boolean authEnabled;
    private String authPrincipalType;
    private String authPrincipalValue;
    private boolean authUseTimestamp;
    private String authPrivateKey;
    private Set<String> domainSuffixes;
    private String secondaryUrl;

    public DomainsServiceConfig() {
        this.domainSuffixes = new HashSet<>();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public boolean isAuthEnabled() {
        return authEnabled;
    }

    public void setAuthEnabled(boolean authEnabled) {
        this.authEnabled = authEnabled;
    }

    public String getAuthPrincipalType() {
        return authPrincipalType;
    }

    public void setAuthPrincipalType(String authPrincipalType) {
        this.authPrincipalType = authPrincipalType;
    }

    public String getAuthPrincipalValue() {
        return authPrincipalValue;
    }

    public void setAuthPrincipalValue(String authPrincipalValue) {
        this.authPrincipalValue = authPrincipalValue;
    }

    public boolean isAuthUseTimestamp() {
        return authUseTimestamp;
    }

    public void setAuthUseTimestamp(boolean authUseTimestamp) {
        this.authUseTimestamp = authUseTimestamp;
    }

    public String getAuthPrivateKey() {
        return authPrivateKey;
    }

    public void setAuthPrivateKey(String authPrivateKey) {
        this.authPrivateKey = authPrivateKey;
    }

    public Set<String> getDomainSuffixes() {
        return this.domainSuffixes;
    }

    public void setDomainSuffixes(Set<String> domainSuffixes) {
        if(domainSuffixes != null) {
            // protect against setting this to null
            this.domainSuffixes = domainSuffixes;
        }
    }

    public String getSecondaryUrl() {
        return secondaryUrl;
    }

    public void setSecondaryUrl(String secondaryUrl) {
        this.secondaryUrl = secondaryUrl;
    }

    public Element toXML() {
        Element domainsService = new Element(ROOT_NODE);
        domainsService.setAttribute(SERVICE_URL_ATTR, this.url);
        domainsService.setAttribute(SERVICE_TIMEOUT_ATTR, Integer.toString(this.timeout));
        domainsService.setAttribute(AUTH_ENABLED_ATTR, Boolean.toString(this.authEnabled));
        domainsService.setAttribute(AUTH_PRINCIPAL_TYPE_ATTR, this.authPrincipalType);
        domainsService.setAttribute(AUTH_PRINCIPAL_VALUE_ATTR, this.authPrincipalValue);
        String domainSuffexesStr = "";
        if(this.domainSuffixes != null) {
            domainSuffexesStr = String.join(",", this.domainSuffixes);
        }
        domainsService.setAttribute(SUPPORTED_DOMAIN_SUFFIXES, domainSuffexesStr);
        domainsService.setAttribute(SECONDARY_SERVICE_URL_ATTR, this.secondaryUrl);
        return domainsService;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DomainsService [");
        sb.append("url=").append(this.url);
        sb.append("; ").append("timeout=").append(this.timeout);
        sb.append("; ").append("authEnabled=").append(this.authEnabled);
        if(this.authEnabled) {
            sb.append("; ").append("authPrincipalType=").append(this.authPrincipalType);
            sb.append("; ").append("authPrincipalValue=").append(this.authPrincipalValue);
            sb.append("; ").append("authUseTimestamp=").append(this.authUseTimestamp);
            if(!Strings.isNullOrEmpty(this.authPrivateKey)) {
                sb.append("; ").append("authPrivateKey=********");
            } else {
                sb.append("; ").append("authPrivateKey=").append(this.authPrivateKey);
            }
        }
        sb.append("; ").append("domainSuffixes=").append(this.domainSuffixes);
        sb.append("; ").append("secondaryUrl=").append(this.secondaryUrl);
        sb.append("]");
        return sb.toString();

    }

    public String getDomainFromSipUri(String sipUri) {
        // a supported domain should be in the format:
        //   <domainName>.<domainSuffix>
        // without leading sip:/sips:, user part, tag parameters, or query parameters
        // if we need to support these in the future, might need a full SIP URI parser
        if((sipUri == null) || sipUri.isEmpty()) {
            // no value to test?
            return null;
        }
        if((getDomainSuffixes() == null) || (getDomainSuffixes().isEmpty())) {
            // no domain suffixes!
            return null;
        }
        String stringToTest = extractHostFromSipUri(sipUri);
        for(String domainSuffix : getDomainSuffixes()) {
            if(stringToTest.endsWith("."+domainSuffix)) {
                return stringToTest.split("\\.")[0];
            }
        }
        return null;
    }

    protected String extractHostFromSipUri(String sipUri) {
        //
        String schemeRemoved = sipUri;
        if(sipUri.startsWith("sip:")) {
            // remove sip scheme
            schemeRemoved = sipUri.substring(4);
        } else if (sipUri.startsWith("sips:")) {
            // remove sips scheme
            schemeRemoved = sipUri.substring(5);
        }
        // make sure we don't have a user part
        String userRemoved = schemeRemoved;
        String[] userHost = schemeRemoved.split("@");
        if(userHost.length == 2) {
            // we have split on an @, take the second part
            userRemoved = userHost[1];
        }
        // make sure we remove any headers
        String headersRemoved = userRemoved;
        String[] hostHeaders = userRemoved.split("\\?");
        if(hostHeaders.length == 2) {
            headersRemoved = hostHeaders[0];
        }
        // make sure we remove any parameters
        String parametersRemoved = headersRemoved;
        String[] hostParameters = headersRemoved.split(";");
        if(hostParameters.length >= 2) {
            parametersRemoved = hostParameters[0];
        }
        // finally any port
        String hostOnly = parametersRemoved;
        // the host part can be a hostname, IPv4 address or IPv6 reference
        // IPv6 reference is the IPv6 address enclosed in square brackets
        // but we're looking for a hostname so assume that it isn't an IPv4 address or IPv6 reference
        String[] hostPort = parametersRemoved.split(":");
        if(hostPort.length == 2) {
            hostOnly = hostPort[0];
        }
        return hostOnly;
    }

    public String getUserFromSipUri(String sipUri) {
        //
        String schemeRemoved = sipUri;
        if(sipUri.startsWith("sip:")) {
            // remove sip scheme
            schemeRemoved = sipUri.substring(4);
        } else if (sipUri.startsWith("sips:")) {
            // remove sips scheme
            schemeRemoved = sipUri.substring(5);
        }
        // check if we have a user part
        String user = null;
        String[] userHost = schemeRemoved.split("@");
        if(userHost.length == 2) {
            // we have split on an @, take the first part
            user = userHost[0];
        }
        return user;
    }

    public String domainFqdn(String domainName) {
        if((domainName == null) || (domainName.isEmpty())) {
            // return an empty string
            return "";
        }
        StringBuilder sb = new StringBuilder(domainName);
        String suffix = DEFAULT_SUPPORTED_DOMAIN_SUFFIXES;
        // We could do something like this where we pick a suffix out of our set but then would need to check that it
        // wasn't a geographic one. So let's just be lazy and then this can change if we needed to remove branding
        // or something like that.
        //        if((domainSuffixes == null) || (domainSuffixes.isEmpty())) {
        //            // maybe it should return null?
        //            return domainName;
        //        }
        //        // we will pick a suffix from the set of suffixes and combine it with the domain name
        //        String suffix = domainSuffixes.toArray(new String[0])[0];
        if(!suffix.startsWith(".")) {
            sb.append(".");
        }
        sb.append(suffix);
        return sb.toString();
    }

}
