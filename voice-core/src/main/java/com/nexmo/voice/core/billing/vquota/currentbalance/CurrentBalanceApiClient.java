package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;



public class CurrentBalanceApiClient {
    private final static Logger Log = LogManager.getLogger(CurrentBalanceApiClient.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final CurrentBalanceApiConfig config;
    private final String authValue;
    protected HttpClient client;
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAILURE = "failure";

    public CurrentBalanceApiClient(CurrentBalanceApiConfig config, String authValue) {
        this.config = config;
        this.authValue = authValue;
        int timeout = config.getTimeout();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        this.client = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).build();
    }

    public AccountBalance getCurrentBalance(String apiKey, BigDecimal minBalance, String sessionId) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException {
        String currentBalanceApiUrl = config.constructUri();
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("apiKey", apiKey);
        if(minBalance != null) {
            queryParams.put("minBalance", minBalance.toPlainString());
        }
        String queryString = buildQueryString(queryParams);
        String fullUrl = currentBalanceApiUrl + queryString;
        HttpGet request = new HttpGet(fullUrl);
        int timeout = config.getTimeout();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        request.setConfig(requestConfig);
        addAuthenticationHeaders(request);
        if (Log.isDebugEnabled())
            Log.debug("Querying account current balance through a request [ {} ] for api-key [{}], sessionId [{}]", fullUrl, apiKey, sessionId);

        return executeRequest(request, apiKey, sessionId);
    }

    private AccountBalance executeRequest(HttpGet request, String apiKey, String sessionId) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException {
        final HttpResponse resp;
        try {
            resp = this.client.execute(request);
        } catch (Exception e) {
            String err = "Failed to execute the request";
            throw new QuotaException(err, e);
        }

        int statusCode = resp.getStatusLine().getStatusCode();
        String responseBody = null;
        try {
            responseBody = EntityUtils.toString(resp.getEntity());
        } catch (Exception e) {
            // Not all bodies are expected to be readable
        }
        Log.info("Received current-balance API response with status code [{}] and body [{}] for api-key [{}], sessionId [{}]", statusCode, responseBody, apiKey, sessionId);
        return handleResponse(responseBody);
    }

    private AccountBalance handleResponse(String responseBody) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException {
        if (responseBody == null || responseBody.isEmpty()) {
            throw new QuotaException("Empty or null response body received");
        }

        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(responseBody);
            String status = jsonNode.get("status").asText();

            if (STATUS_SUCCESS.equals(status)) {
                return CurrentBalanceApiSuccessResponse.handleResponse(jsonNode);
            } else if (STATUS_FAILURE.equals(status)) {
                CurrentBalanceApiFailureResponse failureResponse = OBJECT_MAPPER.readValue(responseBody, CurrentBalanceApiFailureResponse.class);
                failureResponse.handleResponse();
                return null;
            } else {
                throw new QuotaException("Request for current-balance API failed: " + responseBody);
            }
        } catch (JsonProcessingException | NullPointerException e) {
            throw new QuotaException("Failed to parse current-balance API JSON response: " + responseBody);
        }
    }

    private String buildQueryString(Map<String, String> params) {
        return params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&", "?", ""));
    }

    private void addAuthenticationHeaders(HttpGet request) {
        String authHeader = "Basic " + this.authValue;
        request.setHeader("Authorization", authHeader);
    }
}
