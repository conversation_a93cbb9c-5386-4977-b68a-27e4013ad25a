package com.nexmo.voice.core.viam;

import org.jdom.Element;

import java.net.URI;
import java.net.URISyntaxException;

public class ViamAuthConfig {

    public static final String ROOT_NODE = "viam-authentication";
    public static final String ATTR_CLIENT_ID = "client-id";
    public static final String ATTR_CLIENT_SECRET = "client-secret";
    public static final String ATTR_PRIMARY_HYDRA_URL = "primary-hydra-url";
    public static final String ATTR_SECONDARY_HYDRA_URL = "secondary-hydra-url";
    public static final String ATTR_HYDRA_TIMEOUT = "hydra-timeout";
    public static final String ATTR_PRIMARY_PORTUNUS_URL = "primary-portunus-url";
    public static final String ATTR_SECONDARY_PORTUNUS_URL = "secondary-portunus-url";
    public static final String ATTR_PORTUNUS_TIMEOUT = "portunus-timeout";
    public static final String HYDRA_TIMEOUT_DEFAULT_VALUE = "2000";
    public static final String PORTUNUS_TIMEOUT_DEFAULT_VALUE = "2000";

    private URI primaryHydraServiceUrl;

    private URI secondaryHydraServiceUrl;

    private int hydraServiceTimeout = 2000;

    private String clientId;

    private String clientSecret;

    private URI primaryPortunusServiceUrl;

    private URI secondaryPortunusServiceUrl;

    private int portunusServiceTimeout = 2000;

    public ViamAuthConfig() {}

    public URI getPrimaryHydraServiceUrl() {
        return primaryHydraServiceUrl;
    }

    public void setPrimaryHydraServiceUrl(String hydraServiceUrl) throws ViamAuthenticationException {
        try {
            setPrimaryHydraServiceUrl(new URI(hydraServiceUrl));
        } catch (URISyntaxException e) {
            throw new ViamAuthenticationException("Could not parse hydra service URL: "+e.getMessage(), e);
        }
    }

    public void setPrimaryHydraServiceUrl(URI hydraServiceUrl) {
        this.primaryHydraServiceUrl = hydraServiceUrl;
    }

    public URI getSecondaryHydraServiceUrl() {
        return secondaryHydraServiceUrl;
    }

    public void setSecondaryHydraServiceUrl(String hydraServiceUrl) throws ViamAuthenticationException {
        try {
            setSecondaryHydraServiceUrl(new URI(hydraServiceUrl));
        } catch (URISyntaxException e) {
            throw new ViamAuthenticationException("Could not parse hydra service URL: "+e.getMessage(), e);
        }
    }

    public void setSecondaryHydraServiceUrl(URI hydraServiceUrl) {
        this.secondaryHydraServiceUrl = hydraServiceUrl;
    }

    public int getHydraServiceTimeout() {
        return hydraServiceTimeout;
    }

    public void setHydraServiceTimeout(int hydraServiceTimeout) {
        this.hydraServiceTimeout = hydraServiceTimeout;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public URI getPrimaryPortunusServiceUrl() {
        return primaryPortunusServiceUrl;
    }

    public void setPrimaryPortunusServiceUrl(String portunusServiceUrl) throws ViamAuthenticationException {
        try {
            setPrimaryPortunusServiceUrl(new URI(portunusServiceUrl));
        } catch (URISyntaxException e) {
            throw new ViamAuthenticationException("Could not parse portunus service URL: "+e.getMessage(), e);
        }
    }

    public void setPrimaryPortunusServiceUrl(URI portunusServiceUrl) {
        this.primaryPortunusServiceUrl = portunusServiceUrl;
    }

    public URI getSecondaryPortunusServiceUrl() {
        return secondaryPortunusServiceUrl;
    }

    public void setSecondaryPortunusServiceUrl(String portunusServiceUrl) throws ViamAuthenticationException {
        try {
            setSecondaryPortunusServiceUrl(new URI(portunusServiceUrl));
        } catch (URISyntaxException e) {
            throw new ViamAuthenticationException("Could not parse portunus service URL: "+e.getMessage(), e);
        }
    }

    public void setSecondaryPortunusServiceUrl(URI portunusServiceUrl) {
        this.secondaryPortunusServiceUrl = portunusServiceUrl;
    }

    public int getPortunusServiceTimeout() {
        return portunusServiceTimeout;
    }

    public void setPortunusServiceTimeout(int portunusServiceTimeout) {
        this.portunusServiceTimeout = portunusServiceTimeout;
    }

    @Override
    public String toString() {
        String maskedClientSecret = clientSecret != null ? "************" : null;
        return "ViamAuthConfig{" +
                "primaryHydraServiceUrl=" + primaryHydraServiceUrl +
                ", secondaryHydraServiceUrl=" + secondaryHydraServiceUrl +
                ", hydraServiceTimeout=" + hydraServiceTimeout +
                ", clientId='" + clientId + '\'' +
                ", clientSecret='" + maskedClientSecret + '\'' +
                ", primaryPortunusServiceUrl=" + primaryPortunusServiceUrl +
                ", secondaryPortunusServiceUrl=" + secondaryPortunusServiceUrl +
                ", portunusServiceTimeout=" + portunusServiceTimeout +
                '}';
    }

    public Element toXML() {
        Element config = new Element(ROOT_NODE);
        config.setAttribute(ATTR_CLIENT_ID, this.clientId);
        config.setAttribute(ATTR_CLIENT_SECRET, this.clientSecret);
        config.setAttribute(ATTR_PRIMARY_HYDRA_URL, String.valueOf(this.primaryHydraServiceUrl));
        config.setAttribute(ATTR_SECONDARY_HYDRA_URL, String.valueOf(this.secondaryHydraServiceUrl));
        config.setAttribute(ATTR_HYDRA_TIMEOUT, Integer.toString(this.hydraServiceTimeout));
        config.setAttribute(ATTR_PRIMARY_PORTUNUS_URL, String.valueOf(this.primaryPortunusServiceUrl));
        config.setAttribute(ATTR_SECONDARY_PORTUNUS_URL, String.valueOf(this.secondaryPortunusServiceUrl));
        config.setAttribute(ATTR_PORTUNUS_TIMEOUT, Integer.toString(this.portunusServiceTimeout));
        return config;
    }
}
