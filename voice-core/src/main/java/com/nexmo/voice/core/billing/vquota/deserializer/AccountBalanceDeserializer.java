package com.nexmo.voice.core.billing.vquota.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;

import java.io.IOException;
import java.math.BigDecimal;
import com.thepeachbeetle.common.iso.Currency;

public class AccountBalanceDeserializer extends JsonDeserializer<AccountBalance> {

    @Override
    public AccountBalance deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
            throws IOException {

        JsonNode node = jsonParser.getCodec().readTree(jsonParser);

        String accountId = node.has("accountId") ? node.get("accountId").asText() : null;
        BigDecimal balance = node.has("balance") && !node.get("balance").isNull() ? node.get("balance").decimalValue() : null;
        BigDecimal creditLimit = node.has("creditLimit") && !node.get("creditLimit").isNull() ? node.get("creditLimit").decimalValue() : null;
        Currency currency = node.has("currency") && !node.get("currency").isNull()
                ? Currency.get(node.get("currency").asText()) : null;

        boolean quotaEnabled = node.has("quotaEnabled") && node.get("quotaEnabled").asBoolean();
        BigDecimal requiredFreeBalance = node.has("requiredFreeBalance") && !node.get("requiredFreeBalance").isNull() ? node.get("requiredFreeBalance").decimalValue() : null;
        Boolean requiredFreeBalanceAvailable = node.has("requiredFreeBalanceAvailable") && !node.get("requiredFreeBalanceAvailable").isNull()
                ? node.get("requiredFreeBalanceAvailable").asBoolean() : null;

        return AccountBalance.builder()
                .withAccountId(accountId)
                .withBalance(balance)
                .withCreditLimit(creditLimit)
                .withCurrency(currency)
                .withQuotaEnabled(quotaEnabled)
                .withRequiredFreeBalance(requiredFreeBalance)
                .withRequiredFreeBalanceAvailable(requiredFreeBalanceAvailable)
                .build();
    }
}