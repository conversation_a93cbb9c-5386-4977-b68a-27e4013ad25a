package com.nexmo.voice.core.sip.event;

import java.util.concurrent.locks.Lock;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.DisconnectEvent;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.gateway.asterisk.AsteriskAMIProcessor;

//This class is used by the SIPApp
//The DisconnectEvent is sent from Asterisk when going down due to Asterisk explicitly shutdown gracefully.

//It is happening when we drain SIPApp as part of deployment or restart.
//On any other case we do not just restart Asterisk, we will always drain the SIPApp and then restart both
public class DisconnectEventHandler extends AsteriskVoiceEventHandler<DisconnectEvent> {

    private static final long serialVersionUID = -842365646911385865L;

    private final static Logger Log = LogManager.getLogger(DisconnectEventHandler.class);

    public DisconnectEventHandler() {
        super(DisconnectEvent.class);
    }

    @Override
    public void handle(DisconnectEvent event) throws VoiceEventHandlerException {
        Log.warn("Processing SIP Disconnect Event ['" + event + "'] hashCode=" + event.hashCode());

        Lock shutdownLock;
        try {
            shutdownLock = Core.getInstance().acquireShutdownLock();
        } catch (InterruptedException ex) {
            throw new VoiceEventHandlerException("FAILED TO ACQUIRE SHUTDOWN LOCK ::: SOMETHING IS WRONG!", ex);
        }

        try {
            if (Core.getInstance().isAsteriskShutdown()) {
                Log.info("SIP Disconnect Event corresponding to Shutdown or Restart detected");
                Core.getInstance().setAsteriskShutdown(false);
            } else {
                Log.error("Unexpected (non restart or shutdown) SIP disconnect from Asterisk's manager connection");
                AsteriskAMIProcessor.handleDirtyShutdown();
            }
        } catch (Exception e) {
            Log.warn("Excpetion during processing SIP Disconnect Event ['" + event + "'] hashCode=" + event.hashCode(), e);
        } finally {
            if (shutdownLock != null)
                shutdownLock.unlock();
        }
    }

}
