package com.nexmo.voice.core.sip.event;

import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import com.nexmo.voice.core.cache.*;
import com.nexmo.voice.core.logger.CallLoggerController;
import com.nexmo.voice.core.logger.SIPAppLogger;
import io.prometheus.client.Counter;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;

import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.BillingManager;
import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.nexmo.voice.core.callback.VoiceCallback;
import com.nexmo.voice.core.types.AsteriskVersion;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.SIPCode;
import com.nexmo.voice.core.types.VoiceDirection;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.common.callback.types.CallbackMethod;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Message;
import com.thepeachbeetle.messaging.hub.core.Product;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.exceptions.NoPriceFoundException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;

import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import io.prometheus.client.Gauge;

import static org.asteriskjava.live.HangupCause.AST_CAUSE_NO_USER_RESPONSE;

public final class CdrEventHandler extends AsteriskVoiceEventHandler<CdrEvent> {

    private static final Logger Log = LogManager.getLogger(CdrEventHandler.class);

    //DialStatus values
    public static final String DIAL_STATUS_ANSWER = "ANSWER";
    public static final String DIAL_STATUS_NO_ROUTE = "NO_ROUTE";
    public static final String DIAL_STATUS_NO_MONEY = "NO_MONEY";
    public static final String DIAL_STATUS_BAD_GATEWAY = "BAD_GATEWAY";
    public static final String DIAL_STATUS_DECLINED = "DECLINED";
    public static final String DIAL_STATUS_NOT_FOUND = "NOT_FOUND";
    public static final String DIAL_STATUS_CHANUNAVAIL = "CHANUNAVAIL";
    public static final String DIAL_STATUS_CONGESTION = "CONGESTION";
    public static final String DIAL_STATUS_NO_CAPACITY = "NO_CAPACITY";
    public static final String DIAL_STATUS_CANCEL = "CANCEL";
    public static final String DIAL_STATUS_BUSY = "BUSY";
    public static final String DIAL_STATUS_NO_ANSWER = "NO_ANSWER";


    //The SIPCodes which will allow sip destination failover to continue to the next available destination
    public static final HashSet<SIPCode> nonBlockingSipAttemptCodes =
            new HashSet<>(Arrays.asList(SIPCode.SERVER_INTERNAL_ERROR,
                                            SIPCode.NOT_IMPLEMENTED,
                                            SIPCode.BAD_GATEWAY,
                                            SIPCode.SERVICE_UNAVAILABLE,
                                            SIPCode.REQUEST_TIMEOUT));

    private static final Gauge CS_LATENCY = Gauge.build().name("rtc_dependency_cs_latency").labelNames("latency").help("Time in seconds to get response back from CS").register();

    private static final Counter EMERGENCY_CALLS_FAILED_OVER = Counter.build().name("rtc_emergency_call_failed_over").labelNames("locale","number").help("Emergency calls failed over by locale and number").register();
    private static final Counter EMERGENCY_CALLS_COMPLETED = Counter.build().name("rtc_emergency_call_completed").labelNames("locale","number").help("Emergency calls completed by locale and number").register();
    private static final Counter EMERGENCY_CALLS_UNANSWERED = Counter.build().name("rtc_emergency_call_unanswered").labelNames("locale","number").help("Emergency calls unanswered by locale and number").register();

    private static final Counter EMERGENCY_CALLS_FINAL_STATUS = Counter.build().name("rtc_emergency_call_final_status").labelNames("gateway","status").help("Emergency calls final status by gateway").register();


    public CdrEventHandler() {
        super(CdrEvent.class);
    }

    @Override
    public void handle(CdrEvent event) throws VoiceEventHandlerException {
        VoiceContext channelContext = null;

        Log.info("Processing SIP CDR Event " + event + " hashCode=" + event.hashCode());
        AsteriskVersion asteriskVersion = toAsteriskVersion(event.getUniqueId());

        //Get the channel unique Id - an exception is thrown in case of issues
        String channelUniqueId = parseChannelUniqueId(event);

        //Process custom fields - an exception is thrown in case of issues or invalid values
        CdrEventUserData eventUserData = CdrEventUserData.of(asteriskVersion, event, channelUniqueId);
        if(eventUserData.ignoreEvent()){
            if (Log.isDebugEnabled()) {
                Log.debug("CDR event is being ignored for event" + event + " hashCode=" + event.hashCode());
            }
            return;
        }

        //Get the voice context (this is the current leg (channel) context created during AGIServer request)
        channelContext = loadChannelVoiceContext(channelUniqueId, eventUserData, event);

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, event.getBillableSeconds(), channelContext);

        boolean isOnGoingGatewaysFailover = isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData);

        dumpEventEvaluatedValues(channelUniqueId, eventUserData, sipCode, channelContext, isOnGoingGatewaysFailover, event);

        if (channelContext.isEmergencyCall()) {
            // this is an emergency call
            final String currentGateway = eventUserData.getCurrentGateway();
            Log.info("{} emergency call gateway {} final status {} ({})", channelContext.getSessionId(), currentGateway, sipCode.getCode(), sipCode.getMessage());
            EMERGENCY_CALLS_FINAL_STATUS.labels(currentGateway, Integer.toString(sipCode.getCode())).inc();
        }

        // If this CDR event is an indication for a gateways failover attempt with potential for further attempts, 
        // we should just create the relevant rejected CDR 
        // If it is the final gateways failover attempt, or no further gateways failover attempts will take place, 
        //    we should handle it as STOP conversation
        if (isOnGoingGatewaysFailover) {
            handleOnGoingGwsFailoverCdrEvent(channelContext, event, eventUserData, channelUniqueId, sipCode, event.hashCode());
            return;
        }

        //
        //This is a proper end-of-call event, we should close this call.
        //

        //Verify we have a channel context
        if (channelContext == null) {
            handleChannelContextNotFound(event, eventUserData, channelUniqueId, sipCode, isOnGoingGatewaysFailover);
            return;
        }
        //Verify the call is not on emergency stop status
        if (channelContext.getBillingInfo().isOnEmergencyStop()) {
            Log.warn("{} {} call is on emergency stop process. Ignoring the CDR event. CDR will be created as part of the emergency stop.",
                    channelContext.getSessionId(), channelContext.getConnectionId());
            return;
        }

        String sessionId = channelContext.getSessionId();
        Collection<VoiceContext> contextsInSession = Core.getInstance().getVoiceContextCache().
                getInnerValues(sessionId);
        dumpSessionContext(sessionId, contextsInSession);

        //Chargeable call is a call which actually started.
        boolean isAnsweredCall = isAnsweredCall(sipCode, eventUserData, sessionId);

        if (isAnsweredCall) {
            //verify that the start event has arrived already, or wait for it before concluding the call.
            if (LegFlowContext.LEG_STATUS.STOP_P.equals(
                    Core.getInstance().getLegFlowCache().stopEventArrived(channelUniqueId, event).getLegStatus())) {

                //SIP-1170: An edge case:
                //The carrier sends BYE in the same millisec it sends the 200OK for the invite.
                //This causes a race condition in Astersik 1.8 and sometimes we do not get the BridgeEvent,
                //while the CDREvent has indication that the call took place.
                //It is reproducible only under manual stress test.
                //The solution is to not to wait for the BridgeEvent and close the call properly.
                // The CDREvent in such case will include the following indicators:
                // HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=;
                if (Objects.isNull(eventUserData.getChannel2Id())) {
                    Log.warn("CDR event for " + channelUniqueId +
                            " account " + event.getAccountCode() +
                            " has arrived as successful call, without BridgeEvent and without LEG2ID. SessionId " + sessionId +
                            " sipCode " + sipCode +
                            " dialStatus " + eventUserData.getDialStatus() +
                            " hangupCause " + eventUserData.getHangupCause() + " call will be considered as done");
                } else {
                    Log.info("CDR event for " + channelUniqueId +
                            " account " + event.getAccountCode() +
                            " is pending until start processing completion. sessionId " + sessionId +
                            " sipCode " + sipCode +
                            " dialStatus " + eventUserData.getDialStatus() +
                            " hangupCause " + eventUserData.getHangupCause() +
                            " leg2Id " + eventUserData.getChannel2Id());
                    return;
                }
            }

            if(channelContext.isEmergencyCall()) {
                EMERGENCY_CALLS_COMPLETED.labels(channelContext.getCountryCode(), channelContext.getTo()).inc();

                // outbound leg of emergency calls is only chargable if it is a failover from the primary route
                // NOTE: may need to change this behavior on a locale-by-locale basis
                String[] alternativeGateways = channelContext.getAlternativeGateways().toArray(new String[0]);
                Log.debug("{} isEmergencyCall=true, currentGateway={}, alternativeGateways={}", sessionId, eventUserData.getCurrentGateway(), alternativeGateways);
                if(alternativeGateways.length > 0 && !alternativeGateways[0].equals(eventUserData.getCurrentGateway())) {
                    for(VoiceContext contextInSession : contextsInSession) {
                        if(VoiceDirection.OUTBOUND.equals(contextInSession.getVoiceDirection())) {
                            contextInSession.setChargeable(true);
                            Log.debug("{} set chargable=true for OUTBOUND emergency call context {}", sessionId, contextInSession);
                        }
                    }
                }
            }
        } else if(channelContext.isEmergencyCall()) {
            EMERGENCY_CALLS_UNANSWERED.labels(channelContext.getCountryCode(), channelContext.getTo()).inc();
        }

        boolean quotaDownDuringCall = contextsInSession.stream().anyMatch(x -> SIPAppLogger.concatInternalFlags(x.getInternalFlags()).contains("06"));

        //We are ok to process the stop and conclude the call. Handle each channel of the call
        for (VoiceContext contextInSession : contextsInSession) {
            Log.info(" ==================  About to Conclude the call for VoiceContext of: " + sessionId +
                    " contextInSession ConnectionId=" + contextInSession.getConnectionId() +
                    " isAnsweredCall=" + isAnsweredCall );

            //Set cdrevent value to true, and in case Quota is down, set negative balance to true
            contextInSession.setCdrEventArrived(true);
            if (quotaDownDuringCall) {
                contextInSession.setQuotaDown(true);
                contextInSession.setNegativeBalance(true);
            }

            if(!asteriskVersion.equals(contextInSession.getAsteriskVersion())){
                Log.warn("{} CALL WAS STARTED WITH ASTERISK VERSION {} BUT ENDED WITH {}, PLEASE CHECK WHATS GOING ON !!", sessionId, contextInSession.getAsteriskVersion(), asteriskVersion);
            }

            BillingManager billingManager = Core.getInstance().getBillingManager();

            billingManager.stopCharging(contextInSession, event, eventUserData);
            if (Log.isDebugEnabled()) {
                Log.debug("After the stopCharging is called: CdrEventHandler.handle() contextInSession: {}", contextInSession);
            }
            handleChannelCallbacks(channelUniqueId, sipCode, contextInSession, isAnsweredCall, contextsInSession.size());

            if (!Core.getInstance().isAsyncQuotaFlag()) {
                verifyWhetherCostUpdateRequired(contextInSession, eventUserData); // for retry gws
                handleCallEndPerChannel(channelUniqueId,
                        event,
                        eventUserData,
                        sipCode,
                        contextInSession,
                        contextsInSession.size(),
                        isAnsweredCall);
            }

            Log.info(" ==================  Done Conclude the call for VoiceContext of: " + sessionId +
                    " contextInSession ConnectionId=" + contextInSession.getConnectionId() +
                    " isAnsweredCall=" + isAnsweredCall);
        }
        // consider purging ...
        handleEndOfSession(channelContext.getSessionId());

        Log.info("End of processing CDR Event SessionID " + channelContext.getSessionId() + " ChannelUniqueID " + channelUniqueId);
    }


    public void verifyWhetherCostUpdateRequired(VoiceContext contextInSession, CdrEventUserData eventUserData) {
        //Whether the call is chargeable or not, we want to make sure the cost value is correct for the used gateway. 
        //In case of using an alternative GW, we need to update the cost accordingly. 
        //The alternative GW feature might be used only in Nexmo OUTBOUND calls, and will be available only for the 
        //OUTBOUND channel of the call

        //The price is not changing, as it is part of the agreement with the customer.
        //The cost might be different per used gateway

        if (contextInSession.getVoiceDirection() == VoiceDirection.OUTBOUND
                && eventUserData.getCurrentGWAttempt() > 1
                && Objects.nonNull(eventUserData.getCurrentGateway())
                && !eventUserData.getCurrentGateway().equals(contextInSession.getCurrentGateway())) {
            Log.info("Updating gateway details. Real used GW: " + eventUserData.getCurrentGateway() +
                    " attempt number " + eventUserData.getCurrentGWAttempt() +
                    " original gateway " + contextInSession.getCurrentGateway() +
                    " SessionId " + contextInSession.getSessionId() +
                    " ConnectionId " + contextInSession.getConnectionId() +
                    " " + contextInSession);

            contextInSession.updateGatewayFields(
                    SipAppUtils.convertGatewaysListToSet(eventUserData.getGatewaysList()),
                    eventUserData.getCurrentGateway(),
                    eventUserData.getCurrentGWAttempt());
            BigDecimal cost = getCostFromMatrixOrDefault(contextInSession);

            //This is thread-safe as the updateCostPerMinute is sync
            contextInSession.getBillingInfo().updateCostPerMinute(cost, contextInSession.getSessionId(), contextInSession.getConnectionId());
        }
    }

    public void handleCallEndPerChannel(
            String channelUniqueId,
            CdrEvent event,
            CdrEventUserData eventUserData,
            SIPCode sipCode,
            VoiceContext contextInSession,
            int numberOfChannelsInSession,
            boolean isChargeableCall) {

        //SIP-409: if the call was stopped because the account was not found or banned - there wont be application context
        if (contextInSession.isAccountNotAvailableErrorContext()) {
            Log.info(" {} Handling end of failed call due to account not found or banned. error context {}. number of channels in session:{}",
                    contextInSession.getSessionId(), contextInSession.getDebugString(), numberOfChannelsInSession);

            // generating a rejected CDR 
            Log.info("Account not available: About to log REJECTED for " + contextInSession.getSessionId() +
                    " sipCode " + sipCode.name() +
                    " channelUniqueID " + channelUniqueId +
                    " eventUserData " + eventUserData);

            logCDR(contextInSession,
                    eventUserData,
                    sipCode,
                    null, //idSuffix,
                    channelUniqueId,
                    isChargeableCall,
                    event.hashCode());

            //After creating CDRs - the conversation has ended - clean the leg status cache entries
            Core.getInstance().getLegFlowCache().removeLegContext(contextInSession.getConnectionId());
            cleanUpCaches(contextInSession, event);
            return;
        }


        SIPAsteriskContext appContext = (SIPAsteriskContext) contextInSession.getApplicationContext();
        Log.info(" SIPAsteriskContext of ConnectionID " + contextInSession.getSessionId() +
                " : " + appContext.getDebugString() +
                " number of channels in session: " + numberOfChannelsInSession);

        //If the numberOfChannelsInSession == 1 :
        //
        // It means that there is only one channel in this session, that
        // implies a failed call - we need to create CDR. - that is the less frequent event of error.
        //
        //
        //If !auxiliary:
        //
        // If we are looking at the FIRST leg:
        //                     OUTBOUND => auxiliary=true - Do not create CDR 
        //                                                  (??!! - I suspect this never happens as we never start a call without first
        //                                                   getting a request from customer or NCCO and then it wont be the first leg to
        //                                                   be the outbound call)
        //                     INBOUND => auxiliary=false - Create CDR
        // 
        // If we are looking at the SECOND leg:
        //            the first leg was NOT routing to an application => auxiliary=false  - Create CDR                                    
        //            the first leg had routing to an application => auxiliary=true - Do not create CDR
        //
        if (Log.isDebugEnabled())
            Log.debug("{} {} handleCallEndPerChannel: isAuxiliary {} numberOfChannelsInSession {} ",
                    contextInSession.getSessionId(), contextInSession.getConnectionId(),
                    appContext.isAuxiliary(), numberOfChannelsInSession);


        if (!appContext.isAuxiliary() || numberOfChannelsInSession == 1) {
            if (Log.isDebugEnabled())
                Log.debug(" SIPAsteriskContext of ConnectionID " + contextInSession.getSessionId() + " : " + " single contextInSession and NOT AuxiliaryLeg ");

            String idSuffix = null;
            if (appContext.getRedirectionCallbackAddress() != null && appContext.getApplicationId() == null) {
                // We just want this behavior in INBOUND product ...
                if (contextInSession.getVoiceDirection() == VoiceDirection.OUTBOUND) {
                    idSuffix = "-2";
                } else if (contextInSession.getVoiceDirection() == VoiceDirection.INBOUND) {
                    idSuffix = "-1";
                }
            }
            // generating a CDR
            Log.info("About to log IN/OUT CDR or ATTEMPT or REJECT for " + contextInSession.getSessionId() +
                    " sipCode " + sipCode.name() +
                    " channelUniqueID " + channelUniqueId +
                    " eventUserData " + eventUserData);

            logCDR(contextInSession,
                    eventUserData,
                    sipCode,
                    idSuffix,
                    channelUniqueId,
                    isChargeableCall,
                    event.hashCode());

            //After the CDR creation - send the callback if required
            //handleChannelCallbacks(channelUniqueId, sipCode, contextInSession, isChargeableCall, numberOfChannelsInSession);

            //After creating CDRs - the conversation has ended - clean the leg status cache entries
            Core.getInstance().getLegFlowCache().removeLegContext(contextInSession.getConnectionId());
        } else {
            if (Log.isDebugEnabled())
                Log.debug(" SKIP CDR Creation of ConnectionID " + contextInSession.getSessionId());
        }

        // Remove any hangup and partial event caches for leg ID
        cleanUpCaches(contextInSession, event);
    }

    private void cleanUpCaches(VoiceContext contextInSession, CdrEvent event) {
        // Remove any hangup and partial event caches for leg ID
        if (Log.isDebugEnabled()) {
            Log.debug("Attempt to remove cache for: " + contextInSession.getConnectionId() + "; linkID: " + event.getUniqueId() + "; channel ID: " + event.getDestinationChannel());
        }

        Core.getInstance().getPartialCdrEventCache().deleteUniqueId(event.getUniqueId());
        Core.getInstance().getCdrUserFieldEventCache().deleteLinkId(event.getUniqueId());

        if (Log.isDebugEnabled()) {
            Log.debug("Removed hangup and partial CDR cache for: " + contextInSession.getConnectionId() + "; linkID: " + event.getUniqueId() + "; channel ID: " + event.getDestinationChannel());
        }
    }

    private void handleChannelCallbacks(String channelUniqueId, SIPCode sipCode, VoiceContext contextInSession, boolean isChargeableCall, int numberOfChannelsInSession) {

        if (contextInSession.isAccountNotAvailableErrorContext()) {
            Log.info("Don't handle callbacks as account not found");
            return;
        }

        SIPAsteriskContext appContext = (SIPAsteriskContext) contextInSession.getApplicationContext();
        Log.info(" SIPAsteriskContext of ConnectionID " + contextInSession.getSessionId() +
                " : " + appContext.getDebugString() +
                " number of channels in session: " + numberOfChannelsInSession);

        if (!appContext.isAuxiliary() || numberOfChannelsInSession == 1) {

            if (Log.isDebugEnabled())
                Log.debug("About to handle the callbacks for channelUniqueId: " + channelUniqueId +
                        " SessionID: " + contextInSession.getSessionId() +
                        " InternalCallbackUrl: " + contextInSession.getInternalCallbackUrl() +
                        " CallbackUrl: " + contextInSession.getCallbackUrl() +
                        " Direction: " + contextInSession.getVoiceDirection().name());

            // Send internal callback for the leg
            if (StringUtils.isNotBlank(contextInSession.getInternalCallbackUrl())) {

                if (Log.isDebugEnabled())
                    Log.debug("{} : About to handle the internal callback using url {} for leg {} isChargeableCall{} ", contextInSession.getSessionId(),
                            contextInSession.getConnectionId(), contextInSession.getInternalCallbackUrl(), isChargeableCall);

                sendCallback(contextInSession,
                        contextInSession.getInternalCallbackUrl(),
                        contextInSession.getInternalCallbackMethod(),
                        sipCode, isChargeableCall);
            }

            // Send customer callback for the leg - only for INBOUND or TTS
            if (StringUtils.isNotBlank(contextInSession.getCallbackUrl()) &&
                    (contextInSession.getVoiceDirection() == VoiceDirection.INBOUND || VoiceProduct.TTS.equals(contextInSession.getVoiceProduct()))) {

                if (Log.isDebugEnabled())
                    Log.debug("About to handle the leg callback using url: " + contextInSession.getCallbackUrl());
                sendCallback(contextInSession,
                        contextInSession.getCallbackUrl(),
                        contextInSession.getCallbackMethod(),
                        sipCode, isChargeableCall);
            }
        }
    }


    private void sendCallback(VoiceContext ctx, String callbackUrl, CallbackMethod callbackMethod, SIPCode sipCode, boolean isChargeableCall) {

        boolean isRealPriceRequiredInCallback = false;
        if (Objects.nonNull(ctx.getAGIRequestContext()))
            isRealPriceRequiredInCallback = ctx.getAGIRequestContext().isRealPriceRequiredInCallback();

        if (Log.isDebugEnabled())
            Log.debug("{} : sendCallback using URL {} sipCode {} isChargeableCall {} isCostRequiredInCallback {}. Context {} ",
                    ctx.getSessionId(), callbackUrl, sipCode, isChargeableCall, isRealPriceRequiredInCallback, ctx.getDebugString());

        if (Objects.isNull(Core.getInstance().getCallbackIssuer())) {
            Log.error("{} : sendCallback using URL {} sipCode {} isChargeableCall {} isCostRequiredInCallback {}. Context {} ",
                    ctx.getSessionId(), callbackUrl, sipCode, isChargeableCall, isRealPriceRequiredInCallback, ctx.getDebugString());
            return;
        }

        VoiceCallback callback = null;
        if (isChargeableCall)
            callback = new VoiceCallback(ctx, "ok", null, null, callbackUrl, callbackMethod);
        else {
            callback = new VoiceCallback(ctx, "failed", "" + sipCode.getCode(), null, callbackUrl, callbackMethod);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("sendCallBack: {}", callback.toString());
        }

        if (Objects.isNull(callback)) {
            Log.error("{} : Failed to create the callback task. Unable to send callback using URL {} sipCode {} isChargeableCall {} isCostRequiredInCallback {}. Context {} ",
                    ctx.getSessionId(), callbackUrl, sipCode, isChargeableCall, isRealPriceRequiredInCallback, ctx.getDebugString());
            return;
        }

        try {
            final long timer = System.nanoTime();
            Core.getInstance().getCallbackIssuer().issueCallback(callback);

            if (callbackUrl.contains("/v1/statusEvent")) {
                CS_LATENCY.labels("cs_latency").set((System.nanoTime() - timer) / NANOSECONDS_PER_SECOND);
            }
        } catch (Exception e) {
            if (callbackUrl.contains("/v1/statusEvent")) {
                Core.getInstance().incrementCSErrorCounter();
            }
            Log.error("Failed to submit the callback task for SessionID: " + ctx.getSessionId()
                    + " ConnectionID: " + ctx.getConnectionId()
                    + " callbackUrl: " + callbackUrl
                    + " method: " + callbackMethod
                    + " sipCode: " + sipCode
                    + " isChargeableCall: " + isChargeableCall
                    + " Exception: " + e);
        }
    }


    private static void logCDR(VoiceContext context,
                               CdrEventUserData eventUserData,
                               SIPCode sipCode,
                               String idSuffix,
                               String channelUniqueId,
                               boolean isChargeableCall,
                               int eventHashCode) {

        Log.debug("About to log CDR of channelUniqueId " + channelUniqueId +
                " isChargeableCall=" + isChargeableCall +
                " context: " + context +
                " for event hashcode: " + eventHashCode);

        String dialStatus = eventUserData.getDialStatus();
        String currentGW = eventUserData.getCurrentGateway();
        String channel2Id = eventUserData.getChannel2Id();

        if (isChargeableCall) {
            //Different logger for INBOUND / OUTBOUND
            if (context.getVoiceDirection() == VoiceDirection.OUTBOUND) {
                Log.debug("About to log OUTBOUND CDR of uniqueId " + channelUniqueId +
                        " VoiceDirection=OUTBOUND sessionId=" + context.getSessionId() +
                        " for event hashcode: " + eventHashCode);
                Core.getInstance().getOutboundCallLoggerController(context.getVoiceProduct()).logCall(context,
                                                                                        dialStatus != null ? dialStatus : "ANSWER",
                                                                                        sipCode,
                                                                                        channel2Id,
                                                                                        idSuffix,
                                                                                        Core.PHONE_NUMBER_TRUNCATION_LENGTH,
                                                                                        eventUserData);
            } else if (context.getVoiceDirection() == VoiceDirection.INBOUND) {
                Log.debug("About to log INBOUND CDR of uniqueId " + channelUniqueId +
                        " VoiceDirection=INBOUND sessionId=" + context.getSessionId() +
                        " for event hashcode: " + eventHashCode);
                Core.getInstance().getInboundCallLoggerController(context.getVoiceProduct()).logCall(context,
                                                                    dialStatus != null ? dialStatus : "ANSWER",
                                                                    sipCode,
                                                                    channel2Id,
                                                                    idSuffix,
                                                                    Core.PHONE_NUMBER_TRUNCATION_LENGTH,
                                                                    eventUserData);
            }
        } else if (isOnGoingSipDestinationFailoverCdrEvent(sipCode, eventUserData)) {
            Log.debug("About to log ATTEMPT CDR of uniqueId " + channelUniqueId +
                    " isChargeableCall=" + isChargeableCall +
                    " currentFallbackAttempt=" + eventUserData.getCurrentFallbackAttempt() +
                    " availableFallbackAlternatives=" + eventUserData.getAvailableFallbackAlternatives() +
                    " sessionId=" + context.getSessionId());
            Core.getInstance().getAttemptLoggerController(context.getVoiceProduct()).logAttempt(context,
                                                                                        dialStatus,
                                                                                        "" + sipCode.getCode(),
                                                                                        sipCode.getMessage(),
                                                                                        channel2Id,
                                                                                        idSuffix,
                                                                                        currentGW,
                                                                                        Core.PHONE_NUMBER_TRUNCATION_LENGTH,
                                                                                        eventUserData);
        } else {
            if ("ANSWER".equals(dialStatus) && eventUserData.getReportedBillableSeconds() > 0 &&
                    AST_CAUSE_NO_USER_RESPONSE.equals(eventUserData.getHangupCause())) {
                Log.debug("Redirected REJECT CDR to {} CDR for uniqueId {} isChargeableCall={} sessionId={} for event hashcode: {}",
                        context.getVoiceDirection(), channelUniqueId, isChargeableCall, context.getSessionId(), eventHashCode);
                final CallLoggerController callLoggerController;
                if (VoiceDirection.OUTBOUND.equals(context.getVoiceDirection())) {
                    callLoggerController = Core.getInstance().getOutboundCallLoggerController(context.getVoiceProduct());
                } else {
                    callLoggerController = Core.getInstance().getInboundCallLoggerController(context.getVoiceProduct());
                }
                callLoggerController.logCall(context, dialStatus, sipCode, channel2Id, idSuffix, Core.PHONE_NUMBER_TRUNCATION_LENGTH, eventUserData);
            } else {
                Log.debug("About to log REJECT CDR of uniqueId {} isChargeableCall={} sessionId={} for event hashcode: {}",
                        channelUniqueId, isChargeableCall, context.getSessionId(), eventHashCode);
                Core.getInstance().getRejectedLoggerController(context.getVoiceProduct()).logFailure(context,
                        dialStatus != null ? dialStatus : "null",
                        "" + sipCode.getCode(),
                        sipCode.getMessage(),
                        channel2Id,
                        idSuffix,
                        currentGW,
                        Core.PHONE_NUMBER_TRUNCATION_LENGTH,
                        eventUserData);
            }
        }

    }

    public static boolean isAnsweredCall(SIPCode sipCode, CdrEventUserData eventUserData, String sessionId) {
        //Call is chargeable when it ended in one of three scenarios:
        //If the call status is SIPCode.OK and the dialStatus indicate that a call took place.
        //If the call status is SIPCode.PAYMENT_REQUIRED  && HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED -
        //      i.e. the call was stopped in the middle because the customer run out of funds.
        //(SIP-1834)If the call status is SIPCode.BANNED_CALL_ENDED  && HangupCause.AST_CAUSE_WRONG_CALL_STATE
        //      i.e the account was banned during a call or failed to retrieve the account to check for ban
        //SIP-273: In case where session timers are used, RE-INVITE might be sent during the call. If the caller
        //         reject a RE_INVITE, the call is ending with an error code, but still chargeable, and an OUTBOUND cdr
        //         should be created. In such case, the CDREvent will include: HANGUPCAUSE=38;DIALSTATUS=ANSWER => SIPCode=430
        boolean isAnsweredCall =
                (SIPCode.OK.equals(sipCode) && DIAL_STATUS_ANSWER.equals(eventUserData.getDialStatus()))
                        ||
                        (SIPCode.PAYMENT_REQUIRED.equals(sipCode) &&
                                HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED.equals(eventUserData.getHangupCause()))
                        ||
                        (SIPCode.BANNED_CALL_ENDED.equals(sipCode) &&
                                HangupCause.AST_CAUSE_WRONG_CALL_STATE.equals(eventUserData.getHangupCause()))
                        ||
                        (SIPCode.FLOW_FAILED.equals(sipCode) &&
                                DIAL_STATUS_ANSWER.equals(eventUserData.getDialStatus()))
                        ||
                        (SIPCode.VERSION_NOT_SUPPORTED.equals(sipCode) &&
                                DIAL_STATUS_ANSWER.equals(eventUserData.getDialStatus()));                        

        if (!isAnsweredCall) {
            Log.info("Unanswered call: sessionId={} sipCode={} dialStatus={} hangupCause={}",
                    sessionId, sipCode.name(), eventUserData.getDialStatus(), eventUserData.getHangupCause().name());
        }

        if (Log.isDebugEnabled())
            Log.debug("sessionId={} sipCode={} dialStatus={} hangupCause={} isAnsweredCall={}",
                    sessionId, sipCode.name(), eventUserData.getDialStatus(), eventUserData.getHangupCause().name(), isAnsweredCall);

        return isAnsweredCall;
    }

    protected static boolean isOnGoingGwsFailoverCdrEvent(SIPCode sipCode, CdrEventUserData eventUserData) {
        //On going attempts cdr events are those which:

        // the call has not ended successfully on this attempt, and 
        // the call has not ended due to run-out-of-credit, and
        // the call has not answered, and (Added for the SIP-213)
        // the current attempt is not the last, and
        // the caller has not cancelled the call, and
        // the callee has not declined the call (Asterisk has 2 enums with the same value to describe this situation)

        if (SIPCode.OK.equals(sipCode) || SIPCode.PAYMENT_REQUIRED.equals(sipCode))
            return false; // => fail over is NOT going to happen, the call started properly

        //Find all the cases when fail over will not happen
        if (//not last attempt
                !eventUserData.isLastRetryGateway() &&

                        //the caller didn't cancel the call
                        !DIAL_STATUS_CANCEL.equals(eventUserData.getDialStatus()) &&

                        // the callee didn't decline
                        !HangupCause.AST_CAUSE_USER_BUSY.equals(eventUserData.getHangupCause()) &&
                        !HangupCause.AST_CAUSE_BUSY.equals(eventUserData.getHangupCause()) &&

                        //The call was answered and then rejected as part of re-invite
                        !DIAL_STATUS_ANSWER.equals(eventUserData.getDialStatus()) &&

                        //SIP-355: The callee wasn't busy
                        !(HangupCause.AST_CAUSE_CALL_REJECTED.equals(eventUserData.getHangupCause())
                                && DIAL_STATUS_BUSY.equals(eventUserData.getDialStatus())) &&

                        //SIP-355: HANGUPCAUSE=0;DIALSTATUS=NOANSWER implies no fail-over (Example 1 case)
                        !(HangupCause.AST_CAUSE_NOTDEFINED.equals(eventUserData.getHangupCause())
                                && DIAL_STATUS_NO_ANSWER.equals(eventUserData.getDialStatus())) &&

                        //SIP-456: SIPApp - do not fail-over in case of 480
                        !((HangupCause.AST_CAUSE_NOANSWER.equals(eventUserData.getHangupCause()) ||
                                HangupCause.AST_CAUSE_NO_ANSWER.equals(eventUserData.getHangupCause()))
                                && DIAL_STATUS_CONGESTION.equals(eventUserData.getDialStatus())) &&

                        //SIP-2839: SIPApp - 480 with DIALSTATUS=BUSY in Asterisk 16
                        !((HangupCause.AST_CAUSE_NOANSWER.equals(eventUserData.getHangupCause()) ||
                                HangupCause.AST_CAUSE_NO_ANSWER.equals(eventUserData.getHangupCause()))
                                && DIAL_STATUS_BUSY.equals(eventUserData.getDialStatus()))
        ) {
            return true; // => fail over is expected
        }
        else {
            return false; // => fail over is NOT going to happen
        }
    }

    protected static boolean isOnGoingSipDestinationFailoverCdrEvent(SIPCode sipCode, CdrEventUserData eventUserData) {
        //On going sip destination attempts cdr events are those which:

        // this attempt is not the last available destination on the list, and
        // the call has NOT ended successfully on this attempt, and 
        // the call failure is not blocking further attempts (for example, the caller cancel the call)
        if (Objects.isNull(eventUserData))
            return false;   //This is to avoid null pointer exceptions in case there is no userData

        if (eventUserData.isLastSipDestination()) {
            return false;  //This is the last attempt - so this is NOT ongoing
        }

        if (SIPCode.OK.equals(sipCode) || SIPCode.PAYMENT_REQUIRED.equals(sipCode))
            return false;  //The call ended with charges - so this is NOT ongoing 

        if (!nonBlockingSipAttemptCodes.contains(sipCode))
            return false; //The current attempt failure is blocking further attempts

        return true;
    }


    private void handleOnGoingGwsFailoverCdrEvent(
            VoiceContext context,
            CdrEvent event,
            CdrEventUserData eventUserData,
            String channelUniqueId,
            SIPCode sipCode,
            int eventHashCode) {

        VoiceContext channelContext = context;
        //The channelContext might be null in case there are several gateways to fail over to, and the call has
        //ended using gateway x>1, but later the event about gateway < x has arrive. 
        //i.e. call ended on 2nd gw, and the event of gw 1 arrived afterwards.
        if (Objects.isNull(context)) {
            Log.warn("On going gateway fail over attempt of " + channelUniqueId +
                    " for event hashcode: " + eventHashCode +
                    " is missing the channelContext.");

            channelContext = buildMissingChannelContext(event, eventUserData, channelUniqueId, sipCode);
        }

        String sessionId = channelContext.getSessionId();

        Log.info("This CDR event is for on-going attempt. Just generate a rejected CDR and wait for the last event. " +
                " uniqueId: " + channelUniqueId +
                " sessionId: " + sessionId +
                " eventUserData: " + eventUserData.toString() +
                " sipCode: " + sipCode.name() +
                " for event hashcode: " + eventHashCode);

        if(context.isEmergencyCall()) {
            EMERGENCY_CALLS_FAILED_OVER.labels(context.getCountryCode(), context.getTo()).inc();
            // We need to set failover reason on the context.
            for(VoiceContext ctx : Core.getInstance().getVoiceContextCache().getAllContextsForSessionId(context.getSessionId())) {
                ctx.addEmergencyCallFailoverReason(eventUserData.getCurrentGateway(), sipCode);
                Core.getInstance().getVoiceContextCache().updateContext(ctx);
            }
        }

        Core.getInstance().getRejectedLoggerController(context.getVoiceProduct()).logFailure(channelContext,
                                                                                    eventUserData.getDialStatus(),
                                                                                    String.valueOf(sipCode.getCode()),
                                                                                    sipCode.getMessage(),
                                                                                    "", //channel2Id,
                                                                                    "", //idSuffix,
                                                                                    eventUserData.getCurrentGateway(), //retryGW,
                                                                                    Core.PHONE_NUMBER_TRUNCATION_LENGTH,
                                                                                    eventUserData);
    }


    private static BigDecimal getCostFromMatrixOrDefault(VoiceContext ctx) {
        BigDecimal cost = null;
        final BigDecimal defaultCost = Core.getInstance().getConfig().getChargingConfig().getDefaultCost();
        final GatewayInfoMatrixConfig gwConfig = Core.getInstance().getConfig().selectGatewayInfoMatrixConfig(ctx.getVoiceProduct());

        PriceMatrixList priceMatrixList = null;

        if (gwConfig != null) {
            final SupplierMappingConfig supplierConfig = gwConfig.getGatewayInfo(ctx.getCurrentGateway());
            priceMatrixList = supplierConfig != null ? supplierConfig.getCostMatrix() : null;
        }

        SmppAccount smppAccount = null;
        try {
            smppAccount = Accounts.getInstance().getSmppAccount(ctx.getAccountId());
        } catch (AccountsException ex) {
            Log.error("Failed to obtain account for accountId: " + ctx.getAccountId() + ". Will use default price", ex);
        }

        if (priceMatrixList != null && smppAccount != null)
            try {
                String cli = ctx.getFrom();
                String forceSender = ctx.getForcedSender();
                if (Objects.nonNull(forceSender))
                    cli = forceSender;

                // Create a fake Message to hold the 'from' number
                Message message = null;
                if (cli != null) {
                    message = new Message(Message.TYPE_OTHER, Product.PRODUCT_VOICE_CALL);
                    message.oa = cli;
                }

                Price priceForNumber = priceMatrixList.getPriceRule(Product.PRODUCT_VOICE_CALL,
                                                                    ctx.getTo(),
                                                                    null,    // network
                                                                    message,
                                                                    smppAccount);
                cost = priceForNumber.getPrice().setScale(8,  RoundingMode.HALF_UP);
            } catch (NoPriceFoundException ex) {
                if (Log.isDebugEnabled())
                    Log.debug("Couldn't find a price for msisdn: " + ctx.getTo(), ex);
            }

        // wrap defaultPrice
        if (cost == null)
            cost = defaultCost.setScale(8, RoundingMode.HALF_UP);

        return cost;
    }


    public void handleEndOfSession(String sessionId) {
        //After the fix regarding the gateways fail-over events control, we will arrive to this
        //point only when the call has really ended, so there is no need to keep the session. 
        //
        Log.info("{} - Handling end of session", sessionId);
        AsteriskVoiceEventHandler.cleanUpSession(sessionId);
    }


    private void dumpEventEvaluatedValues(
            String channelUniqueId,
            CdrEventUserData eventUserData,
            SIPCode sipCode,
            VoiceContext channelContext,
            boolean isOnGoingAttempet,
            CdrEvent event) {

        StringBuilder sb = new StringBuilder().
                append(" channelUniqueId=").append(channelUniqueId).
                append(" CdrEventUserData=").append(eventUserData).
                append(" sipCode=").append(sipCode).
                append(" isOnGoingAttempet=").append(isOnGoingAttempet).
                append(" reported billable seconds=").append(String.valueOf(eventUserData.getReportedBillableSeconds()));

        if (Objects.isNull(channelContext))
            sb.append(" channelContext=null");
        else
            sb.append(" channelContext.sessionId=").append(channelContext.getSessionId());

        sb.append(" event hashcode=").append(event.hashCode());

        Log.info(" Event Evaluated Values:  " + sb.toString());
    }


    private void dumpSessionContext(String sessionId, Collection<VoiceContext> contextsInSession) {
        if (contextsInSession.size() > 2)
            Log.warn("SessionContext of " + sessionId +
                    " includes " + contextsInSession.size() +
                    " channelContext elements. (max expected is 2)");

        StringBuilder sb = new StringBuilder();
        sb.append("SessionContext:").append(" sessionId=").append(sessionId);
        if (Objects.isNull(contextsInSession))
            sb.append(" has null list of  VoiceContext");
        else {
            sb.append(" contains ").append(contextsInSession.size()).append(" VoiceContexts :").append("\n");
            contextsInSession.stream().forEach(e -> sb.append(e.getDebugString()).append("\n"));
        }
        Log.info(sb.toString());
    }


    public String parseChannelUniqueId(CdrEvent event) throws VoiceEventHandlerException {
        String channelUniqueId = event.getUniqueId();

        if (channelUniqueId == null || channelUniqueId.trim().isEmpty())
            throw new VoiceEventHandlerException("Error handling Cdr Event. ChannelUniqueId not found. event " + event);
        return channelUniqueId;
    }

    private VoiceContext loadChannelVoiceContext(String channelUniqueId, CdrEventUserData eventUserData, CdrEvent event) {

        VoiceContext channelContext;
        String sessionId;

        //For backward compatibility, we keep the old way of fetching the context, but in reality it should not happen
        if (Objects.isNull(eventUserData.getNexmoUUID())) {
            Log.warn("NEXMOUUID is missing from CDR Event. Using in-direct-fetch using channelUniqueId=" + channelUniqueId + " hashCode=" + event.hashCode());

            channelContext = Core.getInstance().getVoiceContextCache().
                    getFirstContextWithConnectionId(channelUniqueId);
        } else {
            sessionId = SipAppUtils.getSessionId(eventUserData.getNexmoUUID(), eventUserData.getCurrentFallbackAttempt());
            Log.info("sessionId for this CDR event: " + sessionId + " and channelUniqueId: " + channelUniqueId + " hashCode=" + event.hashCode());

            channelContext = Core.getInstance().getVoiceContextCache().getCacheEntry(sessionId, channelUniqueId);
            if (Objects.isNull(channelContext)) {
                channelContext = Core.getInstance().getVoiceContextCache().getFirstContextWithConnectionId(channelUniqueId);
                if (Objects.isNull(channelContext)) {
                    Log.warn("Failed to find the channelContext for " + sessionId + " and channelUniqueId: " + channelUniqueId + " hashCode=" + event.hashCode());
                } else {
                    sessionId = channelContext.getSessionId();
                }
            }
        }
        return channelContext;
    }

    private void handleChannelContextNotFound(
            CdrEvent event,
            CdrEventUserData eventUserData,
            String channelUniqueId,
            SIPCode sipCode,
            boolean isOnGoingAttempet) {

        //If the call has ended and there is no channelContext for it, something is really wrong, or that the call
        //has gone via emergency stop and then the Asterisk connection has returned back.
        //(i.e. false positive indication of Asterisk disconnect)
        //just log the details.
        Log.warn("Event context is null for CDREvent of channelId:" + channelUniqueId +
                " eventUserData: " + eventUserData +
                " isOnGoingAttempet: " + isOnGoingAttempet +
                " sipCode: " + sipCode.name() +
                " eventHashCode: " + event.hashCode()
        );
    }

    public static AsteriskVersion toAsteriskVersion(String uniqueId) {
        final AsteriskVersion asteriskVersion;
        if (uniqueId.startsWith("asterisk16")) {
            asteriskVersion = AsteriskVersion.V16;
        } else if (uniqueId.startsWith("asterisk20")) {
            asteriskVersion = AsteriskVersion.V20;
        } else {
            asteriskVersion = AsteriskVersion.V1_8;
        }
        if (Log.isDebugEnabled()) {
            Log.debug("Flagged uniqueId {} as Asterisk Version {}", uniqueId, asteriskVersion);
        }
        return asteriskVersion;
    }

    private VoiceContext buildMissingChannelContext(CdrEvent event, CdrEventUserData eventUserData, String channelUniqueId, SIPCode sipCode) {
        Log.warn("Event context is null. BUilding the missing context for CDREvent of channelId:" + channelUniqueId +
                " eventUserData: " + eventUserData +
                " sipCode: " + sipCode.name() +
                " eventHashCode: " + event.hashCode()
        );

        SIPAsteriskContext missingSIPContext = new SIPAsteriskContext(channelUniqueId,
                "unknown", //clientCallId,
                null, //callbackAddress
                eventUserData.getCurrentGateway(),
                false, //auxiliary
                eventUserData.getAvailableGWAttempts() - eventUserData.getCurrentGWAttempt(), //remainingAttempts
                null); //applicationId 

        VoiceContext missingVoiceContext = new VoiceContext.Builder()
                .withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(event.getCallerId())
                .withTo(event.getDestination())
                .withAccountId(event.getAccountCode())
                .withGateway(eventUserData.getCurrentGateway())
                .withNetwork(null)
                .withNetworkType(null)
                .withNetworkName(null)
                .withCountryCode(null)
                .withVoiceDirection(VoiceDirection.OUTBOUND)
                .withApplicationContext(missingSIPContext)
                .withProductClass(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct().getDescriptor())
                .withCallbackMethod(null)
                .withCallbackUrl(null)
                .build();

        missingVoiceContext.setConnectionId(channelUniqueId);
        missingVoiceContext.setSessionId("missing session context");


        if (Log.isDebugEnabled()) {
            Log.debug("Create missing ChannelContext: " + missingVoiceContext + " for " + channelUniqueId + " eventHashCode: " + event.hashCode());
        }
        return missingVoiceContext;

    }


}
