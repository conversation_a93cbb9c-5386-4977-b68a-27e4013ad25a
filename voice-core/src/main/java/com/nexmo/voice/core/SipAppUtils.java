package com.nexmo.voice.core;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.UnknownHostException;
import java.util.*;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.commons.lang3.StringUtils;

import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.hlr.staticprefixmap.SimpleHlrNetworkPrefixMapLookup.Network;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import com.nexmo.voice.core.cache.VoiceContext;

import static com.nexmo.voice.core.sip.AsteriskAGIServer.CUSTOMER_DOMAIN_TYPE_TRUNKING;

/**
 * General utilities
 *
 * <AUTHOR>
 */
public class SipAppUtils {

    //The suffix to add to the sessionId in order to distinguish between the
    //SIP-destinations-fail-over attempts:
    //Each attempt create a separate call using the same sessionId.
    //SFAN => SIP FAILOVER ATTEMPT NUMBER
    public final static String SESSION_ID_SUFFIX = "-SFAN-";
    public static final String PSIP_STR = "psip";
    private static final Logger Log = LogManager.getLogger(SipAppUtils.class);

    private static final String VBC_GATEWAY = "vbc";

    // Set of prefixes that make 'chargeable' field in CDR false
    private static final Set<String> CHARGEABLE_FALSE_PRICE_PREFIXES = new HashSet<>(Arrays.asList(
            "default-inbound-to-application-price",
            "default-outbound-to-vbc-price",
            "default-destination-price"));

    public enum NumberType {
        CLIVerified,
        TollFree,
        Landline,
        Mobile,
        CLIUnverified,
        CLIMissing
    }

    /**
     * Return prefix of given string representing phone number
     * Requirements taken from :
     * https://nexmoinc.atlassian.net/wiki/spaces/CORE/pages/403701781/CDR+Number+Prefix+Requirements
     * <p>
     * If the given string includes digits and a + or space or - these special chars should be removed before creating the prefix.
     * <p>
     * If the given string contains any non-numeric character (other than space, + or -)  or null return empty string.
     * <p>
     * else:
     * While creating a prefix:
     *  if a number has five or more digits:
     *      first (n-4) digits of the phone number field value
     *      if the field value is alphabetic or alphanumeric, value is an empty string
     *  if a number has four or less digits:
     *      the prefix field is blank
     * <p>
     * The prefix length is (n-x) while n is the given string length and
     * x is the length of the part that should be removed.
     *
     * @param phoneNumber      : The Phone number that should be prefixed
     * @param truncationLength : The number of digits to remove from the number's tail.
     * @return the phoneNumber prefix
     */
    public static String extractPrefix(String phoneNumber, int truncationLength) {
        if (Objects.isNull(phoneNumber) || phoneNumber.isEmpty())
            return phoneNumber;

        if (truncationLength <= 0) // negative or zero length to truncate -> return empty prefix
            return "";

        // Remove all plus, space and separator characters
        String cleanedNumber = phoneNumber.replaceAll("\\+|\\s|\\-", "");

        // If the cleaned number include some other non-digit characters
        // Return an empty prefix
        if (!cleanedNumber.matches("[0-9]+"))
            return "";

        // If the cleaned number length is shorter or equal than the expected truncation
        // length
        // return cleaned phonenumebr as prefix
        if (cleanedNumber.length() <= truncationLength)
            return cleanedNumber;

        return cleanedNumber.substring(0, cleanedNumber.length() - truncationLength);
    }

    public static String getHostName() {
        try {
            return java.net.InetAddress.getLocalHost().getCanonicalHostName();
        } catch (UnknownHostException e) {
            Log.info("Failed to fetch the local host name");
            return "Unknown Host";
        }
    }

    public static Set<String> convertGatewaysListToSet(String gatewaysList) {
        if (Objects.isNull(gatewaysList))
            return null;
        // TODO Tally: that will not be ok if the list of gateways will include the same
        // gateway more than once.
        // In order to mitigate this, it is required to fix the VoiceContext:
        // private Set<String> alternativeGateways.
        // In addition, the PHUB should be updated to allow the same gateway to
        // be used several times.
        HashSet<String> gwsSet = new HashSet<String>(Arrays.asList(gatewaysList.split(",")));
        gwsSet.remove("");
        return gwsSet;
    }

    // TODO: Tally: the below comment is for future use, when we will use sessionId
    // to be exactly as the NEXMOUUID. 
    // When this will be implemented, we will use internally sessionKey which will identify
    // the attempt. - it means rename sessionId to sessionKey all over the sources, then
    // add sessionId to the context, and make sure to log it as well.

    // INBOUND calls with several alternatives of sip destinations are composed of
    // several calls, all of them using the same sessionId. The sessionId is used to distinguish
    // between the different attempts.
    // Each attempt creates its own AGI server request.

    // OUBOUND calls with several gateway options are composed of one call
    // as the fail over is managed in the call itself by Asterisk.
    // there is only one AGI server request for such call
    public static String getSessionId(String nexmoUUID, int attempt) {
        return nexmoUUID + SESSION_ID_SUFFIX + attempt;
    }

    public static String getNexmoUUID(String sessionId) {
        if (Objects.isNull(sessionId) || !sessionId.contains(SESSION_ID_SUFFIX))
            return sessionId;

        return sessionId.substring(0, sessionId.indexOf(SESSION_ID_SUFFIX));
    }

    //This utility is generating the quota update reference to be a concatenation of the
    //sessionId and and the call direction.
    //This will provide the quota reconciliation system a unique identification for each quota update
    public static String generateQuotaReference(String sessionId, boolean isInbound) {
        if (Objects.isNull(sessionId) || sessionId.trim().isEmpty())
            return sessionId;

        String direction = isInbound ? "-in" : "-out";

        if (!sessionId.contains(SESSION_ID_SUFFIX))
            return sessionId + direction;
        else
            return sessionId.substring(0, sessionId.indexOf(SESSION_ID_SUFFIX)) + direction;
    }


    //This utility obfuscate the original content by exposing its prefix and suffix 
    //If the requested exposure is contradicting the original value length
    //the original value is returned
    //The obfuscated string does not expose the original value length.
    public static String obfuscate(String original, int exposedPrefix, int exposedSuffix) {
        if (Objects.isNull(original))
            return original;

        int exposedPrefixLen = (exposedPrefix >= 0) ? exposedPrefix : 0;
        int exposedSuffixLen = (exposedSuffix >= 0) ? exposedSuffix : 0;

        if (original.length() < exposedPrefixLen + exposedSuffixLen)
            return original;

        return original.substring(0, exposedPrefixLen) +
                "***" +
                original.substring(original.length() - exposedSuffixLen);
    }


    public static VoiceProduct getVoiceProduct(String productName) {
        VoiceProduct voiceProduct = null;
        if (Objects.isNull(productName) || productName.isEmpty())
            voiceProduct = VoiceProduct.SIP;
        else
            voiceProduct = VoiceProduct.get(productName);

        if (Log.isDebugEnabled())
            Log.debug("Requested productName {} converted to VoiceProduct {}", productName, voiceProduct.getDescriptor());

        return voiceProduct;
    }

    public static String findTerminationType(String extension, String requestedAppId, boolean isInbound) {
        //TODO: Tally, add this in the future to be LVN or Application, then we will also use the requestedAppId value
        if (isInbound)
            return null;

        if (Objects.isNull(extension))
            return "pstn"; // FIXME: historical reasons

        if (extension.startsWith("sip")) // "sip:" or "sips:"
            return "sip";
        else if (extension.startsWith("ws")) // "ws://" or "wss://" or "ws%3A%2F%2F" or "wss%3A%2F%2F"
            return "websocket";
        else // TODO: Check for only digits or '+'
            return "pstn";
    }

    public static String getNetworkType(Network network) {
        String networkType = (Objects.isNull(network) ||
                Objects.isNull(network.getNetworkType())) ? null : network.getNetworkType().name();
        if (Log.isDebugEnabled())
            Log.debug("For network: {}, about to return networkType {}", network, networkType);
        return networkType;
    }

    public static String getAccountPricingGroup(String accountId, String sessionId) {
        if (Objects.isNull(accountId) || accountId.isEmpty())
            return null;
        SmppAccount account = null;
        String accountPricingGroup = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
            if (Objects.nonNull(account))
                accountPricingGroup = account.getQuotaPricingGroupId();
            if (Log.isDebugEnabled())
                Log.debug("Account found for accountId {} with accountPricingGroup {} sessionId {}",
                        accountId, accountPricingGroup, sessionId);
        } catch (AccountsException e) {
            Log.warn("{} Failed to retrieve account {} due to {}. ",
                    sessionId, accountId, e.getMessage());
        }
        return accountPricingGroup;
    }

    public static String getValueFromEnvProperties(String propertyName) {
        String propertyValue = "";
        Properties prop = new Properties();
        FileInputStream fileInputStream = null;
        try {
            String filename = System.getProperty("user.home") + "/runtime/env.properties";
            fileInputStream = new FileInputStream(filename);
            if (fileInputStream != null) {
                prop.load(fileInputStream);
                propertyValue = prop.getProperty(propertyName);
            }
            fileInputStream.close();
        } catch (IOException e) {
            Log.error("Caught IOException!", e);
        } catch (Exception e) {
            Log.error("Something went terribly wrong!", e);
        } finally {
            if (fileInputStream != null) try {
                fileInputStream.close();
            } catch (final IOException io) {
                Log.error("Exception closing stream ...", io);
            }
        }
        return propertyValue;
    }

    /**
     * Determines if the given price prefix should make the 'chargeable' field false in a CDR.
     * If 'pricePrefix' is in the CHARGEABLE_FALSE_PRICE_PREFIXES set, returns true (not chargeable).
     * Otherwise, returns false (chargeable).
     *
     * @param pricePrefix The prefix to check
     * @return true if the price prefix is not chargeable, false otherwise
     */
    public static boolean isNotChargeablePricePrefix(String pricePrefix) {
        return pricePrefix != null && CHARGEABLE_FALSE_PRICE_PREFIXES.contains(pricePrefix);
    }

    /**
     * Returns the 2-character ISO country code for given phone number
     * @param phoneNumber string of an e164 telephone number
     * @return ISO country code or null if phone number invalid
     */
    public static String phoneNumberIsoCountryCode(String phoneNumber) {
        if(Log.isDebugEnabled()) {
            Log.debug("Identifying ISO country code for number: " + phoneNumber);
        }
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
        Phonenumber.PhoneNumber parsedNumber = null;

        String phoneNumberWithLeadingPlus = null;

        if((phoneNumber != null) && (!phoneNumber.startsWith("+"))) {
            phoneNumberWithLeadingPlus = "+" + phoneNumber;
        } else {
            phoneNumberWithLeadingPlus = phoneNumber;
        }

        try {
            parsedNumber = phoneUtil.parse(phoneNumberWithLeadingPlus, null);
        } catch (NumberParseException ex) {
            Log.warn("Number could not be parsed: "+phoneNumber, ex);
        }

        if(parsedNumber != null) {
            String isoCountryCode = phoneUtil.getRegionCodeForNumber(parsedNumber);
            if(Log.isDebugEnabled()) {
                Log.debug("Returning ISO country code " + isoCountryCode + " for number " + phoneNumber);
            }
            return isoCountryCode;
        }
        if(Log.isDebugEnabled()) {
            Log.debug("Could not determine ISO country code");
        }
        return null;
    }

    public static String determineCallType(String srcCountryCode, String destCountryCode) {
        if (StringUtils.isBlank(srcCountryCode)) {
            return "noCli";
        } else if (srcCountryCode.equals(destCountryCode)) {
            return "domestic";
        } else {
            return "international";
        }
    }

    public static String getMappedNumberType(ShortCodeType shortCodeType) {
        switch (shortCodeType) {
            case VERIFIED_CLI:
                return SipAppUtils.NumberType.CLIVerified.name();
            case MOBILE_LVN:
                return SipAppUtils.NumberType.Mobile.name();
            case LANDLINE_TOLL_FREE:
                return SipAppUtils.NumberType.TollFree.name();
            case LANDLINE:
                return SipAppUtils.NumberType.Landline.name();
            default:
                return null;
        }
    }

    public static String getProductClass(VoiceContext ctx) {
        String productClass = ctx.getProductClass();
        if (isPsip(ctx)) {
            //Customer domain CDRs should use product class psip
            Log.info("changing product class from {} to {} for cdr with id {}", productClass, PSIP_STR, ctx.getId());
            productClass = PSIP_STR;
        }
        return productClass;
    }

    private static boolean isPsip(VoiceContext ctx) {
        boolean isPsipOutbound = ctx.isOutboundProgrammableSip() && !VBC_GATEWAY.equals(ctx.getCurrentGateway());
        boolean isNonTrunkingDomain = StringUtils.isNotBlank(ctx.getCustomerDomain()) && !CUSTOMER_DOMAIN_TYPE_TRUNKING.equalsIgnoreCase(ctx.getCustomerDomainType());
        return isPsipOutbound || isNonTrunkingDomain || ctx.isSipOriginToLVNToApplication();
    }

}
