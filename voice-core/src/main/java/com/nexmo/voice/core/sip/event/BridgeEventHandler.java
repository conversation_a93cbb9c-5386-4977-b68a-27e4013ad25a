package com.nexmo.voice.core.sip.event;

import java.util.Collection;
import java.util.Objects;

import com.nexmo.voice.core.billing.BillingManager;
import com.nexmo.voice.core.billing.QuotaInternalError;
import com.nexmo.voice.core.gateway.asterisk.AsteriskActionIssuer;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.SIPCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.BridgeEvent;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.LegFlowContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;

public final class BridgeEventHandler extends AsteriskVoiceEventHandler<BridgeEvent> {

    private static final Logger Log = LogManager.getLogger(BridgeEventHandler.class);

    public BridgeEventHandler() {
        super(BridgeEvent.class);
    }

    @Override
    public void handle(BridgeEvent event) throws VoiceEventHandlerException {
        Log.info("Processing Bridge Event " + event + " hashCode=" + event.hashCode());

        // TODO: Should we deal with BridgeEvent when bridgestate is Unlink?
        if (event.isUnlink()) {
            Log.info("Possibly received a RE-INVITE [BridgeState:" + event.getBridgeState() + "]. DOING NOTHING!  event " + event.hashCode());
            return;
        }

        // Only interested for now when the status is ANSWER, for starting charging...
        String origUniqueId = event.getUniqueId1();
        if (origUniqueId == null || origUniqueId.isEmpty())
            throw new VoiceEventHandlerException("Error handling Bridge Event. No origUniqueId found. event ['" + event.hashCode() + "']");
        String destUniqueId = event.getUniqueId2();
        if (destUniqueId == null || destUniqueId.isEmpty())
            throw new VoiceEventHandlerException("Error handling Bridge Event. No destUniqueId found. event ['" + event.hashCode() + "']");
        String origChannelId = event.getChannel1();
        if (origChannelId == null || origChannelId.isEmpty())
            throw new VoiceEventHandlerException("Error handling Bridge Event. No origChannelId found. event ['" + event.hashCode() + "']");
        String destChannelId = event.getChannel2();
        if (destChannelId == null || destChannelId.isEmpty())
            throw new VoiceEventHandlerException("Error handling Bridge Event. No destChannelId found. event ['" + event.hashCode() + "']");

        //Mark the first leg as processing the start event
        Core.getInstance().getLegFlowCache().startEventArrived(origUniqueId);
        //Mark the second leg as processing the start event
        Core.getInstance().getLegFlowCache().startEventArrived(destUniqueId);

        String sessionId = null;
        try {

            VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
            VoiceContext origContext = cache.getFirstContextWithConnectionId(origUniqueId);
            if (origContext == null) {
                Log.warn("BridgeEvent failed to find the original context of leg {} ", origUniqueId);
                //This can happen if the call was cleaned up by the emergency stop event, while Asterisk was not really disconnected.
                //It is better to let the call continue without exceptions if it is still there, and if it is not, the exception is not helpful
                return;
            }
            if (Log.isDebugEnabled())
                Log.debug("BridgeEvent orig context: ['" + origContext.getDebugString() + " for event " + event.hashCode() + " ']");

            //If the first leg context is already going under emergency stop -there is no point to create the second leg context. The call
            //is not going to continue its communication with SIPApp
            if (origContext.getBillingInfo().isOnEmergencyStop()) {
                Log.warn("{} {} Leg1 of the call is on emergency stop process, before the call has started. Ignoring the Bridge event. CDR will be created as part of the emergency stop.",
                        origContext.getSessionId(), origContext.getConnectionId());
                return;
            }

            VoiceContext destContext = cache.getFirstContextWithConnectionId(destUniqueId);
            if (destContext != null) {
                Log.debug("BridgeEvent dest context: ['" + destContext.getDebugString() + " for event " + event.hashCode() + " ']");
                Log.info("Received a RE-INVITE [connectionId:" + destContext.getConnectionId() + "]. DOING NOTHING!  event " + event.hashCode());
                return;
            } else {
                Log.debug("BridgeEvent dest context not defined yet. The second leg context will be created now for event " + event.hashCode() + " ']");
            }

            //The call destination is the second leg.
            //The Bridge event include the linkage between the first leg and the second.
            //At this point we already found the first leg (created in the AGIServer request stage)
            //Now we are going to create the second leg context:

            sessionId = origContext.getSessionId();
            VoiceProduct voiceProduct = origContext.getVoiceProduct();

            switch (voiceProduct) {
                case SIP:
                case CALL_API:
                    BridgeEventSIPHandler.handleBridgeEvent(event, sessionId, origContext);
                    break;
                case TTS:
                case VERIFY:
                    BridgeEventTTSNGHandler.handleBridgeEvent(event, sessionId, origContext);
                    break;
                default:
                    Log.error("{} : Unsupported VoiceProduct: {} in BridgeEvent", sessionId, voiceProduct.name());
                    throw new VoiceEventHandlerException("Unsupported VoiceProduct in BridgeEvent ");
            }

        } catch (Exception e) {
            Log.error("Due to Exception in BridgeEvent: About to set APP_REASON to {} and finish the call with HangupCause: {}, for SessionId {}, origChannelId {}, exception {}  ",
                    SIPCode.SERVER_INTERNAL_ERROR, HangupCause.AST_CAUSE_FAILURE, sessionId, origChannelId, e);
            finishCall(sessionId, origChannelId, SIPCode.SERVER_INTERNAL_ERROR, HangupCause.AST_CAUSE_FAILURE);
        } finally {
            if (Log.isDebugEnabled())
                Log.debug("{} : Handle the call status", sessionId);

            //After processing the start event - verify if we need to continue with the stop
            //This is not perfect in case of exceptions during the start - but this requires rewrite
            //the whole method as it depends on the exception point. :-(
            //
            //The expected return status are:
            // STOP_P - stop event is pending to be processed - go ahead finish the thing
            // START_E - the start event processing has completed - do nothing, 
            // - the regular started conversation process is taking place (counting time and updating quota)
            LegFlowContext legFlowContext1 = Core.getInstance().getLegFlowCache().startEventProcessingCompleted(origUniqueId);
            if (LegFlowContext.LEG_STATUS.START_E.equals(legFlowContext1.getLegStatus())) {
                Log.info("BridgeEvent processing of leg " + origUniqueId + " has completed");

                verifySecondLegStopStatus(origUniqueId, destUniqueId);

            } else {
                //Process the pending stop (or any unclear situation)
                processPendingStopEvent(origUniqueId, destUniqueId, legFlowContext1);
            }
            //log the session context after completing the start.
            //It is expected to include 2 legs or to be not exits - if the stop was pending and already executed at this point
            //TODO Tally - on the final correct impl - remove this part
            VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
            Collection<VoiceContext> contextsInSession = cache.getInnerValues(sessionId);
            if (Objects.isNull(contextsInSession)) {
                Log.info("On the BridgeEvent completion the " + sessionId + " context is gone - verify in the logs that stop was processed - this is an ok situation");
            } else {
                Log.info("On the BridgeEvent completion the " + sessionId + " context includes " + contextsInSession.size());
                //On usual normal flows, we should have 2 voice  contexts here - one per leg.
                //On some cases, when there are exceptions we end up with one voice context.
                //In such case, the stop event will generate only one CDR and wont remove the other leg from the legs flow cache
                //So we need to remove the other leg here.
                //In very bad situations there wont be any voice context - in such case we have to remove both legs from the legs flow
                //If later a stop will arrive - that will cause the STOP_P entry to be stuck in the cache (havn't seen it in the tests)
                //If the stop will never arrive - we are in better place.

                if (contextsInSession.size() == 0) {
                    Log.warn("On the BridgeEvent completion the " + sessionId + " has no registered legs - remove the legs entries from the leg status cache");
                    Core.getInstance().getLegFlowCache().removeLegContext(origUniqueId);
                    Core.getInstance().getLegFlowCache().removeLegContext(destUniqueId);
                } else if (contextsInSession.size() == 1) {
                    Log.warn("On the BridgeEvent completion the " + sessionId + " include only one leg - remove the other leg entry from the leg status cache");
                    VoiceContext voiceContext = (VoiceContext) contextsInSession.toArray()[0];
                    String registeredLegId = voiceContext.getConnectionId();
                    if (destUniqueId.equals(registeredLegId))
                        Core.getInstance().getLegFlowCache().removeLegContext(origUniqueId);
                    else
                        Core.getInstance().getLegFlowCache().removeLegContext(destUniqueId);
                }
            }
        }  //EOF Finally
    }

    private void finishCall(String sessionId, String origChannelId, SIPCode sipCode, HangupCause hangupCause) throws VoiceEventHandlerException {
        try {
            AsteriskActionIssuer.setVariable(origChannelId, "APP_REASON", String.valueOf(sipCode.getCode()));
            AsteriskActionIssuer.finishCall(origChannelId, hangupCause);
        } catch (Exception ex) {
            String errorMessage = String.format("Failed to finish the call with ChannelId %s, APP_REASON = %s and HangupCause = %s, for SessionId = %s",
                    origChannelId, sipCode, hangupCause, sessionId);
            Log.error(errorMessage, ex);
            throw new VoiceEventHandlerException(errorMessage, ex);
        }
    }


    private void processPendingStopEvent(String stoppedLeg, String otherLeg, LegFlowContext stoppedLegFlowContext) {
        Log.info("BridgeEvent processing pending stop event of leg " + stoppedLeg + " with leg " + otherLeg);
        Core.getInstance().getAsteriskAMIProcessor().onManagerEvent(stoppedLegFlowContext.getCdrEvent());
    }

    private void verifySecondLegStopStatus(String origUniqueId, String destUniqueId) {
        //Check the second leg (this seems to be never happening 
        // -we get the CDR using the first leg, adding this in case.. )
        LegFlowContext legFlowContext2 = Core.getInstance().getLegFlowCache().startEventProcessingCompleted(destUniqueId);
        if (LegFlowContext.LEG_STATUS.START_E.equals(legFlowContext2.getLegStatus())) {
            Log.info("BridgeEvent processing of leg " + destUniqueId + " has completed");
        } else { //In some bizarre way the second leg got the stop ..
            //Process the pending stop (or any unclear situation)
            processPendingStopEvent(destUniqueId, origUniqueId, legFlowContext2);
        }
    }


    protected static void startChargingContext(VoiceContext context,
                                             String origChannelId,
                                             String destChannelId,
                                             BridgeEvent event) throws VoiceEventHandlerException {
    
        Log.info("Start charging. context id {}  sessionId {} direction {} for BridgeEvent {}", context.getConnectionId(), context.getSessionId(), Objects.toString(context.getVoiceDirection(), "null"), event.hashCode());
        // Start charging ...
        BillingManager billingManager = Core.getInstance().getBillingManager();
    
        try {
            billingManager.startCharging(context, event);
            // log the call now that we started billing
            Core.getInstance().getApplicationMetrics().logCall();
        } catch (NotEnoughBalanceException e) {
            Log.warn("NotEnoughBalanceException for" + context.getSessionId() + " during startCharging. Notify Astrisk to end the call");
            BridgeEventHandler.handleStartChargingError(context, origChannelId, SIPCode.PAYMENT_REQUIRED, HangupCause.AST_CAUSE_CALL_REJECTED, CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);
            return;
        } catch (QuotaInternalError e) {
            //Allow call to continue
            if (Core.getInstance().isAsyncQuotaFlag()) {
                Log.warn("AsyncQuota QuotaInternalError for " + context.getSessionId() + " during startCharging. Allowing the call to go through");
                context.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
            } else {
                Log.error("QuotaInternalError for " + context.getSessionId() + " during startCharging. Notify Astrisk to end the call");
                BridgeEventHandler.handleStartChargingError(context, origChannelId, SIPCode.REQUEST_TERMINATED, HangupCause.AST_CAUSE_CALL_REJECTED, CallInternalFlag.BLOCKED_ON_QUOTA_ISSUES);
                return;
            }
        } catch (QuotaDisabledException e) {
            Log.error("QuotaDisabledException for " + context.getSessionId() + " during startCharging. Notify Astrisk to end the call");
            BridgeEventHandler.handleStartChargingError(context, origChannelId, SIPCode.REQUEST_TERMINATED, HangupCause.AST_CAUSE_CALL_REJECTED, CallInternalFlag.BLOCKED_ON_QUOTA_DISABLED);
            return;
        } catch (AccountsException e) {
            Log.error("AccountsException for " + context.getSessionId() + " during startCharging. Notify Astrisk to end the call");
            BridgeEventHandler.handleStartChargingError(context, origChannelId, SIPCode.REQUEST_TERMINATED, HangupCause.AST_CAUSE_CALL_REJECTED, CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            return;
        }
        catch (Exception e) {
            Log.error("Internal error while attempting to start charging session for " + context.getSessionId() + " Notify Astrisk to end the call  due to: ", e);
            BridgeEventHandler.handleStartChargingError(context, origChannelId, SIPCode.SERVER_INTERNAL_ERROR, HangupCause.AST_CAUSE_FAILURE, CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            return;
        } 
    
        Log.info("Start charging completed for account " + context.getAccountId() +
                " connection: " + context.getConnectionId() +
                " session: " + context.getSessionId() +
                " status: " + context.getBillingInfo().getStatus().name() +
                " for BridgeEvent " + event.hashCode()
        );
    

    }
    
    
    public static void handleStartChargingError(VoiceContext context, String origChannelId, SIPCode sipCode, HangupCause hangupCause, CallInternalFlag internalFlag) {
        context.addInternalFlag(internalFlag);
        try {
            AsteriskActionIssuer.setVariable(origChannelId, "APP_REASON", String.valueOf(sipCode.getCode()));
            AsteriskActionIssuer.finishCall(origChannelId, hangupCause);
        } catch (Exception e1) {
            Log.error("Failed to set parameters to stop the call while handling startCharging errors. sessionId: " + context.getSessionId(), e1);
        }
        finally {
            Core.getInstance().getApplicationMetrics().logErrorCall();
        }
    }



}
