package com.nexmo.voice.core.monitoring;

import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.asteriskjava.manager.ManagerConnection;
import org.asteriskjava.manager.ManagerConnectionState;

import com.nexmo.voice.config.gateway.asterisk.AsteriskManagerConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.cache.LegFlowContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.gateway.asterisk.AsteriskAMIProcessor;
import com.nexmo.voice.core.monitoring.metrics.ApplicationMetrics;
import com.thepeachbeetle.common.app.monitoring.AbstractMonitoringServlet.MonitoringCommand;

public class CallMetricsCommand extends MonitoringCommand {

    private static final Logger Log = Logger.getLogger(CallMetricsCommand.class.getName());

    private final Core core;

    public CallMetricsCommand(final Core core) {
        this.core = core;
    }

    @Override
    protected String getMetrics(HttpServletRequest request,
                                Map<String, String> metrics) {

        String debugMode = request.getParameter("debug");
        if (Objects.nonNull(debugMode))
            return allCallMetricsDebugMode(metrics);

        String sessionId = request.getParameter("session_id");
        if (Objects.nonNull(sessionId))
            return allCallMetricsSessionMode(metrics, sessionId);

        String accountId = request.getParameter("account");
        if (accountId == null)
            return allCallMetrics(metrics);

        return accountCallMetrics(accountId, metrics);
    }


    private String accountCallMetrics(String accountId,
                                      Map<String, String> metrics) {

        int callsUpForAccount = 0;
        int maxRequestsForAccount = 0;

        //That was returning always zero

        metrics.put("CALLS-UP", String.valueOf(callsUpForAccount));
        metrics.put("MAX-CALLS-ALLOWED", String.valueOf(maxRequestsForAccount));

        return "CALL-METRICS-" + accountId;
    }


    private static String allCallMetrics(Map<String, String> metrics) {
        VoiceContextCache contextCache = Core.getInstance().getVoiceContextCache();

        long totalCallsUp = contextCache.outerSize();

        metrics.put("TOTAL-CALLS-UP", String.valueOf(totalCallsUp));

        ApplicationMetrics applicationMetrics = Core.getInstance().getApplicationMetrics();
        metrics.putAll(applicationMetrics.getGlobalMetrics().getMetricsOutputMap());

        getAsteriskStatus(metrics);

        return "ALL-CALLS";
    }

    //For debug mode only.
    //This method explores the details of the cache, it use a snapshot as the cache is changing by the calls's status. 
    //Its ok to use shallow copy,as we care mainly about the number of keys and not about the entries content.
    private String allCallMetricsDebugMode(Map<String, String> metrics) {
        VoiceContextCache contextCache = Core.getInstance().getVoiceContextCache();

        ConcurrentMap<String, ConcurrentMap<String, VoiceContext>> cacheShallowCopy =
                new ConcurrentHashMap<String, ConcurrentMap<String, VoiceContext>>(
                        (Map<? extends String, ? extends ConcurrentMap<String, VoiceContext>>)
                                contextCache.getCache());

        metrics.put("DEBUG-TOTAL-CALLS-OUTER-SIZE", String.valueOf(contextCache.outerSize()));
        metrics.put("DEBUG-TOTAL-CALLS-SHALLOW-COPY-SIZE (might be little differrent than the above) ", String.valueOf(cacheShallowCopy.size()));

        cacheShallowCopy.entrySet().stream().forEach(outerEntry -> dumpCacheOuterEntry(outerEntry, metrics));

        return "ALL-CALLS-DEBUG-MODE";
    }

    //For testing.
    //This method fetch a specific sessionId entries from the cache
    //Its main goal is to provide the tests a mechanism to verify the cache was properly cleaned
    //after the call completion.
    private String allCallMetricsSessionMode(Map<String, String> metrics, String sessionId) {
        VoiceContextCache contextCache = Core.getInstance().getVoiceContextCache();

        ConcurrentMap<String, ConcurrentMap<String, VoiceContext>> cacheShallowCopy =
                new ConcurrentHashMap<String, ConcurrentMap<String, VoiceContext>>(
                        (Map<? extends String, ? extends ConcurrentMap<String, VoiceContext>>)
                                contextCache.getCache());

        String sessionKey =
                cacheShallowCopy.keySet().stream().
                        filter(k -> k.startsWith(sessionId)).
                        findAny().
                        orElse(null);

        if (Objects.isNull(sessionKey)) {
            metrics.put("SESSION-ID", "NOT_FOUND");
        } else {
            ConcurrentMap<String, VoiceContext> sessionMap = cacheShallowCopy.get(sessionKey);
            if (Objects.isNull(sessionMap)) {
                metrics.put("SESSION-ID", "NOT_FOUND");
            } else {
                sessionMap.entrySet().stream().forEach(innerEntry -> dumpCacheInnerEntry(sessionId, innerEntry, metrics));
            }
        }

        return "SPECIFIC-SESSION-MODE";
    }


    private void dumpCacheOuterEntry(Entry<String, ConcurrentMap<String, VoiceContext>> outerEntry, Map<String, String> metrics) {
        ConcurrentMap<String, VoiceContext> outerEntryValue = outerEntry.getValue();
        outerEntryValue.entrySet().stream().forEach(innerEntry -> dumpCacheInnerEntry(outerEntry.getKey(), innerEntry, metrics));
    }

    private void dumpCacheInnerEntry(String outerKey, Entry<String, VoiceContext> innerEntry, Map<String, String> metrics) {

        String cacheDetails = "Already gone from cache";
        String cacheDoubleKey = outerKey;
        VoiceContext voiceContext = null;

        if (Objects.nonNull(innerEntry)) {
            cacheDoubleKey = outerKey + "-" + innerEntry.getKey();
            voiceContext = innerEntry.getValue();
        }


        if (Objects.nonNull(voiceContext)) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            String creationTime = dateFormat.format(voiceContext.getContextCreationDate());

            cacheDetails = "creation=" + creationTime +
                    " product=" + voiceContext.getVoiceProduct().name() +
                    " productClass=" + voiceContext.getProductClass() +
                    " account=" + voiceContext.getAccountId();
            BillingInfo billingInfo = voiceContext.getBillingInfo();
            if (Objects.nonNull(billingInfo))
                cacheDetails = cacheDetails +
                        " status=" + billingInfo.getStatus();
            else
                cacheDetails = cacheDetails +
                        " status=billingInfo-not-found";
        }

        LegFlowContext legFlowContext = Core.getInstance().getLegFlowCache().getLegsStatus().get(innerEntry.getKey());
        if (Objects.nonNull(legFlowContext))
            cacheDetails = cacheDetails +
                    " legStatus=" + legFlowContext.getLegStatus().name();
        else
            cacheDetails = cacheDetails +
                    " legStatus=not-found";

        metrics.put(cacheDoubleKey, cacheDetails);
    }


    private static void getAsteriskStatus(Map<String, String> metrics) {
        AsteriskManagerConfig asteriskManagerConfig = Core.getInstance().getConfig().getAsteriskManagerConfig();
        if (asteriskManagerConfig != null && asteriskManagerConfig.isEnabled()) {
            String asteriskStatus = "DOWN";
            ManagerConnection managerConnection = null;
            AsteriskAMIProcessor amiProcessor = Core.getInstance().getAsteriskAMIProcessor();

            if (amiProcessor != null)
                managerConnection = amiProcessor.getManagerConnection();

            if (managerConnection != null &&
                    managerConnection.getState() == ManagerConnectionState.CONNECTED)
                asteriskStatus = "UP";

            metrics.put("ASTERISK-STATUS", asteriskStatus);
        }
    }
}
