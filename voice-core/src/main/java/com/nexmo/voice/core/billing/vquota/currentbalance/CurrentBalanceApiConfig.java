package com.nexmo.voice.core.billing.vquota.currentbalance;

import org.jdom.Element;
import java.math.BigDecimal;

public class CurrentBalanceApiConfig implements java.io.Serializable  {
    private static final long serialVersionUID = -84881478193565585L;
    public static final String ROOT_NODE = "current-balance-api";
    public static final String API_HOST_URL_ATTR = "host-url";
    public static final String API_BASE_PATH_ATTR = "base-path";
    public static final String API_MIN_BALANCE_VALUE_ATTR = "min-balance";
    public static final String API_TIMEOUT_ATTR = "timeout";

    private final String host;
    private final String basePath;
    private final BigDecimal minBalance;
    private final int timeout;

    public CurrentBalanceApiConfig(String host, String basePath, BigDecimal minBalance, int timeout) {
        this.host = host;
        this.basePath = basePath;
        this.minBalance = minBalance;
        this.timeout = timeout;
    }

    public String getHost() {
        return host;
    }

    public String getBasePath() {
        return basePath;
    }

    public BigDecimal getMinBalance() {
        return minBalance;
    }

    public int getTimeout() {
        return timeout;
    }

    public String constructUri() {
        StringBuilder sb = new StringBuilder();
        sb.append(host);
        if (!basePath.startsWith("/")) {
            sb.append("/");
        }
        sb.append(basePath);
        return sb.toString();
    }

    public Element toXML() {
        Element currentBalanceApiElement = new Element(ROOT_NODE);
        currentBalanceApiElement.setAttribute(API_HOST_URL_ATTR, this.host);
        currentBalanceApiElement.setAttribute(API_BASE_PATH_ATTR, this.basePath);
        currentBalanceApiElement.setAttribute(API_MIN_BALANCE_VALUE_ATTR, this.minBalance.toPlainString());
        currentBalanceApiElement.setAttribute(API_TIMEOUT_ATTR, Integer.toString(this.timeout));
        return currentBalanceApiElement;
    }

    @Override
    public String toString() {
        return "CurrentBalanceApiConfig{" +
                "host='" + host + '\'' +
                ", basePath='" + basePath + '\'' +
                ", minBalance=" + minBalance +
                ", timeout=" + timeout +
                '}';
    }
}
