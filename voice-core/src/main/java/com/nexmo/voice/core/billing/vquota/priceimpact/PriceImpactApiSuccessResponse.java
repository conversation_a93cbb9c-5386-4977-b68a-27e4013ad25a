package com.nexmo.voice.core.billing.vquota.priceimpact;

import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.nexmo.voice.core.billing.vquota.deserializer.AccountBalanceDeserializer;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceImpactApiSuccessResponse {
    private String status;
    private BigDecimal estimatedPriceImpact;
    private String message;
    private String currency;
    private String groupId;
    private String callType;
    private String numberType;
    private String country;
    private String networkType;
    private MediationCdrField mediationCdrField;

    @JsonDeserialize(using = AccountBalanceDeserializer.class)
    private AccountBalance accountBalance;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getEstimatedPriceImpact() {
        return estimatedPriceImpact;
    }

    public void setEstimatedPriceImpact(BigDecimal estimatedPriceImpact) {
        this.estimatedPriceImpact = estimatedPriceImpact;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCallType() {
        return callType;
    }

    public void setCallType(String callType) {
        this.callType = callType;
    }

    public String getNumberType() {
        return numberType;
    }

    public void setNumberType(String numberType) {
        this.numberType = numberType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }

    public MediationCdrField getMediationCdrField() {
        return mediationCdrField;
    }

    public AccountBalance getAccountBalance() { return accountBalance; }

    public void setMediationCdrField(MediationCdrField mediationCdrField) {
        this.mediationCdrField = mediationCdrField;
    }

    public static class MediationCdrField implements Serializable {
        private String originPrefixGroup;
        private String originCountryRegion;

        // Getters and Setters
        public String getOriginPrefixGroup() {
            return originPrefixGroup;
        }

        public void setOriginPrefixGroup(String originPrefixGroup) {
            this.originPrefixGroup = originPrefixGroup;
        }

        public String getOriginCountryRegion() {
            return originCountryRegion;
        }

        public void setOriginCountryRegion(String originCountryRegion) {
            this.originCountryRegion = originCountryRegion;
        }

        @Override
        public String toString() {
            return "MEDIATION-CDR-FIELD{" +
                    "originPrefixGroup='" + originPrefixGroup + '\'' +
                    ", originCountryRegion='" + originCountryRegion + '\'' +
                    '}';
        }
    }
}