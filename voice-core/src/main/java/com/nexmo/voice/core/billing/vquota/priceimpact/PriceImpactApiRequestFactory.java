package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.QuotaUpdateDetails;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.types.ProductType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public class PriceImpactApiRequestFactory {
    private final static Logger Log = LogManager.getLogger(PriceImpactApiRequestFactory.class);
    public static PriceImpactApiRequest createRequest(ProductType productType, VoiceContext ctx, Long duration, QuotaUpdateDetails.Operation cmd) {
        PriceImpactApiRequest.Builder builder = new PriceImpactApiRequest.Builder()
                .setGroupId(ctx.getAccountId())
                .setAllowNegativeBalance(ctx.allowNegativeBalance())
                .setRefId(ctx.getQuotaRef())
                .setConnId(ctx.getConnectionId())
                .setDuration(duration)
                .setCmd(cmd.getValue())
                .setProduct(productType.getProductString())
                .setProductDetail(SipAppUtils.getProductClass(ctx));

        if (productType == ProductType.VOICE_INBOUND || productType == ProductType.VOICE_OUTBOUND) {
            String decodedTo = ctx.getTo();
            try {
                decodedTo = Objects.nonNull(ctx.getTo()) ? URLDecoder.decode(ctx.getTo(), StandardCharsets.UTF_8.name()) : "";
            } catch (UnsupportedEncodingException e) {
                Log.warn("Failed to decode the TO param " + ctx.getTo() + " due to " + e.getMessage());
            }

            String from = Objects.nonNull(ctx.getForcedSender()) ? ctx.getForcedSender() : ctx.getFrom();

            builder.setCallType(ctx.getCallType())
                    .setNumberType(ctx.getNumberType())
                    .setPrefixTo(SipAppUtils.extractPrefix(decodedTo, Core.PHONE_NUMBER_TRUNCATION_LENGTH))
                    .setPrefixFrom(SipAppUtils.extractPrefix(from, Core.PHONE_NUMBER_TRUNCATION_LENGTH))
                    .setCountry(ctx.getCountryCode())
                    .setSourceCountry(ctx.getSourceCountryCode())
                    .setNetworkType(ctx.getNetworkType());

            if(productType == ProductType.VOICE_INBOUND) {
                builder.setToNumber(decodedTo);
            }
        }
        return builder.build();
    }
}