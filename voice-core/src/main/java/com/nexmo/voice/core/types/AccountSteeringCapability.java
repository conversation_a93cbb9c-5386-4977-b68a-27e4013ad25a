package com.nexmo.voice.core.types;

import java.io.Serializable;

public enum AccountSteeringCapability implements Serializable {

    RTC_AWS_US("rtc_aws_us", "rtc-aws-us"),
    RTC_AWS_EU("rtc_aws_eu", "rtc-aws-eu"),
    RTC_AWS_AP("rtc_aws_ap", "rtc-aws-ap");

    private final String key;
    private final String capability;

    AccountSteeringCapability(String key, String capability) {
        this.key = key;
        this.capability = capability;
    }

    public String getKey() {
        return key;
    }

    public String getCapability() {
        return capability;
    }
}
