package com.nexmo.voice.core.sip.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.VarSetEvent;


/**
 * Created on 01/10/23.
 *
 * <AUTHOR>
 */
public class VarSetEventHandler extends AsteriskVoiceEventHandler<VarSetEvent> {

    private static final Logger Log = LogManager.getLogger(VarSetEventHandler.class);

    public VarSetEventHandler() {
        super(VarSetEvent.class);
    }

    @Override
    public void handle(VarSetEvent event) throws VoiceEventHandlerException {
        Log.info("Processing VarSet Event ['" + event + "'] hashCode=" + event.hashCode());
    }
}
