package com.nexmo.voice.core.sip.event;
/**
 * This class is the base for all the incoming Asterisk events.
 * For SIPApp, the events' specific handle implementation is in package:
 * com.nexmo.voice.core.sip.event
 */


import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.ManagerEvent;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.gateway.asterisk.task.AsteriskTaskExecutor;
import com.nexmo.voice.core.types.SIPCode;

public abstract class AsteriskVoiceEventHandler<T extends ManagerEvent> {

    protected final static Logger Log = LogManager.getLogger(AsteriskVoiceEventHandler.class.getName());

    protected static final Map<HangupCause, SIPCode> errorCodeTranslationMap = new HashMap<>();

    static {
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NORMAL,                       SIPCode.OK);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_UNALLOCATED,                  SIPCode.NOT_FOUND);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NOTDEFINED,                   SIPCode.REQUEST_TERMINATED);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NO_ROUTE_DESTINATION,         SIPCode.BAD_EXTENSION);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_UNREGISTERED,                 SIPCode.BAD_EXTENSION);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_BUSY,                         SIPCode.BUSY_HERE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NO_USER_RESPONSE,             SIPCode.REQUEST_TIMEOUT);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NOANSWER,                     SIPCode.TEMPORARILY_UNAVAILABLE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_CALL_REJECTED,                SIPCode.FORBIDDEN);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NUMBER_CHANGED,               SIPCode.GONE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_DESTINATION_OUT_OF_ORDER,     SIPCode.BAD_GATEWAY);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_INVALID_NUMBER_FORMAT,        SIPCode.ADDRESS_INCOMPLETE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_FACILITY_REJECTED,            SIPCode.NOT_IMPLEMENTED);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NETWORK_OUT_OF_ORDER,         SIPCode.SERVER_INTERNAL_ERROR);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_FAILURE,                      SIPCode.SERVER_INTERNAL_ERROR);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_SWITCH_CONGESTION,            SIPCode.SERVER_INTERNAL_ERROR);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_BEARERCAPABILITY_NOTAVAIL,    SIPCode.NOT_ACCEPTABLE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_RECOVERY_ON_TIMER_EXPIRE,     SIPCode.SERVER_TIMEOUT);

        //SIPApp choose the SIPCode based on the HANGUPCAUSE from the CDREvent.
        //505 is the correct SIPCode for HANGUPCAUSE=127 and it is an indication to no failover attempt
        //500 was SIPApp interpretation which implying a retry will take place and this is not 
        // the case for  HANGUPCAUSE=127
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_INTERWORKING,                 SIPCode.VERSION_NOT_SUPPORTED);

        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NORMAL_CIRCUIT_CONGESTION,    SIPCode.SERVICE_UNAVAILABLE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_CONGESTION,                   SIPCode.SERVICE_UNAVAILABLE);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED,         SIPCode.OK);
        errorCodeTranslationMap.put(HangupCause.AST_CAUSE_NORMAL_CLEARING,              SIPCode.OK); // It could be anything (analog always terminates with this)
    }


    private final Class<T> eventClass;

    public AsteriskVoiceEventHandler(Class<T> eventClass) {
        this.eventClass = eventClass;
    }

    public abstract void handle(T event) throws VoiceEventHandlerException;

    @SuppressWarnings("unchecked")
    public void tryHandling(ManagerEvent event) throws VoiceEventHandlerException {
        handle((T) event);
    }

    public Class<T> getEventClass() {
        return this.eventClass;
    }

    public static void cleanUpSession(String sessionId) {
        if (Log.isDebugEnabled())
            Log.debug("Clean up session ['" + sessionId + "']");

        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        AsteriskTaskExecutor executor = Core.getInstance().getAsteriskTaskExecutor();

        // Only one leg will have a pool, but we abstract this method from that fact ...
        Collection<VoiceContext> sessionContexts = cache.getInnerValues(sessionId);
        Set<String> accountsForReleasing = new HashSet<>();
        for (VoiceContext connection : sessionContexts) {
            Log.info("Clean up sessionId" + sessionId + " connectionId " + connection.getConnectionId() + " account "
                    + connection.getAccountId());
            // For most cases that will be already done when the CDR is created.
            // When the call is redirected to an application there is no CDR, and then the
            // entry
            // will be removed here.
            try {
                Core.getInstance().getLegFlowCache().removeLegContext(connection.getConnectionId());
                executor.shutdownPoolAndRemove(connection.getConnectionId());
            } catch (Exception e) {
                Log.warn("Problems during cleanup session {} connectionId {} due to {} ", sessionId,
                        connection.getAccountId(), e);
            }
            accountsForReleasing.add(connection.getAccountId());
        }

        try {
            cache.removeAllForSession(sessionId);
        } catch (Exception e) {
            Log.error("Problems during releasing accounts for session {} due to {} ", sessionId, e);
        }
    }

    public static  SIPCode translateOrDefault(HangupCause hangupCause, SIPCode defaultCode){
        return errorCodeTranslationMap.getOrDefault(hangupCause, defaultCode);
    }

}
