package com.nexmo.voice.core.types;

import java.io.Serializable;

public enum CDRType implements Serializable {

    KEY_VALUE("key_value"), 
    JSON("json"), 
    BOTH("both");

    private final String cdrType;

    private CDRType(final String cdrType) {
        this.cdrType = cdrType;
    }

    public String getType() {
        return this.cdrType;
    }

    public static CDRType getDefault() {
        return KEY_VALUE;
    }

    public static CDRType of(String cdrTypeName) {
        for (CDRType t : values()) {
            if (t.cdrType.equals(cdrTypeName)) {
                return t;
            }
        }
        return getDefault();
    }

}
