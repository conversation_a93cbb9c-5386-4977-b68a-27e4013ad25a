package com.nexmo.voice.core.monitoring.metrics;

import io.prometheus.client.Info;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class GlobalMetrics {

    private volatile long timeOfLastProcessing;
    private volatile long timeOfLastProcessingError;

    private final AtomicLong[] processingMinuteCount = new AtomicLong[15];
    private final AtomicLong[] processingErrorMinuteCount = new AtomicLong[15];

    private final AtomicInteger highestUsedMinuteCountSlot = new AtomicInteger(0);

    private AtomicLong processingInLastHour = new AtomicLong(0L);
    private AtomicLong processingErrorInLastHour = new AtomicLong(0L);

    private AtomicLong processingToday = new AtomicLong(0L);
    private AtomicLong processingErrorToday = new AtomicLong(0L);

    private static final Info PROCESSING_INFO = Info.build().name("sipapp_processing_metrics").help("Processing Metrics.").register();

    public GlobalMetrics() {
        for (int i = 0; i < this.processingMinuteCount.length; i++) {
            this.processingMinuteCount[i] = new AtomicLong(0L);
            this.processingErrorMinuteCount[i] = new AtomicLong(0L);
        }
    }

    public void logCall() {
        this.timeOfLastProcessing = System.currentTimeMillis();
        this.processingInLastHour.incrementAndGet();
        this.processingToday.incrementAndGet();
        this.processingMinuteCount[0].incrementAndGet();
    }

    public void logErrorCall() {
        logCall();
        this.timeOfLastProcessingError = System.currentTimeMillis();
        this.processingErrorInLastHour.incrementAndGet();
        this.processingErrorToday.incrementAndGet();
        this.processingErrorMinuteCount[0].incrementAndGet();
    }

    public void doMinuteUpdates() {
        for (int i = this.processingMinuteCount.length - 1; i > 0; i--) {
            this.processingMinuteCount[i] = this.processingMinuteCount[i - 1];
            this.processingErrorMinuteCount[i] = this.processingErrorMinuteCount[i - 1];
        }

        this.processingMinuteCount[0] = new AtomicLong(0L);
        this.processingErrorMinuteCount[0] = new AtomicLong(0L);

        if (this.highestUsedMinuteCountSlot.get() < this.processingMinuteCount.length - 1)
            this.highestUsedMinuteCountSlot.incrementAndGet();

        PROCESSING_INFO.info("time_of_last_processing", String.valueOf(timeOfLastProcessing),
                "processing_in_last_hour", String.valueOf(processingInLastHour),
                "processing_today", String.valueOf(processingToday),
                "time_of_last_processing_error", String.valueOf(timeOfLastProcessingError),
                "processing_error_in_last_hour", String.valueOf(processingErrorInLastHour),
                "processing_error_today", String.valueOf(processingErrorToday));
    }

    public void doHourUpdates() {
        this.processingInLastHour = new AtomicLong(0L);
        this.processingErrorInLastHour = new AtomicLong(0L);
    }

    public void doDayUpdates() {
        this.processingToday = new AtomicLong(0L);
        this.processingErrorToday = new AtomicLong(0L);
    }

    public Map<String, String> getMetricsOutputMap() {
        Map<String, String> map = new LinkedHashMap<>();

        map.put("PROCESSING-PER-SEC", "" + getProcessing15MinuteAverage());
        map.put("PROCESSING-ERROR-PER-SEC", "" + getProcessingError15MinuteAverage());

        map.put("PROCESSING-1MIN", "" + getProcessingWithinLastMinute());
        map.put("PROCESSING-HOUR", "" + getProcessingInLastHour());
        map.put("PROCESSING-TODAY", "" + getProcessingToday());

        map.put("PROCESSING-ERROR-1MIN", "" + getProcessingErrorWithinLastMinute());
        map.put("PROCESSING-ERROR-HOUR", "" + getProcessingErrorInLastHour());
        map.put("PROCESSING-ERROR-TODAY", "" + getProcessingErrorToday());

        map.put("SINCE-LAST-PROCESSING", "" + getTimeOfLastProcessing());
        map.put("SINCE-LAST-PROCESSING-ERROR", "" + getTimeOfLastProcessingError());

        map.put("ASR-TODAY", "" + getASRToday().toPlainString());
        map.put("ASR-LAST-MIN", "" + getASRLastMinute().toPlainString());
        map.put("ASR-LAST-HOUR", "" + getASRLastHour().toPlainString());
        map.put("ASR-LAST-15-MIN-AVG", "" + getASRLast15MinuteAverage().toPlainString());

        return map;
    }

    public BigDecimal getASRToday() {
        return calculateASR(getProcessingErrorToday(), getProcessingToday());
    }

    public BigDecimal getASRLastMinute() {
        return calculateASR(getProcessingErrorWithinLastMinute(), getProcessingWithinLastMinute());
    }

    public BigDecimal getASRLastHour() {
        return calculateASR(getProcessingErrorInLastHour(), getProcessingInLastHour());
    }

    public BigDecimal getASRLast15MinuteAverage() {
        return calculateASR(getProcessingError15MinuteAverage(), getProcessing15MinuteAverage());
    }

    private static BigDecimal calculateASR(long errors, long totalProcessed) {
        BigDecimal errorsLastHour = BigDecimal.valueOf(errors);
        BigDecimal processed = BigDecimal.valueOf(totalProcessed);

        BigDecimal errorPercentage = BigDecimal.ZERO.compareTo(processed) == 0 ? BigDecimal.ZERO : errorsLastHour.divide(processed, 4, RoundingMode.HALF_UP);

        BigDecimal asr = BigDecimal.ONE.subtract(errorPercentage).multiply(BigDecimal.valueOf(100));

        return asr;
    }

    public long getProcessingWithinLastMinute() {
        return this.processingMinuteCount[0].get();
    }

    public long getProcessingErrorWithinLastMinute() {
        return this.processingErrorMinuteCount[0].get();
    }

    public long getProcessing15MinuteAverage() {
        long tot = 0;
        int slots = this.processingMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot.get())
            slots = this.highestUsedMinuteCountSlot.get() + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingError15MinuteAverage() {
        long tot = 0;
        int slots = this.processingErrorMinuteCount.length;
        if (slots > this.highestUsedMinuteCountSlot.get())
            slots = this.highestUsedMinuteCountSlot.get() + 1;
        for (int i = 0; i < slots; i++)
            tot += this.processingErrorMinuteCount[i].get();
        return tot / slots;
    }

    public long getProcessingInLastHour() {
        return this.processingInLastHour.get();
    }

    public long getProcessingErrorInLastHour() {
        return this.processingErrorInLastHour.get();
    }

    public long getProcessingToday() {
        return this.processingToday.get();
    }

    public long getProcessingErrorToday() {
        return this.processingErrorToday.get();
    }

    public long getTimeOfLastProcessing() {
        return this.timeOfLastProcessing != 0 ? System.currentTimeMillis() - this.timeOfLastProcessing : 0;
    }

    public long getTimeOfLastProcessingError() {
        return this.timeOfLastProcessingError != 0 ? System.currentTimeMillis() - this.timeOfLastProcessingError : 0;
    }
}
