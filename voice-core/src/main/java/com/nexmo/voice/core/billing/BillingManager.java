package com.nexmo.voice.core.billing;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.billing.exceptions.AccountBannedException;
import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeEvent;
import org.asteriskjava.manager.event.CdrEvent;
import com.nexmo.voice.core.billing.vquota.VQuotaService;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Objects;

/**
 * This class is responsible for the billing status updates.
 *
 * Threads management clarifications: (no problem here)
 * The BillingManager provides startCharging, stopCharging and delta methods.
 * The delta method (Updating the quota during the on-going call) is called by the UpdateQuotaTask,
 *    which is activated on a separate thread started on the server startup by QuotaUpdatesExecuter.
 *
 * The startCharging is called by the BridgeEventHandler - using its thread.
 * The stopCharging is called by the CDREventHandler - using its thread.
 */

public final class BillingManager {

    private static final Logger Log = LogManager.getLogger(BillingManager.class);


    //The voiceQuotaClient 
    private final QuotaClient voiceQuotaClient;

    public BillingManager(final QuotaClient voiceQuotaClient) {
        this.voiceQuotaClient = voiceQuotaClient;
    }

    //Called by BridgeEvents
    public void startCharging(final VoiceContext ctx, BridgeEvent event) throws QuotaInternalError, NotEnoughBalanceException, QuotaDisabledException, AccountsException {

        if (Log.isDebugEnabled())
            Log.debug("BillingManager: StartCharging using {}", ctx.getDebugString());

        //Both legs should start the charging at the same time, which is the time that the BridgeEvent arrived.
        long reportedStartTime;
        if (Objects.nonNull(event.getDateReceived())) {
            reportedStartTime = event.getDateReceived().getTime();
            if (Log.isDebugEnabled())
                Log.debug("{} bridgeEvent reportedStartTime from the event: {} ", ctx.getSessionId(), reportedStartTime);
        } else {
            reportedStartTime = System.currentTimeMillis();
            if (Log.isDebugEnabled())
                Log.debug("{} bridgeEvent reportedStartTime from the System: {} ", ctx.getSessionId(), reportedStartTime);
        }

        final BillingInfo billingInfo = ctx.getBillingInfo();
        final QuotaUpdateDetails quotaUpdateDetails = billingInfo.startCharging(ctx.getSessionId(), ctx.getConnectionId(), reportedStartTime);

        //Verify if we can start the charging - i.e. correct status and call price not zero.
        if (quotaUpdateDetails.shouldSkipQuotaUpdate()) {
            if (Log.isDebugEnabled())
                Log.debug("BillingManager startCharging result was SKIP QUOTA UPDATE. sessionId {} channelId {}", ctx.getSessionId(), ctx.getConnectionId());
            return;
        }

        // handle quota charge for the first time 
        // The reason for not calling voiceQuotaClient from inside the BillingInfo is that it is generating an http request
        // to the Quota Service, and we do not want to lock the BillingInfo for that long.
        // If it is locked for long, it is affecting the OTHER calls quota updates, as all the BillingInfo are scanned 
        // and if one is locked, all the following are waiting for it.  
        if (Core.getInstance().isAsyncQuotaFlag()) {
            consumeAsyncQuotaStartCharging(ctx, quotaUpdateDetails);

        } else {
            consumeQuotaStartCharging(ctx, billingInfo, quotaUpdateDetails, voiceQuotaClient);
        }
    }

    private static void consumeQuotaStartCharging(VoiceContext ctx,
                                                  BillingInfo billingInfo,
                                                  QuotaUpdateDetails quotaUpdateDetails,
                                                  QuotaClient voiceQuotaClient) throws QuotaInternalError, NotEnoughBalanceException, QuotaDisabledException, AccountsException {
        try {
            executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.CONSUME);

        } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} Start charging: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw new QuotaInternalError("Quota service is not working properly", ex);

        } catch (NotEnoughBalanceException ex) {
            billingInfo.setOutOfFundsStatusDuringStart(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} Start charging: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw ex;

        } catch (QuotaDisabledException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} Start charging: Failed to update the quota due to {}. If accountid {} is smoke tests account - this is OK ", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage(), ctx.getAccountId());
            throw ex;

        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} Start charging: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw new AccountsException("Accounts service issues: " + ex.getMessage());
        } finally {
            if (Log.isDebugEnabled())
                Log.debug("============  BillingManager startCharging AFTER consume ========== : {}", ctx.getDebugString());
        }
    }

    private void consumeAsyncQuotaStartCharging(VoiceContext ctx,
                                                QuotaUpdateDetails quotaUpdateDetails) {
        try {
            if (BillingInfo.Status.STARTED.equals(ctx.getBillingInfo().getStatus())) {
                QuotaItem item = new QuotaItem(ctx, QuotaItem.EventType.BRIDGE, QuotaUpdateDetails.Operation.CONSUME, quotaUpdateDetails.getAmount(), null, quotaUpdateDetails.getDuration());
                Core.getInstance().getQuotaQueueExecuter().enqueueItem(item);
            }
        } catch (Exception ex) {
            Log.error("AsyncQuota StartCharging Error occurred while queuing request:", ex);

        } finally {
            if (Log.isDebugEnabled())
                Log.debug("============AsyncQuota  BillingManager startCharging AFTER consume ========== : {}", ctx.getDebugString());
        }

    }

    //Called by CDREvents
    // The first thing to do is when stopping a call - immediately mark it as not-taking-part-in-quota-updates thread.
    // Then perform the final calculation and either consume or refund as needed.
    // Also, make sure that double refund cannot happen in any case.
    public void stopCharging(final VoiceContext ctx, final CdrEvent event, final CdrEventUserData eventUserData) {
        Log.info("{} {} Stop charging for account={} hashCode={}",
                ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), this.hashCode());

        final BillingInfo billingInfo = ctx.getBillingInfo();

        //Not sure about this: it is a left over from the old impl.- 
        //I think it is the very rare case, where the AGI request managed to create the first leg context
        //and the BridgeEvent had internal error and didnt managed to complete the second leg context. 
        //In such case, the second leg didnt charge, and there is no need to stop the charging.
        //It is here to make sure there is no N.P.E in such case.
        if (Objects.isNull(billingInfo)) {
            Log.error("Null billingInfo in VoiceContext {} for event{} ", ctx.getDebugString(), event);
            return;
        }

        //ASAP - mark the status as ENDED 
        billingInfo.setEndedStatus(ctx.getSessionId(), ctx.getConnectionId());

        //Calculate the end of call details
        final QuotaUpdateDetails quotaUpdateDetails = billingInfo.stopCharging(ctx.getSessionId(), ctx.getConnectionId(), event, eventUserData);

        if (Log.isDebugEnabled())
            Log.debug("{} {} stopCharging in BillingManager, the result is: {} ", ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails);

        if (Core.getInstance().isAsyncQuotaFlag()) {
            consumeAsyncQuotaStopCharging(ctx, quotaUpdateDetails, event);

        } else {
            consumeQuotaStopCharging(ctx, billingInfo, quotaUpdateDetails, voiceQuotaClient);
        }

    }

    private static void consumeQuotaStopCharging(VoiceContext ctx,
                                                 BillingInfo billingInfo,
                                                 QuotaUpdateDetails quotaUpdateDetails,
                                                 QuotaClient voiceQuotaClient) {
        if (quotaUpdateDetails.shouldSkipQuotaUpdate()) {
            Log.info("{} {} No further reconciliation is needed on stop charging for account={} hashCode={}",
                    ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), billingInfo.hashCode());
            return;
        }

        // handle the final quota reconciliation
        // we are expecting to REFUND, as we charged extra on the start.
        // Yet, it might be that additional CONSUM is needed.
        try {
            if (quotaUpdateDetails.shouldRefund()) {
                if (Log.isDebugEnabled())
                    Log.debug("{} {} stopCharging in BillingManager, FINAL REFUND {} ",
                            ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());

                executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.REFUND);
            } else {
                Log.info("{} {} stopCharging in BillingManager, FINAL CONSUME {} ",
                        ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());

                executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.CONSUME);
            }

            //If something wrong here, there is not much we can do - just log
            //the details, the call has already ended, and the charging has stopped
            //In very rare occasions, the call will end, additional CONSUM will be required, and this last
            //CONSUM will cause out-of-funds situation. - In such case the call is still completed in 200
        } catch (Exception ex) {
            Log.error("{} {} stop charging: Failed to update the quota with the final result of {} due to {}. VoiceContext: {}",
                    ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails, ex.getMessage(), ctx.getDebugString());
            ctx.addInternalFlag(CallInternalFlag.QUOTA_ISSUES_ON_CALL_FINAL_BILLING);
        }
    }

    private void consumeAsyncQuotaStopCharging(VoiceContext ctx,
                                               QuotaUpdateDetails quotaUpdateDetails, CdrEvent event) {
        QuotaItem item = null;
        Collection<VoiceContext> contextsInSession = Core.getInstance().getVoiceContextCache().
                getInnerValues(ctx.getSessionId());


        if (quotaUpdateDetails.shouldSkipQuotaUpdate()) {
            if (Log.isDebugEnabled())
                Log.debug("{} {} AsyncQuota No further reconciliation is needed on stop charging for account={} hashCode={}",
                    ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), this.hashCode());

            item = new QuotaItem(ctx, QuotaItem.EventType.SKIP_QUOTA, null, null, event, null);
            item.setVoiceContextSize(contextsInSession.size());
            Core.getInstance().getQuotaQueueExecuter().enqueueItem(item);
            return;
        }

        // handle the final quota reconciliation
        // we are expecting to REFUND, as we charged extra on the start.
        // Yet, it might be that additional CONSUME is needed.

        try {
            if (quotaUpdateDetails.shouldRefund()) {
                if (Log.isDebugEnabled())
                    Log.debug("{} {} stopCharging in BillingManager, FINAL REFUND {} ",
                            ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                item = new QuotaItem(ctx, QuotaItem.EventType.CDR, QuotaUpdateDetails.Operation.REFUND, quotaUpdateDetails.getAmount(), event, quotaUpdateDetails.getDuration());
                item.setVoiceContextSize(contextsInSession.size());
            } else {
                Log.info("{} {} stopCharging in BillingManager, FINAL CONSUME {} ",
                        ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                item = new QuotaItem(ctx, QuotaItem.EventType.CDR, QuotaUpdateDetails.Operation.CONSUME, quotaUpdateDetails.getAmount(), event, quotaUpdateDetails.getDuration());
                item.setVoiceContextSize(contextsInSession.size());
            }
            Core.getInstance().getQuotaQueueExecuter().enqueueItem(item);

            //If something wrong here, there is not much we can do - just log
            //the details, the call has already ended, and the charging has stopped
            //In very rare occasions, the call will end, additional CONSUM will be required, and this last
            //CONSUM will cause out-of-funds situation. - In such case the call is still completed in 200

        } catch (Exception ex) {
            Log.error("{} {} stop charging: Failed to update the quota with the final result of {} due to {}. VoiceContext: {}",
                    ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails, ex.getMessage(), ctx.getDebugString());
        }
    }

    /**
     * This method is used ONLY by the UpdateQuotaTask.
     * Stopping the call from Asterisk point of view will generate CDR etc - this will happened in the UpdateQuotaTask.
     * here just make sure to update the BillingInfo status correctly.
     * @throws QuotaDisabledException 
     */
    public void delta(VoiceContext ctx) throws QuotaInternalError, NotEnoughBalanceException, AccountBannedException, AccountsException, QuotaDisabledException {
        if (Log.isTraceEnabled())
            Log.trace("Calling delta of the BillingManager for: " + ctx.getDebugString());

        final BillingInfo billingInfo = ctx.getBillingInfo();
        final QuotaUpdateDetails quotaUpdateDetails = billingInfo.delta(ctx.getSessionId(), ctx.getConnectionId());
        if (Log.isTraceEnabled())
            Log.trace("Delta for {} with {}", ctx.getDebugString(), quotaUpdateDetails);

        //Quota updates are required only every x seconds, but we check it every 1 second, so there will be many times
        //that quota update should be skipped.
        if (quotaUpdateDetails.shouldSkipQuotaUpdate())
            return;

        // handle quota update
        if (Core.getInstance().isAsyncQuotaFlag()) {
            if (!ctx.isCdrEventArrived() && (BillingInfo.Status.STARTED.equals(ctx.getBillingInfo().getStatus()))) {
                consumeAsyncQuota(ctx, quotaUpdateDetails);
            }
        } else {
            consumeQuota(ctx, billingInfo, quotaUpdateDetails, voiceQuotaClient);
        }

        //handle banned account
        assertAccountNotBanned(ctx.getSessionId(), ctx.getAccountId());
    }


    // Called during emergency shutdown
    // The first thing to do is when stopping a call - immediately mark it as not-taking-part-in-quota-updates thread.
    // Then perform the final calculation and either consume or refund as needed.
    // Also, make sure that double refund cannot happen in any case.
    public boolean emergencyStopCharging(final VoiceContext ctx) {
        Log.info("{} {} Emergency Stop charging for account={} hashCode={}",
                ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), this.hashCode());

        final BillingInfo billingInfo = ctx.getBillingInfo();

        //Not sure about this: it is a left over from the old impl.- 
        //I think it is the very rare case, where the AGI request managed to create the first leg context
        //and the BridgeEvent had internal error and didnt managed to complete the second leg context. 
        //In such case, the second leg didnt charge, and there is no need to stop the charging.
        //It is here to make sure there is no N.P.E in such case.
        if (Objects.isNull(billingInfo)) {
            Log.info("Emergency Stop charging: Null billingInfo in VoiceContext {} ", ctx.getDebugString());
            return false;
        }
        //ASAP - mark the status as EMERGENCY-ENDED 
        //Handle only calls which not started or on-going. i.e ignore all those which are in the process of ending or already
        //in error of any kind.
        //calls which are in no_charges status are calls with price zero. Yet, their correct end time is important as it affects the cost. 
        //(It is possible to have calls with price zero and cost > zero . This is due to special deals with customers.)
        if (!billingInfo.shouldHandleEmergencyStopCharging(ctx.getSessionId(), ctx.getConnectionId())) {
            Log.info("{} {} skip emergencyStopCharging in BillingManager. Current billing status: {} ", ctx.getSessionId(), ctx.getConnectionId(), billingInfo.getStatus());
            return false;
        }

        //All reasons not to handle the emergency stop call are verified.
        //If we are here - we need to do it

        //Calculate the end of call details
        final QuotaUpdateDetails quotaUpdateDetails = billingInfo.emergencyStopCharging(ctx.getSessionId(), ctx.getConnectionId());

        if (Log.isDebugEnabled())
            Log.debug("{} {} emergencyStopCharging in BillingManager, the result is: {} ", ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails);

        if (Core.getInstance().isAsyncQuotaFlag()) {
            asyncHandleCallFinalBilling(ctx, quotaUpdateDetails);

        } else {
            handleCallFinalBilling(ctx, quotaUpdateDetails);
        }
        return true; // Yup - we did perform emergency stop charging on this call
    }

    private void asyncHandleCallFinalBilling(final VoiceContext ctx, final QuotaUpdateDetails quotaUpdateDetails) {
        // Verify if final quota reconciliation is needed
        if (quotaUpdateDetails.shouldSkipQuotaUpdate()) {
            Log.info(
                    "{} {} AsyncQuota emergencyStopCharging No further reconciliation is needed on emergency stop charging for account={} hashCode={}",
                    ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), this.hashCode());
            return;
        }

        // handle the final quota reconciliation
        // we are expecting to REFUND as we charged extra on the start, but might also be CONSUME
        QuotaItem item = null;
        try {
            if (quotaUpdateDetails.shouldRefund()) {
                Log.info("{} {} emergencyStopCharging in BillingManager, FINAL REFUND {} ", ctx.getSessionId(),
                        ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                item = new QuotaItem(ctx, QuotaItem.EventType.HANDLE_FINAL_CALL, QuotaUpdateDetails.Operation.REFUND, quotaUpdateDetails.getAmount(), null, quotaUpdateDetails.getDuration());
            } else {
                Log.info("{} {} emergencyStopCharging in BillingManager, FINAL CONSUME {} ", ctx.getSessionId(),
                        ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                item = new QuotaItem(ctx, QuotaItem.EventType.HANDLE_FINAL_CALL, QuotaUpdateDetails.Operation.CONSUME, quotaUpdateDetails.getAmount(), null, quotaUpdateDetails.getDuration());

            }
            Core.getInstance().getQuotaQueueExecuter().enqueueItem(item);

            // If something wrong here, there is not much we can do - just log
            // the details, the call has already ended, and the charging has stopped.
            // In very rare occasions, the call will end, additional CONSUM will be
            // required, and this last
            // CONSUM will cause out-of-funds situation. - In such case the call is still
            // completed in 200
        } catch (Exception ex) {
            Log.error(
                    "{} {} emergencyStopCharging: Failed to update the quota with the final result of {} due to {}. VoiceContext: {}",
                    ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails, ex.getMessage(),
                    ctx.getDebugString());
        }
    }

    private void handleCallFinalBilling(final VoiceContext ctx, final QuotaUpdateDetails quotaUpdateDetails) {
        // Verify if final quota reconciliation is needed
        if (quotaUpdateDetails.shouldSkipQuotaUpdate()) {
            Log.info(
                    "{} {} emergencyStopCharging No further reconciliation is needed on emergency stop charging for account={} hashCode={}",
                    ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), this.hashCode());
            return;
        }

        // handle the final quota reconciliation
        // we are expecting to REFUND as we charged extra on the start, but might also be CONSUM
        try {
            if (quotaUpdateDetails.shouldRefund()) {
                Log.info("{} {} emergencyStopCharging in BillingManager, FINAL REFUND {} ", ctx.getSessionId(),
                        ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.REFUND);
            } else {
                Log.info("{} {} emergencyStopCharging in BillingManager, FINAL CONSUME {} ", ctx.getSessionId(),
                        ctx.getConnectionId(), quotaUpdateDetails.getAmount().toPlainString());
                executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.CONSUME);
            }

            // If something wrong here, there is not much we can do - just log
            // the details, the call has already ended, and the charging has stopped.
            // In very rare occasions, the call will end, additional CONSUM will be
            // required, and this last
            // CONSUM will cause out-of-funds situation. - In such case the call is still
            // completed in 200
        } catch (Exception ex) {
            Log.error(
                    "{} {} emergencyStopCharging: Failed to update the quota with the final result of {} due to {}. VoiceContext: {}",
                    ctx.getSessionId(), ctx.getConnectionId(), quotaUpdateDetails, ex.getMessage(),
                    ctx.getDebugString());
        }
    }

    private static void consumeQuota(VoiceContext ctx,
                                     BillingInfo billingInfo,
                                     QuotaUpdateDetails quotaUpdateDetails,
                                     QuotaClient voiceQuotaClient) throws QuotaInternalError, NotEnoughBalanceException, QuotaDisabledException, AccountsException {
        try {
            executeQuotaAction(ctx, voiceQuotaClient, quotaUpdateDetails, QuotaUpdateDetails.Operation.CONSUME);
            if (Log.isTraceEnabled())
                Log.trace("Delta for {} with {} consumed successfully", ctx.getAccountId(), quotaUpdateDetails);

        } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} delta: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw new QuotaInternalError("Quota service is not working properly: "+ex.getMessage());

        } catch (NotEnoughBalanceException ex) {
            billingInfo.setOutOfFundsStatusDuringCall(ctx.getSessionId(), ctx.getConnectionId());
            Log.info("{} {} delta: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw ex;

        } catch (QuotaDisabledException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} delta: Failed to update the quota due to {}. If accountid {} is smoke tests account - this is OK ", ctx
                    .getSessionId(), ctx.getConnectionId(), ex.getMessage(), ctx.getAccountId());
            throw ex;
        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException ex) {
            billingInfo.setErrorStatus(ctx.getSessionId(), ctx.getConnectionId());
            Log.error("{} {} delta: Failed to update the quota due to {}", ctx.getSessionId(), ctx.getConnectionId(), ex.getMessage());
            throw new AccountsException("Accounts service issues: "+ex.getMessage());
        }
    }

    private void consumeAsyncQuota(VoiceContext ctx,
                                   QuotaUpdateDetails quotaUpdateDetails) {
        try {
            QuotaItem item = null;
            if (quotaUpdateDetails.shouldRefund()) {
                item = new QuotaItem(ctx, QuotaItem.EventType.DELTA, QuotaUpdateDetails.Operation.REFUND, quotaUpdateDetails.getAmount(), null, quotaUpdateDetails.getDuration());
            } else {
                item = new QuotaItem(ctx, QuotaItem.EventType.DELTA, QuotaUpdateDetails.Operation.CONSUME, quotaUpdateDetails.getAmount(), null, quotaUpdateDetails.getDuration());
            }
            Core.getInstance().getQuotaQueueExecuter().enqueueItem(item);

        } catch (Exception ex) {
            Log.error("AsyncQuota Error occurred while queuing request:", ex);

        }
    }

    private static void assertAccountNotBanned(String sessionId, String accountId) throws AccountsException, AccountBannedException {
        SmppAccount account = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
        } catch (AccountsException e) {
            Log.error("{} Could not retrieve account {} due to {}", sessionId, accountId, e.getMessage());
            throw e;
        }
        
        if (account == null) {
            Log.error("{} account {} not found", sessionId, accountId);
            throw new AccountsException(String.format("Couldn't find account %s", accountId));
        }
        
        if (Log.isDebugEnabled())
            Log.debug("{} Account found for accountId {}, account ban status is {}", sessionId, accountId, account.isBanned());
        
        if (account.isBanned()) {
            throw new AccountBannedException(String.format("Account %s has been banned", accountId));
        }

    }

    private static void executeQuotaAction(VoiceContext vctx, QuotaClient voiceQuotaClient, QuotaUpdateDetails quotaUpdateDetails,
                                           QuotaUpdateDetails.Operation cmd)
            throws QuotaUnderMaintenanceException, IllegalOperationOnSubAccountException, QuotaDisabledException, QuotaException,
            AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        if (vctx.isVpricingEnabled()) {
            VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
            PriceImpactApiSuccessResponse resp = vQuotaService.invokePriceImpactApi(vctx, quotaUpdateDetails.getDuration(), cmd);
            if (resp != null) {
                updateVoiceContext(vctx, resp, cmd, quotaUpdateDetails.getDuration());
            }
        } else {
            if(QuotaUpdateDetails.Operation.CONSUME == cmd) {
                voiceQuotaClient.consume(vctx, quotaUpdateDetails.getAmount(), vctx.getQuotaRef(), vctx.getConnectionId());
            } else if (QuotaUpdateDetails.Operation.REFUND == cmd) {
                voiceQuotaClient.refund(vctx, quotaUpdateDetails.getAmount(), vctx.getQuotaRef(), vctx.getConnectionId());
            }
        }
    }

    private static void updateVoiceContext(VoiceContext vctx, PriceImpactApiSuccessResponse resp, QuotaUpdateDetails.Operation cmd, Long duration) {
        if(resp.getEstimatedPriceImpact() != null) {
            if(QuotaUpdateDetails.Operation.CONSUME == cmd) {
                vctx.getBillingInfo().updateTotalEstimatedPriceImpact(vctx.getBillingInfo().getTotalEstimatedPriceImpact().add(resp.getEstimatedPriceImpact())); //Add the estimated-price-impact for consume
            } else if (QuotaUpdateDetails.Operation.REFUND == cmd) {
                vctx.getBillingInfo().updateTotalEstimatedPriceImpact(vctx.getBillingInfo().getTotalEstimatedPriceImpact().subtract(resp.getEstimatedPriceImpact())); //Subtract the estimated-price-impact for refund
            }

            // Set the estimated call price per minute.
            // If the call price per second is zero (which may occur due to vQuota being down),
            // update the price based on the response and item duration.
            if(BigDecimal.ZERO.compareTo(vctx.getBillingInfo().getEstimatedCallPricePerSecond()) == 0) {
                vctx.getBillingInfo().setEstimatedCallPricePerSecond(resp.getEstimatedPriceImpact(), duration, vctx.getSessionId());
            }
        }
        vctx.setMediationCdrField(resp.getMediationCdrField());
    }
}