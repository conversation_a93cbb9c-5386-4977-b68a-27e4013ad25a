package com.nexmo.voice.core.types;

import com.thepeachbeetle.common.callback.types.CallbackMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.defaultIfEmpty;


public class TTSContext implements Serializable {

    private final static Logger LOG = LogManager.getLogger(TTSContext.class);

    public final String ttsXTRaceId;
    public final String accountId;
    public final String clientReference;
    public final String repeat;
    public final String machineDetectionType;
    public final String machineTimeout;

    public String callbackUrl;
    public CallbackMethod callbackMethod;

    public final String languageName;
    public final String mbStyle;

    private TTSContext(String ttsXTRaceId,
                       String accountId,
                       String clientReference,
                       String repeat,
                       String machineDetectionType,
                       String machineTimeout,
                       String callbackUrl,
                       CallbackMethod callbackMethod,
                       String languageName,
                       String mbStyle) {
        this.ttsXTRaceId = ttsXTRaceId;
        this.accountId = accountId;
        this.clientReference = clientReference;
        this.repeat = repeat;
        this.machineDetectionType = machineDetectionType;
        this.machineTimeout = machineTimeout;
        this.callbackUrl = callbackUrl;
        this.callbackMethod = callbackMethod;
        this.languageName = languageName;
        this.mbStyle = mbStyle;
    }

    public static TTSContextBuilder builder() {
        return new TTSContextBuilder();
    }

    public static TTSContext parse(String sessionId, String ttsDetails) throws UnsupportedEncodingException {
        String[] details = ttsDetails.split(";");
        if (details.length == 9) {
            TTSContextBuilder ttsContextBuilder = builder()
                    .setTtsXTRaceId(defaultIfEmpty(details[0], null))
                    .setClientReference(defaultIfEmpty(details[3], null))
                    .setRepeat(defaultIfEmpty(details[4], null))
                    .setMachineDetectionType(defaultIfEmpty(details[5], null))
                    .setMachineTimeout(defaultIfEmpty(details[6], null))
                    .setLanguageName(defaultIfEmpty(details[7], null))
                    .setMbStyle(defaultIfEmpty(details[8], null));
            if (StringUtils.isNotEmpty(details[1])) {
                ttsContextBuilder.setCallbackUrl(URLDecoder.decode(details[1], StandardCharsets.UTF_8.toString()));
            }
            if (StringUtils.isNotEmpty(details[2])) {
                ttsContextBuilder.setCallbackMethod(CallbackMethod.valueOf(details[2]));
            }
            TTSContext context = ttsContextBuilder.build();
            if (LOG.isDebugEnabled()) {
                LOG.debug("{} :  Deserialized {} to {}", sessionId, ttsDetails, context);
            }
            return context;
        }
        LOG.error("{} : Cant deserialize {} because {} is not length of 9 ", ttsDetails, Arrays.toString(details));
        throw new IllegalArgumentException("Failed to deserialize tts context details, length not equals to 9");
    }

    public void populateParams(Map<String, String> params) {
        params.put("BACKEND", "MB");
    }

    public void setCallbackUrl(String value) {
        this.callbackUrl = value;
    }

    public void setCallbackMethod(CallbackMethod value) {
        this.callbackMethod = value;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public CallbackMethod getCallbackMethod() {
        return this.callbackMethod;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("TTSContext [");
        sb.append("ttsXTRaceId=").append(ttsXTRaceId);
        sb.append(", accountId=").append(accountId);
        sb.append(", clientReference=").append(clientReference);
        sb.append(", repeat=").append(repeat);
        sb.append(", machineDetectionType=").append(machineDetectionType);
        sb.append(", machineTimeout=").append(machineTimeout);
        sb.append(", callbackUrl=").append(callbackUrl);
        sb.append(", callbackMethod=").append(callbackMethod);
        sb.append(", languageName=").append(languageName);
        sb.append(", mbStyle=").append(mbStyle);
        sb.append(']');
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TTSContext that = (TTSContext) o;
        return Objects.equals(ttsXTRaceId, that.ttsXTRaceId) &&
                Objects.equals(accountId, that.accountId) &&
                Objects.equals(clientReference, that.clientReference) &&
                Objects.equals(repeat, that.repeat) &&
                Objects.equals(machineDetectionType, that.machineDetectionType) &&
                Objects.equals(machineTimeout, that.machineTimeout) &&
                Objects.equals(callbackUrl, that.callbackUrl) &&
                callbackMethod == that.callbackMethod &&
                Objects.equals(languageName, that.languageName) &&
                Objects.equals(mbStyle, that.mbStyle);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ttsXTRaceId);
    }

    public static class TTSContextBuilder {
        private String ttsXTRaceId;
        private String accountId;
        private String clientReference;
        private String repeat;
        private String machineDetectionType;
        private String machineTimeout;
        private String callbackUrl;
        private CallbackMethod callbackMethod;
        private String languageName;
        private String mbStyle;

        public TTSContextBuilder setTtsXTRaceId(String ttsXTRaceId) {
            this.ttsXTRaceId = ttsXTRaceId;
            return this;
        }

        public TTSContextBuilder setAccountId(String accountId) {
            this.accountId = accountId;
            return this;
        }

        public TTSContextBuilder setClientReference(String clientReference) {
            this.clientReference = clientReference;
            return this;
        }

        public TTSContextBuilder setRepeat(String repeat) {
            this.repeat = repeat;
            return this;
        }

        public TTSContextBuilder setMachineDetectionType(String machineDetectionType) {
            this.machineDetectionType = machineDetectionType;
            return this;
        }

        public TTSContextBuilder setMachineTimeout(String machineTimeout) {
            this.machineTimeout = machineTimeout;
            return this;
        }

        public TTSContextBuilder setCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
            return this;
        }

        public TTSContextBuilder setCallbackMethod(CallbackMethod callbackMethod) {
            this.callbackMethod = callbackMethod;
            return this;
        }

        public TTSContextBuilder setLanguageName(String languageName) {
            this.languageName = languageName;
            return this;
        }

        public TTSContextBuilder setMbStyle(String mbStyle) {
            this.mbStyle = mbStyle;
            return this;
        }

        public TTSContext build() {
            return new TTSContext(ttsXTRaceId, accountId, clientReference, repeat, machineDetectionType, machineTimeout,
                    callbackUrl, callbackMethod, languageName, mbStyle);
        }
    }
}
