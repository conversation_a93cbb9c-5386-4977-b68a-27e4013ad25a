package com.nexmo.voice.core.cache;

import java.io.Serializable;
import java.util.Map;


public abstract class ApplicationContext implements Serializable {

    private static final long serialVersionUID = -4289947586223984295L;

    protected String parentSessionId;
    protected String parentConnectionId;

    public abstract void populateParams(Map<String, String> requiredParams);

    public abstract Map<String, String> getCallbackParams();

    public abstract Map<String, String> getCCXMLParams();

    public String getText() {
        return null;
    }

    public void setParentSessionId(String parentSessionId) {
        this.parentSessionId = parentSessionId;
    }

    public void setParentConnectionId(String parentConnectionId) {
        this.parentConnectionId = parentConnectionId;
    }

    public abstract String getDebugString();

}
