package com.nexmo.voice.core.cdr;

import java.util.LinkedHashMap;

public class CDRData {
    // I dont think we can ever change it, even if we change the class name, I think
    // the other systems are relaying on this at some later stage
    public final static String CDR_CLASS_NAME = "com.nexmo.voice.core.cache.VoiceContext";

    private long cdrCreationTimestamp;
    private final LinkedHashMap<String, String> keyValuePairs;

    public CDRData(LinkedHashMap<String, String> keyValuePairs) {
        this.cdrCreationTimestamp = System.currentTimeMillis();
        this.keyValuePairs = keyValuePairs;
    }

    public long getCdrCreationTimestamp() {
        return cdrCreationTimestamp;
    }

    public LinkedHashMap<String, String> getKeyValuePairs() {
        return keyValuePairs;
    }
    
}
