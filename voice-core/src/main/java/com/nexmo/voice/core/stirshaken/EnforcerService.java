package com.nexmo.voice.core.stirshaken;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

public interface EnforcerService {


    Set<String> STIR_SHAKEN_DESTINATIONS = ImmutableSet.of("US", "CA");

    Attestation attestationLevel(AttestationValidationParams attestationValidationParams);

    default boolean excludedGateway(String gatewayName) {
        return gatewayName == null || !gatewayName.startsWith("vonage");
    }

    default boolean excludedDestination(String destinationCountry) {
        return destinationCountry == null || !STIR_SHAKEN_DESTINATIONS.contains(destinationCountry);
    }


}
