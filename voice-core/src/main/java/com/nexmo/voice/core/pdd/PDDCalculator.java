package com.nexmo.voice.core.pdd;

import java.util.concurrent.TimeUnit;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;

//Tally - get rid of this - take the PDD from the CDR event  - one more cache with threads we do not need
// that should be  handled as part of the Asterisk upgrade. - Can be done as part of the upgrade to Asterisk 16.

/**
 * Created on 15/01/15.
 *
 * <AUTHOR>
 */
public class PDDCalculator implements RemovalListener<String, Long> {

    private static final Logger Log = LogManager.getLogger(PDDCalculator.class);

    private final Cache<String, Long> startTimes;
    private final Cache<String, Long> endTimes;

    public PDDCalculator(long duration) {
        this.startTimes = CacheBuilder.newBuilder()
                                      .expireAfterWrite(duration, TimeUnit.MILLISECONDS)
                                      .removalListener(this)
                                      .build();

        this.endTimes = CacheBuilder.newBuilder()
                                    .expireAfterWrite(duration, TimeUnit.MILLISECONDS)
                                    .removalListener(this)
                                    .build();

    }

    public void storeStartTime(String id, long time) throws PDDCalculationException {
        if (Log.isTraceEnabled())
            Log.trace("TRACE ::: STORING PDD start time " + time + " for connection id " + id);

        if (id == null)
            throw new PDDCalculationException("PDD cannot be calculated: cannot store start time with null id");

        this.startTimes.asMap().putIfAbsent(id, time);
    }

    public void storeEndTime(String id, long time) throws PDDCalculationException {
        if (Log.isTraceEnabled())
            Log.trace("TRACE ::: STORING PDD end time " + time + " for connection id " + id);

        if (id == null)
            throw new PDDCalculationException("PDD cannot be calculated: cannot store end time with null id");

        this.endTimes.asMap().putIfAbsent(id, time);
    }

    public Long calculate(String idStartTime, String idEndTime) throws PDDCalculationException {
        if (Log.isDebugEnabled())
            Log.debug("TRACE ::: CALCULATION PDD start time id " + idStartTime + " and end time id " + idEndTime);

        if (idStartTime == null || idEndTime == null)
            throw new PDDCalculationException("PDD cannot be calculated: cannot calculate pdd with null id");

        final Long startime = this.startTimes.getIfPresent(idStartTime);
        final Long endTime = this.endTimes.getIfPresent(idEndTime);

        if (startime == null || endTime == null) {
            if (Log.isDebugEnabled())
                Log.debug("TRACE ::: ERROR CALCULATING PDD WITH TIME NULL -> STAT DATE=" + startime + " END DATE= " + endTime);
            return 0L;
        }

        return endTime - startime;
    }

    @Override
    public void onRemoval(RemovalNotification<String, Long> notification) {

        if (notification.getCause() == RemovalCause.EXPIRED)
            if (Log.isDebugEnabled())
                Log.debug("TRACE ::: EXPIRED PDD time " + notification.getValue() + " for connection id " + notification.getKey());

    }

}
