package com.nexmo.voice.core.jmx;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.Shutdown;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.nexmo.voice.core.gateway.asterisk.AsteriskAMIProcessor.handleDirtyShutdown;
import static com.nexmo.voice.core.types.CallInternalFlag.TERMINATED_DURING_SHUTDOWN;

public class ShutdownJMX implements ShutdownJMXBean {

    private static final Logger Log = LogManager.getLogger(ShutdownJMXBean.class);

    @Override
    public String shutdown() {
        Log.info("Shutdown Command Received ");
        if (Core.getInstance().startFullShutdown()) {
            final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
                final Thread thread = new Thread(r);
                thread.setName("shutdown-thread");
                return thread;
            });
            executorService.execute(() -> {
                Log.info("FULL SHUTDOWN STARTED");
                int i = 1;
                waitForDirtyShutdown();
                while (cacheNotEmpty()) {
                    Log.info("Handling shutdown {} times", ++i);
                    waitForDirtyShutdown();
                }
                Shutdown.getInstance().shutdown("Triggered Shutdown");
            });
            return "SHUTDOWN SUCCESSFULLY STARTED";
        }
        Log.warn("Duplicate Command to shutdown rejected");
        return "SHUTDOWN ALREADY IN PROGRESS";

    }

    private static boolean cacheNotEmpty(){
        sleep();
        return Core.getInstance().getVoiceContextCache().outerSize() > 0;
    }

    private static void waitForDirtyShutdown(){
        try {
            handleDirtyShutdown(TERMINATED_DURING_SHUTDOWN).get();
        } catch (InterruptedException | ExecutionException e) {
            Log.warn("Failed to wait for dirty shutdown ", e);
        }
    }

    private static void sleep(){
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Log.warn("Sleep was interrupted while sleeping due to", e);
        }
    }
}
