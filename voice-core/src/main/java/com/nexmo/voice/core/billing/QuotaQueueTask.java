package com.nexmo.voice.core.billing;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.nexmo.voice.core.billing.vquota.VQuotaService;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse;
import com.nexmo.voice.core.cache.*;
import com.nexmo.voice.core.gateway.asterisk.AsteriskActionIssuer;
import com.nexmo.voice.core.logger.SIPAppLogger;
import com.nexmo.voice.core.sip.event.BridgeEventHandler;
import com.nexmo.voice.core.sip.event.CdrEventHandler;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.types.*;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import io.prometheus.client.Counter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

public class QuotaQueueTask implements Runnable {


    private static final Logger Log = LogManager.getLogger(QuotaQueueTask.class);
    public QuotaUpdateTask.VoiceApplicationType voiceApplicationType;
    private static final CdrEventHandler cdrEventHandler = new CdrEventHandler();
    private final QuotaClient voiceQuotaClient = Core.getInstance().getQuotaClient();
    private final ConcurrentLinkedQueue<QuotaItem> quotaQueue;
    private static final int maxItemProcessedSize = 500;
    private static final Counter QUOTA_QUEUE_BRIDGE_FAILURE = Counter.build().name("quota_queue_bridge_failure").labelNames("queue_bridge_failure").help(" ").register();
    private Map<String, QuotaItem> quotaItemMap;
    private static final Counter QUOTA_ITEM_DELETION_ACCOUNT_COUNTER = Counter.build().name("deleted_quota_items_by_account_total").help("Total number of accounts for whom the QuotaItems were deleted from the async-quota queue.").register();


    public QuotaQueueTask(ConcurrentLinkedQueue<QuotaItem> queue, QuotaUpdateTask.VoiceApplicationType voiceApplicationType) {
        this.quotaQueue = queue;
        this.voiceApplicationType = voiceApplicationType;
    }

    @Override
    public void run() {

        // if there is previous thread running don't do another processing
        final AtomicBoolean executed = new AtomicBoolean(false);
        if (executed.compareAndSet(false, true)) {
            try {
                int itemsProcessedCount = 0;
                //Map to store all quota items based on the quotareferenceid key
                quotaItemMap = new HashMap<>();
                //Logic for the concurrent queue that runs every 1 second starts here
                //All eligible voice contexts that's eligible for making a call to Quota API are processed here based on the Event type--Bridge/Delta/CDR
                for (QuotaItem item : quotaQueue) {
                    if (item != null && item.getEventType() != null && item.getVoiceContext() != null && this.voiceApplicationType.getVoiceProduct().getDescriptor().equals(item.getVoiceContext().getProductClass())) {
                        itemsProcessedCount++;
                        if(item.getVoiceContext().isVpricingEnabled()) {
                            // Skip if item is marked for removal
                            if (item.isMarkedForSkip()) {
                                Log.error("Skipping the QuotaItem with session-ID: {}, event-type: {}, operation: {}, duration: {} to execute any further Quota actions",
                                        item.getVoiceContext().getSessionId(), item.getEventType(), item.getOperation(), item.getDuration());
                                continue;
                            }
                        }
                        if (QuotaItem.EventType.BRIDGE.equals(item.getEventType())) {
                            //Calls whose event type is BRIDGE
                            invokeBridgeQuotaApi(item);

                        } else if (QuotaItem.EventType.DELTA.equals(item.getEventType())) {
                            //Calls in DELTA Event status
                            String quotaReferenceId = item.getVoiceContext().getQuotaRef();
                            if (quotaItemMap.containsKey(quotaReferenceId)) {
                                //Basic idea here is: Add Quotaitem details if its not avaialble in QuotaItemMap, or else aggregate the amount of this new item to the item
                                //already present in queue and then remove the new item; so that we dont charge multiple times.Any charged QuotaItem should not be re-charged again.

                                //If quotaRefId for QuotaItem is already present in the map, and if this item is already failed previous, due to Quota being down, skip and do nothing
                                //since we already have charged for that duration in our Voicecontext BillingInfo
                                //If that's not the case add the amount present in this new QuotaItem object to the anount that's already present in existing quotaReferenceId
                                QuotaItem quotaItem = quotaItemMap.get(quotaReferenceId);
                                if (quotaItem.getQuotaExceptionCounter() > 1) {
                                    if (Log.isDebugEnabled())
                                        Log.debug("Skipping adding amount:{} for quotaReferenceId {}, since its already failed", item.getAmount(), quotaReferenceId);
                                } else {
                                    quotaItem.setAmount(item.getAmount().add(quotaItem.getAmount()));
                                    quotaItem.setDuration(item.getDuration() + quotaItem.getDuration());
                                    if (SIPAppLogger.concatInternalFlags(item.getVoiceContext().getInternalFlags()).contains("06")) {
                                        quotaItem.getClonedContext().setNegativeBalance(true);
                                    }

                                    quotaQueue.remove(item);
                                }
                            } else {
                                //Add Quotaitem details if its not avaialble in QuotaItemMap
                                quotaItemMap.put(item.getVoiceContext().getQuotaRef(), item);
                            }

                        } else {
                            //Calls are in the ended status here; so invoke the logic needed to make the final reconciliation to Quota and generate cdr
                            invokeCdrQuotaAPI(item);
                        }
                        if (itemsProcessedCount >= maxItemProcessedSize) break;
                    }
                }

                invokeDeltaQuotaApi(quotaItemMap);

                // After processing, remove all marked items
                removeMarkedItemsFromQueue();

            } catch (Exception ex) {
                Log.error("Error from async Quota Queue Consumer thread", ex);
            }
        }
    }

    private void removeMarkedItemsFromQueue() {
        quotaQueue.removeIf(this::isItemMarkedForRemoval);
    }

    private boolean isItemMarkedForRemoval(QuotaItem item) {
        if (item.isMarkedForSkip()) {
            Log.error("Deleting QuotaItem from async-quota queue with sessionID: {}, event-type: {}, operation: {}, and duration: {}",
                    item.getVoiceContext().getSessionId(), item.getEventType(), item.getOperation(), item.getDuration());
            return true; // item will be removed
        }
        return false; // item will not be removed
    }


    private void invokeDeltaQuotaApi(Map<String, QuotaItem> itemMap) {
        for (Map.Entry<String, QuotaItem> entry : itemMap.entrySet()) {
            QuotaItem item = entry.getValue();

            VoiceContext vctx = item.getVoiceContext();
            VoiceContext clonedCtx = item.getClonedContext();
            try {
                processQuotaItem(vctx, clonedCtx, item, QuotaUpdateDetails.Operation.CONSUME);
                quotaQueue.remove(item);

            } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException ex) {
                Log.warn("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());
                vctx.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
                item.setQuotaExceptionCounter(item.getQuotaExceptionCounter() + 1);

            } catch (NotEnoughBalanceException ex) {
                Log.error("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());

                //If the user runs out of money and the call is past Bridge event:
                //Set NegativeBalance property to true in case Quota was previously down
                //If CDR Event has not yet arrived, that means call is still ongoing, so set the appropriate flags and terminate call.
                if (StringUtils.isNotBlank(SIPAppLogger.concatInternalFlags(vctx.getInternalFlags()))
                        && SIPAppLogger.concatInternalFlags(vctx.getInternalFlags()).contains("06")) {
                    clonedCtx.setNegativeBalance(true);
                    if (!vctx.isCdrEventArrived()) {
                        vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_OUT_OF_MONEY);
                        handleError(vctx, SIPCode.PAYMENT_REQUIRED);

                    }

                } else {

                    //added new to handle delta failing to charge extra 6 seconds, after call terminated.
                    vctx.getBillingInfo().setOutOfFundsStatusDuringCall(vctx.getSessionId(), vctx.getConnectionId());
                    if (!vctx.isCdrEventArrived()) {
                        quotaQueue.remove(item);
                        vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_OUT_OF_MONEY);
                        handleError(vctx, SIPCode.PAYMENT_REQUIRED);
                    }
                }

            } catch (QuotaDisabledException ex) {
                quotaQueue.remove(item);
                vctx.getBillingInfo().setErrorStatus(vctx.getSessionId(), vctx.getConnectionId());
                Log.error("{} {} charging: Failed to update the quota due to {}. If accountid {} is smoke tests account - this is OK ", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage(), vctx.getAccountId());
                vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_QUOTA_DISABLED);
                handleError(vctx, SIPCode.SERVER_INTERNAL_ERROR);
            } catch (AccountNotFoundException | IllegalOperationOnSubAccountException ex) {
                quotaQueue.remove(item);
                vctx.getBillingInfo().setErrorStatus(vctx.getSessionId(), vctx.getConnectionId());
                Log.error("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());
                vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_ACCOUNT_ISSUES);
                handleError(vctx, SIPCode.SERVER_INTERNAL_ERROR);
            } catch (Exception e) {
                quotaQueue.remove(item);
                Log.error("Internal error while attempting to start charging session for " + vctx.getSessionId() + " Notify Astrisk to end the call  due to: ", e);
            }
        }

    }


    private void invokeBridgeQuotaApi(QuotaItem item) {
        VoiceContext vctx = item.getVoiceContext();
        VoiceContext clonedCtx = item.getClonedContext();
        try {
            processQuotaItem(vctx, clonedCtx, item, QuotaUpdateDetails.Operation.CONSUME);
            quotaQueue.remove(item);

        } catch (QuotaException | QuotaUnderMaintenanceException | NegativeBalanceRefundException ex) {
            Log.warn("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());
            vctx.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
            item.setBridgeExceptionCounter(item.getBridgeExceptionCounter() + 1);
            if (item.getBridgeExceptionCounter() == 1) {
                CompletableFuture.runAsync(() -> {
                    QUOTA_QUEUE_BRIDGE_FAILURE.labels("bridge_failure").inc();
                });
            }

        } catch (NotEnoughBalanceException ex) {
            Log.error("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());
            vctx.addInternalFlag(CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);

            //If the user runs out of money and the call is in Bridge event and for the use case of Quota services were down:
            //Set NegativeBalance property to true.
            //Do the necessary reconciliation to charge the customer for the duration he talked
            //Here we are reusing existing reconciliation logic in Sipapp for SetOutofFunds status with deltaseconds as zero.
            //Set appropriate billing status and terminate the call

            //If the user runs out of money and the call is in Bridge event and for the use case of Quota services was up:
            //Set appropriate billing status and terminate the call (same as existing production flow)


            if (StringUtils.isNotBlank(SIPAppLogger.concatInternalFlags(vctx.getInternalFlags())) && SIPAppLogger.concatInternalFlags(vctx.getInternalFlags()).contains("06")) {
                clonedCtx.setNegativeBalance(true);
                if (!vctx.isCdrEventArrived()) {
                    handleError(vctx, SIPCode.PAYMENT_REQUIRED);
                }
            } else {
                vctx.getBillingInfo().setOutOfFundsStatusDuringStart(vctx.getSessionId(), vctx.getConnectionId());
                quotaQueue.remove(item);
                String origChannelId = findChannelId(vctx);
                if (null != origChannelId)
                    BridgeEventHandler.handleStartChargingError(vctx, origChannelId, SIPCode.PAYMENT_REQUIRED, HangupCause.AST_CAUSE_CALL_REJECTED, CallInternalFlag.BLOCKED_ON_NO_MIN_BALANCE);
            }

        } catch (QuotaDisabledException ex) {
            quotaQueue.remove(item);
            vctx.getBillingInfo().setErrorStatus(vctx.getSessionId(), vctx.getConnectionId());
            Log.error("{} {} charging: Failed to update the quota due to {}. If accountid {} is smoke tests account - this is OK ", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage(), vctx.getAccountId());
            vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_QUOTA_DISABLED);
            handleError(vctx, SIPCode.SERVER_INTERNAL_ERROR);
        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException ex) {
            quotaQueue.remove(item);
            vctx.getBillingInfo().setErrorStatus(vctx.getSessionId(), vctx.getConnectionId());
            Log.error("{} {} charging: Failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());
            vctx.addInternalFlag(CallInternalFlag.TERMINATED_ON_ACCOUNT_ISSUES);
            handleError(vctx, SIPCode.SERVER_INTERNAL_ERROR);
        } catch (Exception e) {
            quotaQueue.remove(item);
            Log.error("Internal error while attempting to start charging session for " + vctx.getSessionId() + " Notify Astrisk to end the call  due to: ", e);
            String origChannelId = findChannelId(vctx);
            if (null != origChannelId)
                BridgeEventHandler.handleStartChargingError(vctx, origChannelId, SIPCode.SERVER_INTERNAL_ERROR, HangupCause.AST_CAUSE_FAILURE, CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);

        }

    }

    private void invokeCdrQuotaAPI(QuotaItem item) {
        VoiceContext vctx = item.getVoiceContext();
        if (vctx.isVpricingEnabled()) {
            invokeCdrQuotaAPIForVpricing(item);
        } else {
            BigDecimal amount = item.getAmount();
            String operation = item.getOperation();
            try {
                //final AtomicBoolean executed = new AtomicBoolean(false);
                //if (executed.compareAndSet(false, true)) {
                //Reset QuotaDown status before calling Quota API
                vctx.setQuotaDown(false);
                if (vctx != null && amount != null && StringUtils.isNotBlank(operation) && vctx.getQuotaRef() != null && vctx.getConnectionId() != null) {

                    //Based on the value of operation parameter, call either CONSUME or REFUND Quota API
                    if (operation.equals(String.valueOf(QuotaUpdateDetails.Operation.CONSUME))) {
                        processQuotaItem(vctx, null, item, QuotaUpdateDetails.Operation.CONSUME);
                    } else {
                        processQuotaItem(vctx, null, item, QuotaUpdateDetails.Operation.REFUND);
                    }
                }
                //Generate CDR and remove item from Queue provided Quota services are up and billing status is clear of any Quota errors
                if (!item.getVoiceContext().isQuotaDown()) {
                    if (QuotaItem.EventType.CDR.equals(item.getEventType()) || QuotaItem.EventType.SKIP_QUOTA.equals(item.getEventType())) {
                        generateCDR(item);
                    }
                    quotaQueue.remove(item);
                }
                //}

            } catch (QuotaException | QuotaUnderMaintenanceException ex) {
                vctx.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
                vctx.setQuotaDown(true);
                Log.warn("{} {} Stop charging: Call continued, though failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());

            } catch (Exception ex) {
                quotaQueue.remove(item);
                Log.error("{} {} stop charging: Failed to update the quota with the final result of quotaUpdateDetails due to {}. VoiceContext: {}",
                        vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage(), vctx.getDebugString());
                vctx.addInternalFlag(CallInternalFlag.QUOTA_ISSUES_ON_CALL_FINAL_BILLING);
            }
        }
    }

    private String findChannelId(VoiceContext ctx) {
        ApplicationContext applicationContext = ctx.getApplicationContext();
        String channelId = null;
        try {
            SIPAsteriskContext asteriskAppContext = SIPAsteriskContext.class.cast(applicationContext);
            channelId = asteriskAppContext.getSipChannelId();
        } catch (ClassCastException e) {
            Log.error("Failed to cancel context's session in Asterisk. Non Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']", e);
        }
        if (channelId == null || channelId.isEmpty()) {
            Log.error("Failed to cancel context's session in Asterisk. Empty or null channelId in Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']");
            return null;
        }

        // Just in case we don't find the first leg (happens when the first leg has error already)
        String channelToStop = channelId;
        VoiceContext firstLegContext = null;
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextsInSession = cache.getInnerValues(ctx.getSessionId());
        for (VoiceContext context : contextsInSession) {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) context.getApplicationContext();
            if (context.getVoiceDirection() == VoiceDirection.INBOUND || asteriskContext.isAuxiliary())
                firstLegContext = context;
        }
        if (firstLegContext == null)
            Log.warn("1st leg not found in session " + ctx.getSessionId() + " Might've been processed already...");
        else {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) firstLegContext.getApplicationContext();
            if (asteriskContext != null)
                channelToStop = asteriskContext.getSipChannelId();
        }
        return channelToStop;
    }

    private static void handleError(VoiceContext ctx, SIPCode sipCode) {
        if (Log.isDebugEnabled())
            Log.debug("Processing QuotaUpdateTask error in context: {} SIPCode: {}", ctx.getDebugString(), sipCode);

        ApplicationContext applicationContext = ctx.getApplicationContext();
        String channelId = null;
        try {
            SIPAsteriskContext asteriskAppContext = SIPAsteriskContext.class.cast(applicationContext);
            channelId = asteriskAppContext.getSipChannelId();
        } catch (ClassCastException e) {
            Log.error("Failed to cancel context's session in Asterisk. Non Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']", e);
        }
        if (channelId == null || channelId.isEmpty()) {
            Log.error("Failed to cancel context's session in Asterisk. Empty or null channelId in Asterisk ApplicationContext detected for context ['" + ctx.getDebugString() + "']");
            return;
        }

        // Just in case we don't find the first leg (happens when the first leg has error already)
        String channelToStop = channelId;
        // Get first leg ..
        VoiceContext firstLegContext = null;
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextsInSession = cache.getInnerValues(ctx.getSessionId());
        for (VoiceContext context : contextsInSession) {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) context.getApplicationContext();
            if (context.getVoiceDirection() == VoiceDirection.INBOUND || asteriskContext.isAuxiliary())
                firstLegContext = context;
        }
        if (firstLegContext == null)
            Log.warn("1st leg not found in session " + ctx.getSessionId() + " Might've been processed already...");
        else {
            SIPAsteriskContext asteriskContext = (SIPAsteriskContext) firstLegContext.getApplicationContext();
            if (asteriskContext != null)
                channelToStop = asteriskContext.getSipChannelId();
        }

        HangupCause hangupCause = SIPCode.BANNED_CALL_ENDED.equals(sipCode) ? HangupCause.AST_CAUSE_WRONG_CALL_STATE : HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED;
        if (channelToStop != null) {
            try {
                Log.info("About to set APP_REASON to " + SIPCode.REQUEST_TERMINATED.getCode() +
                        " and finish the call with HangupCause: " + hangupCause +
                        " for sessionId: " + ctx.getSessionId());
                // We kill the call via the first leg's channel, or Asterisk will overwrite our HangupCause
                AsteriskActionIssuer.setVariable(channelToStop, "APP_REASON", String.valueOf(SIPCode.REQUEST_TERMINATED.getCode()));
                AsteriskActionIssuer.finishCall(channelToStop, hangupCause);
            } catch (Exception e1) {
                Log.error("Failed to finish the call", e1);
            }
        } else {
            Log.warn("2nd leg error was dealt with already.");
        }

    }


    private void generateCDR(QuotaItem item) throws VoiceEventHandlerException {
        if (item != null && item.getVoiceContext() != null) {
            VoiceContext vctx = item.getVoiceContext();
            String sessionId = vctx.getSessionId();
            CdrEvent event = item.getCdrEvent();
            if (event != null && StringUtils.isNotBlank(sessionId)) {
                String channelUniqueId = cdrEventHandler.parseChannelUniqueId(event);
                AsteriskVersion asteriskVersion = CdrEventHandler.toAsteriskVersion(channelUniqueId);
                CdrEventUserData eventUserData = CdrEventUserData.of(asteriskVersion, event, channelUniqueId);
                //All these logic are borrowed from the existing CDREventHandler class
                if (StringUtils.isNotBlank(channelUniqueId) && eventUserData != null) {
                    SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, event.getBillableSeconds(), vctx);
                    boolean isAnsweredCall = CdrEventHandler.isAnsweredCall(sipCode, eventUserData, sessionId);
                    cdrEventHandler.verifyWhetherCostUpdateRequired(vctx, eventUserData);
                    cdrEventHandler.handleCallEndPerChannel(channelUniqueId, event, eventUserData, sipCode, vctx, item.getVoiceContextCount(), isAnsweredCall);
                    Log.info("End of processing CDR Event SessionID {} and  ChannelUniqueID {}", sessionId, channelUniqueId);
                }
            }
        }

    }

    private void processQuotaItem(VoiceContext vctx, VoiceContext clonedCtx, QuotaItem item, QuotaUpdateDetails.Operation cmd) throws QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException,
            QuotaException, AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        VoiceContext voiceContext = clonedCtx != null ? clonedCtx : vctx;
        if (voiceContext.isVpricingEnabled()) {
            VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
            PriceImpactApiSuccessResponse resp = vQuotaService.invokePriceImpactApi(voiceContext, item.getDuration(), cmd);
            if (resp != null) {
                updateVoiceContext(voiceContext, resp, cmd, item);
                if (clonedCtx != null) {
                    syncWithClonedContext(voiceContext, vctx, item.getEventType());
                }
            }
        } else {
            if(QuotaUpdateDetails.Operation.CONSUME == cmd) {
                voiceQuotaClient.consume(voiceContext, item.getAmount(), voiceContext.getQuotaRef(), voiceContext.getConnectionId());
            } else if (QuotaUpdateDetails.Operation.REFUND == cmd) {
                voiceQuotaClient.refund(voiceContext, item.getAmount(), voiceContext.getQuotaRef(), voiceContext.getConnectionId());
            }
        }
    }

    private void syncWithClonedContext(VoiceContext clonedCtx, VoiceContext vctx, QuotaItem.EventType eventType) {
        vctx.setMediationCdrField(clonedCtx.getMediationCdrField());
        vctx.getBillingInfo().updateTotalEstimatedPriceImpact(clonedCtx.getBillingInfo().getTotalEstimatedPriceImpact());

        if(BigDecimal.ZERO.compareTo(vctx.getBillingInfo().getEstimatedCallPricePerSecond()) == 0) {
            vctx.getBillingInfo().setEstimatedCallPricePerSecond(clonedCtx.getBillingInfo().getEstimatedCallPricePerSecond());
        }
    }

    private void updateVoiceContext(VoiceContext vctx, PriceImpactApiSuccessResponse resp, QuotaUpdateDetails.Operation cmd, QuotaItem item) {
        if(resp.getEstimatedPriceImpact() != null) {
            if(QuotaUpdateDetails.Operation.CONSUME == cmd) {
                vctx.getBillingInfo().updateTotalEstimatedPriceImpact(vctx.getBillingInfo().getTotalEstimatedPriceImpact().add(resp.getEstimatedPriceImpact())); //Add the estimated-price-impact for consume
            } else if (QuotaUpdateDetails.Operation.REFUND == cmd) {
                vctx.getBillingInfo().updateTotalEstimatedPriceImpact(vctx.getBillingInfo().getTotalEstimatedPriceImpact().subtract(resp.getEstimatedPriceImpact())); //Subtract the estimated-price-impact for refund
            }

            // Set the estimated call price per minute.
            // If the call price per second is zero (which may occur due to vQuota being down),
            // update the price based on the response and item duration.
            if(BigDecimal.ZERO.compareTo(vctx.getBillingInfo().getEstimatedCallPricePerSecond()) == 0) {
                vctx.getBillingInfo().setEstimatedCallPricePerSecond(resp.getEstimatedPriceImpact(), item.getDuration(), vctx.getSessionId());
            }
        }
        vctx.setMediationCdrField(resp.getMediationCdrField());
    }

    private void invokeCdrQuotaAPIForVpricing(QuotaItem item) {
        VoiceContext vctx = item.getVoiceContext();
        String operation = item.getOperation();
        Long duration = item.getDuration();

        try {
            if (vctx != null && StringUtils.isNotBlank(operation) && duration != null && vctx.getQuotaRef() != null
                    && vctx.getConnectionId() != null) {
                //Based on the value of operation parameter, call either CONSUME or REFUND Quota API
                if (operation.equals(String.valueOf(QuotaUpdateDetails.Operation.CONSUME))) {
                    processQuotaItem(vctx, null, item, QuotaUpdateDetails.Operation.CONSUME);
                } else {
                    processQuotaItem(vctx, null, item, QuotaUpdateDetails.Operation.REFUND);
                }
            }
        } catch (QuotaException | QuotaUnderMaintenanceException ex) {
            vctx.addInternalFlag(CallInternalFlag.PAUSED_ON_QUOTA_ISSUES);
            Log.warn("{} {} Stop charging: failed to update the quota due to {}", vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage());

        } catch (Exception ex) {
            Log.error("{} {} Stop charging: failed to update the quota with the final result of quotaUpdateDetails due to {}. VoiceContext: {}",
                    vctx.getSessionId(), vctx.getConnectionId(), ex.getMessage(), vctx.getDebugString());
            vctx.addInternalFlag(CallInternalFlag.QUOTA_ISSUES_ON_CALL_FINAL_BILLING);
        }

        if (QuotaItem.EventType.CDR.equals(item.getEventType()) || QuotaItem.EventType.SKIP_QUOTA.equals(item.getEventType())) {
            try {
                generateCDR(item);
            } catch (VoiceEventHandlerException e) {
                Log.error("{} {} Stop charging: failed to generate the CDR with the final result of quotaUpdateDetails due to {}. VoiceContext: {}",
                        vctx.getSessionId(), vctx.getConnectionId(), e.getMessage(), vctx.getDebugString());
            }
        }

        purgeQuotaItemsForSession(item);
    }

    /**
     * Marks the passed QuotaItem and any matching QuotaItem(s) in the queue for skipping based on sessionId.
     * The sessionId of the provided QuotaItem is used to identify and mark related items in the queue.
     *
     * @param item the QuotaItem whose sessionId is used for matching and marking related items for skipping
     */
    private void purgeQuotaItemsForSession(QuotaItem item) {
        quotaQueue.remove(item);

        // The quota queue may contain items other than SKIP_QUOTA, CDR, HANDLE_FINAL_CALL quota event types,
        // which do not need further processing by the quota and should be deleted.
        // The deleted items will be handled by the BSS vRater and Balance Manager services.
        // This condition may occur due to BSS vQuota being down or slow.
        String sessionIdToRemove = item.getVoiceContext().getSessionId(); // Get the sessionId from the provided item
        boolean counterIncremented = false; // Flag to ensure the counter is only incremented once for the same sessionId

        // Remove matching records from the Map
        QuotaItem removedItem = quotaItemMap.remove(item.getVoiceContext().getQuotaRef());
        if (removedItem != null) {
            Log.info("Removed QuotaItem from quotaItemMap with session-ID: {}, event-type: {}, operation: {}, duration: {}",
                    sessionIdToRemove, item.getEventType(), item.getOperation(), item.getDuration());
        }

        // Mark related items as 'skip' if their sessionId matches and eventType is either BRIDGE nor DELTA
        for (QuotaItem currentItem : quotaQueue) {
            if (currentItem.getVoiceContext().getSessionId().equals(sessionIdToRemove) &&
                    (currentItem.getEventType() == QuotaItem.EventType.BRIDGE || currentItem.getEventType() == QuotaItem.EventType.DELTA)) {
                currentItem.setMarkedForSkip(true);
                Log.error("Marked QuotaItem for skipping to execute any further Quota actions. Session-ID: {}, event-Type: {}, operation: {}, duration: {}",
                        currentItem.getVoiceContext().getSessionId(), currentItem.getEventType(), currentItem.getOperation(), currentItem.getDuration());

                // Increment the counter only once for the sessionId
                if (!counterIncremented) {
                    CompletableFuture.runAsync(() -> {
                        QUOTA_ITEM_DELETION_ACCOUNT_COUNTER.inc();
                    });
                    counterIncremented = true;
                }
            }
        }
    }
}