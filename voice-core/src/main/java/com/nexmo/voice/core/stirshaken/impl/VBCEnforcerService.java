package com.nexmo.voice.core.stirshaken.impl;

import com.nexmo.voice.core.stirshaken.Attestation;
import com.nexmo.voice.core.stirshaken.AttestationValidationParams;
import com.nexmo.voice.core.stirshaken.EnforcerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.nexmo.voice.core.stirshaken.Attestation.NONE;

public class VBCEnforcerService implements EnforcerService {

    private final static Logger Log = LogManager.getLogger(VBCEnforcerService.class);

    @Override
    public Attestation attestationLevel(AttestationValidationParams attestationValidationParams) {
        if (Log.isDebugEnabled()) {
            Log.debug("{} Starting VBC Stir Shaken Calculation, params = {}", attestationValidationParams
                    .getSessionId(), attestationValidationParams);
        }
        return NONE;
    }
}
