package com.nexmo.voice.core.callback;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.nexmo.voice.core.sip.AsteriskAGIServer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.common.callback.AbstractCallbackTask;
import com.thepeachbeetle.common.callback.config.CallbackConfig;
import com.thepeachbeetle.common.callback.types.CallbackMethod;
import com.thepeachbeetle.common.http.HttpCallbackIssuer;
import com.thepeachbeetle.common.http.config.HttpProxyConfig;
import com.thepeachbeetle.common.workqueue.exceptions.QueueJobExpireException;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;

/**
 * <AUTHOR> Cook
 * @version
 *
 *
 * Callback content include keywords and their values.
 * For backward compatibility issues, each keyword which
 *  include '-' in its name is duplicated with '_' as well.
 *
 * For example:  call_direction and call-direction
 */
public class VoiceCallback extends AbstractCallbackTask {

    private static final long serialVersionUID = -845771461689700922L;

    private static final Logger Log = LogManager.getLogger(VoiceCallback.class);
    private static final String OBFUSCATED_PRICE = "0.********";

    private Map<String, String> callbackDetails;

    public VoiceCallback(VoiceContext ctx, String status, String digits) {
        this(ctx, status, null, digits);
    }

    public VoiceCallback(VoiceContext ctx, String status, String sipStatus, String digits) {
        this(ctx, status, sipStatus, digits, ctx.getCallbackUrl(), ctx.getCallbackMethod());
    }

    public VoiceCallback(VoiceContext ctx, String status, String sipStatus, String digits, String callbackUrl, CallbackMethod callbackMethod) {
        super(initBuilder(ctx.getAccountId(), callbackUrl, callbackMethod,
                initParams(ctx, status, sipStatus, digits)));


        //All the local kept variables are used just for the debug printing of this callback task
        this.callbackDetails = new LinkedHashMap<>(this.getParams());
        //avoid logging the password
        this.callbackDetails.computeIfPresent("password", (old, newValue) -> "XXXXXX");

        //and add some general identifiers for the log
        this.callbackDetails.put("PRODUCT-CLASS", ctx.getProductClass());
        this.callbackDetails.put("ACC", ctx.getAccountId());
        this.callbackDetails.put("SUPERHUB-ACC", ctx.getMasterAccountId());
        this.callbackDetails.put("CALL_BACK_METHOD", callbackMethod.getHttpMethod().getMethod());
        this.callbackDetails.put("CALL_BACK_URL", callbackUrl);
    }

    private static Builder initBuilder(String accountId,
                                       String callbackUrl,
                                       CallbackMethod callbackMethod,
                                       Map<String, String> callbackParams) {

        if (Log.isDebugEnabled()) {
            Log.debug("VoiceCallBack init the builder for call back of accountId: {}", accountId);
        }

        CallbackConfig config = Core.getInstance().getConfig().getCallbackConfig();
        HttpProxyConfig proxyConfig = config == null ? null : config.getHttpProxyConfig();
        Builder builder = new Builder(proxyConfig, accountId).setHostName(Core.getInstance().getConfig().getHostName())
                .setHttpMethod(callbackMethod)
                .setCallbackAddress(callbackUrl);

        builder.setParams(callbackParams);

        try {
            SmppAccount account = Accounts.getInstance().getSmppAccount(accountId);
            if (account != null) {
                if (account.isSecuritySignMoAndDlrHttpRequests())
                    builder.setSignatureSharedSecret(account.getSignatureSecret(), account.getSignedRequestSignatureMethod());
                builder.setCallbackCredentialsUsername(account.getHttpForwardUsername());
                builder.setCallbackCredentialsPassword(account.getHttpForwardPassword());
            }
        } catch (AccountsException e) {
            Log.error("Failed to retrieve account [ " + accountId + " ] ", e);
        }

        return builder;
    }

    private static Map<String, String> initParams(VoiceContext ctx, String status, String sipStatus, String digits) {
        Map<String, String> params = new LinkedHashMap<>();

        params.put("call_id", SipAppUtils.getNexmoUUID(ctx.getSessionId()));
        params.put("call-id", SipAppUtils.getNexmoUUID(ctx.getSessionId()));

        params.put("status", status);
        if (sipStatus != null) {
            params.put("sip_status", sipStatus);
            params.put("sip-status", sipStatus);
        }
        params.put("call_direction", ctx.getVoiceDirection().getValue());
        params.put("call-direction", ctx.getVoiceDirection().getValue());

        String decodedTo = ctx.getTo();
        try {
            decodedTo = Objects.nonNull(ctx.getTo()) ?
                    URLDecoder.decode(ctx.getTo(), StandardCharsets.UTF_8.name()) :
                    "";
        } catch (UnsupportedEncodingException e) {
            Log.warn("Failed to decode the TO param {} doe to {}", ctx.getTo(), e.getMessage());
        }
        params.put("to", decodedTo);


        String from = ctx.getFrom();
        if (from != null)
            params.put("from", from);

        if (digits != null)
            params.put("digits", digits);

        String requestTime = formatToCallbackTimeDate(ctx.getContextCreationDate());
        params.put("call_request", requestTime);
        params.put("call-request", requestTime);

        if (ctx.getNetwork() != null) {
            params.put("network_code", ctx.getNetwork());
            params.put("network-code", ctx.getNetwork());
        }


        BillingInfo billingInfo = ctx.getBillingInfo();
        if (Objects.nonNull(billingInfo)) {

            if (Log.isDebugEnabled())
                Log.debug("{} {} building the callback billing params using {} ",
                        ctx.getSessionId(), ctx.getConnectionId(), billingInfo);

            final long callLengthInSeconds = billingInfo.getCallDurationInSeconds();
            params.put("call_duration", String.valueOf(callLengthInSeconds));
            params.put("call-duration", String.valueOf(callLengthInSeconds));
            if (callLengthInSeconds > 0) {
                String startTime = formatToCallbackTimeDate(billingInfo.getCallStartTime());
                if (Objects.nonNull(startTime)) {
                    params.put("call_start", startTime);
                    params.put("call-start", startTime);
                }
                String endTime = formatToCallbackTimeDate(billingInfo.getCallEndTime());
                if (Objects.nonNull(endTime)) {
                    params.put("call_end", endTime);
                    params.put("call-end", endTime);
                }
            }


            //In Old TTS we were not sending the real-price in the callback.
            //In Old verify, which was based on old TTS, there is a parameter named `request-cost`
            //which indicates the real-price should be sent as part of the callback. Verify always use
            //this parameter.

            //real-price is sent ONLY if the price was overwritten.
            //For Verify-NG the price is always overwritten by Verify to be zero
            if (obfuscatePrice(ctx.getAccountId(), ctx.getVoiceProduct())) {
                if (Objects.nonNull(billingInfo.getForcedPricePerMinute())) {
                    params.put("real_price", OBFUSCATED_PRICE);
                    params.put("real-price", OBFUSCATED_PRICE);
                }
                params.put("call_price", OBFUSCATED_PRICE);
                params.put("call-price", OBFUSCATED_PRICE);
                params.put("call_rate", OBFUSCATED_PRICE);
                params.put("call-rate", OBFUSCATED_PRICE);
            } else {
                final BigDecimal forcedPrice = billingInfo.getForcedPricePerMinute();
                if (Objects.nonNull(forcedPrice)) {
                    if(ctx.isVpricingEnabled()) { // if vPricing is enabled and forcedPrice is used, set realPrice to 0.
                        params.put("real_price", OBFUSCATED_PRICE);
                        params.put("real-price", OBFUSCATED_PRICE);
                    } else {
                        BigDecimal realPrice = BillingInfo.calculateAmountPerPeriod(
                                billingInfo.getPricePerSecond(), billingInfo.getCallDurationInSeconds());
                        String realPriceStr = realPrice.toPlainString();
                        params.put("real_price", realPriceStr);
                        params.put("real-price", realPriceStr);
                    }
                }

                String totalCallPriceStr; //The total call price
                String callPricePerMinuteStr; //The configured price-per-minute (not the overwritten price)
                if(ctx.isVpricingEnabled()) {
                    if(Objects.nonNull(ctx.getBillingInfo().getForcedPricePerMinute())) {
                        totalCallPriceStr = OBFUSCATED_PRICE;
                        callPricePerMinuteStr = OBFUSCATED_PRICE;
                    } else {
                        totalCallPriceStr = ctx.getBillingInfo().getEstimatedTotalCallPrice().toPlainString();
                        callPricePerMinuteStr = ctx.getBillingInfo().getEstimatedCallPricePerMinute().toPlainString();
                    }
                } else {
                    totalCallPriceStr = billingInfo.getTotalCallPrice(ctx.getSessionId(), ctx.getConnectionId()).toPlainString();
                    callPricePerMinuteStr = billingInfo.getPricePerMinute().toPlainString();
                }

                params.put("call_price", totalCallPriceStr);
                params.put("call-price", totalCallPriceStr);

                params.put("call_rate", callPricePerMinuteStr);
                params.put("call-rate", callPricePerMinuteStr);
            }
            //For Verify-NG we should send the cost - for backward compatibility
            if (VoiceProduct.VERIFY.equals(ctx.getVoiceProduct())) {
                String totalCallCostStr = billingInfo.getTotalCallCost(ctx.getSessionId(), ctx.getConnectionId()).toPlainString();
                params.put("call_cost", totalCallCostStr);
                params.put("call-cost", totalCallCostStr);
            }

        }

        // put all application specific callback params
        if (ctx.getApplicationContext() != null) {
            Map<String, String> appCtxParams = ctx.getApplicationContext().getCallbackParams();
            if (appCtxParams != null)
                params.putAll(appCtxParams);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("{} : VoiceCallBack initial parameters: {}", ctx.getSessionId(), dumpParams(params));
        }

        return params;
    }

    private static boolean obfuscatePrice(String accountId, VoiceProduct voiceProduct) {
    if (!VoiceProduct.VERIFY.equals(voiceProduct)) {
            try {
                //will fetch from cache
                final SmppAccount account = Accounts.getInstance().getSmppAccount(accountId);
                if (account != null) {
                    final boolean result = account.hasCapability(AsteriskAGIServer.OBFUSCATE_PRICE_IN_CALLBACk);
                    Log.info("Result of price obfuscation for account {} is {}", accountId, result);
                    return result;
                }
            } catch (AccountsException e) {
                Log.error("Failed to retrieve account  while checking if to obfuscate price [ " + accountId + " ] ", e);
            }
        }
        return false;
    }

    private static Object dumpParams(Map<String, String> params) {
        if (Objects.isNull(params))
            return "null";
        else
            return params.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining(", "));
    }

    @Override
    //This method is called while logging the callback content. This logging is done from messageing.jar
    //A new instance of VoiceCallback is created on each callback, so it is ok to use local variables.
    public Map<String, String> getDebugLoggingDeliveryAttemptFields() {
        return this.callbackDetails;
    }

    @Override
    protected boolean shouldRetryFailure(final HttpCallbackIssuer.HTTPCallbackDeliveryResult failedResult) {
        if (Objects.nonNull(this.callbackDetails) &&
                this.callbackDetails.get("CALL_BACK_URL") != null &&
                this.callbackDetails.get("CALL_BACK_URL").contains("/v1/statusEvent")) {
            Log.debug("VoiceCallback:shouldRetryFailure for callback:{} result:{}",
                    this.callbackDetails.toString(), failedResult.toString());
            Core.getInstance().incrementCSErrorCounter();
        }
        return true;
    }

    @Override
    public void expire() throws QueueJobExpireException {
    }

    protected static String deQuote(String in) {
        if (in == null)
            return null;
        char[] chars = in.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (char ch : chars)
            if (ch == '\"')
                sb.append("\"\"");
            else
                sb.append(ch);
        return sb.toString();
    }

    private static String formatToCallbackTimeDate(long timeDate) {
        return formatToCallbackTimeDate(new Date(timeDate));
    }

    private static String formatToCallbackTimeDate(Date timeDate) {
        if (Objects.isNull(timeDate))
            return "null";

        SimpleDateFormat cbDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return cbDateFormat.format(timeDate);
    }


}
