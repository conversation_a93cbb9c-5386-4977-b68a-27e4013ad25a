package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceImpactApiRequest {
    private String groupId;
    private String product;
    private String productDetail;
    private Boolean allowNegativeBalance;
    private String refId;
    private String connId;
    private Long duration;
    private String cmd;
    private String callType;
    private String numberType;
    private String prefixTo;
    private String prefixFrom;
    private String country;
    private String sourceCountry;
    private String networkType;
    private String toNumber;

    private PriceImpactApiRequest(Builder builder) {
        this.groupId = builder.groupId;
        this.product = builder.product;
        this.productDetail = builder.productDetail;
        this.allowNegativeBalance = builder.allowNegativeBalance;
        this.refId = builder.refId;
        this.connId = builder.connId;
        this.duration = builder.duration;
        this.cmd = builder.cmd;
        this.callType = builder.callType;
        this.numberType = builder.numberType;
        this.prefixTo = builder.prefixTo;
        this.prefixFrom = builder.prefixFrom;
        this.country = builder.country;
        this.sourceCountry = builder.sourceCountry;
        this.networkType = builder.networkType;
        this.toNumber = builder.toNumber;
    }

    public String getGroupId() {
        return groupId;
    }

    public String getProduct() { return product; }

    public String getProductDetail() { return productDetail; }

    public Boolean getAllowNegativeBalance() { return allowNegativeBalance; }

    public String getRefId() { return refId; }

    public String getConnId() { return connId; }

    public Long getDuration() { return duration; }

    public String getCmd() { return cmd; }

    public String getCallType() { return callType; }

    public String getNumberType() { return numberType; }

    public String getPrefixTo() {  return prefixTo; }

    public String getPrefixFrom() { return prefixFrom; }

    public String getCountry() { return country; }

    public String getSourceCountry() { return sourceCountry; }

    public String getNetworkType() { return networkType; }

    public String getToNumber() {  return toNumber; }

    @Override
    public String toString() {
        return "PriceImpactApiRequest{" +
                "groupId='" + groupId + '\'' +
                ", product='" + product + '\'' +
                ", productDetail='" + productDetail + '\'' +
                ", allowNegativeBalance='" + allowNegativeBalance + '\'' +
                ", refId='" + refId + '\'' +
                ", connId='" + connId + '\'' +
                ", duration='" + duration + '\'' +
                ", cmd='" + cmd + '\'' +
                ", callType='" + callType + '\'' +
                ", numberType='" + numberType + '\'' +
                ", prefixTo='" + prefixTo + '\'' +
                ", prefixFrom='" + prefixFrom + '\'' +
                ", country='" + country + '\'' +
                ", sourceCountry='" + sourceCountry + '\'' +
                ", networkType='" + networkType + '\'' +
                ", toNumber='" + toNumber + '\'' +
                '}';
    }

    public static class Builder {
        private String groupId;
        private String product;
        private String productDetail;
        private Boolean allowNegativeBalance;
        private String refId;
        private String connId;
        private Long duration;
        private String cmd;
        private String callType;
        private String numberType;
        private String prefixTo;
        private String prefixFrom;
        private String country;
        private String sourceCountry;
        private String networkType;
        private String toNumber;

        public Builder setGroupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder setProduct(String product) {
            this.product = product;
            return this;
        }

        public Builder setProductDetail(String productDetail) {
            this.productDetail = productDetail;
            return this;
        }

        public Builder setAllowNegativeBalance(Boolean allowNegativeBalance) {
            this.allowNegativeBalance = allowNegativeBalance;
            return this;
        }

        public Builder setRefId(String refId) {
            this.refId = refId;
            return this;
        }

        public Builder setConnId(String connId) {
            this.connId = connId;
            return this;
        }

        public Builder setDuration(Long duration) {
            this.duration = duration;
            return this;
        }

        public Builder setCmd(String cmd) {
            this.cmd = cmd;
            return this;
        }

        public Builder setCallType(String callType) {
            this.callType = callType;
            return this;
        }

        public Builder setNumberType(String numberType) {
            this.numberType = numberType;
            return this;
        }

        public Builder setPrefixTo(String prefixTo) {
            this.prefixTo = prefixTo;
            return this;
        }

        public Builder setPrefixFrom(String prefixFrom) {
            this.prefixFrom = prefixFrom;
            return this;
        }

        public Builder setCountry(String country) {
            this.country = country;
            return this;
        }

        public Builder setSourceCountry(String sourceCountry) {
            this.sourceCountry = sourceCountry;
            return this;
        }

        public Builder setNetworkType(String networkType) {
            this.networkType = networkType;
            return this;
        }

        public Builder setToNumber(String toNumber) {
            this.toNumber = toNumber;
            return this;
        }

        public PriceImpactApiRequest build() {
            return new PriceImpactApiRequest(this);
        }
    }
}

