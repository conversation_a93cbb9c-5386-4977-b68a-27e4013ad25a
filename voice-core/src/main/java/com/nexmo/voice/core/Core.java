package com.nexmo.voice.core;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.nexmo.voice.config.callblocks.CallBlocksConfig;
import com.nexmo.voice.core.billing.QuotaQueueExecuter;
import com.nexmo.voice.core.billing.vquota.VQuotaService;
import com.nexmo.voice.core.cache.*;
import com.nexmo.voice.core.randomize.Randomizer;
import com.nexmo.voice.core.callinfo.dynamodb.DynamoDB;
import com.nexmo.voice.core.sip.event.user.CdrUserFieldEvent;
import com.nexmo.voice.core.stirshaken.EnforcerService;
import com.nexmo.voice.core.stirshaken.impl.SIPEnforcerService;
import com.nexmo.voice.core.stirshaken.impl.TTSEnforcerService;
import com.nexmo.voice.core.stirshaken.impl.VBCEnforcerService;
import com.nexmo.voice.core.stirshaken.impl.VerifyEnforcerService;
import com.nexmo.voice.core.sync.EnvVariableUpdatesExecuter;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.common.app.logging.AppStartCDRS;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Info;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.fastagi.AgiServerThread;
import org.asteriskjava.fastagi.ClassNameMappingStrategy;
import org.asteriskjava.fastagi.DefaultAgiServer;
import org.asteriskjava.manager.AuthenticationFailedException;
import org.asteriskjava.manager.ManagerConnection;
import org.asteriskjava.manager.ManagerConnectionFactory;
import org.asteriskjava.manager.TimeoutException;
import org.asteriskjava.manager.action.StatusAction;
import org.asteriskjava.manager.event.ManagerEvent;
import org.hibernate.exception.GenericJDBCException;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.caches.CacheConfig;
import com.nexmo.voice.config.caches.CacheControlConfig;
import com.nexmo.voice.config.caches.CacheType;
import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.config.gateway.asterisk.AsteriskManagerConfig;
import com.nexmo.voice.config.sip.AsteriskAgiServerConfig;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.application.ApplicationLookupException;
import com.nexmo.voice.core.billing.BillingManager;
import com.nexmo.voice.core.billing.QuotaClient;
import com.nexmo.voice.core.billing.QuotaUpdatesExecuter;
import com.nexmo.voice.core.gateway.asterisk.AsteriskAMIProcessor;
import com.nexmo.voice.core.gateway.asterisk.AsteriskPingThread;
import com.nexmo.voice.core.gateway.asterisk.task.AsteriskTaskExecutor;
import com.nexmo.voice.core.logger.AttemptLoggerController;
import com.nexmo.voice.core.logger.CallLoggerController;
import com.nexmo.voice.core.logger.RejectedLoggerController;
import com.nexmo.voice.core.monitoring.metrics.ApplicationMetrics;
import com.nexmo.voice.core.pdd.PDDCalculator;
import com.nexmo.voice.core.routing.VoiceMTRouter;
import com.nexmo.voice.core.sip.event.AsteriskVoiceEventHandler;
import com.thepeachbeetle.common.app.ServerStartException;
import com.thepeachbeetle.common.callback.AbstractCallbackTask;
import com.thepeachbeetle.common.callback.CallbackPipeline;
import com.thepeachbeetle.hlr.core.exceptions.ConfigException;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingConfig;
import com.thepeachbeetle.messaging.hub.core.provisioning.client.ProvisioningRandomClient;
import com.thepeachbeetle.services.networks.client.NetworksClient;

import static com.nexmo.voice.core.SipAppUtils.getValueFromEnvProperties;
import static com.nexmo.voice.core.types.VoiceProduct.TTS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import io.prometheus.client.Histogram;
import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;


/**
 * <AUTHOR> Cook
 */
public class Core {

    private static final Info CORE_INFO = Info.build().name("sipapp_build").help("Build information.") .register();

    public static final String MAX_CONCURRENT_CALLS_THRESHOLD = "max-concurrent-calls";

    private final static Logger Log = LogManager.getLogger(Core.class);

    private static Core instance = new Core();

    private final Lock shutdownLock;
    private PDDCalculator pddCalculator;

    private Config config;

    private QuotaClient quotaClient;

    private VoiceContextCache voiceContextCache;
    private QuotaUpdatesExecuter quotaUpdatesExecuter;

    private QuotaQueueExecuter quotaQueueExecuter;
    private boolean asyncQuotaFlag;
    private EnvVariableUpdatesExecuter envUpdatesExecuter;
    private boolean blockUnknownCLIFlag;

    private boolean domesticRoutingFlag;
    private VoiceMTRouter mtRouter;

    private CallbackPipeline<AbstractCallbackTask> callbackIssuer;

    private ApplicationMetrics applicationMetrics;

    private AsteriskAMIProcessor asteriskAMIProcessor;
    private AsteriskTaskExecutor asteriskTaskExecutor;
    private AgiServerThread agiServerThread;
    private AsteriskPingThread asteriskPingThread;
    private LegFlowCache legFlowCache;

    private PartialCdrEventCache partialCdrEventCache;
    private CdrUserFieldEventCache cdrUserFieldEventCache;

    private BillingManager billingManager;

    private NetworksClient networksClient;

    private ProvisioningRandomClient provisioningRandomClient;

    private ApplicationCache applicationsCache;
    private VQuotaService vQuotaService;

    private volatile boolean asteriskShutdown = false;

    public static final int PHONE_NUMBER_TRUNCATION_LENGTH = 4;
    //TODO Tally: make this configurable

    private static final List<String> SNG_CALLSPOOF_PREFIX_BLOCKLIST = Arrays.asList("28", "83", "89", "210", "214", "215", "217", "219", "259", "292", "293", "294", "295", "296", "379", "384", "388", "422", "424", "425", "426", "427", "428", "429", "650", "651", "652", "654", "655", "657", "801", "802", "803", "804", "805", "806", "807", "809", "851", "854", "857", "858", "859", "871", "872", "873", "874", "875", "876", "877", "879", "884", "885", "887", "889", "969", "978", "979", "990", "991", "995", "997", "999", "1481", "1482", "1483", "1484", "1485", "65995", "65999", "6599591", "31292992", "61111111", "62022059", "62022062", "62022063", "62102500", "62110888", "62130622", "62130626", "62130629", "62130659", "62130694", "62130740", "62130747", "62130779", "62130806", "62130821", "62130838", "62138585", "62138979", "62140797", "62148427", "62168900", "62180000", "62181343", "62181348", "62200183", "62202340", "62213127", "62216666", "62221211", "62221712", "62221928", "62222121", "62222585", "62222888", "62223399", "62225295", "62228888", "62229999", "62231313", "62234811", "62238793", "62240000", "62250167", "62255225", "62255322", "62255353", "62255432", "62255529", "62255577", "62255582", "62255632", "62255777", "62255782", "62259911", "62260806", "62263866", "62271188", "62358577", "62369050", "62369999", "62411292", "62419999", "62426033", "62440000", "62447200", "62447357", "62448999", "62449999", "62483963", "62483964", "62486028", "62489999", "62506675", "62507999", "62508999", "62519999", "62529999", "62549999", "62550000", "62566657", "62567377", "62589999", "62600777", "62619999", "62659999", "62675002", "62689999", "62700141", "62700855", "62707988", "62727777", "62729999", "62739999", "62750388", "62759999", "62789999", "62800000", "62809999", "62829999", "62849999", "62869999", "62879999", "62899999", "62911601", "62949999", "62959999", "62963469", "62968999", "62969999", "63167508", "63167541", "63239829", "63240017", "63245025", "63250000", "63250518", "63251120", "63251184", "63252488", "63252489", "63252493", "63252494", "63256647", "63256666", "63257619", "63258282", "63259220", "63259806", "63259819", "63266027", "63272265", "63281877", "63281878", "63293237", "63293434", "63308658", "63308660", "63321055", "63321066", "63323659", "63323964", "63324184", "63324210", "63324220", "63324257", "63324286", "63324287", "63324288", "63324310", "63324351", "63324352", "63326666", "63327591", "63327798", "63335532", "63339999", "63342310", "63353530", "63380000", "63383632", "63386622", "63396666", "63398616", "63425222", "63438999", "63441177", "63448222", "63449999", "63459999", "63469400", "63512442", "63512775", "63512833", "63512868", "63513126", "63513288", "63513341", "63513352", "63513360", "63513363", "63513390", "63513511", "63513566", "63513883", "63524727", "63524728", "63530000", "63549020", "63551140", "63551142", "63551143", "63551145", "63552000", "63552183", "63552488", "63552959", "63555000", "63567012", "63568015", "63568225", "63568233", "63568300", "63568622", "63568633", "63580000", "63591199", "63633333", "63639999", "63647559", "63676767", "63684652", "63689999", "63751600", "63760000", "63773131", "63773400", "63773800", "63775925", "63775926", "63775939", "63779999", "63798000", "63798855", "63910000", "63914753", "63914762", "63916017", "63916074", "63916097", "63916100", "63916174", "63916231", "63916343", "63916521", "64252500", "64350000", "64352662", "64353500", "64355077", "64355398", "64355471", "64355473", "64355864", "64358277", "64385122", "64409999", "64428999", "64429999", "64439999", "64459999", "64489999", "64499999", "64519999", "64529999", "64589999", "64599999", "64604923", "64629999", "64660000", "64667460", "64689999", "64717300", "64717808", "64719999", "64722669", "64739999", "64749999", "64783947", "64785068", "64787010", "64849999", "64880999", "64890999", "64901111", "64902927", "64903039", "64903177", "64903203", "65005000", "65017000", "65091757", "65103000", "65118595", "65121322", "65129290", "65138967", "65340219", "65384452", "65412668", "65412672", "65421122", "65425024", "65425314", "65427788", "65428976", "65430433", "65432515", "65437049", "65451616", "65452123", "65452127", "65454078", "65454482", "65466738", "65466739", "65466743", "65466832", "65467169", "65467170", "65467171", "65467172", "65469221", "65469293", "65469498", "65469539", "65470000", "65476391", "65476393", "65529999", "65545655", "65549999", "65573083", "65573503", "65575076", "65577495", "65600056", "65639999", "65648999", "65659999", "65679999", "65687000", "65703053", "65852999", "65871999", "65872999", "65878423", "65895555", "66049999", "66432555", "66659999", "66717117", "66841503", "66842031", "66842032", "66842036", "66842037", "66842038", "66842041", "67107548", "67213507", "67213508", "67213509", "67213511", "67213512", "67213513", "67213516", "67213518", "67213649", "67213677", "67217705", "67217709", "67217720", "67217721", "67217722", "67217723", "67217724", "67217726", "67217727", "67217728", "67217729", "67217773", "67217776", "67217777", "67319000", "67359213", "67359999", "67366622", "67375133", "67375522", "67389116", "67407022", "67449999", "67459999", "67479999", "67489999", "67529999", "67539999", "67609999", "67629999", "67659999", "67660576", "67675718", "67675885", "67675963", "67679999", "67721234", "67721260", "67721270", "67721288", "67721411", "67721433", "67721693", "67738235", "67740000", "67747667", "67751133", "67751605", "67759999", "67760168", "67769036", "67779999", "67780955", "67788999", "67789999", "67797090", "67818999", "67819999", "67839999", "67855785", "67929999", "67937428", "67959731", "68044780", "68052871", "68125555", "68195333", "68266111", "68326832", "68350000", "68358000", "68379655", "68454401", "68486999", "68522999", "68529999", "68654612", "68661111", "68663066", "68663070", "68663073", "68663485", "68702439", "68702444", "68702447", "68704509", "68706020", "68721110", "68722220", "68727683", "68727684", "68729999", "68741733", "68765976", "68835885", "68877477", "68877888", "68929999", "68981800", "68999999", "69089000", "69550200", "69550424", "69550456", "69821114", "69821390", "6531292992", "6561111111", "6562022059", "6562022062", "6562022063", "6562102500", "6562110888", "6562130622", "6562130626", "6562130629", "6562130659", "6562130694", "6562130740", "6562130747", "6562130779", "6562130806", "6562130821", "6562130838", "6562138585", "6562138979", "6562140797", "6562148427", "6562168900", "6562180000", "6562181343", "6562181348", "6562200183", "6562202340", "6562213127", "6562216666", "6562221211", "6562221712", "6562221928", "6562222121", "6562222585", "6562222888", "6562223399", "6562225295", "6562228888", "6562229999", "6562231313", "6562234811", "6562238793", "6562240000", "6562250167", "6562255225", "6562255322", "6562255353", "6562255432", "6562255529", "6562255577", "6562255582", "6562255632", "6562255777", "6562255782", "6562259911", "6562260806", "6562263866", "6562271188", "6562358577", "6562369050", "6562369999", "6562411292", "6562419999", "6562426033", "6562440000", "6562447200", "6562447357", "6562448999", "6562449999", "6562483963", "6562483964", "6562486028", "6562489999", "6562506675", "6562507999", "6562508999", "6562519999", "6562529999", "6562549999", "6562550000", "6562566657", "6562567377", "6562589999", "6562600777", "6562619999", "6562659999", "6562675002", "6562689999", "6562700141", "6562700855", "6562707988", "6562727777", "6562729999", "6562739999", "6562750388", "6562759999", "6562789999", "6562800000", "6562809999", "6562829999", "6562849999", "6562869999", "6562879999", "6562899999", "6562911601", "6562949999", "6562959999", "6562963469", "6562968999", "6562969999", "6563167508", "6563167541", "6563239829", "6563240017", "6563245025", "6563250000", "6563250518", "6563251120", "6563251184", "6563252488", "6563252489", "6563252493", "6563252494", "6563256647", "6563256666", "6563257619", "6563258282", "6563259220", "6563259806", "6563259819", "6563266027", "6563272265", "6563281877", "6563281878", "6563293237", "6563293434", "6563308658", "6563308660", "6563321055", "6563321066", "6563323133", "6563323659", "6563323964", "6563324184", "6563324210", "6563324220", "6563324257", "6563324286", "6563324287", "6563324288", "6563324310", "6563324351", "6563324352", "6563326666", "6563327591", "6563327798", "6563335532", "6563339999", "6563342310", "6563353530", "6563380000", "6563383632", "6563386622", "6563396666", "6563398616", "6563425222", "6563438999", "6563441177", "6563448222", "6563449999", "6563459999", "6563469400", "6563512442", "6563512775", "6563512833", "6563512868", "6563513126", "6563513288", "6563513341", "6563513352", "6563513360", "6563513363", "6563513390", "6563513511", "6563513566", "6563513883", "6563524727", "6563524728", "6563530000", "6563549020", "6563551140", "6563551142", "6563551143", "6563551145", "6563552000", "6563552183", "6563552488", "6563552959", "6563555000", "6563567012", "6563568015", "6563568225", "6563568233", "6563568300", "6563568622", "6563568633", "6563580000", "6563591199", "6563633333", "6563639999", "6563647559", "6563676767", "6563684652", "6563689999", "6563751600", "6563760000", "6563773131", "6563773400", "6563773800", "6563775925", "6563775926", "6563775939", "6563779999", "6563798000", "6563798855", "6563910000", "6563914753", "6563914762", "6563916017", "6563916074", "6563916097", "6563916100", "6563916174", "6563916231", "6563916343", "6563916521", "6564252500", "6564350000", "6564352662", "6564353500", "6564355077", "6564355398", "6564355471", "6564355473", "6564355864", "6564358277", "6564385122", "6564409999", "6564428999", "6564429999", "6564439999", "6564459999", "6564489999", "6564499999", "6564519999", "6564529999", "6564589999", "6564599999", "6564604923", "6564629999", "6564660000", "6564667460", "6564689999", "6564717300", "6564717808", "6564719999", "6564722669", "6564739999", "6564749999", "6564783947", "6564785068", "6564787010", "6564849999", "6564880999", "6564890999", "6564901111", "6564902927", "6564903039", "6564903177", "6564903203", "6565005000", "6565017000", "6565091757", "6565103000", "6565118595", "6565121322", "6565129290", "6565138967", "6565340219", "6565384452", "6565412668", "6565412672", "6565421122", "6565425024", "6565425314", "6565427788", "6565428976", "6565430433", "6565432515", "6565437049", "6565451616", "6565452123", "6565452127", "6565454078", "6565454482", "6565466738", "6565466739", "6565466743", "6565466832", "6565467169", "6565467170", "6565467171", "6565467172", "6565469221", "6565469293", "6565469498", "6565469539", "6565470000", "6565476391", "6565476393", "6565529999", "6565545655", "6565549999", "6565573083", "6565573503", "6565575076", "6565577495", "6565600056", "6565639999", "6565648999", "6565659999", "6565679999", "6565687000", "6565703053", "6565852999", "6565871999", "6565872999", "6565878423", "6565895555", "6566049999", "6566432555", "6566659999", "6566717117", "6566841503", "6566842031", "6566842032", "6566842036", "6566842037", "6566842038", "6566842041", "6567107548", "6567213507", "6567213508", "6567213509", "6567213511", "6567213512", "6567213513", "6567213516", "6567213518", "6567213649", "6567213677", "6567217705", "6567217709", "6567217720", "6567217721", "6567217722", "6567217723", "6567217724", "6567217726", "6567217727", "6567217728", "6567217729", "6567217773", "6567217776", "6567217777", "6567319000", "6567359213", "6567359999", "6567366622", "6567375133", "6567375522", "6567389116", "6567407022", "6567449999", "6567459999", "6567479999", "6567489999", "6567529999", "6567539999", "6567609999", "6567629999", "6567659999", "6567660576", "6567675718", "6567675885", "6567675963", "6567679999", "6567721234", "6567721260", "6567721270", "6567721288", "6567721411", "6567721433", "6567721693", "6567738235", "6567740000", "6567747667", "6567751133", "6567751605", "6567759999", "6567760168", "6567769036", "6567779999", "6567780955", "6567788999", "6567789999", "6567797090", "6567818999", "6567819999", "6567839999", "6567855785", "6567910000", "6567929999", "6567937428", "6567959731", "6568044780", "6568052871", "6568125555", "6568195333", "6568266111", "6568326832", "6568350000", "6568358000", "6568379655", "6568454401", "6568486999", "6568522999", "6568529999", "6568654612", "6568661111", "6568663066", "6568663070", "6568663073", "6568663485", "6568702439", "6568702444", "6568702447", "6568704509", "6568706020", "6568721110", "6568722220", "6568727683", "6568727684", "6568729999", "6568741733", "6568765976", "6568835885", "6568877477", "6568877888", "6568929999", "6568981800", "6568999999", "6569089000", "6569550200", "6569550424", "6569550456", "6569821114", "6569821390", "18001111111", "1801292992", "18002022059", "18002022062", "18002022063", "18002102500", "18002102600", "18002110888", "18002130622", "18002130626", "18002130629", "18002130659", "18002130694", "18002130740", "18002130747", "18002130779", "18002130800", "18002130806", "18002130821", "18002130838", "18002138585", "18002138979", "18002140797", "18002148427", "18002168900", "18002180000", "18002181343", "18002181348", "18002200183", "18002202340", "18002212616", "18002213127", "18002216666", "18002219001", "18002221211", "18002221712", "18002221928", "18002222121", "18002222585", "18002222888", "18002223399", "18002225295", "18002228888", "18002229999", "18002231313", "18002234811", "18002238793", "18002240000", "18002250167", "18002255225", "18002255322", "18002255353", "18002255432", "18002255529", "18002255577", "18002255582", "18002255632", "18002255777", "18002255782", "18002259911", "18002260806", "18002263320", "18002263866", "18002270000", "18002271188", "18002273920", "18002330000", "18002358577", "18002369050", "18002369999", "18002411292", "18002419999", "18002426033", "18002440000", "18002447200", "18002447357", "18002448999", "18002449999", "18002483963", "18002483964", "18002486028", "18002489999", "18002506675", "18002507999", "18002508999", "18002519999", "18002529999", "18002549999", "18002550000", "18002566657", "18002567377", "18002589999", "18002619999", "18002659999", "18002675002", "18002689999", "18002700141", "18002700855", "18002707988", "18002727777", "18002729999", "18002739999", "18002750388", "18002759999", "18002789999", "18002800000", "18002809999", "18002829999", "18002849999", "18002869999", "18002879999", "18002899999", "18002911601", "18002949999", "18002959999", "18002963469", "18002968999", "18002969999", "18003167508", "18003167541", "18003239829", "18003240017", "18003245025", "18003250000", "18003250518", "18003251120", "18003251184", "18003252488", "18003252489", "18003252493", "18003252494", "18003256647", "18003256666", "18003257619", "18003258282", "18003259220", "18003259806", "18003259819", "18003266027", "18003272265", "18003281877", "18003281878", "18003293237", "18003293434", "18003308658", "18003308660", "18003321055", "18003321066", "18003323133", "18003323659", "18003323964", "18003324184", "18003324210", "18003324220", "18003324257", "18003324286", "18003324287", "18003324288", "18003324310", "18003324351", "18003324352", "18003326666", "18003327591", "18003327798", "18003335532", "18003339999", "18003342310", "18003353530", "18003380000", "18003381034", "18003383632", "18003386622", "18003396666", "18003398616", "18003425222", "18003438999", "18003441177", "18003448222", "18003449999", "18003459999", "18003469400", "18003512442", "18003512775", "18003512833", "18003512868", "18003513126", "18003513288", "18003513341", "18003513352", "18003513360", "18003513363", "18003513390", "18003513511", "18003513566", "18003513883", "18003524727", "18003524728", "18003530000", "18003549020", "18003551140", "18003551142", "18003551143", "18003551145", "18003552000", "18003552183", "18003552488", "18003552959", "18003555000", "18003567012", "18003568015", "18003568225", "18003568233", "18003568300", "18003568622", "18003568633", "18003580000", "18003591199", "18003633333", "18003639999", "18003647559", "18003676767", "18003684652", "18003689999", "18003751600", "18003760000", "18003773131", "18003773400", "18003773800", "18003775925", "18003775926", "18003775939", "18003779999", "18003798000", "18003798855", "18003910000", "18003914753", "18003914762", "18003916017", "18003916074", "18003916097", "18003916100", "18003916174", "18003916231", "18003916343", "18003916521", "18004252500", "18004350000", "18004352662", "18004353500", "18004355077", "18004355398", "18004355471", "18004355473", "18004355864", "18004358277", "18004385122", "18004409999", "18004428999", "18004429999", "18004439999", "18004459999", "18004489999", "18004499999", "18004519999", "18004529999", "18004589999", "18004599999", "18004604923", "18004629999", "18004660000", "18004689999", "18004717300", "18004717808", "18004719999", "18004722669", "18004739999", "18004749999", "18004761600", "18004783947", "18004785068", "18004785478", "18004787010", "18004849999", "18004880999", "18004890999", "18004901111", "18004902927", "18004903039", "18004903177", "18004903203", "18005005000", "18005017000", "18005091757", "18005103000", "18005118595", "18005121322", "18005129290", "18005138967", "18005340219", "18005368333", "18005384452", "18005412668", "18005412672", "18005421122", "18005423366", "18005425024", "18005425314", "18005427788", "18005428976", "18005430433", "18005431825", "18005432515", "18005437049", "18005451616", "18005452123", "18005452127", "18005454078", "18005454482", "18005466738", "18005466739", "18005466743", "18005466832", "18005467169", "18005467170", "18005467171", "18005467172", "18005469221", "18005469293", "18005469498", "18005469539", "18005470000", "18005476391", "18005476393", "18005529999", "18005545655", "18005549999", "18005573083", "18005573503", "18005575076", "18005577495", "18005600056", "18005639999", "18005648999", "18005659999", "18005679999", "18005687000", "18005703053", "18005852999", "18005871999", "18005872999", "18005878423", "18005895555", "18006049999", "18006432555", "18006659999", "18006841503", "18006842031", "18006842032", "18006842036", "18006842037", "18006842038", "18006842041", "18007107548", "18007213507", "18007213508", "18007213509", "18007213511", "18007213512", "18007213513", "18007213516", "18007213518", "18007213649", "18007213677", "18007217705", "18007217709", "18007217720", "18007217721", "18007217722", "18007217723", "18007217724", "18007217726", "18007217727", "18007217728", "18007217729", "18007217773", "18007217776", "18007217777", "18007319000", "18007349177", "18007359213", "18007359999", "18007366622", "18007375133", "18007375522", "18007389116", "18007407022", "18007415567", "18007449999", "18007459999", "18007479999", "18007489999", "18007529999", "18007539999", "18007609999", "18007629999", "18007659999", "18007660576", "18007675718", "18007675885", "18007675963", "18007679999", "18007721234", "18007721260", "18007721270", "18007721288", "18007721411", "18007721433", "18007721693", "18007738235", "18007740000", "18007747667", "18007751133", "18007751605", "18007759999", "18007760168", "18007779999", "18007780955", "18007788999", "18007789999", "18007797090", "18007818999", "18007819999", "18007839999", "18007910000", "18007929999", "18007937428", "18007959731", "18008044780", "18008052871", "18008125555", "18008195333", "18008266111", "18008326832", "18008350000", "18008358000", "18008379655", "18008379790", "18008379979", "18008454401", "18008486999", "18008522999", "18008529999", "18008654612", "18008661111", "18008663066", "18008663070", "18008663073", "18008663485", "18008702439", "18008702444", "18008702447", "18008704509", "18008706020", "18008722220", "18008727683", "18008727684", "18008729999", "18008741733", "18008765976", "18008835885", "18008877477", "18008877888", "18008929999", "18008981800", "18008999999", "18009089000", "18009550200", "18009550424", "18009550456", "18009821114", "18009821390", "861082519067", "861085225050", "861085953400", "862082270828", "862083116688", "862134074800", "862150614567", "862162310110", "862162588800", "862163242200", "864598610110", "865942383725", "8601082519067", "8601085225050", "8601085953400", "8602082270828", "8602083116688", "8602134074800", "8602150614567", "8602162310110", "8602162588800", "8602163242200", "8604598610110", "8605942383725", "1622", "1623", "1627", "1630", "1631", "1632", "1633", "1637", "66555577", "68082222", "68299915", "87206021", "96801622", "96801623", "96801627", "98500000", "6566555577", "6568082222", "6568299915", "6587206021", "6596801622", "6596801623", "6596801627", "6598500000", "18008233333", "18008299918", "18008438288");
    private ManagerConnection managerConnection;

    private EnforcerService sipEnforcerService;
    private EnforcerService ttsEnforcerService;
    private EnforcerService vbcEnforcerService;
    private EnforcerService verifyEnforcerService;

    private boolean initialized = false;
    private CallLoggerController sipOutCallLoggerController;
    private CallLoggerController sipInCallLoggerController;
    private CallLoggerController ttsOutCallLoggerController;

    private RejectedLoggerController sipRejectedLoggerController;
    private RejectedLoggerController ttsRejectedLoggerController;

    private AttemptLoggerController sipAttemptLoggerController;
    private AttemptLoggerController ttsAttemptLoggerController;

    private DynamoDB dynamoDB;

    private final HashSet<String> sgCallerIdBlockPrefixList = new HashSet<String>();

    private final AtomicBoolean fullShutdownInProgress = new AtomicBoolean(false);
    private static final Counter SHORTCODES_CONNECTION_ERROR_COUNTER = Counter.build().name("rtc_dependency_shortcodesdb_error").help("JDBC connection to shortcodes Db").register();
    private static final Gauge SHORTCODESDB_LATENCY = Gauge.build().name("rtc_dependency_shortcodesdb_latency").labelNames("latency").help("Time in seconds to connect to ShortCodesDB").register();

    private static final Counter CS_CALLBACK_ERROR_COUNTER = Counter.build().name("rtc_dependency_cs_callbackurl_error").help("conversation service callback counter").register();

    private static final Gauge DOMAINS_SERVICE_REQUEST_LATENCY =  Gauge.build().name("rtc_dependency_domains_service_latency").labelNames("status").help("Time in seconds Http Requests Latency to Fetch Domains Service routing rules").register();
    private static final Counter DOMAINS_SERVICES_ERROR = Counter.build().name("rtc_dependency_domains_service_error").help("domains service error counter").register();
    private static final Counter DOMAINS_SERVICES_SUCCESS = Counter.build().name("rtc_dependency_domains_service_success").help("domains service success counter").register();
    private boolean randomPoolJMXNotificationFlag;

    private Core() {
        this.shutdownLock = new ReentrantLock();
    }

    public static Core getInstance() {
        return instance;
    }

    public Config getConfig() {
        return this.config;
    }

    public QuotaClient getQuotaClient() {
        return this.quotaClient;
    }

    public VoiceContextCache getVoiceContextCache() {
        return this.voiceContextCache;
    }

    public VoiceMTRouter getMtRouter() {
        return this.mtRouter;
    }

    public boolean isInitialised() {
        return initialized;
    }

    public ProvisioningRandomClient getProvisioningRandomClient() {
        return provisioningRandomClient;
    }

    public VQuotaService getVQuotaService() {
        return this.vQuotaService;
    }

    public Application getApplication(String applicationId) throws ApplicationLookupException {
        if (applicationsCache == null)
            throw new ApplicationLookupException("ApplicationCache is not initialised");
        return applicationsCache.getApplication(applicationId);
    }

    public ApplicationCache getApplicationsCache() {
        if (applicationsCache != null)
            return applicationsCache;
        else
            return null;
    }

    public CallLoggerController getOutboundCallLoggerController(VoiceProduct voiceProduct) {
        return TTS.equals(voiceProduct) ? this.ttsOutCallLoggerController : this.sipOutCallLoggerController;
    }

    public CallLoggerController getInboundCallLoggerController(VoiceProduct voiceProduct) {
        return TTS.equals(voiceProduct) ? null : this.sipInCallLoggerController;
    }

    public RejectedLoggerController getRejectedLoggerController(VoiceProduct voiceProduct) {
        return TTS.equals(voiceProduct) ? this.ttsRejectedLoggerController : this.sipRejectedLoggerController;
    }

    public AttemptLoggerController getAttemptLoggerController(VoiceProduct voiceProduct) {
        return TTS.equals(voiceProduct) ? this.ttsAttemptLoggerController : this.sipAttemptLoggerController;
    }

    public CallbackPipeline<AbstractCallbackTask> getCallbackIssuer() {
        return this.callbackIssuer;
    }

    public QuotaUpdatesExecuter getQuotaUpdatesExecuter() {
        return this.quotaUpdatesExecuter;
    }

    public QuotaQueueExecuter getQuotaQueueExecuter() {
        return this.quotaQueueExecuter;
    }

    public EnvVariableUpdatesExecuter getEnvVariableUpdatesExecuter() {
        return this.envUpdatesExecuter;
    }

    public ApplicationMetrics getApplicationMetrics() {
        return this.applicationMetrics;
    }

    public AsteriskAMIProcessor getAsteriskAMIProcessor() {
        return this.asteriskAMIProcessor;
    }

    public AsteriskTaskExecutor getAsteriskTaskExecutor() {
        return this.asteriskTaskExecutor;
    }

    public BillingManager getBillingManager() {
        return this.billingManager;
    }

    public AgiServerThread getAgiServerThread() {
        return this.agiServerThread;
    }

    public AsteriskPingThread getAsteriskPingThread() {
        return this.asteriskPingThread;
    }

    public NetworksClient getNetworksClient() {
        return this.networksClient;
    }

    public PDDCalculator getPddCalculator() {
        return this.pddCalculator;
    }

    public void setPddCalculator(PDDCalculator pddCalculator) {
        this.pddCalculator = pddCalculator;
    }


    public void init(Config config) throws Exception {
        this.config = config;

        this.applicationMetrics = new ApplicationMetrics();
        this.applicationMetrics.init();

        loadLoggers(config);

        //Initialize pdd calculator
        //add pdd duration in the config
        this.pddCalculator = new PDDCalculator(config.getPddCacheDuration());


        Log.info("===== Registering QuotaAPIService =====");
        this.quotaClient = new QuotaClient(config.getQuotaAPIConfig(), config.getExtendedQuotaAPIConfig());
        Log.info("===== Registering QuotaAPIService [done] =====");


        if (config.getCacheCoreConfig() == null || config.getCacheCoreConfig().getInstances() == null
                || config.getCacheCoreConfig().getInstances().isEmpty()) {
            throw new ServerStartException("VoiceContext cache configuration is missing");
        } else {
            Log.info("===== Registering VoiceContextCache =====");
            this.voiceContextCache = new VoiceContextCache(config.getContextCacheConfig());
            Log.info("===== Registering VoiceContextCache [done] =====");
        }

        Log.info("===== Initializing NetworksClient =====");
        this.networksClient = new NetworksClient(config.getNetworksMatrixConfig());
        Log.info("===== Registering NetworksClient [done] =====");

        Log.info("===== Registering BillingManager =====");
        this.billingManager = new BillingManager(this.quotaClient);
        Log.info("===== Registering BillingManager [done] =====");


        MtRoutingConfig routingConfig = this.config.getMtRoutingConfig();
        if (routingConfig == null)
            throw new ServerStartException("Failed to instantiate the MTRouter class. Config was null...");

        Log.info("===== Registering VoiceMTRouter =====");
        this.mtRouter = new VoiceMTRouter(routingConfig);
        Log.info("===== Registering VoiceMTRouter [done] =====");

        if (config.getCallbackConfig() != null && config.getCallbackConfig().isEnabled()) {
            Log.info("===== Registering CallbackIssuer =====");
            this.callbackIssuer = new CallbackPipeline<>(AbstractCallbackTask.class, config.getCallbackConfig());
            Log.info("===== Registering CallbackIssuer [done] =====");
        }

        Log.info("===== Initialize Async Quota Flag =====");
        initializeAsyncQuotaFlag();

        if (config.getChargingUpdaterConfig() != null && config.getChargingUpdaterConfig().isEnabled()) {
            Log.info("===== Registering QuotaUpdatesExecuter =====");
            this.quotaUpdatesExecuter = new QuotaUpdatesExecuter(config.getChargingUpdaterConfig());
            this.quotaUpdatesExecuter.init();
            Log.info("===== Registering QuotaUpdatesExecuter [done] =====");

            initializeAsyncQuotaExecutor();

            Log.info("===== Registering EnvVariableUpdatesExecuter =====");
            this.envUpdatesExecuter = new EnvVariableUpdatesExecuter(config.getChargingUpdaterConfig());
            this.envUpdatesExecuter.init();
            Log.info("===== Registering EnvVariableUpdatesExecuter [done] =====");

        }

        if(config.getVQuotaServiceConfig() != null && config.getVQuotaServiceConfig().isEnabled()) {
            Log.info("===== Registering VQuotaService clients =====");
            this.vQuotaService = new VQuotaService(config.getVQuotaServiceConfig(), config.getExtendedQuotaAPIConfig());
            Log.info("===== Registering VQuotaService clients [done] =====");
        }

        loadSipProcessors(config);

        initializeProvisioningClients();

        initializeCaches();

        initializeEnforcers();

        initializeCoreInformation();

        Log.info("===== Build SG block CallerId set =====");
        initializeSGCallerIdBlockList();

        Log.info("===== Initialize domestic routing Flag =====");
        initializeDomesticRoutingFlag();

        Log.info("===== Initialize DynamoDB Client =====");
        dynamoDB = new DynamoDB();

        /*Log.info("===== Initialize randomPoolJMXNotification  Flag =====");
        initializeRandomPoolJMXNotificationFlag();
        Randomizer.getInstance().initializeRandomPools();*/

        initialized = true;

        Log.info("---------------- Core initialization .......................");
    }
    private void initializeAsyncQuotaFlag() {
        this.asyncQuotaFlag = false;
        String asyncFlag = getValueFromEnvProperties("quota_async_flag");
        if (StringUtils.isNotBlank(asyncFlag))
            this.asyncQuotaFlag = Boolean.parseBoolean(asyncFlag);
    }

    public boolean isAsyncQuotaFlag() {
        return this.asyncQuotaFlag;
    }

    public void initializeAsyncQuotaExecutor() {
        if (this.asyncQuotaFlag) {
            Log.info("===== Registering QuotaQueueExecuter =====");
            this.quotaQueueExecuter = new QuotaQueueExecuter(config.getChargingUpdaterConfig());
            this.quotaQueueExecuter.init();
            Log.info("===== Registering QuotaQueueExecuter [done] =====");
        }
    }

    public void updateQuotaFlag(boolean asyncQuotaFlag) {
        this.asyncQuotaFlag = asyncQuotaFlag;
    }

    public boolean isBlockUnknownCLIFlag() {
        return this.blockUnknownCLIFlag;
    }

    public void updateBlockUnknownCLIFlag(boolean blockUnknownCLIFlag) {
        this.blockUnknownCLIFlag = blockUnknownCLIFlag;
    }

    private void initializeDomesticRoutingFlag() {
        this.domesticRoutingFlag = false;
        String domesticRoutingFlag = getValueFromEnvProperties("domestic_routing_flag");
        if (StringUtils.isNotBlank(domesticRoutingFlag))
            this.domesticRoutingFlag = Boolean.parseBoolean(domesticRoutingFlag);
    }

    public boolean isDomesticRoutingFlag() {
        return this.domesticRoutingFlag;
    }

    public void updateDomesticRoutingFlag(boolean domesticRoutingFlag) {
        Log.debug("Updating domesticRoutingFlag value changed");
        this.domesticRoutingFlag = domesticRoutingFlag;
    }

    private void initializeSGCallerIdBlockList() {
        for (String prefix : SNG_CALLSPOOF_PREFIX_BLOCKLIST) {
            this.sgCallerIdBlockPrefixList.add(prefix);
        }
    }

    private void initializeEnforcers() {
        this.sipEnforcerService = new SIPEnforcerService();
        this.ttsEnforcerService = new TTSEnforcerService();
        this.vbcEnforcerService = new VBCEnforcerService();
        this.verifyEnforcerService = new VerifyEnforcerService();
    }

    private void initializeProvisioningClients() {
        if (this.config.getProvisioningApiClientConfig() != null) {
            Log.info("========= Initializing Provisioning Random Client ===========");
            this.provisioningRandomClient = new ProvisioningRandomClient(this.config.getProvisioningApiClientConfig());
            Log.info("========= Initializing Provisioning Random Client [done] ===========");
        }
    }

    public synchronized void initializeCaches() {
        final CacheControlConfig cachesConfig = this.config.getCachesConfig();
        if (cachesConfig == null) {
            Log.warn("CacheControlConfig is not present, cannot configure caches");
            return;
        }

        final boolean isEnabled = cachesConfig.isEnabled();
        Log.info("Global cache control: caches are " + (isEnabled ? "enabled" : "DISABLED"));

        Log.info("========= Initializing Applications Cache ===========");
        final CacheConfig applicationCacheConfig = cachesConfig.getCacheConfig(CacheType.APPLICATION);
        if (isEnabled && (applicationCacheConfig != null)) {
            Log.info("Configuration Applications Cache as: " + applicationCacheConfig);
            this.applicationsCache = new ApplicationCache(applicationCacheConfig, this.config.getProvisioningApiClientConfig(), this.config.getApplicationsServiceConfig());
        } else {
            Log.info("Applications Cache disabled");
            this.applicationsCache = null;
        }
        Log.info("========= Initializing Applications Cache [done] ===========");

        // TODO: Other caches?
    }


    private void loadLoggers(Config config) throws ConfigException {

        Log.info("========= Initialize Loggers with CdrsConfig()={}", config.getCdrsConfig());
        String cdrsLogDir = config.getStatsLogDir();
        CdrsConfig cdrsConfig = config.getCdrsConfig();

        //sip-outbound; sip-json-outbound
        try {
            if (config.isCallLoggingEnabled() && config.getCallLoggingFilePrefix() != null) {
                this.sipOutCallLoggerController = new CallLoggerController(cdrsLogDir,
                        config.getCallLoggingFilePrefix(), cdrsConfig);
            }
        } catch (Exception e) {
            throw new ConfigException("Failed to set up call logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        //sip-inbound; sip-json-inbound
        try {
            if (config.isCallInboundLoggingEnabled() && config.getCallInboundLoggingFilePrefix() != null)
                this.sipInCallLoggerController = new CallLoggerController(cdrsLogDir,
                        config.getCallInboundLoggingFilePrefix(), cdrsConfig);
        } catch (Exception e) {
            throw new ConfigException("Failed to set up inbound call logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        //sip-rejected; sip-json-rejected
        try {
            if (config.isRejectedLoggingEnabled() && config.getRejectedLoggingFilePrefix() != null)
                this.sipRejectedLoggerController = new RejectedLoggerController(cdrsLogDir,
                        config.getRejectedLoggingFilePrefix(), cdrsConfig);
        } catch (Exception e) {
            throw new ConfigException("Failed to set up rejected logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        //sip-attempt; sip-json-attempt
        try {
            if (config.isAttemptLoggingEnabled() && config.getAttemptLoggingFilePrefix() != null)
                this.sipAttemptLoggerController = new AttemptLoggerController(cdrsLogDir,
                        config.getAttemptLoggingFilePrefix(), cdrsConfig);
        } catch (Exception e) {
            throw new ConfigException("Failed to set up attempt logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        //tts-outbound; tts-json-outbound
        try {
            if (config.isTTSLogCallLoggingEnabled() && config.getTTSCallLoggingFilePrefix() != null) {
                this.ttsOutCallLoggerController = new CallLoggerController(cdrsLogDir,
                        config.getTTSCallLoggingFilePrefix(), cdrsConfig);
            }
        } catch (Exception e) {
            throw new ConfigException("Failed to set up call tts logger in dir [ " + cdrsLogDir + " ] ....", e);
        }


        try {
            if (config.isTTSRejectedLoggingEnabled() && config.getTTSRejectedLoggingFilePrefix() != null)
                this.ttsRejectedLoggerController = new RejectedLoggerController(cdrsLogDir,
                    config.getTTSRejectedLoggingFilePrefix(), cdrsConfig);
        } catch (Exception e) {
            throw new ConfigException("Failed to set up tts rejected logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        try {
            if (config.isTTSAttemptLoggingEnabled() && config.getTTSAttemptLoggingFilePrefix() != null)
                this.ttsAttemptLoggerController = new AttemptLoggerController(cdrsLogDir,
                        config.getTTSAttemptLoggingFilePrefix(), cdrsConfig);
        } catch (Exception e) {
            throw new ConfigException("Failed to set up tts attempt logger in dir [ " + cdrsLogDir + " ] ....", e);
        }

        Log.info("========= Initialize Loggers [Done]");
    }

    private void loadSipProcessors(Config config) throws ServerStartException {

        //This is used by SIPApp
        ManagerConnection managerConnection = initializeAMIConnection();
        if (managerConnection == null) {
            Log.info("SIP: Didnt initialize managerConnection using initializeAMIConnection");
        }
        this.managerConnection = managerConnection;

        registerUserEvents(managerConnection);

        Log.info("========= Initializing SIP processors =========");
        Log.info("Creating asterisk manager connection " + managerConnection);

        // do the login in the managerConnection...
        try {
            login(managerConnection);
        } catch (IOException | TimeoutException e) {
            AsteriskManagerConfig managerConfig = config.getAsteriskManagerConfig();
            if (managerConfig != null && managerConfig.isInitialRetryEnabled())
                retryLogin(managerConnection, managerConfig);
            else
                throw new ServerStartException("Could not login through Asterisk's Manager Connection", e);
        } catch (IllegalStateException | AuthenticationFailedException e) {
            throw new ServerStartException("Could not login through Asterisk's Manager Connection", e);
        }

        AsteriskManagerConfig asteriskManagerConfig = config.getAsteriskManagerConfig();
        if (asteriskManagerConfig == null)
            throw new ServerStartException("Cannot start Asterisk, no Manager Config detected");

        //Initialise the legs flow cache BEFORE any Asterisk session can be created
        this.legFlowCache = new LegFlowCache();

        // Initialize hangup cache and partial cdr cache BEFORE any Asterisk session can be created
        this.partialCdrEventCache = new PartialCdrEventCache();
        this.cdrUserFieldEventCache = new CdrUserFieldEventCache();

        // Load the AsteriskThreadpoolExecutor and AMIProcessor...
        this.asteriskTaskExecutor = new AsteriskTaskExecutor();
        this.asteriskTaskExecutor.startPurging();
        Log.info("Creating AsteriskAMIProcessor...");

        String productClass = asteriskManagerConfig.getProductClass();
        Set<AsteriskVoiceEventHandler<? extends ManagerEvent>> eventHandlers = getAsteriskHandlersSet(productClass);
        this.asteriskAMIProcessor = new AsteriskAMIProcessor(managerConnection,
                                                             this.asteriskTaskExecutor,
                                                             eventHandlers);
        Log.info("AsteriskAMIProcessor connected [hostname: " + managerConnection.getHostname() + " port: " + managerConnection.getPort() + "]");

        Log.info("Creating AsteriskAGIServer...");

        DefaultAgiServer agiServer = initializeAGIServer();

        if (this.agiServerThread != null && agiServer != null) {
            try {
                this.agiServerThread.startup();
            } catch (IllegalStateException e) {
                throw new ServerStartException("Could not start Asterisk's FastAGI Server", e);
            }
            Log.info("AsteriskAgiServer connected [hostname: " + agiServer.getAddress() + "] port: " + agiServer.getPort() + "] poolSize [" + agiServer.getPoolSize() + "] max [" + agiServer.getMaximumPoolSize() + "]");
            Log.info("========= Initializing SIP processors [done] =========");
        }

        // Start ping thread ...
        if (config.getAsteriskManagerConfig().isPingThreadEnabled()) {
            if (this.asteriskPingThread != null)
                this.asteriskPingThread.die();
            this.asteriskPingThread = new AsteriskPingThread(managerConnection);
            this.asteriskPingThread.setInterval(asteriskManagerConfig.getPingThreadInterval());
            this.asteriskPingThread.setTimeout(asteriskManagerConfig.getPingThreadTimeout());
            this.asteriskPingThread.start();
            Log.info("Created AsteriskPingThread [interval: " + asteriskManagerConfig.getPingThreadInterval() + " ms] [timeout: " + asteriskManagerConfig.getPingThreadTimeout() + " ms]");
        }

    }

    private void registerUserEvents(ManagerConnection managerConnection) {
        // Register your custom UserEvent class here
        managerConnection.registerUserEventClass(CdrUserFieldEvent.class);
    }

    private static void login(ManagerConnection managerConnection) throws IllegalStateException, IOException, AuthenticationFailedException, TimeoutException {
        if (managerConnection == null)
            return;
        managerConnection.login();
        // send status action to tell the AMI that
        // we are ready to receive events
        managerConnection.sendAction(new StatusAction());
    }

    //TALLY: While handling this as part of the Asterisk 16 upgrade, investigate the difference
    // between DisconnectEvent and ShutDown event and what are we doing at each of them.

    //Also as part of Asterisk 16 - get rid of the pdd calculator and take the info from the CDREvent.
    //When we do this, w can also stop using the NewStateEvent

    private Set<AsteriskVoiceEventHandler<? extends ManagerEvent>> getAsteriskHandlersSet(String product) {
        Set<AsteriskVoiceEventHandler<? extends ManagerEvent>> eventHandlers = new HashSet<>();

        eventHandlers.add(new com.nexmo.voice.core.sip.event.BridgeEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.CdrEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.ShutdownEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.DisconnectEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.NewStateEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.BridgeCreateEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.BridgeLeaveEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.BridgeDestroyEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.VarSetEventHandler());
        eventHandlers.add(new com.nexmo.voice.core.sip.event.CdrUserFieldEventHandler());

        return eventHandlers;
    }

    private static void retryLogin(ManagerConnection managerConnection, AsteriskManagerConfig config) throws ServerStartException {
        Log.info("FAILED TO CONNECT TO ASTERISK ON FIRST TRY ::: retrying... ");
        final long numberOfRetries = config.getInitialRetryMax();
        final long timeout = config.getInitialRetryInterval();

        long retryNumber = 0;
        while (retryNumber < numberOfRetries) {
            // do the login in the managerConnection...
            try {
                managerConnection.login();
                // send status action to tell the AMI that
                // we are ready to receive events
                managerConnection.sendAction(new StatusAction());

                Log.info("Logged in to Asterisk SUCCESSFULLY!");
                return;
            } catch (IOException | TimeoutException e) {
                Log.info("Failed to connect [" + retryNumber + "]: Retrying in " + timeout + " ms....");

                try {
                    Thread.sleep(timeout);
                } catch (InterruptedException ex) {
                    throw new ServerStartException("Failed to retry AMI login", ex);
                }
                retryNumber++;

                // try again
            } catch (IllegalStateException | AuthenticationFailedException e) {
                throw new ServerStartException("Could not login through Asterisk's Manager Connection", e);
            }
        }

        throw new ServerStartException("Could not login to the AMI: Exceeded the number of retries! [max: " + numberOfRetries + "]");
    }

    private DefaultAgiServer initializeAGIServer() {
        AsteriskAgiServerConfig asteriskAgiServerConfig = this.config.getAsteriskAgiServerConfig();
        if (asteriskAgiServerConfig == null || !asteriskAgiServerConfig.isEnabled()) {
            this.agiServerThread = null;
            return null;
        }
        DefaultAgiServer agiServer = new DefaultAgiServer(new ClassNameMappingStrategy());
        agiServer.setPort(asteriskAgiServerConfig.getPort());
        agiServer.setPoolSize(asteriskAgiServerConfig.getPoolSize());
        agiServer.setMaximumPoolSize(asteriskAgiServerConfig.getMaximumPoolSize());
        this.agiServerThread = new AgiServerThread(agiServer);
        return agiServer;
    }

    private ManagerConnection initializeAMIConnection() {
        AsteriskManagerConfig asteriskConfig = this.config.getAsteriskManagerConfig();

        if (asteriskConfig == null || !asteriskConfig.isEnabled())
            return null;

        ManagerConnectionFactory managerConnectionFactory = new ManagerConnectionFactory(asteriskConfig.getHostname(),
                                                                                         asteriskConfig.getPort(),
                                                                                         asteriskConfig.getUsername(),
                                                                                         asteriskConfig.getPassword());

        final ManagerConnection managerConnection = managerConnectionFactory.createManagerConnection();
        // TODO add these after we can actually read some events
        // managerConnection.setSocketTimeout(asteriskConfig.getSocketTimeout());
        // managerConnection.setSocketReadTimeout(asteriskConfig.getSocketReadTimeout());

        return managerConnection;
    }

    public Lock acquireShutdownLock() throws InterruptedException {
        Lock shutdownLock = this.shutdownLock;
        boolean lockAcquired = false;
        try {
            do {
                lockAcquired = shutdownLock.tryLock(5, TimeUnit.SECONDS);
            } while (!lockAcquired);
        } catch (InterruptedException ex) {
            throw new InterruptedException("Failed to acquire shutdown lock! Timed out waiting for it...");
        }
        return shutdownLock;
    }

    public void setAsteriskShutdown(boolean shutdown) {
        this.asteriskShutdown = shutdown;
    }

    public boolean isAsteriskShutdown() {
        return this.asteriskShutdown;
    }

    public LegFlowCache getLegFlowCache() {
        return legFlowCache;
    }

    public PartialCdrEventCache getPartialCdrEventCache() {
        return this.partialCdrEventCache;
    }

    public CdrUserFieldEventCache getCdrUserFieldEventCache() {
        return this.cdrUserFieldEventCache;
    }

    public ManagerConnection getManagerConnection() {
        return managerConnection;
    }

    public EnforcerService getSIPEnforcerService() {
        return sipEnforcerService;
    }

    public EnforcerService getTTSEnforcerService() {
        return ttsEnforcerService;
    }

    public EnforcerService getVBCEnforcerService() {
        return vbcEnforcerService;
    }
   
    public EnforcerService getVerifyEnforcerService() {
        return verifyEnforcerService;
    }

    private static void initializeCoreInformation(){
        try {
            final AppStartCDRS currentAndPreviousAppStartupCDRs = AppStartCDRS.getCurrentAndPreviousAppStartupCDRs();
            CORE_INFO.info("host_name", SipAppUtils.getHostName(),
                    "app_release_git_version", currentAndPreviousAppStartupCDRs.getCurrentDeploy().getAppReleaseGitRevision(),
                    "app_release_version", currentAndPreviousAppStartupCDRs.getCurrentDeploy().getAppReleaseVersion(),
                    "start_up_time", LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME));
        } catch (Exception e) {
            CORE_INFO.info("host_name", SipAppUtils.getHostName(), "core_information_failed", e.getMessage());
        }
    }

    public boolean isShutdownInProgress(){
        return fullShutdownInProgress.get();
    }

    /**
     *
     * @return true if shutdown was not in progress
     */
    public boolean startFullShutdown(){
        return fullShutdownInProgress.compareAndSet(false, true);
    }

    public String checkCallerIdHasBlockedPrefixForDestination(String callerId, String destination) {
        // we have blocking rules for SG destination only for now
        String prefix = "";
        if (destination.equalsIgnoreCase("SG")) {
            for (int i=callerId.length(); i>0; i--) {
                if (this.sgCallerIdBlockPrefixList.contains(callerId.substring(0, i))) {
                    prefix = callerId.substring(0, i);
                    break;
                }
            }
        }
        return prefix;
    }

    public String checkCallerIdHasBlockedPrefixForDestination(String callerId, String destination, String account) {
        // we have blocking rules for SG destination only for now
        CallBlocksConfig callBlocksConfig = this.getConfig().getCallBlocksConfig();

        String prefix = "";
        for (int i=callerId.length(); i>0; i--) {
            if (callBlocksConfig.hasCallBlock(account, destination, callerId.substring(0, i))) {
                prefix = callerId.substring(0, i);
                break;
            }
        }

        return prefix;
    }

    public void incrementCSErrorCounter() {
        CS_CALLBACK_ERROR_COUNTER.inc();
    }

    public void incrementShortCodesDBErrorCounter(Exception e) {
        if (ExceptionUtils.indexOfThrowable(e, org.hibernate.exception.GenericJDBCException.class) != -1) {
            Log.debug("Increment ShortCodes DB unreachable counter");
            SHORTCODES_CONNECTION_ERROR_COUNTER.inc();
        }
        return;
    }

    public void updateShortCodesDBLatencyMetrics(double value) {
        SHORTCODESDB_LATENCY.labels("shortcodesdb_latency").set(value);
    }

    public void incrementDomainsServiceSuccessCounter() { DOMAINS_SERVICES_SUCCESS.inc(); }
    public void incrementDomainsServiceErrorCounter() { DOMAINS_SERVICES_ERROR.inc(); }

    public void updateDomainsServiceLatencyMetrics(double value) {
        DOMAINS_SERVICE_REQUEST_LATENCY.labels("domains_service_latency").set(value);
    }

    public DynamoDB getDynamoDB() {
        return dynamoDB;
    }

    private void initializeRandomPoolJMXNotificationFlag() {
        this.randomPoolJMXNotificationFlag = false;
        String randomPoolJMXNotification = getValueFromEnvProperties("random_pool_jmx_notification_flag");
        if (StringUtils.isNotBlank(randomPoolJMXNotification))
            this.randomPoolJMXNotificationFlag = Boolean.parseBoolean(randomPoolJMXNotification);
    }

    public boolean isRandomPoolJMXNotificationFlag() {
        return this.randomPoolJMXNotificationFlag;
    }

    public void updateRandomPoolJMXNotificationFlag(boolean randomPoolJMXNotificationFlag) {
        Log.debug("Updating random pool jmx flag value changed");
        this.randomPoolJMXNotificationFlag = randomPoolJMXNotificationFlag;
    }
}

