package com.nexmo.voice.core.logger;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.thepeachbeetle.common.util.DateUtil;

/**
 * This class route the attempt CDRs writing to the configured cdrs file.
 * Key-value CDRs should be written the XXX-attempt XXX=> sip or tts json
 * Json CDRs should be written to the XXX-json-attempt XXX=> sip or tts
 * 
 * 
 * <AUTHOR>
 *
 */


public class AttemptLoggerController extends SIPAppLoggerController {
    private final static Logger Log = LogManager.getLogger(AttemptLoggerController.class);
    
    public static final String[] AttemptCDROrder = {
            "PRODUCT",
            "PRODUCT-CLASS",
            "PRODUCT-VERSION",
            "DIRECTION",
            "HOST",
            "ID",
            "LEG1-ID",
            "LEG2-ID",
            "SUPERHUB-ACC",
            "ACC",
            "FROM",
            "TO",
            "PREFIX-FROM",
            "PREFIX-TO",
            "FORCED_SENDER",
            "PREFIX-FORCED_SENDER",
            "SIP-DEST-ATTEMPT",
            "TEXT",
            "VOICE",
            "REROUTE-ADDRESS",
            "COUNTRY",
            "NET",
            "GW",
            "GWS",
            "GW_ATTEMPT",
            "CALL_RETRIES",
            "STATUS",
            "REASON",
            "REASON_DESC",
            "START",
            "END",
            "CALL_DATE",
            "ROUTING_SEQ",
            "BACKEND",
            "PDD",
            "REQUEST_IP",
            "CALL_ORIGIN",
            "CALL_TERMINATION",
            "CUSTOMER_DOMAIN",
            "TTS_XTRACE_ID",
            "CALL_BACK_URL",
            "CALL_BACK_METHOD",
            "CLIENT_REFERENCE",
            "REPEAT",
            "MACHINE_DETECTION_TYPE",
            "MACHINE_TIMEOUT",
            "LANGUAGE_NAME",
            "MB_STYLE",
            "CDR_UUID",
            "STIR_SHAKEN",
            "CARRIER_PLATFORM",
            "INTERNAL_FLAG",
            "CHARGEABLE",
            "SRTP",
            "SIP_TRANSPORT",
            "SOURCE_COUNTRY",
            "HANGUP_CAUSE",
            "RULE_ID",
            "BLOCKING_SUBSYSTEM",
            "REGION",
            "ASTERISK_VERSION",
            "PRODUCT-PATH",
            "EMERGENCY_CALL",
            "EMERGENCY_CALL_FAILOVER",
            "EMERGENCY_CALL_LOCATION_ID",
        };
    

    public AttemptLoggerController(String logDir, String pfx, CdrsConfig cdrsConfig) throws Exception {
        super(logDir, pfx, cdrsConfig);
    }

    public synchronized void logAttempt(
            final VoiceContext ctx,
            final String status,
            final String errorCode,
            final String extraDesc,
            final String carrierCallId,
            final String idSuffix,
            final String retryGW,
            final int truncationLength,
            CdrEventUserData eventUserData) {

        if (Log.isDebugEnabled())
            Log.debug("We are logging an ATTEMPT for sessionId: " + ctx.getSessionId() +
                    " status: " + status +
                    " carrierCallId: " + carrierCallId +
                    " idSuffix: " + idSuffix +
                    " retryGW: " + retryGW +
                    " eventUserData: " + eventUserData +
                    " product: " + ctx.getVoiceProduct().name());

        
        
        CDRData cdrData = buildCDRData(ctx, status,
                        errorCode, extraDesc,
                        carrierCallId, idSuffix,
                        retryGW, truncationLength,
                        eventUserData);
        
        logCDR(cdrData, ctx.getSessionId(), AttemptCDROrder, "Finished logging the ATTEMPT CDR ");

    }

    protected CDRData buildCDRData(
            final VoiceContext ctx,
            final String status,
            final String errorCode,
            final String extraDesc,
            final String carrierCallId,
            final String idSuffix,
            final String retryGW,
            final int truncationLength,
            final CdrEventUserData eventUserData) {

        LinkedHashMap<String, String> cdrDetails = buildBaseCDRData(ctx, carrierCallId, idSuffix, truncationLength, eventUserData);
        
        //Override "Attempt CDR" special values
        cdrDetails.put("LEG1-ID", "");
        cdrDetails.put("REROUTE-ADDRESS", "");
        ctx.getApplicationContext().populateParams(cdrDetails);

        if (Objects.nonNull(ctx.getTtsContext())) {
            ctx.getTtsContext().populateParams(cdrDetails);
        }
        
        //Special handling of the ID:
        //Original code:  sb.append("\"ID=").append(SipAppUtils.getNexmoUUID(ctx.getSessionId())).append("\","); 
        String id = SipAppUtils.getNexmoUUID(ctx.getSessionId());
        cdrDetails.put("ID", id);
        
        cdrDetails.put("STATUS", status);
        cdrDetails.put("REASON", errorCode);
        cdrDetails.put("REASON_DESC", SIPAppLogger.handleQuote(extraDesc));


        long creationTime = ctx.getContextCreationDate();
        long startTime = ctx.getBillingInfo().getCallStartTime();
        if (startTime == 0)
            startTime = creationTime;
        long endTime = ctx.getBillingInfo().getCallEndTime();
        if (endTime == 0)
            endTime = startTime;

        String start = DateUtil.getMessageLogTimestampString(new Date(startTime));
        String end = DateUtil.getMessageLogTimestampString(new Date(endTime));
        String creationDate = DateUtil.getMessageLogTimestampString(new Date(creationTime));

        cdrDetails.put("START", start);
        cdrDetails.put("END", end);
        cdrDetails.put("CALL_DATE", creationDate);

        if (Objects.nonNull(ctx.getRegion())) {
            cdrDetails.put("REGION", ctx.getRegion());
        }

        cdrDetails.put("CHARGEABLE", String.valueOf(ctx.isChargeable()));

        if (Objects.nonNull(ctx.getAsteriskVersion())) {
            cdrDetails.put("ASTERISK_VERSION", ctx.getAsteriskVersion().getCdrVersion());
        }

        CDRData cdrData = new CDRData(cdrDetails);
        return cdrData;
    }
    

}
