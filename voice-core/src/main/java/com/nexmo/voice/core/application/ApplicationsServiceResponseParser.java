package com.nexmo.voice.core.application;

import org.json.JSONException;
import org.json.JSONObject;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.application.ApplicationLookupException;

public class ApplicationsServiceResponseParser {
    private final static Logger Log = LogManager.getLogger(ApplicationsServiceResponseParser.class);

    private ApplicationsServiceResponseParser() {
        // Prevent construction
    }

    public static Application fromJSON(JSONObject json) throws JSONException {
//      id:
//          type: string
        String id = json.getString("id");

//        name:
//          type: string
//          minLength: 1
//          maxLength: 255
        String name = json.getString("name");

//        api_key:
//          type: string
        String apiKey = json.getString("api_key");

//        type:
//          type: string
//          description: standard(default) or demo
//          enum:
//            - standard
//            - demo
//          default: standard

//        status:
//          type: string
//          enum:
//            - enabled
//            - disabled
//            - banned
//          default: enabled
        StatusType status = StatusType.ENABLED;
        if (json.has("status")) {
            status = StatusType.getMatching(json.getString("status"));
        }


//        status_details:
//          type: object
//          properties:
//            modified_date_time:
//              type: string
//            reason:
//              type: string
//              description: Reason for status change
//            modified_by:
//              type: string
//              description: Modifier account_id or application
//          description: Details for the last status change
      //JSONObject statusDetails = json.getJSONObject("status_details");

//        security:
//          type: object
//          properties:
//            auth:
//              type: array
//              items:
//                type: object
//                properties:
//                  id:
//                    type: string
//                  type:
//                    type: string
//                  public_key:
//                    type: string
//                  signature_method:
//                    type: string
//                  created_timestamp:
//                    type: string
      //JSONObject security = json.getJSONObject("security");

        Boolean paymentEnabled = null;
        String region = null;
        if (json.has("capabilities")) {
            JSONObject capabilities = json.getJSONObject("capabilities");
//        capabilities:
//          type: object
//          properties:

//            messages:
//              type: object
//              properties:
//                webhooks:
//                  type: object
//                  properties:
//                    inbound_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                          enum:
//                            - POST
//                    status_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                          enum:
//                            - POST

            if (capabilities.has("voice")) {
                JSONObject voice = capabilities.getJSONObject("voice");
//            voice:
//              type: object
//              properties:
//                webhooks:
//                  type: object
//                  properties:
//                    answer_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                        socket_timeout:
//                          type: integer
//                        connect_timeout:
//                          type: integer
//                    event_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                        socket_timeout:
//                          type: integer
//                        connect_timeout:
//                          type: integer
//                    fallback_answer_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                        socket_timeout:
//                          type: integer
//                        connect_timeout:
//                          type: integer
//                signed_callbacks:
//                  type: boolean
//                paymentEnabled: // FIXME: API docs are wrong here...
//                  type: boolean
                if (voice.has("payment_enabled")) {
                    paymentEnabled = voice.getBoolean("payment_enabled");
                }
//                region:
//                  type: string
                if (voice.has("region")) {
                    region = voice.getString("region");
                }
            }

//            rtc:
//              type: object
//              properties:
//                webhooks:
//                  type: object
//                  properties:
//                    event_url:
//                      type: object
//                      properties:
//                        address:
//                          type: string
//                          format: uri
//                        http_method:
//                          type: string
//                push_notifications:
//                  type: object
//                  additionalProperties:
//                    type: object
//                    required:
//                      - enabled
//                      - template
//                    properties:
//                      enabled:
//                        type: boolean
//                      template:
//                        type: object
//                        properties:
//                          notification:
//                            type: object
//                            properties:
//                              title:
//                                type: string
//                              body:
//                                type: string
//                              icon:
//                                type: string
//                          data:
//                            type: object
//

//            vbc:
//              type: object
        }

        return new Application(id, name, apiKey, region, paymentEnabled);
    }

}
