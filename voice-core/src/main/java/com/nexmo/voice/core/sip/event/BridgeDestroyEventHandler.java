package com.nexmo.voice.core.sip.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeDestroyEvent;


/**
 * Created on 01/10/23.
 *
 * <AUTHOR>
 */
public class BridgeDestroyEventHandler extends AsteriskVoiceEventHandler<BridgeDestroyEvent> {

    private static final Logger Log = LogManager.getLogger(BridgeDestroyEventHandler.class);

    public BridgeDestroyEventHandler() {
        super(BridgeDestroyEvent.class);
    }

    @Override
    public void handle(BridgeDestroyEvent event) throws VoiceEventHandlerException {
        Log.info("Processing BridgeDestroy Event ['" + event + "'] hashCode=" + event.hashCode());
    }
}
