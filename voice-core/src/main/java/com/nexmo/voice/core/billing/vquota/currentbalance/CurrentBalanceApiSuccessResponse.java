package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.thepeachbeetle.common.iso.Currency;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.fasterxml.jackson.databind.JsonNode;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;

import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;

public class CurrentBalanceApiSuccessResponse {
    public static AccountBalance handleResponse(JsonNode rootNode) throws QuotaException {
        try {
            JsonNode accountBalanceNode = rootNode.get("accountBalance");
            if (accountBalanceNode == null) {
                throw new QuotaException("Field 'accountBalance' is missing in success response");
            }

            String accountId = accountBalanceNode.has("accountId") ? accountBalanceNode.get("accountId").asText() : null;

            final JsonNode balanceNode = accountBalanceNode.get("balance");
            final JsonNode limitNode = accountBalanceNode.get("creditLimit");
            final String balanceStr = balanceNode != null ? StringUtils.trimToNull(balanceNode.asText()) : null;
            final String limitStr = limitNode != null ? StringUtils.trimToNull(limitNode.asText()) : null;
            double balance = 0.0;
            double creditlimit = 0.0;
            if (balanceStr != null) {
                balance = Double.parseDouble(balanceStr);
            }
            if (limitStr != null) {
                creditlimit = Double.parseDouble(limitStr);
            }

            String currencyCode = accountBalanceNode.has("currency") ? accountBalanceNode.get("currency").asText() : null;
            Currency currency = (currencyCode != null && !currencyCode.isEmpty()) ? Currency.get(currencyCode) : null;

            boolean quotaEnabled = accountBalanceNode.has("quotaEnabled") && accountBalanceNode.get("quotaEnabled").asBoolean();

            BigDecimal requiredFreeBalance = null;
            JsonNode requiredFreeBalanceNode = accountBalanceNode.get("requiredFreeBalance");
            if (requiredFreeBalanceNode != null && !requiredFreeBalanceNode.isNull()) {
                requiredFreeBalance = new BigDecimal(requiredFreeBalanceNode.asText());
            }

            Boolean requiredFreeBalanceAvailable = null;
            JsonNode requiredFreeBalanceAvailableNode = accountBalanceNode.get("requiredFreeBalanceAvailable");
            if (requiredFreeBalanceAvailableNode != null && !requiredFreeBalanceAvailableNode.isNull()) {
                requiredFreeBalanceAvailable = accountBalanceNode.get("requiredFreeBalanceAvailable").asBoolean();
            }

            return new AccountBalance(
                    accountId,
                    BigDecimal.valueOf(balance),
                    BigDecimal.valueOf(creditlimit),
                    currency,
                    quotaEnabled,
                    requiredFreeBalance,
                    requiredFreeBalanceAvailable
            );
        } catch (NullPointerException | IllegalArgumentException e) {
            throw new QuotaException("Error parsing account balance data for response of current-balance API", e);
        }
    }
}

