package com.nexmo.voice.core.callblocking;

import org.json.JSONException;
import org.json.JSONObject;

public class CallBlockingServiceResponseParser {

    private static final String DEFAULT_RULE_ID = "-1";

    private CallBlockingServiceResponseParser() {
        // Prevent construction
    }

    public static CallBlockingResponse fromJSON(JSONObject json) throws J<PERSON>NException {
        String ruleId = DEFAULT_RULE_ID;
        if (json.has("rule_id") && json.get("rule_id") != null) {
            ruleId = json.getString("rule_id");
        }
        String action = json.optString("action", "allow");
        String subsystem = json.optString("subsystem", "prefix");
        return new CallBlockingResponse(ruleId, action, subsystem);
    }

}
