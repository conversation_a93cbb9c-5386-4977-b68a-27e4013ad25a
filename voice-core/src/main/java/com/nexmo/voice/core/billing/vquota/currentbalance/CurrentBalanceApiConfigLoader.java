package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;
import java.math.BigDecimal;

public class CurrentBalanceApiConfigLoader extends NestedXmlHandler {
    private String host;
    private String basePath;
    private BigDecimal minBalance;
    private int timeout;

    private CurrentBalanceApiConfig config;

    public CurrentBalanceApiConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public CurrentBalanceApiConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.host = xmlContent.getAttribute(CurrentBalanceApiConfig.API_HOST_URL_ATTR, true);
            this.basePath = xmlContent.getAttribute(CurrentBalanceApiConfig.API_BASE_PATH_ATTR, true);
            this.minBalance = parseBigDecimal(xmlContent.getAttribute(CurrentBalanceApiConfig.API_MIN_BALANCE_VALUE_ATTR, true), BigDecimal.ZERO);
            this.timeout = parseInt(xmlContent.getAttribute(CurrentBalanceApiConfig.API_TIMEOUT_ATTR, true));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new CurrentBalanceApiConfig(this.host, this.basePath, this.minBalance, this.timeout);
            notifyComplete();
        }
    }
}
