package com.nexmo.voice.core.billing.vquota.priceimpact;

import org.jdom.Element;

public class PriceImpactApiConfig implements java.io.Serializable {
    private static final long serialVersionUID = -5267334692017271222L;
    public static final String ROOT_NODE = "price-impact-api";
    public static final String API_HOST_URL_ATTR = "host-url";
    public static final String API_BASE_PATH_ATTR = "base-path";
    public static final String API_TIMEOUT_ATTR = "timeout";

    private final String host;
    private final String basePath;
    private final int timeout;

    public PriceImpactApiConfig(String host, String basePath, int timeout) {
        this.host = host;
        this.basePath = basePath;
        this.timeout = timeout;
    }

    public String getHost() {
        return host;
    }

    public String getBasePath() {
        return basePath;
    }

    public int getTimeout() {
        return timeout;
    }

    public String constructUri() {
        StringBuilder sb = new StringBuilder();
        sb.append(host);
        if (!basePath.startsWith("/")) {
            sb.append("/");
        }
        sb.append(basePath);
        return sb.toString();
    }

    public Element toXML() {
        Element priceImpactApiElement = new Element(ROOT_NODE);
        priceImpactApiElement.setAttribute(API_HOST_URL_ATTR, this.host);
        priceImpactApiElement.setAttribute(API_BASE_PATH_ATTR, this.basePath);
        priceImpactApiElement.setAttribute(API_TIMEOUT_ATTR, Integer.toString(this.timeout));
        return priceImpactApiElement;
    }

    @Override
    public String toString() {
        return "PriceImpactApiConfig{" +
                "host='" + host + '\'' +
                ", basePath='" + basePath + '\'' +
                ", timeout=" + timeout +
                '}';
    }
}
