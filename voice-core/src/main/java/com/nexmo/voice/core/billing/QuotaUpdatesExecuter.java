package com.nexmo.voice.core.billing;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.nexmo.voice.config.charging.ChargingUpdaterConfig;
import com.nexmo.voice.config.charging.UpdateTaskConfig;
import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;

/**
 * This class is initiated during the server startup.
 * It starts 1 thread per product which run every second.
 * The thread has one task which is executed again and again. The task is scanning all the existing ChargingContexts
 *     of this product and update the quota as required. The quota update is happening every 6 seconds.
 *
 * The relevant config in sip.xml:
 *
 *     <charging-updater enabled="true" shutdown-interval-count="30" shutdown-interval-unit="second">
 *        <update-task product-class="sip_asterisk" sweeping-interval-count="1" sweeping-interval-unit="second"/>
 *        <update-task product-class="api" sweeping-interval-count="1" sweeping-interval-unit="second" />
 *     </charging-updater>
 *
 *     NOTICE:
 *     The configuration is using the seconds as unit. While building the ChargingUpdaterConfig from this configuration
 *     these values are converted to milliseconds, and here they are referred as such.
 *
 *     The concept:
 *     There is one VoiceContext per leg of a call. Each VoiceContext includes BillingInfo that holds
 *     the details of the call billing.
 *
 *     The QuotaUpdatesExecuter holds a scheduled-threads-pool.
 *     Each thread in the pool is activated every 1 second, and run a UpdateQuotaTask.
 *     The UpdateQuotaTask instance is specific per product-type:
 *         In SIPApp: sip_asterisk, api
 *
 *     The task is handling all the legs of a specific product.
 *
 */

public class QuotaUpdatesExecuter {

    private static final Logger Log = Logger.getLogger(QuotaUpdatesExecuter.class);

    private final ChargingUpdaterConfig config;
    private final ScheduledExecutorService pool;

    public QuotaUpdatesExecuter(ChargingUpdaterConfig config) {
        this.config = config;

        final int numberOfThreads = config.getUpdateTaskConfigs().size();
        this.pool = Executors.newScheduledThreadPool(numberOfThreads); // 1 thread per product

    }

    public void init() {
        Log.info(">>>> Starting QuotaUpdatesExecuter tasks...");
        for (UpdateTaskConfig config : this.config.getUpdateTaskConfigs()) {
            final VoiceApplicationType handledProduct = config.getVoiceApplicationType();
            final long sweepingPeriod = config.getSweepingPeriod();

            //scheduleAtFixedRate means that a task will start every second.
            this.pool.scheduleAtFixedRate(new QuotaUpdateTask(handledProduct),
                                          0, // no initial delay
                                          sweepingPeriod,
                                          TimeUnit.MILLISECONDS);
            Log.info(" # Created [" + handledProduct +"] update task sweeping every " + sweepingPeriod + " ms");
        }

        Log.info("QuotaUpdatesExecuter initialization was successful. Tasks are now sweeping the voice context cache...");
    }

    //This method is called when we take down the SIPApp. Its goal is to stop the updater threads
    public void shutdown() {
        Log.info(">>>> Shutting down QuotaUpdatesExecuter...");

        try {
            this.pool.shutdown();
            boolean successfulShutdown = this.pool.awaitTermination(this.config.getShutdownPeriod(), TimeUnit.MILLISECONDS);
            if (successfulShutdown)
                Log.info("Shutdown all tasks successfully!");
            else
                Log.error("FAILED TO SHUTDOWN ALL TASKS SUCCESSFULLY!");

            // TODO persist cache to disk? generate report of connections open? whatever...
        } catch (InterruptedException ex) {
            Log.error("Something went terribly wrong!", ex);
        }
    }
}
