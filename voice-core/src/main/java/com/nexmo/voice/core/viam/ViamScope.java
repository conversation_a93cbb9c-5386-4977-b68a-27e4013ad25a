package com.nexmo.voice.core.viam;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ViamScope {

    private static final String ANY_API_KEY_VALUE = "any";

    private static final String ANY_APPLICATION_ID_VALUE = "any";

    private static final String KEY_VALUE_SEPARATOR = ":";

    private static final String SCOPE_SEPARATOR = " ";

    private enum ScopeType {
        API_KEY("api_key"),
        APPLICATION_ID("application_id"),
        ;

        private String value;

        ScopeType(String value) {
            this.value = value;
        }

        String value() {
            return this.value;
        }
    }

    private Map<ScopeType, Set<String>> scopes;

    public ViamScope() {
        scopes = new HashMap<>();
    }

    public ViamScope withApiKey(String apiKey) {
        if(!scopes.containsKey(ScopeType.API_KEY)) {
            scopes.put(ScopeType.API_KEY, new HashSet<>());
        }
        scopes.get(ScopeType.API_KEY).add(apiKey);
        return this;
    }

    public ViamScope withAnyApiKey() {
        return this.withApiKey(ANY_API_KEY_VALUE);
    }

    public ViamScope withApplicationId(String applicationId) {
        if(!scopes.containsValue(ScopeType.APPLICATION_ID)) {
            scopes.put(ScopeType.APPLICATION_ID, new HashSet<>());
        }
        scopes.get(ScopeType.APPLICATION_ID).add(applicationId);
        return this;
    }

    public ViamScope withAnyApplicationId() {
        return this.withApplicationId(ANY_APPLICATION_ID_VALUE);
    }

    public String build() {
        String output = "";
        for(ScopeType key : this.scopes.keySet()) {
            for(String value : this.scopes.get(key)) {
                String thisKV = key.value() + KEY_VALUE_SEPARATOR + value;
                if(!output.isEmpty()) {
                    output = output + SCOPE_SEPARATOR;
                }
                output = output + thisKV;
            }
        }
        return output;
    }

    @Override
    public String toString() {
        return build();
    }

}
