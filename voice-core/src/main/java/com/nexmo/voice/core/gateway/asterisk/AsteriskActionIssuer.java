package com.nexmo.voice.core.gateway.asterisk;

import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.action.HangupAction;
import org.asteriskjava.manager.action.SetVarAction;

import com.nexmo.voice.core.Core;


public class AsteriskActionIssuer {

    public static void finishCall(String channelId,
                                  HangupCause hangupCause) throws Exception {
        /*
         * SEND:
         *      ACTION: Hangup
         *      Channel: SIP/x7065558529-99a0
         *
         * RECEIVE:
         *      Event: Unlink
         *
         *      Channel1: SIP/***************-44df88e8
         *      Channel2: SIP/x7065558529-99a0
         *      Uniqueid1: 1124989110.20473
         *      Uniqueid2: 1124989110.20474
         *
         *      Event: Hangup
         *      Channel: SIP/x7065558529-99a0
         *      Uniqueid: 1124989110.20474
         *      Cause: 16
         *
         *      Event: Hangup
         *      Channel: SIP/***************-44df88e8
         *      Uniqueid: 1124989110.20473
         *      Cause: 16
         *
         *      Response: Success
         *      Message: Channel Hungup
         */
        HangupAction hangupAction = new HangupAction();
        hangupAction.setChannel(channelId);
        hangupAction.setCause(hangupCause.getCode());
        Core.getInstance().getAsteriskAMIProcessor().tryExecuting(hangupAction);
    }


    public static void setVariable(String channelId, String variableName, String variableValue) throws Exception {
        SetVarAction setVarAction = new SetVarAction();
        setVarAction.setVariable(variableName);
        setVarAction.setValue(variableValue);
        setVarAction.setChannel(channelId);
        Core.getInstance().getAsteriskAMIProcessor().tryExecuting(setVarAction);
    }

}
