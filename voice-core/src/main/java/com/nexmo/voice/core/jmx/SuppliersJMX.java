package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.Set;

import com.nexmo.voice.config.gateway.*;
import org.apache.log4j.Logger;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.core.Core;

import com.thepeachbeetle.common.xml.XmlUtil;


/**
 * <AUTHOR>
 */
public class SuppliersJMX implements SuppliersJMXMBean {

    private static final Logger Log = Logger.getLogger(SuppliersJMX.class.getName());

    @Override
    public String dumpSuppliersListToLog() {
        Log.info("About to dump suppliers list");

        Config config = Core.getInstance().getConfig();
        config.dumpXmlToLog();

        return "Hopefully that worked... check the logs";
    }

    @Override
    public String viewSuppliersMetadata() {
        Log.info("About to fetch suppliers metadata from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final Config config = Core.getInstance().getConfig();
        final GatewayInfoMatrixConfig gatewayConfig = config.getSIPGatewayInfoMatrixConfig();
        final GatewayInfoMatrixSource gatewaySource = config.getSIPGatewayInfoMatrixSource();

        sb.append("Source: ");
        sb.append(gatewaySource.getSource());
        sb.append("<br><br>");

        sb.append("Number of suppliers: ");
        sb.append(gatewayConfig.getGatewayCount());
        sb.append("<br><br>");

        return sb.toString();
    }

    @Override
    public String listSuppliers() {
        Log.info("About to fetch all suppliers from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final Config config = Core.getInstance().getConfig();

        final Set<String> suppliers = config.getSIPGatewayInfoMatrixConfig().getGatewayNames();
        for (String key : suppliers) {
            sb.append(key);
            sb.append("<br><br>");
        }

        return sb.toString();
    }

    @Override
    public String viewSupplier(String supplier) {
        Log.info("About to fetch supplier details from config for supplier " + supplier);

        if (supplier == null) {
            return "ERROR: No supplier name provided";
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("Supplier: \"");
        sb.append(supplier);
        sb.append("\" => ");

        final Config config = Core.getInstance().getConfig();
        final SupplierMappingConfig supplierConfig = config.getSIPGatewayInfoMatrixConfig().getGatewayInfo(supplier);

        if (supplierConfig == null) {
            sb.append("NOT FOUND");
        } else {
            sb.append("<br><br>\n<pre>\n");
            String xml = supplierConfig.toString();
            String escaped = XmlUtil.XMLClean(xml);
            sb.append(escaped);
            sb.append("</pre>\n");
        }

        return sb.toString();
    }

    @Override
    public String loadSuppliersListFromFile(String filename) {
        Log.info("About to load new suppliers config from " + filename);

        if (filename == null) {
            return "ERROR: No filename provided.";
        }

        final File f = new File(filename);
        if (!f.exists()) {
            return "ERROR: File \"" + filename + "\" does not exist";
        }

        long timeCalled = System.currentTimeMillis();
        final StringBuilder sb = new StringBuilder();
        sb.append("Loading from file: \"");
        sb.append(filename);
        sb.append("\" => ");

        final GatewayInfoMatrixSource newSource = new GatewayInfoMatrixSource(filename);
        final SIPGatewayInfoMatrixConfig newConfig = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(filename);
        if (newConfig != null) {
            sb.append("SUCCESS!<br><br>");

            final GatewayInfoMatrixConfig oldConfig = Core.getInstance().getConfig().getSIPGatewayInfoMatrixConfig();

            if (newConfig.hasGateways()) {
                // Compare old/new configs and report the difference
                final String diffs = GatewayInfoMatrixConfigUtils.compareConfigs(oldConfig, newConfig);
                sb.append(diffs);

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setSIPGatewayInfoMatrixConfig(newConfig, newSource);
            } else {
                sb.append("New config contains no suppliers: REJECTED!");
            }
        } else {
            sb.append("FAILED!");
        }
        long timeTaken = System.currentTimeMillis() - timeCalled;
        Log.info("::: SuppliersJMX ::: loadSuppliersListFromFile call-total-time [ " + timeTaken + "]");
        return sb.toString();
    }

}
