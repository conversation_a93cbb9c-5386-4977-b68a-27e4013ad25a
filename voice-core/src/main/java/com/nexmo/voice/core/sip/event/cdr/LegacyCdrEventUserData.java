package com.nexmo.voice.core.sip.event.cdr;

import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.types.CarrierPlatform;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;

import java.util.Map;
import java.util.Objects;

public class LegacyCdrEventUserData extends AbstractCdrEventUserData {

    private static final Logger Log = LogManager.getLogger(LegacyCdrEventUserData.class.getName());

    public LegacyCdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        super(event, channelUniqueId);
    }

    protected void handleMissingUserFieldEvent(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {

        // Try to create the userField data for CANCEL event. If unsuccessful - throw an
        // exception
        if (!attemptFixingUserFieldData(event, channelUniqueId)) {
            Log.error("Failed to fix the missing userField, missing CANCEL indicators. channelUniqueId = "
                    + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField not found. channelUniqueId: " + event.getUniqueId());
        }
    }

    protected void parseUserField(String userField, String channelUniqueId) throws VoiceEventHandlerException {
        if (Log.isDebugEnabled()) {
            Log.debug("Parsing userField {} with channelUniqueId {} ", userField, channelUniqueId);
        }
        Map<String, String> userFieldsMap = extractUserField(userField, channelUniqueId);

        // Fetch the HANGUPCAUSE and DIALSTATUS - these are mandatory in any scenario
        // If their values are wrong or missing, an exception is thrown
        setHangupCause(userFieldsMap.get(CdrEventUserData.HANGUP_CAUSE), channelUniqueId);
        setDialStatus(userFieldsMap.get(CdrEventUserData.DIAL_STATUS), channelUniqueId);

        for (String userFieldKey : userFieldsMap.keySet()) {
            String userFieldValue = userFieldsMap.get(userFieldKey);

            switch (userFieldKey) {
                case CdrEventUserData.GATEWAYS_LIST:
                    gatewaysList = userFieldValue;
                    continue;
                case CdrEventUserData.CURRENT_GATEWAY:
                    currentGateway = userFieldValue;
                    continue;
                case CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT:
                    setGatewayAttemptDetails(userFieldValue, channelUniqueId);
                    continue;
                case CdrEventUserData.CHANNEL_2_ID:
                    channel2Id = userFieldValue;
                    continue;
                case CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO:
                    setTargetFallbackAttemptNo(userFieldValue, channelUniqueId);
                    continue;
                case CdrEventUserData.SIP_TARGET_FALLBACK_ALTERNATIVES:
                    setTargetFallbackAlternatives(userFieldValue, channelUniqueId);
                    continue;
                case CdrEventUserData.NEXMO_UUID:
                    nexmoUUID = userFieldValue;
                    continue;

                case CdrEventUserData.DIAL_STATUS: // already fetched
                case CdrEventUserData.HANGUP_CAUSE:
                    continue;

                case CdrEventUserData.TO_INPS:
                    carrierPlatform = CarrierPlatform.from(userFieldValue);
                    continue;

                default: // unexpected elements
                    additionalParams.put(userFieldKey, userFieldValue);
                    continue;
            }
        }
    }

    @Override
    public long getReportedBillableSeconds() {
        if (Objects.nonNull(cdrEventBillableSeconds)) {
            Log.info("{} calculated {} billable seconds for this call", nexmoUUID, cdrEventBillableSeconds.longValue());
            return cdrEventBillableSeconds.longValue();
        }
        Log.warn("{} had a null billable second for this call, while default to 0", nexmoUUID);
        return 0;
    }
}
