package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nexmo.voice.config.reload.*;
import com.nexmo.voice.core.randomize.Randomizer;
import com.thepeachbeetle.messaging.hub.core.provisioning.exceptions.ProvisioningException;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import org.apache.log4j.Logger;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.hibernate.exception.GenericJDBCException;

import com.thepeachbeetle.common.app.jmx.AbstractReloadJMX;
import com.thepeachbeetle.common.util.DateUtil;
import com.thepeachbeetle.common.xml.LoaderException;

import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.config.accounts.AccountCapabilitiesConfig;
import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.nexmo.voice.core.Core;

import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;


/**
 * <AUTHOR> Cook
 */
public class ReloadJMX extends AbstractReloadJMX implements ReloadJMXMBean {

    private static final Logger Log = Logger.getLogger(ReloadJMX.class.getName());
    private static final Counter CONFIGDB_CONNECTION_ERROR = Counter.build().name("rtc_dependency_configdb_error").help("JDBC connection on reload to configDb").register();
    private static final Gauge CONFIGDB_LATENCY = Gauge.build().name("rtc_dependency_configdb_latency").labelNames("latency").help("Time in seconds to connect to configDB").register();

    public ReloadJMX() {
    }

    @Override
    public String getUptime() {
        return "" + (System.currentTimeMillis() - Core.getInstance().getConfig().getTimeLoaded());
    }

    @Override
    public String getTimeStarted() {
        return DateUtil.createCommonTimestampString(new Date(Core.getInstance().getConfig().getTimeLoaded()));
    }

    @Override
    public String getConfigXmlFileLocation() {
        return Core.getInstance().getConfig().getConfigXmlFileLocation().getAbsolutePath();
    }

    private void logTimeTaken(long startTimeInNano, String functionName) {
        double timeTaken = (System.nanoTime() - startTimeInNano)/ NANOSECONDS_PER_SECOND;
        Log.info("ReloadJMX::" + functionName + " time-taken [ " + timeTaken * 1000 + " ]");
    }

    @Override
    public void reloadConfig() {
        Log.debug("reloadConfig()");
        synchronized (this) {
            final long timer = System.nanoTime();
            Config config = Core.getInstance().getConfig();
            File configXmlFileLocation = config.getConfigXmlFileLocation();
            ConfigReader configReader = new ConfigReader();
            try {
                configReader.read(configXmlFileLocation);
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                throw new RuntimeException(err, e);
            }
            Config newConfig = configReader.getConfig();
            try {
                Core.getInstance().init(newConfig);
                logTimeTaken(timer, "reloadConfig");
            } catch (Exception e) {
                Log.error("....... failed to re-load configs ....", e);
                throw new RuntimeException("....... failed to re-load configs ....", e);
            }
        }

        Log.info(">>>>>>>>>>>>>>>>>>>> CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void reloadChargingConfig() {
        Log.debug("reloadChargingConfig()");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            final File configXmlFileLocation = config.getConfigXmlFileLocation();
            final ReloadChargingConfigReader configReader = new ReloadChargingConfigReader(config);
            try {
                configReader.read(configXmlFileLocation);
                logTimeTaken(timer, "reloadChargingConfig");
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                throw new RuntimeException(err, e);
            }
        }
        Log.info(">>>>>>>>>>>>>>>>>>>> PRICING-CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void reloadMtPriceMatrix() {
        Log.debug("reloadMtPriceMatrix()");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            final File configXmlFileLocation = config.getConfigXmlFileLocation();
            final ReloadMtPriceMatrixConfigReader configReader = new ReloadMtPriceMatrixConfigReader(config);
            try {
                configReader.read(configXmlFileLocation);
                CONFIGDB_LATENCY.labels("configdb_latency").set((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                logTimeTaken(timer, "reloadMtPriceMatrix");
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                incrementConfigDBErrorCounter(e);
                throw new RuntimeException(err, e);
            }
        }
        Log.info(">>>>>>>>>>>>>>>>>>>> MT-PRICING-MATRIX-CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void reloadMoPriceMatrix() {
        Log.warn("reloadMoPriceMatrix() - NOT IMPLEMENTED!");
    }

    @Override
    public void reloadGatewayInfoMatrixConfig() {
        Log.debug("reloadGatewayInfoMatrixConfig()");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            final File configXmlFileLocation = config.getConfigXmlFileLocation();
            final ReloadSIPGatewayInfoMatrixConfigReader sipConfigReader = new ReloadSIPGatewayInfoMatrixConfigReader(config);
            final ReloadTTSGatewayInfoMatrixConfigReader ttsConfigReader = new ReloadTTSGatewayInfoMatrixConfigReader(config);
            try {
                sipConfigReader.read(configXmlFileLocation);
                ttsConfigReader.read(configXmlFileLocation);
                CONFIGDB_LATENCY.labels("configdb_latency").set((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                logTimeTaken(timer, "reloadGatewayInfoMatrixConfig");
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                incrementConfigDBErrorCounter(e);
                throw new RuntimeException(err, e);
            }
        }
        Log.info(">>>>>>>>>>>>>>>>>>>> GATEWAY-INFO-MATRIX-CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void reloadAllGatewaysMOCostMatrixes() {
        Log.debug("reloadAllGatewaysMOCostMatrixes()");
        synchronized (this) {
            reloadGatewayInfoMatrixConfig();
        }
    }

    @Override
    public void reloadAllGatewaysMTCostMatrixes() {
        Log.debug("reloadAllGatewaysMTCostMatrixes()");
        synchronized (this) {
            reloadGatewayInfoMatrixConfig();
        }
    }

    @Override
    public void reloadMTRoutingTable() {
        Log.debug("reloadMTRoutingTable()");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            final File configXmlFileLocation = config.getConfigXmlFileLocation();
            final ReloadMTRoutingConfigReader configReader = new ReloadMTRoutingConfigReader(config);
            try {
                configReader.read(configXmlFileLocation);
                CONFIGDB_LATENCY.labels("configdb_latency").set((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                logTimeTaken(timer, "reloadMTRoutingTable");
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                incrementConfigDBErrorCounter(e);
                throw new RuntimeException(err, e);
            }
        }
        Log.info(">>>>>>>>>>>>>>>>>>>> MT-ROUTING-CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void updateMTRoutingRules(Collection<MtRoutingRule> newRules) {
        Log.debug("updateMTRoutingRules(" + newRules + ")");
        long timeCalled = System.currentTimeMillis();
        synchronized (this) {
            long timeGotLock = System.currentTimeMillis();
            final Config config = Core.getInstance().getConfig();
            if (config.getMtRoutingConfig() != null)
                config.getMtRoutingConfig().updateRoutingRules(newRules);
            long timeTaken = System.currentTimeMillis() - timeCalled;
            long timeSinceGotLock = System.currentTimeMillis() - timeGotLock;
            Log.info("::: JMX-RELOAD-OPERATION ::: injectMtRoutingRules call-total-time [ " + timeTaken + "] time-since-lock-aquired [ " + timeSinceGotLock + " ] ");
        }
    }

    @Override
    public void removeMTRoutingRules(Collection<Long> sequences) {
        Log.debug("removeMTRoutingRules(" + sequences + ")");
        long timeCalled = System.currentTimeMillis();
        synchronized (this) {
            long timeGotLock = System.currentTimeMillis();
            final Config config = Core.getInstance().getConfig();
            if (config.getMtRoutingConfig() != null)
                config.getMtRoutingConfig().removeRoutingRules(sequences);
            long timeTaken = System.currentTimeMillis() - timeCalled;
            long timeSinceGotLock = System.currentTimeMillis() - timeGotLock;
            Log.info("::: JMX-RELOAD-OPERATION ::: removeFromMtRoutingRules call-total-time [ " + timeTaken + "] time-since-lock-aquired [ " + timeSinceGotLock + " ] ");
        }
    }

    @Override
    public void reloadMTRoutingTargetGroups() throws Exception {
        Log.debug("reloadMTRoutingTargetGroups()");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            ReloadMTRoutingTargetGroupsConfigReader reader = new ReloadMTRoutingTargetGroupsConfigReader(config);
            try {
                reader.read(new File(getConfigXmlFileLocation()));
                CONFIGDB_LATENCY.labels("configdb_latency").set((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                logTimeTaken(timer, "reloadMTRoutingTargetGroups");
            } catch (LoaderException e) {
                Log.error("Unable to reload mt routing target groups from configuration file [ " + getConfigXmlFileLocation() + " ] --- data will not be updated!!", e);
                incrementConfigDBErrorCounter(e);
                throw new Exception("Unable to reload mt routing target groups from configuration file [ " + getConfigXmlFileLocation() + " ] --- data will not be updated!!", e);
            }
        }
    }

    @Override
    public void reloadMTRoutingTargetGroup(String groupId) throws Exception {
        Log.debug("reloadMTRoutingTargetGroup(" + groupId + ")");
        synchronized (this) {
            final long timer = System.nanoTime();
            final Config config = Core.getInstance().getConfig();
            ReloadMTRoutingTargetGroupsConfigReader reader = new ReloadMTRoutingTargetGroupsConfigReader(config, groupId);
            try {
                reader.read(new File(getConfigXmlFileLocation()));
                CONFIGDB_LATENCY.labels("configdb_latency").set((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                logTimeTaken(timer, "reloadMTRoutingTargetGroup(" + groupId + ")");
            } catch (LoaderException e) {
                Log.error("Unable to reload mt routing target group [ " + groupId + " ] from configuration file [ " + getConfigXmlFileLocation() + " ] --- data will not be updated!!", e);
                incrementConfigDBErrorCounter(e);
                throw new Exception("Unable to reload mt routing target group [ " + groupId + " ] from configuration file [ " + getConfigXmlFileLocation() + " ] --- data will not be updated!!", e);
            }
        }
    }

    @Override
    public Map<String, String> getAllConfiguredAccountCapabilities() {
        Config config = Core.getInstance().getConfig();
        Map<String, String> capabilities = new HashMap<>();

        if (config.getAuthConfig().isRequireAccountCapabilityEnabled())
            capabilities.put("APPS - Voice :: ENABLE - Require account capability ", config.getAuthConfig().getRequireAccountCapability());

        AccountCapabilitiesConfig capabilitiesConfig = config.getAccountCapabilitiesConfig();

        if (capabilitiesConfig != null) {
            if (capabilitiesConfig.isDroppedCallOverwriteFromRequired())
                capabilities.put("APPS - Voice :: ENABLE - Dropped Call Overwrite", capabilitiesConfig.getDroppedCallOverwriteFrom());

            if (capabilitiesConfig.isForceGatewayCapabilityRequired())
                capabilities.put("APPS - Voice :: ENABLE - SIP Force Gateway", capabilitiesConfig.getForceGatewayCapability());

            if (capabilitiesConfig.isSubmitDroppedCallRequired())
                capabilities.put("APPS - Voice :: ENABLE - Dropped Call Submit", capabilitiesConfig.getSubmitDroppedCall());

            if (capabilitiesConfig.isForceRingingCapabilityRequired())
                capabilities.put("APPS - Voice :: ENABLE - Force Ringing Capability", capabilitiesConfig.getForceRingingCapability());
        }

        Log.debug("getAllConfiguredAccountCapabilities() => " + capabilities);
        return capabilities;
    }

    @Override
    public void reloadInternalApiConfig() {
        Log.debug("reloadMTRoutingTargetGroups()");
        long timeCalled = System.nanoTime();
        synchronized (this) {
            final Config config = Core.getInstance().getConfig();
            final File configXmlFileLocation = config.getConfigXmlFileLocation();
            final ReloadInternalApiConfigReader configReader = new ReloadInternalApiConfigReader(config);
            try {
                configReader.read(configXmlFileLocation);
                logTimeTaken(timeCalled, "reloadInternalApiConfig");
            } catch (LoaderException e) {
                String err = "..... Failed to reload from config file [ " + configXmlFileLocation + " ] ... .. Aborting";
                Log.error(err, e);
                throw new RuntimeException(err, e);
            }
        }
        Log.info(">>>>>>>>>>>>>>>>>>>> INTERNAL-API-CONFIG-RELOADED SUCCESSFULLY!!!!!!!");
    }

    @Override
    public void updateMtPrice(Collection<Price> prices) {
        Log.debug("updateMtPrice(" + prices + ")");
        prices = ensureTimestamp(prices);
        synchronized (this) {
            long timeCalled = System.nanoTime();
            if (Core.getInstance().getConfig().getMtPriceMatrixList() != null)
                Core.getInstance().getConfig().getMtPriceMatrixList().injectPrice(prices);
            logTimeTaken(timeCalled, "updateMtPrice");
        }
    }

    @Override
    public void removeMtPrice(Collection<Price> prices) {
        Log.debug("removeMtPrice(" + prices + ")");
        synchronized (this) {
            long timeCalled = System.nanoTime();
            if (Core.getInstance().getConfig().getMtPriceMatrixList() != null)
                Core.getInstance().getConfig().getMtPriceMatrixList().removePrice(prices);
            logTimeTaken(timeCalled, "removeMtPrice");
        }
    }

    @Override
    public void updateMoPrice(Collection<Price> prices) {
        Log.debug("updateMoPrice(" + prices + ")");
        prices = ensureTimestamp(prices);
        synchronized (this) {
            long timeCalled = System.nanoTime();
            if (Core.getInstance().getConfig().getMoPriceMatrixList() != null)
                Core.getInstance().getConfig().getMoPriceMatrixList().injectPrice(prices);
            logTimeTaken(timeCalled, "updateMoPrice");
        }
    }

    @Override
    public void removeMoPrice(Collection<Price> prices) {
        Log.debug("removeMoPrice(" + prices + ")");
        synchronized (this) {
            long timeCalled = System.nanoTime();
            if (Core.getInstance().getConfig().getMoPriceMatrixList() != null)
                Core.getInstance().getConfig().getMoPriceMatrixList().removePrice(prices);
            logTimeTaken(timeCalled, "removeMoPrice");
        }
    }

    @Override
    public void updateMoCostEntries(Map<String, Collection<Price>> costUpdates) {
        Log.debug("updateMoCostEntries(" + costUpdates + ")");
        costUpdates = ensureTimestamp(costUpdates);
        synchronized (this) {
            final Config config = Core.getInstance().getConfig();
            if (config.getSIPGatewayInfoMatrixConfig() == null)
                return;

            long timeCalled = System.nanoTime();
            for (Map.Entry<String, Collection<Price>> entry : costUpdates.entrySet()) {
                String gateway = entry.getKey();
                Collection<Price> prices = entry.getValue();

                SupplierMappingConfig gatewayInfo = config.getSIPGatewayInfoMatrixConfig().getGatewayInfo(gateway);
                if (gatewayInfo != null && gatewayInfo.getInboundCostMatrix() != null)
                    gatewayInfo.getInboundCostMatrix().injectPrice(prices);
            }
            logTimeTaken(timeCalled, "updateMoCostEntries");
        }
    }

    @Override
    public void removeMoCostEntries(Map<String, Collection<Price>> costUpdates) {
        Log.debug("removeMoCostEntries(" + costUpdates + ")");
        synchronized (this) {
            final Config config = Core.getInstance().getConfig();
            if (config.getSIPGatewayInfoMatrixConfig() == null)
                return;

            long timeCalled = System.nanoTime();
            for (Map.Entry<String, Collection<Price>> entry : costUpdates.entrySet()) {
                String gateway = entry.getKey();
                Collection<Price> prices = entry.getValue();

                SupplierMappingConfig gatewayInfo = config.getSIPGatewayInfoMatrixConfig().getGatewayInfo(gateway);
                if (gatewayInfo != null && gatewayInfo.getInboundCostMatrix() != null)
                    gatewayInfo.getInboundCostMatrix().removePrice(prices);
            }
            logTimeTaken(timeCalled, "removeMoCostEntries");
        }
    }

    @Override
    public void updateMtCostEntries(Map<String, Collection<Price>> costUpdates) {
        Log.debug("updateMtCostEntries(" + costUpdates + ")");
        costUpdates = ensureTimestamp(costUpdates);
        synchronized (this) {
            final Config config = Core.getInstance().getConfig();
            if (config.getSIPGatewayInfoMatrixConfig() == null)
                return;

            long timeCalled = System.nanoTime();
            for (Map.Entry<String, Collection<Price>> entry : costUpdates.entrySet()) {
                String gateway = entry.getKey();
                Collection<Price> prices = entry.getValue();

                SupplierMappingConfig gatewayInfo = config.getSIPGatewayInfoMatrixConfig().getGatewayInfo(gateway);
                if (gatewayInfo != null && gatewayInfo.getCostMatrix() != null)
                    gatewayInfo.getCostMatrix().injectPrice(prices);
            }
            logTimeTaken(timeCalled, "updateMtCostEntries");
        }
    }

    @Override
    public void removeMtCostEntries(Map<String, Collection<Price>> costUpdates) {
        Log.debug("removeMtCostEntries(" + costUpdates + ")");
        synchronized (this) {
            final Config config = Core.getInstance().getConfig();
            if (config.getSIPGatewayInfoMatrixConfig() == null)
                return;

            long timeCalled = System.nanoTime();
            for (Map.Entry<String, Collection<Price>> entry : costUpdates.entrySet()) {
                String gateway = entry.getKey();
                Collection<Price> prices = entry.getValue();

                SupplierMappingConfig gatewayInfo = config.getSIPGatewayInfoMatrixConfig().getGatewayInfo(gateway);
                if (gatewayInfo != null && gatewayInfo.getCostMatrix() != null)
                    gatewayInfo.getCostMatrix().removePrice(prices);
            }
            logTimeTaken(timeCalled, "removeMtCostEntries");
        }
    }

    @Override
    protected void flushCacheForAccountCustomBehaviour(String accountId) throws Exception {
        Log.info("Flushed caches for account " + accountId);
    }

    @Override
    public void reloadSenderRandomizerRandomPool(final String poolId)  {
        final long timeCalled = System.currentTimeMillis();
        synchronized (this) {
            final long timeGotLock = System.currentTimeMillis();
            Log.info("::: JMX-RELOAD-OPERATION ::: reloadSenderRandomizerRandomPool poolId called:[" + poolId + "]");
            Randomizer.getInstance().processRandomPoolNotification(poolId);
            final long timeTaken = System.currentTimeMillis() - timeCalled;
            final long timeSinceGotLock = System.currentTimeMillis() - timeGotLock;
            Log.info("::: JMX-RELOAD-OPERATION ::: reloadSenderRandomizerRandomPool poolId:[" + poolId + "] call-total-time [ " + timeTaken + "] time-since-lock-aquired [ " + timeSinceGotLock + " ] ");
        }
    }

    private static boolean isValidDate(Date date) {
        if (date == null)
            return false;
        final long timestamp = date.getTime();
        return (timestamp > 0L);
    }

    private static Price ensureTimestamp(Price price) {
        if (isValidDate(price.getTimeLastModified()) || isValidDate(price.getDateCreated()))
            return price;

        if (Log.isDebugEnabled()) {
            Log.debug("Adding timestamp to price "+price);
        }

        final long timestamp = System.currentTimeMillis();
        return new Price(price.getProduct(),
                         price.getCurrency(),
                         price.getNetwork(),
                         price.getPrefix(),
                         price.getSenderPrefix(),
                         price.getAccount(),
                         price.getPricingGroup(),
                         price.getPrice(),
                         price.getDescription(),
                         null /*countryCode*/,
                         null /*dateCreated*/,
                         new Date(timestamp));
    }

    private static List<Price> ensureTimestamp(Collection<Price> prices) {
        List<Price> ret = new ArrayList<Price>();
        for (Price p : prices) {
            ret.add( ensureTimestamp(p) );
        }
        return ret;
    }

    private static Map<String, Collection<Price>> ensureTimestamp(Map<String, Collection<Price>> map) {
        Map<String, Collection<Price>> ret = new HashMap<String, Collection<Price>>();
        for (Map.Entry<String, Collection<Price>> e : map.entrySet()) {
            String gateway = e.getKey();
            List<Price> prices = ensureTimestamp(e.getValue());
            ret.put(gateway, prices);
        }
        return ret;
    }

    private static void incrementConfigDBErrorCounter(Exception e) {
        if (ExceptionUtils.indexOfThrowable(e, org.hibernate.exception.GenericJDBCException.class) != -1) {
            Log.debug("Increment ConfigDB unreachable counter");
            CONFIGDB_CONNECTION_ERROR.inc();
        }
    }

}
