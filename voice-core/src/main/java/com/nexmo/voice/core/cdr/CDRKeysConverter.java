package com.nexmo.voice.core.cdr;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


public class CDRKeysConverter {
    
    private static final Logger Log = LogManager.getLogger(CDRKeysConverter.class);

    public static ConcurrentHashMap<String, String> keysDictionary = new ConcurrentHashMap();

    static {
        keysDictionary.put("ACC", "acc");
        keysDictionary.put("ACCOUNT-PRICING-GROUP", "accountPricingGroup");
        keysDictionary.put("ASTERISK_VERSION", "asteriskVersion");
        keysDictionary.put("BACKEND", "backend");
        keysDictionary.put("CALL_BACK_METHOD", "callbackMethod");
        keysDictionary.put("CALL_BACK_URL", "callbackUrl");
        keysDictionary.put("CALL_DATE", "callDate");
        keysDictionary.put("CALL_DURATION", "callDuration");
        keysDictionary.put("CALL_MARGIN", "callMargin");
        keysDictionary.put("CALL_ORIGIN", "callOrigin");
        keysDictionary.put("CALL_TERMINATION", "callTermination");
        keysDictionary.put("CALL_TYPE", "callType");
        keysDictionary.put("CALL_RETRIES", "callRetries");
        keysDictionary.put("CARRIER_PLATFORM", "carrierPlatform");
        keysDictionary.put("CDR_UUID", "cdrUuid");
        keysDictionary.put("CHARGEABLE", "chargeable");
        keysDictionary.put("CLIENT_REFERENCE", "clientReference");
        keysDictionary.put("COST", "cost");
        keysDictionary.put("COST_PREFIX", "costPrefix");
        keysDictionary.put("COST_PREFIX_GROUP", "costPrefixGroup");
        keysDictionary.put("COST_SENDER_PREFIX", "costSenderPrefix");
        keysDictionary.put("COST_TIMESTAMP", "costTimestamp");
        keysDictionary.put("COUNTRY", "country");
        keysDictionary.put("CUSTOMER_DOMAIN", "customerDomain");
        keysDictionary.put("DIRECTION", "direction");
        keysDictionary.put("DURATION", "duration");
        keysDictionary.put("END", "end");
        keysDictionary.put("FORCED_SENDER", "forceSender");
        keysDictionary.put("FROM", "from");
        keysDictionary.put("GW", "gw");
        keysDictionary.put("GW_ATTEMPT", "gwAttempt");
        keysDictionary.put("GWS", "gws");
        keysDictionary.put("HANGUP_CAUSE", "hangupCause");
        keysDictionary.put("HOST", "host");
        keysDictionary.put("ID", "id");
        keysDictionary.put("INTERNAL", "internal");
        keysDictionary.put("INTERNAL_FLAG", "internalFlag");
        keysDictionary.put("LANGUAGE_NAME", "languageName");
        keysDictionary.put("LEG1-ID", "leg1Id");
        keysDictionary.put("LEG2-ID", "leg2Id");
        keysDictionary.put("MACHINE_DETECTION_TYPE", "machineDetectionType");
        keysDictionary.put("MACHINE_TIMEOUT", "machineTimeout");
        keysDictionary.put("MASTER-ACCOUNT-PRICING-GROUP", "masterAccountPricingGroup");
        keysDictionary.put("MB_STYLE", "mbStyle");
        keysDictionary.put("MIN_UNIT", "minUnit");
        keysDictionary.put("NET", "net");
        keysDictionary.put("NETWORK-NAME", "networkName");
        keysDictionary.put("NETWORK-TYPE", "networkType");
        keysDictionary.put("NUMBER_TYPE", "numberType");
        keysDictionary.put("OUTBOUND-GW", "outboundGw");
        keysDictionary.put("OVERRIDE_PRICE", "overridePrice");
        keysDictionary.put("PAYMENT-ENABLED-APP", "paymentEnabledApp");
        keysDictionary.put("REGION", "region");
        keysDictionary.put("PDD", "pdd");
        keysDictionary.put("PREFIX-FORCED_SENDER", "prefixForcedSender");
        keysDictionary.put("PREFIX-FROM", "prefixFrom");
        keysDictionary.put("PREFIX-TO", "prefixTo");
        keysDictionary.put("PRICE", "price");
        keysDictionary.put("PRICE_PREFIX", "pricePrefix");
        keysDictionary.put("PRICE_PREFIX_GROUP", "pricePrefixGroup");
        keysDictionary.put("PRICE_SENDER_PREFIX", "priceSenderPrefix");
        keysDictionary.put("PRICE_TIMESTAMP", "priceTimestamp");
        keysDictionary.put("PRODUCT", "product");
        keysDictionary.put("PRODUCT-CLASS", "productClass");
        keysDictionary.put("PRODUCT-PATH", "productPath");
        keysDictionary.put("PRODUCT-VERSION", "productVersion");
        keysDictionary.put("PSTN_PLATFORM", "pstnPlatform");
        keysDictionary.put("REASON", "reason");
        keysDictionary.put("REASON_DESC", "reasonDesc");
        keysDictionary.put("RECURRING_UNIT", "recurringUnit");
        keysDictionary.put("REPEAT", "repeat");
        keysDictionary.put("REQUEST_IP", "requestIp");
        keysDictionary.put("REROUTE-ADDRESS", "rerouteAddress");
        keysDictionary.put("ROUTING_BIND_ID", "routingBindId");
        keysDictionary.put("ROUTING_GROUP", "routingGroup");
        keysDictionary.put("ROUTING_OA", "routingOA");
        keysDictionary.put("ROUTING_SEQ", "routingSeq");
        keysDictionary.put("RULE_ID", "ruleId");
        keysDictionary.put("BLOCKING_SUBSYSTEM", "blocking_subsystem");
        keysDictionary.put("SESSION-ID", "sessionId");
        keysDictionary.put("SIP-DEST-ATTEMPT", "sipDestAttempt");
        keysDictionary.put("SOURCE_COUNTRY", "sourceCountry");
        keysDictionary.put("SRTP", "srtp");
        keysDictionary.put("SIP_TRANSPORT", "sipTransport");
        keysDictionary.put("START", "start");
        keysDictionary.put("STATUS", "status");
        keysDictionary.put("STIR_SHAKEN", "stirShaken");
        keysDictionary.put("SUPERHUB-ACC", "superHubAcc");
        keysDictionary.put("TO", "to");
        keysDictionary.put("TOTAL_COST", "totalCost");
        keysDictionary.put("TOTAL_PRICE", "totalPrice");
        keysDictionary.put("TTS_XTRACE_ID", "ttsXtraceId");
        keysDictionary.put("UNIT", "unit");
        keysDictionary.put("VPRICING-ENABLED", "vpricingEnabled");
        keysDictionary.put("ORIGIN_PREFIX_GROUP", "originPrefixGroup");
        keysDictionary.put("ORIGIN_COUNTRY_REGION", "originCountryRegion");
        keysDictionary.put("EMERGENCY_CALL", "emergencyCall");
        keysDictionary.put("EMERGENCY_CALL_FAILOVER", "emergencyCallFailover");
        keysDictionary.put("EMERGENCY_CALL_LOCATION_ID", "emergencyCallLocationId");
    }
    
    
    //Convert to CamelCase while keeping the first letter as lower case.
    protected static String convertKeyOnTheFly(String cdrKey) {
        if (Objects.isNull(cdrKey))
            return null;
        
         return  WordUtils.uncapitalize(
                WordUtils.capitalizeFully(cdrKey, new char[]{'_', ' ', '-'})
                .replaceAll("_","")
                .replaceAll(" ", "")
                .replaceAll("-", ""));
    }
    
    public static String getJsonKey(String cdrKey) {
        if (Objects.isNull(cdrKey))
            return null;
        
        String jsonKey = keysDictionary.get(cdrKey);
        if (Objects.isNull(jsonKey)) {
            jsonKey = convertKeyOnTheFly(cdrKey);
            if (Objects.isNull(jsonKey) || jsonKey.isEmpty()) {
                Log.warn("{} converted to empty/null json key. will retain the original value", cdrKey);
                jsonKey=cdrKey;
            } else {
                Log.info("Adding {} to the pre-defined dictionary as {}", cdrKey, jsonKey);
                keysDictionary.put(cdrKey, jsonKey);
            }
        }
        return jsonKey;
    }


}
