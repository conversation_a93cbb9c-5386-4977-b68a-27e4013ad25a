package com.nexmo.voice.core.billing.vquota;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.config.quota.ExtendedQuotaApiClientConfig;
import com.nexmo.voice.core.aws.secretsmanager.SecretManagerUtils;
import com.nexmo.voice.core.billing.QuotaUpdateDetails;
import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.nexmo.voice.core.billing.vquota.currentbalance.CurrentBalanceApiClient;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiClient;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiRequest;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiRequestFactory;
import com.nexmo.voice.core.billing.vquota.priceimpact.PriceImpactApiSuccessResponse;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.sip.AsteriskAGIServer;
import com.nexmo.voice.core.types.ProductType;
import com.thepeachbeetle.hlr.core.exceptions.ConfigException;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import java.math.BigDecimal;
import java.util.EnumMap;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;
import static java.util.Objects.nonNull;

public class VQuotaService {
    private final static Logger Log = LogManager.getLogger(VQuotaService.class);
    private final static Logger Quota_updates_Log = LogManager.getLogger("quota.updates.log");
    private static final Histogram CURRENT_BALANCE_API_SUCCESS_LATENCY =  Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("rtc_dependency_current_balance_api_success_latency").labelNames("status").help("Time in seconds successful Http Requests Latency to get account balance from currentBalance api").register();
    private static final Histogram CURRENT_BALANCE_API_FAILURE_LATENCY =  Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("rtc_dependency_current_balance_api_failure_latency").labelNames("status", "exception").help("Time in seconds failed Http Requests Latency to get account balance from currentBalance api").register();
    private static final Counter CURRENT_BALANCE_API_SUCCESS_COUNTER = Counter.build().name("rtc_dependency_current_balance_api_success").labelNames("status").help("current balance api success counter").register();
    private static final Counter CURRENT_BALANCE_API_FAILURE_COUNTER = Counter.build().name("rtc_dependency_current_balance_api_error").labelNames("status", "exception").help("current balance api error counter").register();
    private static final Histogram PRICE_IMPACT_API_SUCCESS_LATENCY =  Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("rtc_dependency_price_impact_api_success_latency").labelNames("type").help("Time in seconds successful Http Requests Latency for priceImpact api").register();
    private static final Histogram PRICE_IMPACT_API_FAILURE_LATENCY =  Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("rtc_dependency_price_impact_api_failure_latency").labelNames("type", "exception").help("Time in seconds failed Http Requests Latency for priceImpact api").register();
    private static final Counter PRICE_IMPACT_API_SUCCESS_COUNTER = Counter.build().name("rtc_dependency_price_impact_api_success").labelNames("type").help("price impact api success counter").register();
    private static final Counter PRICE_IMPACT_API_FAILURE_COUNTER = Counter.build().name("rtc_dependency_price_impact_api_error").labelNames("type", "exception").help("price impact api error counter").register();
    private final CurrentBalanceApiClient currentBalanceClient;
    private final PriceImpactApiClient priceImpactClient;
    private final ProductTypeResolver productTypeResolver;
    private final boolean logEnabled;
    private static final EnumMap<QuotaUpdateDetails.Operation, String> OPERATION_TO_LABEL_MAP = new EnumMap<>(QuotaUpdateDetails.Operation.class);

    static {
        OPERATION_TO_LABEL_MAP.put(QuotaUpdateDetails.Operation.CONSUME, "CONSUME-REJECTED");
        OPERATION_TO_LABEL_MAP.put(QuotaUpdateDetails.Operation.REFUND, "REFUND-REJECTED");
        OPERATION_TO_LABEL_MAP.put(QuotaUpdateDetails.Operation.QUERY, "QUERY-REJECTED");
    }

    public static String getLabel(QuotaUpdateDetails.Operation cmd, String sessionId) {
        String label = OPERATION_TO_LABEL_MAP.getOrDefault(cmd, "UNKNOWN-REJECTED");
        if ("UNKNOWN-REJECTED".equals(label)) {
            Log.warn("Unexpected Operation: {} for sessionID: {}.", cmd, sessionId);
        }
        return label;
    }

    public VQuotaService(VQuotaServiceConfig vQuotaServiceConfig, ExtendedQuotaApiClientConfig extendedConfig) throws ConfigException {
        String vQuotaEndpointsAuthValue = SecretManagerUtils.getSecretValue(vQuotaServiceConfig.getSecretName());
        this.currentBalanceClient = new CurrentBalanceApiClient(vQuotaServiceConfig.getCurrentBalanceApiConfig(), vQuotaEndpointsAuthValue);
        this.priceImpactClient = new PriceImpactApiClient(vQuotaServiceConfig.getPriceImpactApiConfig(), vQuotaEndpointsAuthValue);
        this.productTypeResolver = new ProductTypeResolver();
        this.logEnabled = extendedConfig.isLogEnabled();
        Log.info("SIPApp ExtendedQuotaClient logEnabled = {} ", logEnabled);
    }

    public AccountBalance getCurrentBalance(String apiKey, BigDecimal minBalance, String sessionId) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException {
        AccountBalance resp;
        final long startHttpRequestTimer = System.nanoTime();
        double responseTime;

        try {
            resp = currentBalanceClient.getCurrentBalance(apiKey, minBalance, sessionId);
            responseTime = (System.nanoTime() - startHttpRequestTimer) / NANOSECONDS_PER_SECOND;
            updateGetCurrentBalanceApiSuccessMetrics(responseTime);
        } catch (Exception e) {
            responseTime = (System.nanoTime() - startHttpRequestTimer) / NANOSECONDS_PER_SECOND;
            updateGetCurrentBalanceApiFailureMetrics(e.getClass().getSimpleName(), responseTime);
            Log.error("Failed to get account balance from currentBalance api for apiKey = [{}], sessionId = [{}]. Exception: [{}] with message: [{}]", apiKey, sessionId, e.getClass().getSimpleName(), e.getMessage());
            throw e;
        }
        return resp;
    }

    public PriceImpactApiSuccessResponse invokePriceImpactApi(final VoiceContext ctx, Long duration, QuotaUpdateDetails.Operation cmd) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        if (!ctx.isChargeable()) {
            Log.info("sessionId: {} channelId: {}, accountId: {} ,invoking priceImpact [{}] API was skipped as this was marked as not chargeable", ctx.getSessionId(), ctx.getConnectionId(), ctx.getAccountId(), cmd.getValue());
            return null;
        }

        PriceImpactApiSuccessResponse resp = null;
        ProductType productType = productTypeResolver.resolveProductType(ctx);
        PriceImpactApiRequest req = PriceImpactApiRequestFactory.createRequest(productType, ctx, duration, cmd);
        final long startHttpRequestTimer = System.nanoTime();
        double responseTime;

        try {
            resp = priceImpactClient.invokePriceImpactApi(req, ctx.getSessionId());
            responseTime = (System.nanoTime() - startHttpRequestTimer) / NANOSECONDS_PER_SECOND;
            String label = cmd.name();
            updatePriceImpactApiSuccessMetrics(responseTime, label);
            if (logEnabled && QuotaUpdateDetails.Operation.QUERY != cmd) {
                StringBuffer sb = new StringBuffer();
                appendCmd(sb, label);
                logQuotaUpdateDetails(ctx, duration, resp, sb);
                Quota_updates_Log.info(sb.toString());
            }
        } catch (Exception e) {
            responseTime = (System.nanoTime() - startHttpRequestTimer) / NANOSECONDS_PER_SECOND;
            String label = getLabel(cmd, ctx.getSessionId());

            updatePriceImpactApiFailureMetrics(e.getClass().getSimpleName(), responseTime, label);
            Log.error("'{}' operation for Account: [{}], duration: [{}], ref: [{}], allowNegativeBalance: [{}], sessionId: [{}] has been rejected due to [{}]",
                    cmd.getValue().toUpperCase(), ctx.getAccountId(), Objects.isNull(duration) ? "null" : duration, ctx.getQuotaRef(),
                    ctx.allowNegativeBalance(), ctx.getSessionId(), e.getMessage());
            if (Objects.nonNull(e.getCause())) {
                Log.error("Further details of '{}' exception for account [{}], ref [{}], sessionId [{}] is [{}]",
                        cmd.getValue().toUpperCase(), ctx.getAccountId(), ctx.getQuotaRef(), ctx.getSessionId(), e.getCause());
            }
            if (logEnabled && QuotaUpdateDetails.Operation.QUERY != cmd) {
                StringBuffer sb = new StringBuffer();
                appendCmd(sb, label);
                logQuotaUpdateDetails(ctx, duration, resp, sb);
                appendRejection(sb, e);
                Quota_updates_Log.info(sb.toString());
            }
            throw e;
        }

        return resp;
    }

    private static boolean isVpricingCapabilityEnabled(SmppAccount account) {
        boolean hasCapabilityEnabled = nonNull(account.getCapabilities()) && account.getCapabilities().contains(AsteriskAGIServer.VPRICING_ENABLED_CAPABILITY);
        if (Log.isDebugEnabled() && hasCapabilityEnabled) {
            Log.debug("{} enabled for {}", AsteriskAGIServer.VPRICING_ENABLED_CAPABILITY, account.getSysId());
        }

        return hasCapabilityEnabled;
    }

    private void logQuotaUpdateDetails(VoiceContext ctx, Long duration, PriceImpactApiSuccessResponse resp, StringBuffer sb) {
        appendAccountId(sb, ctx.getAccountId());
        appendDuration(sb, duration);
        appendAmount(sb, Optional.ofNullable(resp).map(PriceImpactApiSuccessResponse::getEstimatedPriceImpact).orElse(null));
        appendRef(sb, ctx.getQuotaRef());
        appendConnectionId(sb, ctx.getConnectionId());
        appendAllowNegative(sb, ctx.allowNegativeBalance());
    }

    private void appendCmd(StringBuffer sb, String label) {
        sb.append("\"CMD=").append(label).append("\",");
    }

    private void appendAccountId(StringBuffer sb, String accountId) {
        sb.append("\"ACC=");
        if (Objects.nonNull(accountId))
            sb.append(accountId);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendDuration(StringBuffer sb, Long duration) {
        sb.append("\"Duration=");
        if (Objects.nonNull(duration))
            sb.append(duration);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendAmount(StringBuffer sb, BigDecimal amount) {
        sb.append("\"AMOUNT=");
        if (nonNull(amount))
            sb.append(amount.toPlainString());
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendRef(StringBuffer sb, String ref) {
        sb.append("\"REF=");
        if (Objects.nonNull(ref))
            sb.append(ref);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendConnectionId(StringBuffer sb, String connectionId) {
        sb.append("\"CONNECTION-ID=");
        if (Objects.nonNull(connectionId))
            sb.append(connectionId);
        else
            sb.append("null");
        sb.append("\",");
    }

    private void appendAllowNegative(StringBuffer sb, boolean allowNegativeBalance) {
        sb.append("\"ALLOW-NEGATIVE=").append(allowNegativeBalance).append("\",");
    }

    private void appendRejection(StringBuffer sb, Exception e) {
        sb.append("\"REJECTED-REASON=").append(e.getMessage()).append("\",");
    }

    private void updateGetCurrentBalanceApiSuccessMetrics(double value) {
        CompletableFuture.runAsync(() -> {
            CURRENT_BALANCE_API_SUCCESS_LATENCY.labels("success").observe(value);
            CURRENT_BALANCE_API_SUCCESS_COUNTER.labels("success").inc();
        });
    }

    private void updateGetCurrentBalanceApiFailureMetrics(String exception, double value) {
        CompletableFuture.runAsync(() -> {
            CURRENT_BALANCE_API_FAILURE_LATENCY.labels("failure", exception).observe(value);
            CURRENT_BALANCE_API_FAILURE_COUNTER.labels("failure", exception).inc();
        });
    }

    private void updatePriceImpactApiSuccessMetrics(double value, String label) {
        CompletableFuture.runAsync(() -> {
            PRICE_IMPACT_API_SUCCESS_LATENCY.labels(label).observe(value);
            PRICE_IMPACT_API_SUCCESS_COUNTER.labels(label).inc();
        });
    }

    private void updatePriceImpactApiFailureMetrics(String exception, double value, String label) {
        CompletableFuture.runAsync(() -> {
            PRICE_IMPACT_API_FAILURE_LATENCY.labels(label, exception).observe(value);
            PRICE_IMPACT_API_FAILURE_COUNTER.labels(label, exception).inc();
        });
    }

    public static boolean isVpricingEnabled(SmppAccount account) {
        return isVpricingCapabilityEnabled(account) && Core.getInstance().getVQuotaService() != null;
    }
}
