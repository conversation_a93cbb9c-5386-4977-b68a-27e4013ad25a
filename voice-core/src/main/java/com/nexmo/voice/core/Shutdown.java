package com.nexmo.voice.core;

import com.nexmo.voice.core.sync.EnvVariableUpdatesExecuter;
import org.apache.log4j.Logger;

import org.asteriskjava.fastagi.AgiServerThread;

import com.thepeachbeetle.common.app.shutdown.AbstractShutdown;
import com.thepeachbeetle.common.callback.AbstractCallbackTask;
import com.thepeachbeetle.common.callback.CallbackPipeline;
import com.thepeachbeetle.common.period.Periods;

import com.nexmo.common.api.pipeline.AsyncPipeline;
import com.nexmo.voice.core.billing.QuotaUpdatesExecuter;
import com.nexmo.voice.core.billing.QuotaQueueExecuter;
import com.nexmo.voice.core.gateway.asterisk.AsteriskAMIProcessor;
import com.nexmo.voice.core.gateway.asterisk.AsteriskPingThread;

/**
 * <AUTHOR>
 */
public class Shutdown extends AbstractShutdown {

    private static final Logger Log = Logger.getLogger(Shutdown.class.getName());

    private static Shutdown instance = new Shutdown();

    private Shutdown() {
    }

    public static Shutdown getInstance() {
        return instance;
    }

    @Override
    public void shutdown() {
        /*
        Config config = Core.getInstance().getConfig();

        // Shutdown public api first
        PublicApiConfig publicApiConfig = config != null ? config.getPublicApiConfig() : null;
        if (publicApiConfig != null && publicApiConfig.isEnabled()) {
            Log.warn("........... shutting down public api endpoints ............");
            try {
                HttpServer.getInstance().stopHttpServer("core", "end-user");
                Log.warn("........... shutting down public api endpoints ............ [ done ]");
            } catch (Exception e) {
                Log.error("........... shutting down public api endpoints ............ [ FAILED! ]", e);
            }
        }

        // Shutdown internal api
        HttpServerConfig internalApiConfig = config != null ? config.getHttpInternalApiEndpointConfig() : null;
        if (internalApiConfig != null && internalApiConfig.isEnabled()) {
            Log.warn("........... shutting down public api endpoints ............");
            try {
                HttpServer.getInstance().stopHttpServer("core", "internal-api");
                Log.warn("........... shutting down public api endpoints ............ [ done ]");
            } catch (Exception e) {
                Log.error("........... shutting down public api endpoints ............ [ FAILED! ]", e);
            }
        }
        */

        // Shut down Asynchronous Pipeline and wait for all outstanding tasks to complete ....
        Log.warn("....... shutdown -- shutdown async pipeline and wait for all outstanding tasks to clear .....");
        if (AsyncPipeline.getInstance() != null)
            AsyncPipeline.getInstance().shutdown();
        Log.warn("....... shutdown -- shutdown async pipeline and wait for all outstanding tasks to clear ..... [ done ]");

        AsteriskPingThread pingThread = Core.getInstance().getAsteriskPingThread();
        if (pingThread != null) {
            Log.warn("........... shutting Asterisk Ping Thread ............");
            pingThread.die();
            Log.warn("........... shutting Asterisk Ping Thread ............ [ done ]");
        }

        AsteriskAMIProcessor asteriskAMIProcessor = Core.getInstance().getAsteriskAMIProcessor();
        // TODO need to issue a hangup to asterisk, so it doesn't accept any more calls
        if (asteriskAMIProcessor != null) {
            Log.warn("........... shutting SIP AMI processor ............");
            asteriskAMIProcessor.shutdown();
            Log.warn("........... shutting SIP AMI processor ............ [ done ]");
        }

        AgiServerThread agiServerThread = Core.getInstance().getAgiServerThread();
        if (agiServerThread != null) {
            Log.warn("........... shutting SIP AGI server ............");
            agiServerThread.shutdown();
            Log.warn("........... shutting SIP AGI server ............ [ done ]");
        }

        final int cooldownTimeCount = Core.getInstance().getConfig().getShutdownCooldownTimeCount();
        final Periods cooldownTimeUnit = Core.getInstance().getConfig().getShutdownCooldownTimeUnit();
        Log.warn("SHUTDOWN :: Cooldown of " + cooldownTimeCount + " " + cooldownTimeUnit + "....");
        try {
            Thread.sleep(Periods.asMilliseconds(cooldownTimeUnit, cooldownTimeCount));
            Log.warn("SHUTDOWN :: fresh as a daisy!");
        } catch (InterruptedException e) {
            Log.error("Something terribly wrong happened while cooling down!");
        }

        QuotaUpdatesExecuter quotaUpdatesExecuter = Core.getInstance().getQuotaUpdatesExecuter();
        if (quotaUpdatesExecuter != null) {
            Log.warn("........... shutting down QuotaUpdatesExecuter ............");
            Core.getInstance().getQuotaUpdatesExecuter().shutdown();
            Log.warn("........... shutting down QuotaUpdatesExecuter ............ [ done ] ");
        }

        QuotaQueueExecuter asyncQuotaUpdatesExecuter = Core.getInstance().getQuotaQueueExecuter();
        if (asyncQuotaUpdatesExecuter != null) {
            Log.warn("........... shutting down QuotaQueueExecuter ............");
            Core.getInstance().getQuotaQueueExecuter().shutdown();
            Log.warn("........... shutting down QuotaQueueExecuter ............ [ done ] ");
        }

        EnvVariableUpdatesExecuter envVariableUpdatesExecuter = Core.getInstance().getEnvVariableUpdatesExecuter();
        if (envVariableUpdatesExecuter != null) {
            Log.warn("........... shutting down EnvVariableUpdatesExecuter ............");
            Core.getInstance().getEnvVariableUpdatesExecuter().shutdown();
            Log.warn("........... shutting down EnvVariableUpdatesExecuter ............ [ done ] ");
        }


        // shut down callback issuer
        CallbackPipeline<AbstractCallbackTask> callbackIssuer = Core.getInstance().getCallbackIssuer();
        if (callbackIssuer != null) {
            Log.warn("........... shutting down CallbackIssuer ............");
            callbackIssuer.shutdown();
            Log.warn("........... shutting down CallbackIssuer ............ [ done ] ");
        }

        // shut down application metrics updater pool
        Log.warn("........... shutting down ApplicationMetrics updater pool ............");
        Core.getInstance().getApplicationMetrics().shutdown();
        Log.warn("........... shutting down ApplicationMetrics updater pool ............ [ done ] ");
    }

}

