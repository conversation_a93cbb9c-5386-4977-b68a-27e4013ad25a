package com.nexmo.voice.core.types;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

import static java.util.Arrays.stream;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public enum AsteriskVersion {

    V20("20"),
    V16("16"),
    V1_8("1.8");

    private final static AsteriskVersion DEFAULT = V1_8;
    private final static Logger LOGGER = LogManager.getLogger(AsteriskVersion.class);

    private static final Map<String, AsteriskVersion> INTERPRETER = stream(AsteriskVersion.values()).collect(toMap(f -> f.version, identity()));

    private final String version;

    AsteriskVersion(String version) {
        this.version = version;
    }

    public static AsteriskVersion from(String sessionId, String version){
        AsteriskVersion result = null;
        final String temp = trimToNull(version);
        if(temp != null){
            result = INTERPRETER.get(temp);
        }
        if(result == null){
            result = DEFAULT;
            LOGGER.warn("{} Failed to find the right asterisk version from {}, will default to {}", sessionId, version, result);
        }
        return result;
    }

    public String getVersion() {
        return version;
    }

    public String getCdrVersion() {
        // Keeping this separate method as we used to have different version value requirements for CDR
        return this.getVersion();
    }
}
