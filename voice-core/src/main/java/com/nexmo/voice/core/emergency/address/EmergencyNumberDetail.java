package com.nexmo.voice.core.emergency.address;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmergencyNumberDetail {

    @JsonProperty("number")
    private String number;

    @JsonProperty("contact_name")
    private String contactName;

    @JsonProperty("address")
    private EmergencyAddress address;

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public EmergencyAddress getAddress() {
        return address;
    }

    public void setAddress(EmergencyAddress address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return "EmergencyNumberDetail{" +
                "number='" + number + '\'' +
                ", contactName='" + contactName + '\'' +
                ", address=" + address +
                '}';
    }
}
