package com.nexmo.voice.core.types;

import java.io.Serializable;
import java.util.Map;

import static java.util.Arrays.stream;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public enum CarrierPlatform implements Serializable {

    INPS("1"),
    NET("0");

    private static final Map<String, CarrierPlatform> VALUES = stream(CarrierPlatform.values()).collect(toMap(f -> f.userFieldValue, identity()));

    private final String userFieldValue;

    CarrierPlatform(String userFieldValue) {
        this.userFieldValue = userFieldValue;
    }

    public static CarrierPlatform from(String userFieldValue){
        final String temp = trimToNull(userFieldValue);
        if(temp == null){
            return null;
        }
        return VALUES.get(temp);
    }
}
