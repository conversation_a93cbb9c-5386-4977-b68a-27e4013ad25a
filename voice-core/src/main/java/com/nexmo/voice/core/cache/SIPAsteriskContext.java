package com.nexmo.voice.core.cache;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;


public class SIPAsteriskContext extends ApplicationContext {

    private static final long serialVersionUID = -7517321713664395229L;

    private final String sipChannelId;
    private final String clientCallId;
    private final String redirectionCallbackAddress;
    private final String outboundGateway;
    private final int remainingAttempts;
    private long pdd;
    private final boolean auxiliary;
    private final String applicationId;

    public SIPAsteriskContext(final String sipChannelId,
                              final String clientCallId,
                              final String callbackAddress,
                              final String outboundGateway,
                              final boolean auxiliary,
                              final int remainingAttempts) {
        this(sipChannelId,
             clientCallId,
             callbackAddress,
             outboundGateway,
             auxiliary,
             remainingAttempts,
             null);
    }

    public SIPAsteriskContext(final String sipChannelId,
                              final String clientCallId,
                              final String callbackAddress,
                              final String outboundGateway,
                              final boolean auxiliary,
                              final int remainingAttempts,
                              final String applicationId) {
        super();
        this.sipChannelId = sipChannelId;
        this.clientCallId = clientCallId;
        this.redirectionCallbackAddress = callbackAddress;
        this.outboundGateway = outboundGateway;
        this.auxiliary = auxiliary;
        this.remainingAttempts = remainingAttempts;
        this.applicationId = applicationId;
    }

    @Override
    public void populateParams(Map<String, String> requiredParams) {
        if (requiredParams == null)
            return;

        if (requiredParams.containsKey("CHANNEL"))
            requiredParams.put("CHANNEL", this.sipChannelId == null ? "" : this.sipChannelId);
        if (requiredParams.containsKey("LEG1-ID"))
            requiredParams.put("LEG1-ID", this.clientCallId == null ? "" : this.clientCallId);
        if (requiredParams.containsKey("REROUTE-ADDRESS"))
            requiredParams.put("REROUTE-ADDRESS", this.redirectionCallbackAddress == null ? "" : this.redirectionCallbackAddress);
        if (requiredParams.containsKey("OUTBOUND-GW"))
            requiredParams.put("OUTBOUND-GW", this.outboundGateway == null ? "" : this.outboundGateway);
        if (requiredParams.containsKey("AUX"))
            requiredParams.put("AUX", "" + this.auxiliary);
        if (requiredParams.containsKey("REMAINING-ATTEMPTS"))
            requiredParams.put("REMAINING-ATTEMPTS", "" + this.remainingAttempts);
    }

    @Override
    public Map<String, String> getCallbackParams() {
        return null;
    }

    @Override
    public Map<String, String> getCCXMLParams() {
        return null;
    }

    public String getSipChannelId() {
        return this.sipChannelId;
    }

    public String getClientCallId() {
        return this.clientCallId;
    }

    public String getRedirectionCallbackAddress() {
        return this.redirectionCallbackAddress;
    }

    public String getOutboundGateway() {
        return this.outboundGateway;
    }

    public boolean isAuxiliary() {
        return this.auxiliary;
    }

    public int getRemainingAttempts() {
        return this.remainingAttempts;
    }

    public long getPdd() {
        return this.pdd;
    }

    public void setPdd(long pdd) {
        this.pdd = pdd;
    }

    public String getApplicationId() {
        return StringUtils.trimToNull(this.applicationId);
    }

    @Override
    public String getDebugString() {
        StringBuilder sb = new StringBuilder("SIP-ASTERISK-APP-CONTEXT ::: SIP-CHANNEL-ID=");
        sb.append(this.sipChannelId);
        sb.append(" :: CLIENT-CALL-ID=");
        sb.append(this.clientCallId);
        sb.append(" :: REDIRECTION-ADDRESS=");
        sb.append(this.redirectionCallbackAddress);
        sb.append(" :: OUTBOUND-GW=");
        sb.append(this.outboundGateway);
        sb.append(" :: IS-AUXILIARY=");
        sb.append(this.auxiliary);
        sb.append(" :: REMAINING-ATTEMPTS=");
        sb.append(this.remainingAttempts);
        sb.append(" :: PDD=");
        sb.append(this.pdd);
        sb.append(" :: APP-ID=");
        sb.append(this.applicationId);
        return sb.toString();
    }

}
