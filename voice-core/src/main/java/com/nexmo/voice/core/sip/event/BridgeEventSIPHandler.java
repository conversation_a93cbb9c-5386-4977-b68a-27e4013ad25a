package com.nexmo.voice.core.sip.event;

import java.math.BigDecimal;
import java.util.Objects;

import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeEvent;
import com.nexmo.voice.core.sip.AsteriskAGIServer;
import com.nexmo.voice.core.billing.vquota.VQuotaService;

import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.ChargingConfig.CountrySpecificInfo;
import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.billing.QuotaClient;
import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.EffectiveCost;
import com.nexmo.voice.core.types.EffectivePrice;
import com.nexmo.voice.core.types.VoiceDirection;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.common.util.StringUtil;
import com.thepeachbeetle.hlr.staticprefixmap.SimpleHlrNetworkPrefixMapLookup.Network;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.services.networks.client.NetworksClient;

public final class BridgeEventSIPHandler {

    private static final Logger Log = LogManager.getLogger(BridgeEventSIPHandler.class);

    protected static void handleBridgeEvent(BridgeEvent event, String sessionId,
                                            VoiceContext origContext) throws VoiceEventHandlerException {

        Log.info("{}: Processing SIP Bridge Event ", sessionId);

        VoiceContext destContext;
        SIPAsteriskContext originApplicationContext = (SIPAsteriskContext) origContext.getApplicationContext();
        String redirectionCallbackAddress = originApplicationContext.getRedirectionCallbackAddress();
        String to = redirectionCallbackAddress == null ? origContext.getTo() : redirectionCallbackAddress;
        if(origContext.isOutboundNCCOConnectToDomain()) {
            // if this is an NCCO to domain call, we need to set the to back to the original value
            to = origContext.getTo();
        }
        String from = origContext.getFrom();
        String forceSender = origContext.getForcedSender();

        // CLI to use for Caller ID ("From:"), P-Asserted-Identity and source-prefix pricing
        String cli = from;
        if (Objects.nonNull(forceSender))
            cli = forceSender;

        // Retrieve network...
        String network = origContext.getNetwork();
        String networkType = origContext.getNetworkType();
        String networkName = origContext.getNetworkName();
        String countryCode = origContext.getCountryCode();
        String numberType = null;
        String callType = null;
        String sourceCountryCode = origContext.getSourceCountryCode();

        if (Log.isDebugEnabled()) {
            Log.debug("{} bridgeEvent details taken from orig context: to {} network {}",
                    sessionId, to, (Objects.isNull(network) ? "null" : network.toString()));
            Log.debug("{} redirectionCallbackAddress {} wasOriginallyDirectedToApplication {}",
                    sessionId, redirectionCallbackAddress, wasOriginallyDirectedToApplication(originApplicationContext));
        }

        if (redirectionCallbackAddress != null) { // it's INBOUND product
            // there is an LVN and also this call is not directed to an application (i.e.
            // not VBC or Domain)
            if (StringUtil.containsOnlyNumbers(to) && !wasOriginallyDirectedToApplication(originApplicationContext)) {
                if (Log.isDebugEnabled())
                    Log.debug("{} BridgeEvent dest context building. find the network and country for the requested destination ", sessionId);

                NetworksClient networksClient = Core.getInstance().getNetworksClient();
                Network nw = networksClient != null ? networksClient.lookupNetwork(to) : null;
                if (nw != null) {
                    network = nw.getMccMnc();
                    networkType = SipAppUtils.getNetworkType(nw);
                    networkName = nw.getName();
                    countryCode = nw.getCountryCode();
                    if (Log.isDebugEnabled())
                        Log.debug("{} BridgeEvent: found network {} for to-destination {}", sessionId, nw.toString(), to);
                }
            } else {
                if (Log.isDebugEnabled())
                    Log.debug("{} BridgeEvent dest context building: the requested destination is not a number.", sessionId);
                network = null;
                networkType = null;
                networkName = null;
                countryCode = null;
            }
        }

        //Initial requested product class - DO NOT use the context VoiceProduct - this is always SIP :-(
        VoiceProduct origVoiceProduct = SipAppUtils.getVoiceProduct(origContext.getProductClass());

        // Retrieve account...
        String accountId = origContext.getAccountId();

        SmppAccount account = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
        } catch (AccountsException e) {
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            throw new VoiceEventHandlerException("Could not retrieve account['" + accountId + "']", e);
        }

        // Retrieving charging info from config ...
        ChargingConfig chargingConfig = Core.getInstance().getConfig().getChargingConfig();
        if (chargingConfig == null) {
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            throw new VoiceEventHandlerException("No Charging Config specified!! ...");
        }

        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig(); // Can be null

        // Price ..
        PriceMatrixList priceMatrix = Core.getInstance().getConfig().getMtPriceMatrixList();
        if (priceMatrix == null) {
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_INTERNAL_ERROR);
            throw new VoiceEventHandlerException("Could not build context. price matrix was null!");
        }

        if (Log.isDebugEnabled())
            Log.debug("{} BridgeEvent, about to get the price using MtPriceMatrixList network {} for to-destination {}", sessionId, network, to);

        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrix,account,
                                                                          to,
                                                                          cli,
                                                                          originApplicationContext.getApplicationId(),
                                                                          network,
                                                                          chargingConfig.getDefaultSipPrice(),
                                                                          origContext.isVAPIOutboundToVBC(),
                                                                          prefixMapConfig,
                                                                          chargingConfig,
                                                                          origVoiceProduct,
                                                                          "BridgeEvent-price",
                                                                          origContext.isOutboundProgrammableSip(),
                                                                          origContext.isEmergencyCall());

        if (Log.isDebugEnabled())
                Log.debug("{} BridgeEvent, the price using MtPriceMatrixList is {}", sessionId, price);


        // Cost ..
        String gateway = originApplicationContext.getOutboundGateway();
        GatewayInfoMatrixConfig gwInfoMatrixConfig = Core.getInstance().getConfig().getSIPGatewayInfoMatrixConfig();
        //No need to search for the gateway details if it is "default" or "vbc" (when "vbc" is the VAPI-toVBC specific scenario)
        SupplierMappingConfig gwInfo = null;
        PriceMatrixList costMatrix = null;
        if (!origContext.isVAPIOutboundToVBC() && !"default".equals(gateway)) {
            gwInfo = gwInfoMatrixConfig.getGatewayInfo(gateway);
            costMatrix = gwInfo == null ? null : gwInfo.getCostMatrix();
        }

        if (gwInfo == null) {
            if ("default".equals(gateway)) {
                Log.info("{} Outbound gateway is 'default'. WILL USE DEFAULT COST! for account {} for event {}",
                        sessionId, accountId, event.hashCode());
            } else if (origContext.isVAPIOutboundToVBC()) {
                Log.info("{} Outbound gateway is 'vbc' for VAPI outbound to vbc. for account {} for event {}",
                        sessionId, accountId, event.hashCode());
            } else if (!accountId.equals(gateway)) {//SIP-221 - those cases where the gw is the accountId - that should not log an error
                Log.error("{} Could not find gateway-info for outbound gateway {}.  WILL USE DEFAULT COST! for account {} for event ",
                        sessionId, gateway, accountId, event.hashCode());
            }
        }

        //This will be used for the 2nd Leg calculations which 
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(costMatrix,
                                                                      account,
                                                                      to,
                                                                      cli,
                                                                      originApplicationContext.getApplicationId(),
                                                                      network,
                                                                      chargingConfig.getDefaultSipCost(),
                                                                      origContext.isVAPIOutboundToVBC(),
                                                                      "BridgeEvent-cost",
                                                                      origContext.isEmergencyCall());

        if (Log.isDebugEnabled())
                Log.debug("{} BridgeEvent, the cost is {}", sessionId, cost);


        final CountrySpecificInfo specificInfo = chargingConfig.getCountrySpecificInfo(origContext.getCountryCode());
        long firstChargedSeconds = specificInfo.getMinIncrement();
        long quotaUpdatesInterval = specificInfo.getRecurringIncrement();

        boolean isAuxiliary = originApplicationContext.getApplicationId() != null;

        //This is the SECOND leg creation using the above details.
        //auxiliary = originApplicationContext.getApplicationId() != null
        //            While setting the first leg, the appplicationId is the name of the 
        //            application to route to or null.
        //
        //            if applicationId null : the first leg was NOT routing to an application
        //                                    auxiliary=false                                     
        //            if applicationId !null : the first leg had routing to an application
        //                                    auxiliary=true
        //  
        SIPAsteriskContext applicationContext = new SIPAsteriskContext(event.getChannel2(),
                                                                       originApplicationContext.getClientCallId(),
                                                                       originApplicationContext.getRedirectionCallbackAddress(),
                                                                       gateway,
                                                                       isAuxiliary,
                                                                       originApplicationContext.getRemainingAttempts(),
                                                                       originApplicationContext.getApplicationId());

        //Some smoke-tests account has a special capability to ignore the quota, meaning they should not charge.
        //In order to handle this, we set a forcedPrice of zero for such accounts.
        //At this point forcedPrice can be zero or null.
        final BigDecimal forcedPrice = checkQuotaEnabledForcedPrice(account, origContext, sessionId);
        if (Log.isDebugEnabled()) {
            Log.debug("{} forcedPrice is {}", sessionId, Objects.isNull(forcedPrice) ? "null" : forcedPrice.toPlainString());
        }

        //The call termination point type
        String callTermination = null;
        if (Objects.isNull(origContext.getCallTermination())) {
            if (Objects.nonNull(to)) {
                if (to.startsWith("sip:")) {
                    callTermination = "sip";
                } else if (!to.contains("-")) {
                    callTermination = "pstn";
                }

                if (Log.isDebugEnabled())
                    Log.debug("Setting the CALL_TERMINATION= explicitly to {} for sessionId {} to {} connectionId {}",
                            callTermination, sessionId, to, event.getUniqueId2());
            }
        } else {
            callTermination = origContext.getCallTermination();
        }
        if (Log.isDebugEnabled()) {
            Log.debug("{} callTermination is {}", sessionId, callTermination);
        }


        if (Log.isDebugEnabled())
            Log.debug("{} building the second leg context with voice direction OUT using to-dest {} network {}",
                    sessionId, to, network);

        // PREQ_4786, PREQ-4911, PREQ-5039
        // numberType, callType are for PSTN calls
        if (!AsteriskAGIServer.DEFAULT_GATEWAY.equals(originApplicationContext.getOutboundGateway())) {
            numberType = origContext.getNumberType();
            callType = origContext.getCallType();

            // numberType, callType to be written in outbound CDR for inbound product
            if (redirectionCallbackAddress != null) {
                if (Objects.nonNull(forceSender)) {
                    NetworksClient networksClient = Core.getInstance().getNetworksClient();
                    Network sourceNetwork = networksClient != null ? networksClient.lookupNetwork(forceSender) : null;
                    if (sourceNetwork != null && sourceNetwork.getCountryCode() != null) {
                        sourceCountryCode = sourceNetwork.getCountryCode();
                    }
                }
                callType = SipAppUtils.determineCallType(sourceCountryCode, countryCode);

                // PREQ-4786
                if (!AsteriskAGIServer.DEFAULT_GATEWAY.equals(origContext.getCurrentGateway())) { // PSTN -> LVN -> PSTN
                    numberType = SipAppUtils.NumberType.CLIVerified.name();
                } else if (AsteriskAGIServer.isAllowedAnyCallerIdValue(account, to)) {
                    numberType = SipAppUtils.NumberType.CLIUnverified.name();
                } else {
                    numberType = SipAppUtils.NumberType.CLIMissing.name();
                }
            }

        }

        boolean isChargeable = !origContext.isVoiceSkipQuota() && !SipAppUtils.isNotChargeablePricePrefix(price.getPrefix());

        boolean isVpricingEnabled = origContext.isVpricingEnabled();
        if (isChargeable && isVpricingEnabled && forcedPrice != null && BigDecimal.ZERO.compareTo(forcedPrice) == 0) {
            isChargeable = false;
        }

        destContext = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                                                .withFrom(from)
                                                .withTo(to)
                                                .withAccountId(accountId)
                                                .withAccountPricingGroup(origContext.getAccountPricingGroup())
                                                .withMasterAccountId(origContext.getMasterAccountId())
                                                .withMasterAccountPricingGroup(origContext.getMasterAccountPricingGroup())
                                                .withGateway(gateway)
                                                .withNetwork(network)
                                                .withNetworkType(networkType)
                                                .withNetworkName(networkName)
                                                .withCountryCode(countryCode)
                                                .withVoiceDirection(VoiceDirection.OUTBOUND)
                                                .withApplicationContext(applicationContext)
                                                .withProductClass(origContext.getProductClass())
                                                .withPricePerMinute(price.getPrice())
                                                .withPricePrefix(price.getPrefix())
                                                .withPricePrefixGroup(price.getPrefixGroup())
                                                .withPriceSenderPrefix(price.getSenderPrefix())
                                                .withPriceTimestamp(price.getTimestamp())
                                                .withCostPerMinute(cost.getCost())
                                                .withCostPrefix(cost.getPrefix())
                                                .withCostPrefixGroup(cost.getPrefixGroup())
                                                .withCostSenderPrefix(cost.getSenderPrefix())
                                                .withCostTimestamp(cost.getTimestamp())
                                                .withFirstChargedSeconds(firstChargedSeconds)
                                                .withQuotaUpdatesInterval(quotaUpdatesInterval)
                                                .withCallbackMethod(origContext.getCallbackMethod())
                                                .withCallbackUrl(origContext.getCallbackUrl())
                                                .withInternalCallbackMethod(origContext.getInternalCallbackMethod())
                                                .withInternalCallbackUrl(origContext.getInternalCallbackUrl())
                                                .withSequenceNumber(origContext.getSequenceNumber())
                                                .withForcedPrice(forcedPrice)
                                                .withRequestIp((origContext.getRequestIp()))
                                                .withForcedSender(forceSender)
                                                .withCallTermination(callTermination)
                                                .withQuotaRef(SipAppUtils.generateQuotaReference(sessionId, false))
                                                .withCustomerDomain(origContext.getCustomerDomain())
                                                .withCustomerDomainType(origContext.getCustomerDomainType())
                                                //higher preference to the the temporarily calculated outbound stir shaken if exits
                                                .withStirShaken(Objects.toString(origContext.getTemporaryOutboundStirShaken(), origContext.getStirShaken()))
                                                .withVoiceSkipQuota(origContext.isVoiceSkipQuota())
                                                .withInternalFlags(origContext.getInternalFlags())
                                                .withSrtpEnabled(origContext.isSrtpEnabled())
                                                .withAsteriskVersion(origContext.getAsteriskVersion())
                                                .withSipTransport(origContext.getSipTransport())
                                                .withClientCallId(origContext.getClientCallId())
                                                .withIsOutboundProgrammableSip(origContext.isOutboundProgrammableSip())
                                                .withIsSipOriginToLVNToApplication(origContext.isSipOriginToLVNToApplication())
                                                .withSourceCountryCode(sourceCountryCode)
                                                .withId(origContext.getId())
                                                .withBlockingSubsystem(origContext.getBlockingSubsystem())
                                                .withProductPath(origContext.getProductPath())
                                                .withNumberType(numberType)
                                                .withCallType(callType)
                                                .withIsOutboundNCCOConnectToDomain(origContext.isOutboundNCCOConnectToDomain())
                                                .withRoutingGroup(origContext.getRoutingGroup())
                                                .withRoutingOa(origContext.getRoutingOa())
                                                .withRoutingBindId(origContext.getRoutingBindId())
                                                .withChargeable(isChargeable)
                                                .withVpricingEnabled(isVpricingEnabled)
                                                .withIsEmergencyCall(origContext.isEmergencyCall())
                                                .withEmergencyCallFailoverReason(origContext.getEmergencyCallFailoverReason())
                                                .build();

        destContext.setSessionId(sessionId);
        destContext.setConnectionId(event.getUniqueId2());

        try {
            ((SIPAsteriskContext) destContext.getApplicationContext())
                    .setPdd(Core.getInstance().getPddCalculator().calculate(event.getUniqueId1(), event.getUniqueId2()));
        } catch (PDDCalculationException e) {
            Log.info("PDD can't be calculated for the call with session id " + sessionId, e);
        }

        //This is the second leg caching, the charging will not start yet, because the ChargingContext status
        //is still NOT_STARTED
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        cache.storeContext(sessionId, event.getUniqueId2(), destContext);
        if (Log.isDebugEnabled())
            Log.debug("SIP Bridge Event: hashCode=" + event.hashCode() + " of SESSION-ID " + sessionId +
                    " Building and storing context :: " + destContext.getDebugString());

        //one of the "mitigations" for the race condition... yak yak 
        if (cache.getContext(sessionId, event.getUniqueId2()) == null) {
            Log.info("SIP Bridge Event: context has been already removed for ['" + sessionId + "'] don't charge Event: hashCode=" + event.hashCode());
            return;
        }

        //Both legs should start the charging at the same time, which is the time that the BridgeEvent arrived.

        if (Log.isDebugEnabled())
            Log.debug("{} SIP Bridge Event about to start charging both legs", sessionId);

        // Start charging both legs
        BridgeEventHandler.startChargingContext(origContext,  //Leg1
                             event.getChannel1(),
                             event.getChannel2(),
                             event);

        BridgeEventHandler.startChargingContext(destContext,  //Leg2
                             event.getChannel1(),
                             event.getChannel2(),
                             event);
    }


    private static BigDecimal checkQuotaEnabledForcedPrice(SmppAccount account, VoiceContext origContext, String sessionId) throws VoiceEventHandlerException {
        //skip the quota check if origContext.isVoiceSkipQuota() is set
        if(origContext.isVoiceSkipQuota()) {
            return BigDecimal.ZERO;
        }
        QuotaClient quotaClient = Core.getInstance().getQuotaClient();

        BigDecimal quotaNotEnabledPrice = null;

        try {
            AccountBalance accountBalance = null;
            if(origContext.isVpricingEnabled()) {
                VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
                accountBalance = vQuotaService.getCurrentBalance(account.getSysId(), null, sessionId);
            } else {
                accountBalance = quotaClient.checkAccountBalance(account.getSysId(), null);
            }
            if (!accountBalance.isQuotaEnabled()) {
                if (Log.isDebugEnabled())
                    Log.debug("Quota is disabled for account {}. Setting forced price to 0.", account.getSysId());
                quotaNotEnabledPrice = BigDecimal.ZERO;
                origContext.addInternalFlag(CallInternalFlag.ACCOUNT_HAS_QUOTA_DISABLED);
            }
        } catch (QuotaDisabledException e) {
            // since we required free balance is null, we will never catch this exception
            // unless the contract changes... In which case, we do it anyway.
            if (Log.isDebugEnabled())
                Log.debug("Quota is disabled for account {}. Setting forced price to 0.", account.getSysId(), e);
            quotaNotEnabledPrice = BigDecimal.ZERO;
            origContext.addInternalFlag(CallInternalFlag.ACCOUNT_HAS_QUOTA_DISABLED);
        } catch (QuotaException | QuotaUnderMaintenanceException e) {
            Log.warn("Failed to check if quota is enabled for account {} due to {}", account.getSysId(), e.getMessage());
            //On catching QuotaException get the data needed from Accounts and let the call continue
            if (Core.getInstance().isAsyncQuotaFlag()) {
                SmppAccount masterAccount = null;
                try {
                    masterAccount = Accounts.getInstance().getSmppAccount(account.getMasterAccountId());
                } catch (AccountsException aex) {
                    Log.error("AsyncQuota Failed to get master account for {} due to {}", account.getSysId(), aex.getMessage());
                }
                boolean quotaEnabled = account.isQuotaEnabled() || (masterAccount != null && masterAccount.isQuotaEnabled());
                if (!quotaEnabled) {
                    if (Log.isDebugEnabled())
                        Log.debug("AsyncQuota is disabled for account {}. Setting forced price to 0.", account.getSysId());
                    quotaNotEnabledPrice = BigDecimal.ZERO;
                    origContext.addInternalFlag(CallInternalFlag.ACCOUNT_HAS_QUOTA_DISABLED);
                }
            } else {
                origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_QUOTA_ISSUES);
                throw new VoiceEventHandlerException("Could not check if quota was enabled for account: " + account.getSysId(), e);
            }
        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException e) {
            Log.warn("Failed to check if quota is enabled for account {} due to {}", account.getSysId(), e.getMessage());
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            throw new VoiceEventHandlerException("Could not check if quota was enabled for account: " + account.getSysId(), e);
        }

        return quotaNotEnabledPrice;
    }

    //For VBC, the original request was directed to an application_id and the provided LVN is ignored.
    //SIP-409: programmable sip: the call was directed to a Domain with an application
    private static boolean wasOriginallyDirectedToApplication(SIPAsteriskContext originApplicationContext) {
        return Objects.nonNull(originApplicationContext.getApplicationId()) &&
                !originApplicationContext.getApplicationId().trim().isEmpty();
    }


}
