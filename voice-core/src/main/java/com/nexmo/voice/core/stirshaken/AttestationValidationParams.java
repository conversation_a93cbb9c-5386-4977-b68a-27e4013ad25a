package com.nexmo.voice.core.stirshaken;

public class AttestationValidationParams {

    private final String accountId;
    private final boolean hasKyc;
    private final boolean hasDisableMustOwnLVN;
    private final boolean isCallerIdE164;
    private final boolean isOwnedLvn;
    private final String destinationCountry;
    private final String gatewayName;
    private final String forcedSender;
    private final String sessionId;
    private final boolean isBYON;

    private AttestationValidationParams(String accountId,
                                        boolean hasKyc,
                                        boolean hasDisableMustOwnLVN,
                                        boolean isCallerIdE164,
                                        boolean isOwnedLvn,
                                        String destinationCountry,
                                        String gatewayName,
                                        String forcedSender,
                                        String sessionId,
                                        boolean isBYON) {
        this.accountId = accountId;
        this.hasKyc = hasKyc;
        this.hasDisableMustOwnLVN = hasDisableMustOwnLVN;
        this.isCallerIdE164 = isCallerIdE164;
        this.isOwnedLvn = isOwnedLvn;
        this.destinationCountry = destinationCountry;
        this.gatewayName = gatewayName;
        this.forcedSender = forcedSender;
        this.sessionId = sessionId;
        this.isBYON = isBYON;
    }

    public static AttestationValidationParamsBuilder builder() {
        return new AttestationValidationParamsBuilder();
    }

    public String getAccountId() {
        return accountId;
    }

    public boolean isHasKyc() {
        return hasKyc;
    }

    public boolean isHasDisableMustOwnLVN() {
        return hasDisableMustOwnLVN;
    }

    public boolean isCallerIdE164() {
        return isCallerIdE164;
    }

    public boolean isOwnedLvn() {
        return isOwnedLvn;
    }

    public String getDestinationCountry() {
        return destinationCountry;
    }

    public String getGatewayName() {
        return gatewayName;
    }

    public String getForcedSender() {
        return forcedSender;
    }

    public String getSessionId() {
        return sessionId;
    }

    public boolean isBYON() {
        return isBYON;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("AttestationValidationParam{");
        sb.append("accountId='")
                .append(accountId)
                .append('\'');
        sb.append(", hasKyc=")
                .append(hasKyc);
        sb.append(", hasDisableMustOwnLVN=")
                .append(hasDisableMustOwnLVN);
        sb.append(", isCallerIdE164=")
                .append(isCallerIdE164);
        sb.append(", isOwnedLvn='")
                .append(isOwnedLvn)
                .append('\'');
        sb.append(", destinationCountry='")
                .append(destinationCountry)
                .append('\'');
        sb.append(", gatewayName='")
                .append(gatewayName)
                .append('\'');
        sb.append(", forcedSender='")
                .append(forcedSender)
                .append('\'');
        sb.append(", isBYON='")
                .append(isBYON)
                .append('\'');
        sb.append('}');
        return sb.toString();
    }

    public static final class AttestationValidationParamsBuilder {
        private String accountId;
        private boolean hasKyc;
        private boolean hasDisableMustOwnLVN;
        private boolean isCallerIdE164;
        private boolean isOwnedLvn;
        private String destinationCountry;
        private String gatewayName;
        private String forcedSender;
        private String sessionId;
        private boolean isBYON;

        private AttestationValidationParamsBuilder() {
        }


        public AttestationValidationParamsBuilder withAccountId(String accountId) {
            this.accountId = accountId;
            return this;
        }

        public AttestationValidationParamsBuilder withHasKyc(boolean hasKyc) {
            this.hasKyc = hasKyc;
            return this;
        }

        public AttestationValidationParamsBuilder withHasDisableMustOwnLVN(boolean hasDisableMustOwnLVN) {
            this.hasDisableMustOwnLVN = hasDisableMustOwnLVN;
            return this;
        }

        public AttestationValidationParamsBuilder withIsCallerIdE164(boolean isCallerIdE164) {
            this.isCallerIdE164 = isCallerIdE164;
            return this;
        }

        public AttestationValidationParamsBuilder withIsOwnedLvn(boolean isOwnedLvn) {
            this.isOwnedLvn = isOwnedLvn;
            return this;
        }

        public AttestationValidationParamsBuilder withDestinationCountry(String destinationCountry) {
            this.destinationCountry = destinationCountry;
            return this;
        }

        public AttestationValidationParamsBuilder withGatewayName(String gatewayName) {
            this.gatewayName = gatewayName;
            return this;
        }

        public AttestationValidationParamsBuilder withForcedSender(String forcedSender) {
            this.forcedSender = forcedSender;
            return this;
        }

        public AttestationValidationParamsBuilder withSessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }
        public AttestationValidationParamsBuilder withIsBYON(boolean isBYON) {
            this.isBYON = isBYON;
            return this;
        }

        public AttestationValidationParams build() {
            return new AttestationValidationParams(accountId,
                    hasKyc,
                    hasDisableMustOwnLVN,
                    isCallerIdE164,
                    isOwnedLvn,
                    destinationCountry,
                    gatewayName,
                    forcedSender,
                    sessionId,
                    isBYON);
        }
    }
}
