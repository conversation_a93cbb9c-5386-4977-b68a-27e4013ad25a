package com.nexmo.voice.core.types;

public enum ProductType {
    PSIP_OUTBOUND("psip_outbound"),
    PSIP_INBOUND("psip_inbound"),
    VOICE_OUTBOUND("voice_outbound"),
    VOICE_INBOUND("voice_inbound");

    private final String productString;

    ProductType(String productString) {
        this.productString = productString;
    }

    public String getProductString() {
        return productString;
    }
}