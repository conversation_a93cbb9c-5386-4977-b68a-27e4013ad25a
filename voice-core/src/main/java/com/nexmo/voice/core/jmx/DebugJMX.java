package com.nexmo.voice.core.jmx;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;

import org.apache.log4j.Logger;

import com.thepeachbeetle.common.app.jmx.AbstractDebugJMX;

import com.thepeachbeetle.messaging.hub.config.routing.MtRoutingRule;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.randomize.Randomizer;

public class DebugJMX extends AbstractDebugJMX implements DebugJMXMBean {

    private static final Logger Log = Logger.getLogger(DebugJMX.class.getName());

    public DebugJMX() {
        super("dumps", "");
    }

    @Override
    public String getInstanceId() {
        return Core.getInstance().getConfig().getInstanceId();
    }

    @Override
    public void dumpVoiceContextCache() throws Exception {
        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForProductClass = voiceContextCache.getAllContexts();

        final String fileHeader = "===== VoiceContextCache ";
        final String fileName = "context-cache-dump";

        dumpContexts(fileHeader, fileName, contextForProductClass);
    }

    @Override
    public void dumpVoiceContextCache(String productClass) throws Exception {
        if (productClass == null)
            throw new NullPointerException("Product class cannot be null!");

        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForProductClass = voiceContextCache.getAllContexts(productClass);

        final String fileHeader = "===== VoiceContextCache for product class [" + productClass + "]";
        final String fileName = "context-cache-dump-for-" + productClass;

        dumpContexts(fileHeader, fileName, contextForProductClass);
    }

    @Override
    public String[] getVoiceContextCache() {
        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForProductClass = voiceContextCache.getAllContexts();

        return getContexts(contextForProductClass);
    }

    @Override
    public String[] getVoiceContextCache(String productClass) {
        if (productClass == null)
            throw new NullPointerException("Product class cannot be null!");

        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForProductClass = voiceContextCache.getAllContexts(productClass);

        return getContexts(contextForProductClass);
    }

    @Override
    public void dumpContexByAccountToLog(String accountId) throws Exception {
        if (accountId == null)
            throw new NullPointerException("Account id cannot be null!");

        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForAccountId = voiceContextCache.getAllContextsByAccount(accountId);

        Collections.sort(new ArrayList<>(contextForAccountId), durationDescending);

        Log.info("Dumping contexts for accountId: " + accountId);
        for (VoiceContext ctx : contextForAccountId)
            ctx.dumpToLog();
    }

    @Override
    public void dumpContextsByAccount(String accountId) throws Exception {
        if (accountId == null)
            throw new NullPointerException("Account id cannot be null!");

        VoiceContextCache voiceContextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contextForAccountId = voiceContextCache.getAllContextsByAccount(accountId);

        final String fileHeader = "===== VoiceContextCache for account id [" + accountId + "]";
        final String fileName = "context-cache-dump-for-account-" + accountId;

        dumpContexts(fileHeader, fileName, contextForAccountId);
    }

    private void dumpContexts(String fileHeader, String fileName, Collection<VoiceContext> contexts) throws Exception {
        Collection<String> contextStrForDumping = new ArrayList<>();

        contextStrForDumping.add(fileHeader);
        for (VoiceContext ctx : contexts)
            contextStrForDumping.add(ctx.getDebugString());

        createDebugDump(fileName, contextStrForDumping);
    }

    private static String[] getContexts(Collection<VoiceContext> contextForProductClass) {
        Collection<String> dump = new ArrayList<>();
        for (VoiceContext ctx : contextForProductClass)
            dump.add(ctx.getDebugString());

        return dump.toArray(new String[contextForProductClass.size()]);
    }

    private static String[] toStringArray(MtRoutingRule[] rules) {
        if (rules == null)
            return null;
        String[] list = new String[rules.length];
        for (int i = 0; i < rules.length; i++)
            list[i] = rules[i].toString();
        return list;
    }

    @Override
    public String[] getAllAccountsRoutingRules() throws Exception {
        try {
            return toStringArray(Core.getInstance().getMtRouter().getAllAccountsRoutingRules());
        } catch (Exception e) {
            Log.error("Barf ....", e);
            throw e;
        }
    }

    @Override
    public String[] getAllPerAccountRoutingRules() throws Exception {
        try {
            return toStringArray(Core.getInstance().getMtRouter().getAllPerAccountRoutingRules());
        } catch (Exception e) {
            Log.error("Barf ....", e);
            throw e;
        }
    }

    @Override
    public String[] getAllPerGroupRoutingRules() throws Exception {
        try {
            return toStringArray(Core.getInstance().getMtRouter().getAllPerGroupRoutingRules());
        } catch (Exception e) {
            Log.error("Barf ....", e);
            throw e;
        }
    }

    private static Comparator<VoiceContext> durationDescending = new Comparator<VoiceContext>() {

        @Override
        public int compare(VoiceContext ctx1, VoiceContext ctx2) {
            return (int) (ctx2.getDurationSoFar() - ctx1.getDurationSoFar());
        }

    };

    @Override
    public String getCallCountForAccount(String accountId) throws Exception {
        return getCountDebugString(accountId);
    }

    @Override
    public String[] getCallCountForActiveAccounts() {
        String[] result = new String[1];
        result[0] = getCountDebugString("All active accounts");
        return result;
    }

    private static String getCountDebugString(String accountId) {
        return "Call count for [" + accountId + "]  is not supported  ";
    }

    @Override
    public String[] getRandomPoolCache() throws Exception {
        Randomizer randomizer = Randomizer.getInstance();
        Collection<String> keySet = randomizer.getCachedRandomPools();
        return keySet.toArray(new String[keySet.size()]);
    }

    @Override
    public void flushRandomPoolCache() throws Exception {
        Randomizer randomizer = Randomizer.getInstance();
        randomizer.flushCache();
    }

    @Override
    public String[] getTargetGroupCache() throws Exception {
        Collection<String> keySet = Core.getInstance().getMtRouter().getRoutingConfig().getMtRoutingTargetGroups().keySet();
        return keySet.toArray(new String[keySet.size()]);
    }

}
