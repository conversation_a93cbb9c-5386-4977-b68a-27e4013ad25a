package com.nexmo.voice.core.stirshaken.impl;

import com.nexmo.voice.core.stirshaken.Attestation;
import com.nexmo.voice.core.stirshaken.AttestationValidationParams;
import com.nexmo.voice.core.stirshaken.EnforcerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.nexmo.voice.core.stirshaken.Attestation.*;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class SIPEnforcerService implements EnforcerService {

    private final static Logger Log = LogManager.getLogger(SIPEnforcerService.class);

    public Attestation attestationLevel(AttestationValidationParams attestationValidationParams) {
        if (Log.isDebugEnabled()) {
            Log.debug("{} Starting SIP Stir Shaken Calculation, params = {}", attestationValidationParams
                    .getSessionId(), attestationValidationParams);
        }
        if (excludedGateway(attestationValidationParams.getGatewayName())) {
            return NONE;
        }
        if (excludedDestination(attestationValidationParams.getDestinationCountry())) {
            return NONE;
        }
        if (!attestationValidationParams.isHasKyc()) {
            return CLASS_C;
        } else {
            if (!attestationValidationParams.isCallerIdE164()) {
                if (isEmpty(attestationValidationParams.getForcedSender())) {
                    return CLASS_B;
                } else {
                    return CLASS_A;
                }
            } else {
                if (isEmpty(attestationValidationParams.getForcedSender())) {
                    if (attestationValidationParams.isOwnedLvn() && attestationValidationParams.isBYON()) {
                        return CLASS_A;
                    }
                    if (attestationValidationParams.isHasDisableMustOwnLVN() && attestationValidationParams.isOwnedLvn()) {
                        return CLASS_A;
                    }
                    if (!attestationValidationParams.isHasDisableMustOwnLVN() && attestationValidationParams.isOwnedLvn()) {
                        return CLASS_A;
                    }
                    if (attestationValidationParams.isHasDisableMustOwnLVN() && !attestationValidationParams.isOwnedLvn()) {
                        return CLASS_B;
                    }
                    if (!attestationValidationParams.isHasDisableMustOwnLVN() && !attestationValidationParams.isOwnedLvn()) {
                        return CLASS_B;
                    }
                } else {
                    return CLASS_A;
                }
            }
        }
        String errorMessage = String.format("%s Couldn't calculate attestation level", attestationValidationParams.getSessionId());
        Log.error(errorMessage);
        throw new IllegalStateException(errorMessage);
    }
}