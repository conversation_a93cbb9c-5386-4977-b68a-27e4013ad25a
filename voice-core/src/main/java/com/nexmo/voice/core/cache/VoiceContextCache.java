package com.nexmo.voice.core.cache;

import java.util.ArrayList;
import java.util.Collection;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.Lock;

import com.nexmo.voice.core.Core;
import io.prometheus.client.Gauge;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.thepeachbeetle.common.persistentclusteredcache.NonPersistentCacheCore;
import com.thepeachbeetle.common.persistentclusteredcache.config.CachePersistenceConfig;

/**
 * The VoiceContext include all the details per leg in a conversation.
 * The first leg's VoiceContext is created during the AGI request.
 * The second leg's VoiceContext is created during the BridgeEvent.
 * This means that if the call is failing to start we have only one VoiceContext
 *
 * All the VoiceContext instances are kept in a cache implemented in messaging.jar
 * This cache configuration and loader is specifically overwritten by the SIPApp
 *
 * The Cache configuration for SIPApp is:
 *
 *         <context-cache enabled="true" db-persistence="true"> =========> to the best of my understanding of the
 *                                                                         messaging.jar this is not really a DB - it is
 *                                                                         an indicator to add new entries or to update them in the cache.
 *                                                                         in any case - there is no DB configured for this ;-)
 *           <cluster enabled="false"/>  ================================> This is very important - NO Cluster!!
 *           <purge enabled="true"
 *                  purge-interval-count="10"
 *                  purge-interval-unit="minute"
 *                  max-time-to-keep-count="12"
 *                  max-time-to-keep-unit="hour"/>
 *         </context-cache>
 *
 *
 * The VoiceContext cache structure is: Map<SessionId, Map<ConnectionId, VoiceContext>>
 * Each VoiceContext includes CharableContext
 */


public class VoiceContextCache extends NonPersistentCacheCore<String, String, VoiceContext> {

    private static final Logger Log = LogManager.getLogger(VoiceContextCache.class);

    public VoiceContextCache(CachePersistenceConfig cachePersistenceConfig) throws Exception {
        super("VOICE-CONTEXT-CACHE",
              VoiceContext.class,
              cachePersistenceConfig,
              true); // enableIndexMapInnerKeyOnly
    }

    public void storeContext(final String sessionId, final String connectionId, final VoiceContext ctx) {
        if (sessionId == null || connectionId == null || ctx == null)
            return;
        //registerEntryFromCluster(ctx);
        createOrUpdateEntry(sessionId, connectionId, ctx, null);
    }

    public void updateContext(final VoiceContext ctx) {
        if (ctx == null)
            return;
        createOrUpdateEntry(ctx.getSessionId(), ctx.getConnectionId(), ctx, null);
    }

    public void updateContextWithAction(final VoiceContext ctx,
                                        final UpdateCachedEntityAction<String, String, VoiceContext> action) {
        if (ctx == null)
            return;
        updateExistingEntry(ctx, action);
    }

    public VoiceContext getContext(final String sessionId, final String connectionId) {
        if (sessionId == null || connectionId == null) {
            Log.error("Unable to getContext of sessionId:  " + sessionId + " connectionId " + connectionId);
            return null;
        }
        return getCacheEntry(sessionId, connectionId);
    }

    public VoiceContext getFirstContextWithConnectionId(final String connectionId) {
        return getCacheFirstEntryMatchingInnerKey(connectionId);
    }

    //This method attempt to get the list of all the VoiceContexts regardless of their product class.
    public Collection<VoiceContext> getAllContexts() {
        Collection<VoiceContext> contexts = new ArrayList<>();
        for (ConcurrentMap<String, VoiceContext> mapWithContexts : getCache().values())
            for (VoiceContext ctx : mapWithContexts.values())
                contexts.add(ctx);

        return contexts;
    }

    //This method attempt to get the list of all the VoiceContexts ONLY of the specific product class.
    public Collection<VoiceContext> getAllContexts(final String productClass) {
        Collection<VoiceContext> contexts = new ArrayList<>();

        //Fetch all the cache values: i.e. All the Map<ConnectionId, VoiceContext> - This is an iterator built upon ConcurrentMap
        //Hence will not cause issues of concurrency if used on its own thread. (Which is so at the moment) 
        for (ConcurrentMap<String, VoiceContext> mapWithContexts : getCache().values())
            for (VoiceContext ctx : mapWithContexts.values())
                if (productClass.equals(ctx.getProductClass())) {
                    contexts.add(ctx);
                }

        return contexts;
    }

    //This method attempt to get the list of all the VoiceContexts ONLY of the specific product class, which their status
    //is suitable for quota updates (i.e. all those which are not ended or in error or out-of-money or price-zero)
    public Collection<VoiceContext> getAllContextsForQuotaUpdate(final String productClass) {
        Collection<VoiceContext> contexts = new ArrayList<>();
        //Fetch all the cache values: i.e. All the Map<ConnectionId, VoiceContext> - This is an iterator built upon ConcurrentMap
        //Hence will not cause issues of concurrency if used on its own thread. (Which is so at the moment) 
        for (ConcurrentMap<String, VoiceContext> mapWithContexts : getCache().values())
            for (VoiceContext ctx : mapWithContexts.values())
                if (productClass.equals(ctx.getProductClass())) {
                    if (ctx.getBillingInfo().isSuitableForQuotaUpdates(ctx.getSessionId(), ctx.getConnectionId())) {
                        contexts.add(ctx);
                        if (Log.isTraceEnabled())
                            Log.trace("getAllContextsForQuotaUpdate for productClass {} added: {}", productClass, ctx);
                    }
                }

        return contexts;
    }


    /**
     * This method is used purely for JMX logic. It returns a snapshot of all the living contexts
     * of an account. It shouldn't be used for any logic that requires a persistent view instead
     * of a snapshot.
     *
     * @param accountId the accountId of the desired contexts
     * @return snapshot of all the living contexts for the given account
     */
    public Collection<VoiceContext> getAllContextsByAccount(final String accountId) {
        Collection<VoiceContext> contexts = new ArrayList<>();
        for (ConcurrentMap<String, VoiceContext> mapWithContexts : getCache().values())
            for (VoiceContext ctx : mapWithContexts.values())
                if (accountId == null || accountId.equals(ctx.getAccountId()))
                    contexts.add(ctx);

        return contexts;
    }

    /**
     * This method is used purely for JMX logic. It returns a snapshot of all the living contexts
     * of an application context class. It shouldn't be used for any logic that requires a persistent view instead
     * of a snapshot.
     *
     * @param applicationContextClass Application context class
     * @return snapshot of all the living contexts for the given application context class
     */
    public <T> Collection<VoiceContext> getAllContextsByApplicationClass(Class<T> applicationContextClass) {
        Collection<VoiceContext> contexts = new ArrayList<>();
        for (ConcurrentMap<String, VoiceContext> mapWithContexts : getCache().values())
            for (VoiceContext ctx : mapWithContexts.values())
                if (applicationContextClass.isInstance(ctx.getApplicationContext()))
                    contexts.add(ctx);

        return contexts;
    }


    public void removeAllForSession(final String sessionId) {
        if (Log.isDebugEnabled())
            Log.debug("Removing all entries for sessionId: " + sessionId);
        removeOuterEntry(sessionId);
    }


    public Lock acquireLock(final VoiceContext voiceContext) throws Exception {
        String lockName = "context-" + voiceContext.getSessionId();

        return acquireLock(voiceContext, lockName);
    }

    /**
     * Fetches all the VoiceContext objects from cache for the given sessionId.
     *
     * @param sessionId
     * @return Collection of matching VoiceContexts
     */
    public Collection<VoiceContext> getAllContextsForSessionId(final String sessionId) {
        return new ArrayList<>(getInnerValues(sessionId));
    }

}
