package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrentBalanceApiFailureResponse {
    private String status;
    private ErrorDetails error;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ErrorDetails getError() {
        return error;
    }

    public void setError(ErrorDetails error) {
        this.error = error;
    }

    public static class ErrorDetails {
        private QuotaResponse quotaResponse;

        // Getters and Setters
        public QuotaResponse getQuotaResponse() {
            return quotaResponse;
        }

        public void setQuotaResponse(QuotaResponse quotaResponse) {
            this.quotaResponse = quotaResponse;
        }

        public static class QuotaResponse {
            private String code;
            private String description;
            private String exceptionName;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getExceptionName() {
                return exceptionName;
            }

            public void setExceptionName(String exceptionName) {
                this.exceptionName = exceptionName;
            }
        }
    }

    public void handleResponse() throws QuotaException,
            QuotaDisabledException, QuotaUnderMaintenanceException,
            AccountNotFoundException, IllegalOperationOnSubAccountException {

        if (this.getError() == null || this.getError().getQuotaResponse() == null) {
            throw new QuotaException("Invalid response structure: missing 'error' or 'quotaResponse' fields");
        }

        String code = this.getError().getQuotaResponse().getCode();
        String description = this.getError().getQuotaResponse().getDescription();
        String exceptionName = this.getError().getQuotaResponse().getExceptionName();
        String errorMessage = String.format("Error code: %s, Description: %s", code, description);
        if (exceptionName == null) {
            throw new QuotaException(errorMessage);
        } else {
            switch (exceptionName) {
                case "QuotaDisabledException":
                    throw new QuotaDisabledException(errorMessage);
                case "QuotaUnderMaintenanceException":
                    throw new QuotaUnderMaintenanceException(errorMessage);
                case "AccountNotFoundException":
                    throw new AccountNotFoundException(errorMessage);
                case "IllegalOperationOnSubAccountException":
                    throw new IllegalOperationOnSubAccountException(errorMessage);
                default:
                    throw new QuotaException(errorMessage);
            }
        }
    }
}