package com.nexmo.voice.core.callblocking;

/*
 *<callblocking-service service-host="fraud-prevention-tool.main0.api.rtc.dev.euw1.vonagenetworks.net"  base-path="/blocking-rule/internal" service-port="8080" timeout="250" username="sip" password="sip"></callblocking-service>
 */

import org.jdom.Element;


public class CallblockingServiceConfig implements java.io.Serializable {

    private final static String PROTOCOL = "https";
    private static final long serialVersionUID = -4134169018218954876L;

    private final String host;
    private final String basePath;
    private final String username;
    private final String password;
    private final int timeout;
    private final int retryCount;
    private final int retryTimeout;
    private final String fallbackHost;

    public CallblockingServiceConfig(String host, String basePath, int timeout, String username, String password, int retryCount, int retryTimeout, String fallbackHost) {
        this.host = host;
        this.basePath = basePath;
        this.timeout = timeout;
        this.username = username;
        this.password = password;
        this.retryCount = retryCount;
        this.retryTimeout = retryTimeout;
        this.fallbackHost = fallbackHost;
    }

    public String getBasePath() {
        return this.basePath;
    }

    public String getUsername() {
        return this.username;
    }

    public String getPassword() {
        return this.password;
    }

    public int getTimeout() {
        return this.timeout;
    }

    public int getRetryCount() {
        return this.retryCount;
    }

    public int getRetryTimeout() {
        return this.retryTimeout;
    }

    public String constructUri(boolean isFallbackAttempt) {
        StringBuilder sb = new StringBuilder();
        sb.append(PROTOCOL);
        sb.append("://");
        sb.append(isFallbackAttempt ? fallbackHost : host);
        if (!basePath.startsWith("/"))
            sb.append("/");
        sb.append(basePath);
        return sb.toString();
    }

    public Element toXML() {
        Element callblockService = new Element("callblocking-service");
        callblockService.setAttribute("service-host", this.host);
        callblockService.setAttribute("base-path", this.basePath);
        callblockService.setAttribute("timeout", Integer.toString(this.timeout));
        callblockService.setAttribute("username", this.username);
        callblockService.setAttribute("password", this.password);
        callblockService.setAttribute("retry-count", Integer.toString(this.retryCount));
        callblockService.setAttribute("retry-timeout", Integer.toString(this.retryTimeout));
        callblockService.setAttribute("fallback-host", this.fallbackHost);
        return callblockService;
    }

    @Override
    public String toString() {
        return "CallBlockService [host=" + this.host + "; base-path=" + this.basePath + "; timeout=" + this.timeout + "; retry-count="
            + this.retryCount + "; retry-timeout=" + this.retryTimeout + "; fallback-host=" + this.fallbackHost + "]";
    }

}
