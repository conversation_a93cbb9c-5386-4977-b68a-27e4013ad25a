package com.nexmo.voice.core.billing;


import com.nexmo.voice.config.charging.ChargingUpdaterConfig;
import com.nexmo.voice.config.charging.UpdateTaskConfig;
import com.nexmo.voice.core.cache.VoiceContext;
import io.prometheus.client.Counter;
import org.apache.log4j.Logger;

import java.util.concurrent.*;

import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.leansoft.bigqueue.BigQueueImpl;

/**
 * This class is initiated during the server startup.
 * The QuotaQueueExecuter holds a scheduled-threads-pool.
 * Each thread in the pool is activated every 1 second, and run a QuotaQueueTask.
 * The QuotaQueueTask instance is specific per product-type:
 * In SIPApp: sip_asterisk, api
 */

public class QuotaQueueExecuter {

    private static final Logger Log = Logger.getLogger(QuotaQueueExecuter.class);

    private final ChargingUpdaterConfig config;
    private final ScheduledExecutorService pool;
    private static final ConcurrentLinkedQueue<QuotaItem> quotaQueue = new ConcurrentLinkedQueue<>();
    private static BigQueueImpl persistedQuotaQueue;
    private static final Counter QUOTA_QUEUE_FAILURE = Counter.build().name("quota_queue_failure").labelNames("queue_failure").help(" ").register();

    public QuotaQueueExecuter(ChargingUpdaterConfig config) {
        this.config = config;

        final int numberOfThreads = config.getUpdateTaskConfigs().size();
        this.pool = Executors.newScheduledThreadPool(numberOfThreads); // 1 thread per product

    }

    public synchronized void enqueueItem(QuotaItem item) {
        try {
            quotaQueue.add(item);
        } catch (Exception e) {
            Log.error("An exception occurred during enqueueItem!", e);
            CompletableFuture.runAsync(() -> {
                QUOTA_QUEUE_FAILURE.labels("failure").inc();
            });
        }
    }

    public int getQueueSize() {
        if (!quotaQueue.isEmpty()) {
            return quotaQueue.size();
        } else {
            return 0;
        }
    }

    public void init() {
        Log.info(">>>> Starting AsyncQuotaUpdatesExecuter tasks...");

        try {
            // get persisted data before we start tasks
            // dir: /home/<USER>/quota/quotaQueue
            String queueDir = System.getProperty("user.home") + "/quota";
            String queueName = "quotaQueue";
            Log.info("persisted queue info, queueDir=" + queueDir + " queueName=" + queueName);
            persistedQuotaQueue = new BigQueueImpl(queueDir, queueName);
            if (!persistedQuotaQueue.isEmpty()) {
                Log.info("Found " + persistedQuotaQueue.size() + " persisted quota queue items!");

                while (!persistedQuotaQueue.isEmpty()) {
                    byte[] data = persistedQuotaQueue.dequeue();
                    QuotaItem item = QuotaItem.deSerializeObject(data);
                    VoiceContext vctx = item.getVoiceContext();

                    // only process the persisted items whose cdr event has arrived, aka only for completed call.
                    if (vctx.isCdrEventArrived())
                        this.enqueueItem(item);
                }

                // remove items from the disk
                // gc is specific to big queue it's NOT a java garbage collection
                // it will only remove persisted items which are already dequeued
                long startTime = System.currentTimeMillis();
                persistedQuotaQueue.gc();
                Log.info("Time taken for async gc: " + (System.currentTimeMillis() - startTime) + " milliseconds");
                if (Log.isDebugEnabled())
                    Log.debug("Time taken for async gc: " + (System.currentTimeMillis() - startTime) + " milliseconds");
            }
        } catch (Exception ex) {
            Log.error("Got Exception in init", ex);
            return;
        }

        for (UpdateTaskConfig config : this.config.getUpdateTaskConfigs()) {
            final VoiceApplicationType handledProduct = config.getVoiceApplicationType();
            final long sweepingPeriod = config.getSweepingPeriod();

            //scheduleAtFixedRate means that a task will start every second.
            this.pool.scheduleAtFixedRate(new QuotaQueueTask(quotaQueue, handledProduct),
                    0, // no initial delay
                    sweepingPeriod,
                    TimeUnit.MILLISECONDS);
            Log.info(" # Created [" + handledProduct + "] update task sweeping every " + sweepingPeriod + " ms");
        }

        this.pool.scheduleAtFixedRate(new QuotaQueueSyncTask(quotaQueue, persistedQuotaQueue), 6, 6, TimeUnit.SECONDS);

        Log.info("AsyncQuotaUpdatesExecuter initialization was successful. Tasks are now sweeping the voice context cache...");
    }

    //This method is called when we take down the SIPApp. Its goal is to stop the updater threads
    public void shutdown() {
        Log.info(">>>> Shutting down AsyncQuotaQueueExecuter...");

        try {
            this.pool.shutdown();
            boolean successfulShutdown = this.pool.awaitTermination(this.config.getShutdownPeriod(), TimeUnit.MILLISECONDS);
            if (successfulShutdown)
                Log.info("Shutdown all tasks successfully!");
            else
                Log.error("FAILED TO SHUTDOWN ALL TASKS SUCCESSFULLY!");

            if (persistedQuotaQueue != null) {
                persistedQuotaQueue.close();
            }

        } catch (InterruptedException ex) {
            Log.error("Something went terribly wrong!", ex);
        } catch (java.io.IOException ex) {
            Log.error("Got IO Exception!", ex);
        }

    }
}
