package com.nexmo.voice.core.sip.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeCreateEvent;


/**
 * Created on 01/10/23.
 *
 * <AUTHOR>
 */
public class BridgeCreateEventHandler extends AsteriskVoiceEventHandler<BridgeCreateEvent> {

    private static final Logger Log = LogManager.getLogger(BridgeCreateEventHandler.class);

    public BridgeCreateEventHandler() {
        super(BridgeCreateEvent.class);
    }

    @Override
    public void handle(BridgeCreateEvent event) throws VoiceEventHandlerException {
        Log.info("Processing BridgeCreate Event ['" + event + "'] hashCode=" + event.hashCode());
    }
}
