package com.nexmo.voice.core.sip.event.cdr;

import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;

public class Asterisk20CdrEventUserData extends Asterisk16CdrEventUserData {

    private static final Logger Log = LogManager.getLogger(Asterisk20CdrEventUserData.class.getName());

    public Asterisk20CdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        super(event, channelUniqueId);
    }
}
