package com.nexmo.voice.core.monitoring;

import java.util.HashMap;
import java.util.Map;

import com.thepeachbeetle.common.app.monitoring.AbstractMonitoringServlet;

import com.nexmo.voice.core.Core;

/**
 * <AUTHOR>
 */
public class MonitoringServlet extends AbstractMonitoringServlet {

    private static final long serialVersionUID = 3549080556266319357L;

    public MonitoringServlet() {
        super("VOICE", Core.getInstance().getConfig().getInstanceId(), commands());
    }

    private static Map<String, MonitoringCommand> commands() {
        Map<String, MonitoringCommand> map = new HashMap<>();
        map.put("gateway-metrics", new GatewayMetricsCommand(Core.getInstance()));
        map.put("call-metrics", new CallMetricsCommand(Core.getInstance()));
        map.put("cache-core", new CacheCoreCommand(Core.getInstance()));
        return map;
    }

    @Override
    public void getMetrics(final Map<String, String> metrics) {
        metrics.put("LVN-VERSION", Core.getInstance().getConfig()
                .getLVNMappingsSource()
                .getSource());
        metrics.put("SUPPLIERS-VERSION", Core.getInstance().getConfig()
                .getSIPGatewayInfoMatrixSource()
                .getSource());
        metrics.put("TTS-SUPPLIERS-VERSION", Core.getInstance().getConfig()
                .getTTSGatewayInfoMatrixSource()
                .getSource());
    }

}
