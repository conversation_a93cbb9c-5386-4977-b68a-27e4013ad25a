package com.nexmo.voice.core.types;

import java.io.Serializable;

/**
 * CallInternalFlag
 * 
 * This flag will present all kinds of internal call information which is for our internal usage and alerts, but is not part of the CDR public details.
 *  
 * <AUTHOR>
 *
 */
public enum CallInternalFlag implements Serializable {

    TERMINATED_ON_QUOTA_ISSUES("01"),   //The call was started and later terminated due to quota client issues
    TERMINATED_ON_OUT_OF_MONEY("02"),   //The call was started and later terminated due to customer-running-out-of-funds
    TERMINATED_ON_QUOTA_DISABLED("03"), //The call was started and later terminated due to the account having the "quota disabled" feature

    
    TERMINATED_ON_ACCOUNT_ISSUES("04"), //The call was started and later terminated due to account service issues
    TERMINATED_ON_BANNED_ACCOUNT("05"), //The call was started and later terminated due to banned account
    PAUSED_ON_QUOTA_ISSUES("06"),   //The call was let to continue and cdr processing paused due to quota client issues

    
    
    BLOCKED_ON_QUOTA_ISSUES("11"),      //The call blocked and didnt start due to quota client issues.
    BLOCKED_ON_NO_MIN_BALANCE("12"),    //The call blocked and didnt start due to customer do not have enough balance to start the call
    BLOCKED_ON_QUOTA_DISABLED("13"),    //The call blocked and didnt start due to the account having the "quota disabled" feature. This is theoretical situation.
    
    BLOCKED_ON_ACCOUNT_ISSUES("14"),    //The call blocked and didnt start due to account service issues
    BLOCKED_ON_BANNED_ACCOUNT("15"),    //The call blocked and didnt start due to banned account
    
    ACCOUNT_HAS_QUOTA_DISABLED("23"),   //The call will start with forcedPrice zero, as the account has the "quota disabled" feature
    QUOTA_ISSUES_ON_CALL_FINAL_BILLING("33"),  //The call has ended, there were problems during the final consume/refund

    AUTHENTICATED_BY_DOMAIN_ACL("40"), // SIP call authenticated by domain ACL
    AUTHENTICATED_BY_DOMAIN_USER("41"), // SIP call authenticated by domain digest user
    AUTHENTICATED_BY_DOMAIN_TRANSITION("42"), // legacy SIP call authenticated by domain transition
    AUTHENTICATION_DOMAIN_TRANSITION_SERVICE_FALLBACK("43"), // legacy SIP call domain transition fallback due to service error
    AUTHENTICATION_DOMAIN_TRANSITION_AUTH_FALLBACK("44"), // legacy SIP call domain transition fallback due to authentication failure
    AUTHENTICATED_BY_APIKEY("45"), // legacy SIP authentication by apikey
    AUTHENTICATED_BY_SOURCE_IP("46"), // legacy SIP authentication by source IP

    BLOCKED_ON_CALLID_ENFORCER("90"), // the call was blocked due Enforcer API callblocking rules
    BLOCKED_ON_CALLID_UNKNOWN("91"), // the call was blocked due to unknown callerId value
    BLOCKED_ON_CALLID_TO_SG_SPOOF("92"),    //The call blocked and didnt start due to SG prefix matching in call spoof list
    TERMINATED_DURING_SHUTDOWN("93"), // The call is terminated during shutdown
    BLOCKED_DURING_SHUTDOWN("94"), // The call is blocked before starting while shutdown is in progress
    BLOCKED_ON_INVALID_PAYMENT_SCENARIO("95"), //The call blocked and didnt start due to invalid Payments scenario
    BLOCKED_ON_INVALID_DATA("96"),     //The call blocked and didnt start due to some invalid data in the request
    BLOCKED_ON_DROPPING_ROUTE("97"),    //The call blocked and didnt start due to routing definition to drop the call
    TERMINATED_ON_DIRTY_SHUTDOWN("98"), //The call was terminated as part of dirty shutdown: unplanned disconnect from Asterisk
    BLOCKED_ON_INTERNAL_ERROR("99");    //The call blocked and didnt start due to internal errors

    private final String flagValue;

    private CallInternalFlag(final String flagValue) {
        this.flagValue = flagValue;
    }

    public String getFlagValue() {
        return this.flagValue;
    }

}
