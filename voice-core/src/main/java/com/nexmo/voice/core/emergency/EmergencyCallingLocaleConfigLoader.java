package com.nexmo.voice.core.emergency;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class EmergencyCallingLocaleConfigLoader extends NestedXmlHandler {

    public static final String ROOT_NODE = "locale";

    private String country;

    private Set<String> emergencyNumbers;

    private List<String> route;

    private EmergencyCallingLocaleConfig config;

    public EmergencyCallingLocaleConfigLoader(final String nodeName) {
        super(nodeName);
        this.config = null;
    }

    public EmergencyCallingLocaleConfig getConfig() {
        return this.config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) { // <locale>
            this.country = xmlContent.getAttribute(EmergencyCallingLocaleConfig.COUNTRY_ATTR, true);
            this.emergencyNumbers = parseCommaSeparatedStringSet(xmlContent.getAttribute(EmergencyCallingLocaleConfig.EMERGENCY_NUMBERS_ATTR, true));
            this.route = parseCommaSeparatedStringList(xmlContent.getAttribute(EmergencyCallingLocaleConfig.ROUTE_ATTR, false));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new EmergencyCallingLocaleConfig();
            config.setCountry(this.country);
            config.setEmergencyNumbers(this.emergencyNumbers);
            config.setRoute(this.route);
            notifyComplete();
        }
    }


    private List<String> parseCommaSeparatedStringList(String contentData) {
        if (contentData != null && !contentData.isEmpty()) {
            List<String> list = new LinkedList<>();
            String[] bits = contentData.split(",");
            String[] var4 = bits;
            int var5 = bits.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String str = var4[var6];
                list.add(str);
            }

            return list;
        } else {
            return null;
        }
    }
}
