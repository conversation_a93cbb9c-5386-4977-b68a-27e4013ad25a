package com.nexmo.voice.core.jmx;

import com.thepeachbeetle.common.app.jmx.AbstractDebugJMXMBean;

public interface DebugJMXMBean extends AbstractDebugJMXMBean {

    void dumpVoiceContextCache() throws Exception;

    String[] getVoiceContextCache();

    void dumpVoiceContextCache(String productClass) throws Exception;

    String[] getVoiceContextCache(String productClass);

    void dumpContextsByAccount(String accountId) throws Exception;

    void dumpContexByAccountToLog(String accountId) throws Exception;

    public String[] getAllAccountsRoutingRules() throws Exception;

    public String[] getAllPerAccountRoutingRules() throws Exception;

    public String[] getAllPerGroupRoutingRules() throws Exception;

    String getCallCountForAccount(String accountId) throws Exception;

    String[] getCallCountForActiveAccounts() throws Exception;

    public String[] getRandomPoolCache() throws Exception;

    public void flushRandomPoolCache() throws Exception;

    public String[] getTargetGroupCache() throws Exception;

}
