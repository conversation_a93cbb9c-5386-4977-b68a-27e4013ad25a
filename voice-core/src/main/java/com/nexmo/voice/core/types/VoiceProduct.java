package com.nexmo.voice.core.types;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public enum VoiceProduct implements Serializable {

    UNKNOWN("unknown"), //Not a good idea. yet not sure when it might happen
    CALL_API("api"),
    SIP("sip"),
    VBC("vbc"),
    VERIFY("verify"),
    VSPS("vsps"),
    TTS("tts"),
    DOMAIN("domain");

    private final String descriptor;

    private VoiceProduct(final String descriptor) {
        this.descriptor = descriptor;
    }

    public String getDescriptor() {
        return this.descriptor;
    }

    //SIP-309, TTSNG: we are going to use this enum allot for reverse lookup (fetch the enum from the provided
    //SIP_HEADER(P-Nexmo-Class) ) - that should be super efficient, so adding a reverse lookup method:

    //Lookup table populated on loading time
    private static final Map<String, VoiceProduct> reverseLookup = new HashMap<>();

    static {
        for (VoiceProduct voiceProduct : VoiceProduct.values()) {
            reverseLookup.put(voiceProduct.getDescriptor(), voiceProduct);
        }
        //Special "hack" - today, the Asterisk send "SIP_HEADER(P-Nexmo-Class)=vbc" for vbc calls.
        //We use the string value of this header "vbc" to control the process specifically for vbc, but
        //Still refer to the VoiceProduct as CALL_API
        reverseLookup.put(VoiceProduct.VBC.getDescriptor(), VoiceProduct.CALL_API);
        //also treat VSPS as CALL_API
        reverseLookup.put(VoiceProduct.VSPS.getDescriptor(), VoiceProduct.CALL_API);
        // also treat domain as CALL_API
        reverseLookup.put(VoiceProduct.DOMAIN.getDescriptor(), VoiceProduct.CALL_API);
    }

    public static VoiceProduct get(String descriptor) {
        return reverseLookup.get(descriptor);
    }

}
