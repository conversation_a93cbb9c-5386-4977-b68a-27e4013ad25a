package com.nexmo.voice.core.application;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.thepeachbeetle.messaging.hub.core.provisioning.client.ProvisioningApplicationsClient;
import com.thepeachbeetle.messaging.hub.core.provisioning.client.config.ProvisioningApiClientConfig;
import com.thepeachbeetle.messaging.hub.core.provisioning.exceptions.ProvisioningException;

public class ProvisioningClient {
    private final static Logger Log = LogManager.getLogger(ProvisioningClient.class);

    // voice[\s\S]+params[\s\S] ensures that the payment_enabled picked is under the voice->params json object
    private static final Pattern PAYMENT_ENABLED_PATTERN =
            Pattern.compile("voice[\\s\\S]+params[\\s\\S]+\"payment_enabled\"[\\s]?:[\\s]?(true|false)");

    private final ProvisioningApplicationsClient provisioningApplicationsClient;

    public ProvisioningClient(ProvisioningApiClientConfig pconfig) {
        this.provisioningApplicationsClient = new ProvisioningApplicationsClient(pconfig);
    }

    public Application getApplication(String applicationId) throws ApplicationLookupException {
        com.thepeachbeetle.core.apps.config.Application application;
        try {
            application = provisioningApplicationsClient.getApplication(applicationId);
        } catch (ProvisioningException ex) {
            Log.error("Failed to retrieve application " + applicationId + " from PHUB", ex);
            throw new ApplicationLookupException("Failed to retrieve application from PHUB", ex);
        }

        // A null response means that the application doesn't exist
        if (application == null) {
            Log.info("Application {} not found - deleted", applicationId);
            return null;
        }

        // ... but if it does exist, let's convert it to our Application type
        Boolean paymentEnabled = null;
        final String extensions = application.getApplicationExtensionsAsJSONString();
        if (extensions != null) {
            Matcher matcher = PAYMENT_ENABLED_PATTERN.matcher(extensions);
            if (matcher.find()) {
                paymentEnabled = Boolean.valueOf(matcher.group(1));
            }
        }
        return new Application(application.getId(), application.getName(), application.getApiKey(), paymentEnabled);
    }

}
