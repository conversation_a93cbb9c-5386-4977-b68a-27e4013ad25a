package com.nexmo.voice.core.types;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

import com.thepeachbeetle.messaging.hub.config.pricing.Price;

public class EffectiveAmount implements Serializable {
    private final USAGE usage; // Sub-type
    private final BigDecimal amount; // The actual amount

    // These fields are copied from the pricing rule (if applicable) as metadata
    private final long timestamp; // Last-modified time of pricing rule
    private final String destinationPrefix;
    private final String prefixGroup;
    private final String sourcePrefix;

    //The usage of this price 
    // consider if cost management really need such object
    public enum USAGE {
        COST, PRICE
    }

    public EffectiveAmount(final USAGE usage, final BigDecimal amount, final String destinationPrefix) {
        this(usage, -1L, amount, destinationPrefix, null, null);
    }

    public EffectiveAmount(final USAGE usage, final BigDecimal amount, final String destinationPrefix, final String prefixGroup) {
        this(usage, -1L, amount, destinationPrefix, prefixGroup, null);
    }

    public EffectiveAmount(final USAGE usage, final long timestamp, final BigDecimal amount, final String destinationPrefix, final String prefixGroup, final String sourcePrefix) {
        this.amount = amount;
        this.timestamp = timestamp;
        this.destinationPrefix = destinationPrefix;
        this.prefixGroup = prefixGroup;
        this.sourcePrefix = sourcePrefix;
        this.usage = usage;
    }

    protected BigDecimal getAmount() {
        return this.amount;
    }

    public String getPrefix() {
        return this.destinationPrefix;
    }

    public String getPrefixGroup() {
        return this.prefixGroup;
    }

    public String getSenderPrefix() {
        return this.sourcePrefix;
    }

    public long getTimestamp() {
        return this.timestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Usage ");
        sb.append(usage.name());
        sb.append(". amount=");
        String amountStr = Objects.isNull(amount) ? "null" : amount.toPlainString();
        sb.append(amountStr);
        sb.append(", prefix=");
        sb.append(destinationPrefix);
        if (prefixGroup != null) {
            sb.append(" (");
            sb.append(prefixGroup);
            sb.append(")");
        }
        if (sourcePrefix != null) {
            sb.append(", sender-prefix=");
            sb.append(sourcePrefix);
        }
        if (timestamp > 0L) {
            sb.append(", timestamp=");
            sb.append(timestamp);
        }
        return sb.toString();
    }


    //
    // Helper methods
    //
    public static BigDecimal scale(final BigDecimal amount) {
        if (amount == null)
            throw new IllegalArgumentException("Cannot scale a null amount");
        return amount.setScale(8, RoundingMode.HALF_UP);
    }

    protected static long timestampFromDate(final Date date) {
        if (date == null)
            return -1L;
        return date.getTime();
    }

    protected static long timestampFromPricingRule(final Price price) {
        if (price == null)
            throw new IllegalArgumentException("No pricing rule");

        Date date = price.getTimeLastModified();
        if (date == null)
            date = price.getDateCreated();
        return timestampFromDate(date);
    }

}
