package com.nexmo.voice.core.sip.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeLeaveEvent;


/**
 * Created on 01/10/23.
 *
 * <AUTHOR>
 */
public class BridgeLeaveEventHandler extends AsteriskVoiceEventHandler<BridgeLeaveEvent> {

    private static final Logger Log = LogManager.getLogger(BridgeLeaveEventHandler.class);

    public BridgeLeaveEventHandler() {
        super(BridgeLeaveEvent.class);
    }

    @Override
    public void handle(BridgeLeaveEvent event) throws VoiceEventHandlerException {
        Log.info("Processing BridgeLeave Event ['" + event + "'] hashCode=" + event.hashCode());
    }
}
