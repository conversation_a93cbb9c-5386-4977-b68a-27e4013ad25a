package com.nexmo.voice.core.callblocking;

import java.util.List;

public class CallblockingClientJson {

    private String apiKey;
    private String product;
    private String from;
    private String to;
    private List<String> callingServicePassthroughIds;
    private String fullMessage;

    public CallblockingClientJson(String apiKey, String product, String from, String to, List<String> domainIds) {
        this.apiKey = apiKey;
        this.product = product;
        this.from = from;
        this.to = to;
        this.callingServicePassthroughIds = domainIds;
        this.fullMessage = null;
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getProduct() {
        return product;
    }

    public String getFrom() {
        return from;
    }

    public String getTo() {
        return to;
    }

    public List<String> getCallingServicePassthroughIds() {
        return callingServicePassthroughIds;
    }

    public String getFullMessage() {
        return fullMessage;
    }
}
