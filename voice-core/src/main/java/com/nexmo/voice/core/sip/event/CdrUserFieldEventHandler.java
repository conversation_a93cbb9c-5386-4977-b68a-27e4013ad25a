package com.nexmo.voice.core.sip.event;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.sip.event.user.CdrUserFieldEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;


/**
 * Created on 05/23/23.
 *
 * <AUTHOR>
 */
public class CdrUserFieldEventHandler extends AsteriskVoiceEventHandler<CdrUserFieldEvent> {

    private static final Logger Log = LogManager.getLogger(CdrUserFieldEventHandler.class);

    public CdrUserFieldEventHandler() {
        super(CdrUserFieldEvent.class);
    }

    @Override
    public void handle(CdrUserFieldEvent event) throws VoiceEventHandlerException {
        if (Log.isDebugEnabled()) {
            Log.debug("Processing CdrUserFieldEvent ['" + event + "'] channel=" + event.getDestinationChannel() + "; linkID=" + event.getUniqueId() + "; uniqueID=" + event.getUniqueId());
        }

        VoiceContext voiceContext = Core.getInstance().getVoiceContextCache().getFirstContextWithConnectionId(event.getUniqueId());
        if (!Objects.nonNull(voiceContext)) {
            if (Log.isDebugEnabled()) {
                Log.debug("CdrUserFieldEvent not in VoiceContext Cache --- IGNORING hangupcause=" + event.getHangupCause() + " for uniqueID=" + event.getUniqueId());
            }

            return;
        }

        // Add to cdr userfield event cache cache
        Core.getInstance().getCdrUserFieldEventCache().addEvent(event.getUniqueId(), event.getDestinationChannel(), event);

        // Search for partial CDR event
        if (Core.getInstance().getPartialCdrEventCache().containsEvent(event.getUniqueId(), event.getDestinationChannel())) {
            if (Log.isDebugEnabled()) {
                Log.debug("Found a partial CDR for Link ID: " + event.getUniqueId() + "; channel ID: " + event.getDestinationChannel());
            }

            Core.getInstance().getPartialCdrEventCache().processPartialCdr(event.getUniqueId(), event.getDestinationChannel());
        }

        return;
    }
}