package com.nexmo.voice.core.aws.secretsmanager;

import com.thepeachbeetle.hlr.core.exceptions.ConfigException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerException;

public class SecretManagerUtils {

    private static final Logger Log = LogManager.getLogger(SecretManagerUtils.class);

    public static String getSecretValue(String secretName) throws ConfigException {
        try (SecretsManagerClient secretsManagerClient = SecretsManagerClient.create()) {
            GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                    .secretId(secretName)
                    .build();

            GetSecretValueResponse getSecretValueResponse = secretsManagerClient.getSecretValue(getSecretValueRequest);
            return getSecretValueResponse.secretString();
        } catch (SecretsManagerException e) {
            if (e.awsErrorDetails() != null) {
                Log.error("AWS Error Code: {}", e.awsErrorDetails().errorCode());
                Log.error("AWS Error Message: {}", e.awsErrorDetails().errorMessage());
            }
            throw new ConfigException("Failed to retrieve secret value", e);
        }
    }
}
