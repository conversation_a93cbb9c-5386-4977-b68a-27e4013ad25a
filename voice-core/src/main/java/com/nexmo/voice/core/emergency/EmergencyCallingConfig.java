package com.nexmo.voice.core.emergency;

/*
 * <emergency-calling enabled="true">
 *
 * </emergency-calling>
 */

import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import org.jdom.Element;

import java.io.Serializable;
import java.util.*;

public class EmergencyCallingConfig implements Serializable {

    public static final String ROOT_NODE = "emergency-calling";
    public static final String ALLOW_FROM_BANNED_ACCT_ATTR = "allow-banned-account";
    public static final String SKIP_QUOTA_ATTR = "skip-quota";
    public static final String SKIP_PARENT_ACCOUNT_LOOKUP_ATTR = "skip-parent-account-lookup";

    public static final String REQUIRE_CAPABILITY_LOCALE_TAG_ATTR = "require-account-capability-locale";
    public static final String ALLOW_FROM_BYON_ATTR = "allow-from-verified-cli";

    public static final boolean DEFAULT_ALLOW_FROM_BANNED_ACCOUNT_VALUE = true;
    public static final boolean DEFAULT_SKIP_QUOTA_VALUE = true;
    public static final boolean DEFAULT_SKIP_PARENT_ACCOUNT_LOOKUP_VALUE = true;

    public static final boolean DEFAULT_REQUIRE_CAPABILITY_LOCALE_TAG = true;
    public static final boolean DEFAULT_ALLOW_FROM_BYON = false;

    public static final ShortCodeType[] BASE_ALLOWED_LVN_TYPES = {ShortCodeType.LANDLINE, ShortCodeType.LANDLINE_PREMIUM, ShortCodeType.MOBILE_LVN};


    private boolean allowFromBannedAccount;

    private boolean skipQuota;

    private boolean skipParentAccountLookup;

    private boolean requireCapabilityLocale;

    private boolean allowFromByon;

    private Set<String> emergencyServiceNumbers;

    private Map<String, Set<String>> localeEmergencyServiceNumbers;

    private Map<String, Map<String, List<String>>> localeEmergencyServiceRouting;

    private Set<EmergencyCallingLocaleConfig> localeConfig;


    public EmergencyCallingConfig() {
        emergencyServiceNumbers = Collections.emptySet();
        localeEmergencyServiceNumbers = Collections.emptyMap();
        localeEmergencyServiceRouting = Collections.emptyMap();
        localeConfig = Collections.emptySet();
    }

    public boolean isAllowFromBannedAccount() {
        return allowFromBannedAccount;
    }

    public void setAllowFromBannedAccount(boolean allowFromBannedAccount) {
        this.allowFromBannedAccount = allowFromBannedAccount;
    }

    public boolean isSkipQuota() {
        return skipQuota;
    }

    public void setSkipQuota(boolean skipQuota) {
        this.skipQuota = skipQuota;
    }

    public boolean isSkipParentAccountLookup() {
        return skipParentAccountLookup;
    }

    public void setSkipParentAccountLookup(boolean skipParentAccountLookup) {
        this.skipParentAccountLookup = skipParentAccountLookup;
    }

    public void setLocaleConfig(Set<EmergencyCallingLocaleConfig> localeConfigs) {
        // if localeConfigs is null or empty, should we throw an exception here?
        this.localeConfig = localeConfigs;

        if(this.localeConfig != null) {
            // if we have locale config, fill in the set and map of emergency numbers
            Set<String> allEmergencyNumbers = new HashSet<>();
            Map<String, Set<String>> localeEmergencyNumbers = new HashMap<>();
            Map<String, Map<String, List<String>>> localeEmergencyRoutes = new HashMap<>();
            for(EmergencyCallingLocaleConfig config : this.localeConfig) {
                String locale = config.getCountry().toUpperCase();
                allEmergencyNumbers.addAll(config.getEmergencyNumbers());
                if(!localeEmergencyNumbers.containsKey(locale)) {
                    localeEmergencyNumbers.put(locale, new HashSet<>());
                }
                localeEmergencyNumbers.get(locale).addAll(config.getEmergencyNumbers());
                if(!localeEmergencyRoutes.containsKey(locale)) {
                    localeEmergencyRoutes.put(locale, new HashMap<>());
                }
                for(String localeNumber : config.getEmergencyNumbers()) {
                    localeEmergencyRoutes.get(locale).put(localeNumber, config.getRoute());
                }
            }

            // now we can set these fields
            this.emergencyServiceNumbers = allEmergencyNumbers;
            this.localeEmergencyServiceNumbers = localeEmergencyNumbers;
            this.localeEmergencyServiceRouting = localeEmergencyRoutes;
        }
    }

    public Element toXML() {
        Element config = new Element(ROOT_NODE);
        config.setAttribute(ALLOW_FROM_BANNED_ACCT_ATTR, Boolean.toString(this.allowFromBannedAccount));
        config.setAttribute(SKIP_QUOTA_ATTR, Boolean.toString(this.skipQuota));
        config.setAttribute(SKIP_PARENT_ACCOUNT_LOOKUP_ATTR, Boolean.toString(this.skipParentAccountLookup));
        config.setAttribute(REQUIRE_CAPABILITY_LOCALE_TAG_ATTR, Boolean.toString(this.requireCapabilityLocale));
        config.setAttribute(ALLOW_FROM_BYON_ATTR, Boolean.toString(this.allowFromByon));
        for (EmergencyCallingLocaleConfig locale : this.localeConfig)
            config.addContent(locale.toXML());

        return config;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("EmergencyCalling [");
        sb.append("allowFromBannedAccount=").append(this.allowFromBannedAccount);
        sb.append("; ").append("skipQuota=").append(this.skipQuota);
        sb.append("; ").append("skipParentAccountLookup=").append(this.skipParentAccountLookup);
        sb.append("; ").append("requireCapabilityLocale=").append(this.requireCapabilityLocale);
        sb.append("; ").append("allowFromByon=").append(this.allowFromByon);
        String emergencyNumbers = "";
        if(this.emergencyServiceNumbers != null) {
            emergencyNumbers = String.join(",", this.emergencyServiceNumbers);
        }
        sb.append("; ").append("emergencyServiceNumbers=").append(emergencyNumbers);
        StringBuilder localeEmergencyNumbers = new StringBuilder();
        if(this.localeEmergencyServiceNumbers != null) {
            for(String locale : this.localeEmergencyServiceNumbers.keySet()) {
                if(localeEmergencyNumbers.length() > 0) {
                    localeEmergencyNumbers.append(",");
                }
                localeEmergencyNumbers.append(locale)
                        .append(":")
                        .append(String.join(",", this.localeEmergencyServiceNumbers.get(locale)));
            }
        }
        sb.append("; ").append("localeEmergencyServiceNumbers=").append(localeEmergencyNumbers);
        StringBuilder localeEmergencyRoute = new StringBuilder();
        if(this.localeEmergencyServiceRouting != null) {
            for(String locale : this.localeEmergencyServiceRouting.keySet()) {
                if(localeEmergencyRoute.length() > 0) {
                    localeEmergencyRoute.append(",");
                }
                localeEmergencyRoute.append(locale).append(":");
                for(String number: this.localeEmergencyServiceRouting.get(locale).keySet()) {
                    localeEmergencyRoute.append(number)
                            .append(":[")
                            .append(String.join(",", this.localeEmergencyServiceRouting.get(locale).get(number)))
                            .append("]");
                }
            }
        }
        sb.append("; ").append("localeEmergencyServiceRouting=").append(localeEmergencyRoute);
        sb.append("]");
        return sb.toString();

    }

    public boolean isEmergencyServiceNumber(String number){
        if(this.emergencyServiceNumbers != null) {
            return this.emergencyServiceNumbers.contains(number);
        }
        return false;
    }

    public boolean isEmergencyServiceNumberForLocale(String locale, String number) {
        if((this.localeEmergencyServiceNumbers != null) &&
                this.localeEmergencyServiceNumbers.containsKey(locale)){
            return this.localeEmergencyServiceNumbers.get(locale).contains(number);
        }
        return false;
    }

    public List<String> getEmergencyServiceRoutingForLocale(String locale, String number) throws EmergencyCallingException {
        if((this.localeEmergencyServiceRouting != null)
                && this.localeEmergencyServiceRouting.containsKey(locale)
                && this.localeEmergencyServiceRouting.get(locale).containsKey(number)) {
            return this.localeEmergencyServiceRouting.get(locale).get(number);
        }
        throw new EmergencyCallingException("No routing for locale="+locale+", number="+number);
    }

    public boolean isRequireCapabilityLocale() {
        return requireCapabilityLocale;
    }

    public void setRequireCapabilityLocale(boolean requireCapabilityLocale) {
        this.requireCapabilityLocale = requireCapabilityLocale;
    }

    public boolean isAllowFromByon() {
        return allowFromByon;
    }

    public void setAllowFromByon(boolean allowFromByon) {
        this.allowFromByon = allowFromByon;
    }

    public boolean isAllowedLvnType(ShortCodeType type) {
        if(isAllowFromByon() && ShortCodeType.VERIFIED_CLI.equals(type)) {
            return true;
        }
        return Arrays.asList(BASE_ALLOWED_LVN_TYPES).contains(type);
    }
}
