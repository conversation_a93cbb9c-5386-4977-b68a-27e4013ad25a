package com.nexmo.voice.core.sip.event;

import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.sip.event.cdr.Asterisk16CdrEventUserData;
import com.nexmo.voice.core.sip.event.cdr.Asterisk20CdrEventUserData;
import com.nexmo.voice.core.sip.event.cdr.LegacyCdrEventUserData;
import com.nexmo.voice.core.types.AsteriskVersion;
import com.nexmo.voice.core.types.CarrierPlatform;
import com.nexmo.voice.core.types.SIPCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;

public interface CdrEventUserData {

    Logger Log = LogManager.getLogger(CdrEventUserData.class.getName());

    // UserField params:
    String GATEWAYS_LIST = "GWS"; // only for OUTBAOUND calls
    String CURRENT_GATEWAY = "GW"; // only for OUTBAOUND calls
    String GATEWAY_FAIL_OVER_ATTEMPT = "ATTEMPT"; // only for OUTBAOUND calls
    String DIAL_STATUS = "DIALSTATUS";
    String HANGUP_CAUSE = "HANGUPCAUSE";
    String CHANNEL_2_ID = "LEG2ID";
    String SIP_TARGET_FALLBACK_ATTEMPT_NO = "FALLBACKATTEMPT"; // only for INBOUND calls
    String SIP_TARGET_FALLBACK_ALTERNATIVES = "FALLBACKALTERNATIVES"; // only for INBOUND calls
    String NEXMO_UUID = "NEXMOUUID"; // The call id, this is also called sessionId
    String TO_INPS = "TO_INPS";

    public static CdrEventUserData of(AsteriskVersion asteriskVersion, CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        switch (asteriskVersion) {
            case V20:
                return new Asterisk20CdrEventUserData(event, channelUniqueId);
            case V16:
                return new Asterisk16CdrEventUserData(event, channelUniqueId);
            case V1_8:
                return new LegacyCdrEventUserData(event, channelUniqueId);
            default:
                throw new VoiceEventHandlerException("Unknown AsteriskVersion " + asteriskVersion);
        }
    }

    public static long roundUpTime(long timeMillis) {
        if (timeMillis < 1) {
            return timeMillis;
        }
        //same as timeMillis / 1000 + (timeMillis % 1000 > 0 ? 1 : 0)
        return (timeMillis + 999) / 1000;
    }

    public static long toLong(String value, String channelUniqueId) {
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            Log.error("Parse error in ChannelId {}, cant parse {} due to {}", channelUniqueId, value, e);
        }
        return -1;
    }

    //this method is handling all kinds of edge cases where the event's HANGUPCASUE and the DIALSTATUS
    //need to be converted to a specific SIPCode and DIALSTATUS which meet business's requirements for
    //the CDRs' content
    SIPCode calculateCallSIPCodeAndDialStatus(String channelUniqueId, Integer billableSeconds, VoiceContext channelContext);

    long getReportedBillableSeconds();

    boolean isLastRetryGateway();

    boolean isLastSipDestination();

    HangupCause getHangupCause();

    String getDialStatus();

    String getChannel2Id();

    String getNexmoUUID();

    String getGatewaysList();

    String getCurrentGateway();

    String getGatewaysAttemptStr();

    int getCurrentGWAttempt();

    int getAvailableGWAttempts();

    String getFallbackAttemptStr();

    String getFallbackAlternativesStr();

    int getCurrentFallbackAttempt();

    int getAvailableFallbackAlternatives();

    CarrierPlatform getCarrierPlatform();

    long getAnsweredDuration();

    long getPddTimeMillis();

    long getAnsweredDurationMillis();

    boolean ignoreEvent();

    void updateDialStatus(String updatedDialStatus, String channelId);
}
