package com.nexmo.voice.core.sip.api;


import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.Map.Entry;
import java.util.EnumSet;
import java.util.concurrent.CompletableFuture;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.nexmo.voice.core.billing.vquota.VQuotaService;
import com.nexmo.voice.core.domains.DomainRoutingResponse;
import com.nexmo.voice.core.emergency.EmergencyCallingConfig;
import com.nexmo.voice.core.sip.AsteriskAGIServer;
import com.nexmo.voice.core.sip.AsteriskAGIServerSIPHandler;
import com.nexmo.voice.core.types.AccountSteeringCapability;
import com.thepeachbeetle.messaging.hub.core.quota.client.config.QuotaApiClientConfig;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;


import com.nexmo.voice.config.WhitelistedNumbersConfig;
import com.nexmo.voice.config.api.InternalApiConfig;
import com.nexmo.voice.config.mappings.GroupConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.billing.QuotaClient;
import com.nexmo.voice.core.types.ToNumberDetails;
import com.thepeachbeetle.common.msisdn.PhoneNumberTool;
import com.thepeachbeetle.common.msisdn.PhoneNumberTool.BadlyFormattedNumberException;
import com.thepeachbeetle.common.util.IPV4Util;
import com.thepeachbeetle.common.util.MD5Util;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodes;
import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.CallbackType;
import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.ShortCodeMetaData;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;
import com.thepeachbeetle.messaging.hub.core.exceptions.PermittedDestinationMisMatchException;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
//import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

public class InternalApiServlet extends HttpServlet {

    private static final long serialVersionUID = -1207131901978669361L;
    private static final Logger Log = LogManager.getLogger(InternalApiServlet.class);
    private final static String HOST_NAME = SipAppUtils.getHostName();
    private static final int asyncTimeout = 500;

    private static final Histogram INTERNAL_API_REQUESTS_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_internal_api_requests_latency").help("Time taken to complete internal api requests").labelNames("command", "status").register();
    private static final Histogram ERROR_INTERNAL_API_REQUESTS_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_internal_api_error_requests_latency").help("Time taken to complete failed internal api requests").labelNames("command").register();

    private static final Counter TO_NUMBER_DETAIL_TYPES = Counter.build()
            .name("sipapp_api_to_number_detail_types")
            .labelNames("category")
            .help("The category of to number determined by getToNumberDetails")
            .register();

    private static final Set<AccountSteeringCapability> TRAFFIC_STEERING_CAPABILITY = EnumSet.allOf(AccountSteeringCapability.class);

    private static final String DEFAULT_AUTH_REALM = "sip.nexmo.com";

    private static final String VOICE_FEATURE = "VOICE";

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        handleRequest(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        handleRequest(req, resp);
    }

    private static void handleRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        final long timer = System.nanoTime();
        String reqContent = dumpRequestDetails(request);

        String command = StringUtils.stripToNull(request.getParameter("cmd"));

        if (command == null) {
            throw new ServletException("No command was provided!");
        }

        switch (command) {
            case "get-route-for-number":
                getRouteForNumber(request, response, reqContent, timer, command);
                return;

            case "can-accept-request":
                canAcceptRequest(request, response, reqContent, timer, command);
                return;

            case "get-route-for-application":
                getRouteForApplication(request, response, reqContent, timer, command);
                return;

            case "get-route-for-sipcall":
                getRouteForSipCall(request, response, reqContent, timer, command);
                return;

            default:
                Log.warn("Unrecongnizable command: " + command + "  request: " + reqContent);
                respond(reqContent, response, errorResponse("The command is not recognized"), timer, command);
        }

    }

    /**
     * Input parameters in the request:
     * cmd: get-route-for-number
     * number: the number in question
     * <p>
     * Response json object includes:
     * status: ok
     * api_key: AccountId
     * has_capacity: true
     * forwards_to: "sip" / "app"
     * carrier: SupplierId
     * alternatives: alternatives - The number of sip destinations or 1.
     * forward_address: applicationId or sip address destination
     */
    private static void getRouteForNumber(HttpServletRequest request, HttpServletResponse response, String reqContent, long timer, String command) throws ServletException, IOException {


        String number = request.getParameter("number");

        if (Objects.isNull(StringUtils.stripToNull(number))) {
            respond(reqContent, response, errorResponse("Missing parameter: number"), timer, command);
            return;
        }

        number = formatNumber(number);
        if (Log.isDebugEnabled())
            Log.debug("Stripped requested number: " + number);

        ShortCodes shortCodes = Core.getInstance().getConfig().getShortCodes();

        if (shortCodes == null) {
            respond(reqContent, response, errorResponse("ShortCodes is not initialized!"), timer, command);
            return;
        }

        final long shortCodesDBtimer = System.nanoTime();
        ShortCode shortCode = null;
        try {
            shortCode = shortCodes.getFirstShortCodeMatchForNumber(number);
            if (Log.isDebugEnabled())
                Log.debug("number: " + number + " shortCode: " + shortCode);
        } catch (ShortCodeException ex) {
            Log.warn("Failed to retrieve shortcode for shortcode: " + number + " " + reqContent, ex.getMessage());
            Core.getInstance().incrementShortCodesDBErrorCounter(ex);
        }
        Core.getInstance().updateShortCodesDBLatencyMetrics((System.nanoTime() - shortCodesDBtimer)/ NANOSECONDS_PER_SECOND);

        if (shortCode == null) {
            Log.warn("Couldnt find shortcode for number: " + number + " about to return " + ProxyError.ERROR_NUMBER_NOT_OWNED);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_NUMBER_NOT_OWNED), timer, command);
            return;
        }

        if (shortCode.getShortCodeType() == ShortCodeType.VERIFIED_CLI) {
            Log.warn("Shortcode for number: " + number + " is of type verified cli, about to return " + ProxyError.ERROR_NUMBER_NOT_OWNED);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_NUMBER_NOT_OWNED), timer, command);
            return;
        }

        ShortCodeMetaData metadata = shortCode.getMetaData();
        if (Log.isDebugEnabled())
            Log.debug("number: " + number + " ShortCodeMetaData: " + metadata);

        if (!metadata.hasCallbackAddresses()) {

            Log.warn("CallbackAddresses in short-code-metadata is empty or null for shortcode : " + number + " " + reqContent +
                    " about to return " + ProxyError.ERROR_NOTHING_TO_FORWARD);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_NOTHING_TO_FORWARD), timer, command);
            return;
        }

        GroupConfig destinationGeos = Core.getInstance().getConfig().getLVNMappingsConfig().getDestinationGeos(number);

        String forwardsTo = "";
        int alternatives = 0;
        String forwardAddress = null;
        String siptrunk_region = null;

        if (hasCallbackType(metadata, CallbackType.TEL)) {
            forwardsTo = CallbackType.TEL.getType();
            forwardAddress = metadata.getCallbackAddress(CallbackType.TEL);
            alternatives = 1;
        } else if (hasCallbackType(metadata, CallbackType.SIP)) {
            forwardsTo = CallbackType.SIP.getType();
            String configuredSipDestinations = metadata.getCallbackAddress(CallbackType.SIP);
            String[] alternativeAddresses = configuredSipDestinations.split(",");
            if((alternativeAddresses.length == 1)
                    && (AsteriskAGIServerSIPHandler.checkIfPsipDomain(alternativeAddresses[0]) != null)) {
                // if this is a trunking domain, we need to get the endpoint URIs for it to find the number of alternatives
                String psipDomainName = AsteriskAGIServerSIPHandler.checkIfPsipDomain(alternativeAddresses[0]);
                // we have no sessionId?
                DomainRoutingResponse routingResponse = AsteriskAGIServerSIPHandler.getDomainRouting(psipDomainName, number, "");
                if(routingResponse.isSuccess()) {
                    // if success, we will get all of the necessary properties from the response
                    CallbackType finalRedirect = routingResponse.getForwardsTo();
                    if(CallbackType.SIP.equals(finalRedirect)) {
                        // if forwardsTo SIP
                        alternativeAddresses = routingResponse.getUrisWithoutScheme().toArray(new String[0]);
                        // if we do not set alternatives, we will reject the call
                        forwardAddress = takeFirstIpAddress(alternativeAddresses, reqContent);
                        alternatives = alternativeAddresses.length;
                        siptrunk_region = routingResponse.getSiptrunkRegion();
                        Log.info("getRouteForNumber {}, about to return domain sip destination details. Configured lvn forwarding to domain: {}, about to return first forwardAddress: {} alternatives : {} siptrunk_region: {}",
                                number, psipDomainName, forwardAddress, alternatives, siptrunk_region);
                    } else if(CallbackType.APPLICATION.equals(finalRedirect)) {
                        // if forwards to APPLICATION
                        // override the forwardsTo
                        forwardsTo = CallbackType.APPLICATION.getType();
                        // forwardAddress will be the application_id
                        forwardAddress = routingResponse.getUrisWithoutScheme().get(0);
                        // only one
                        alternatives = 1;
                        Log.info("getRouteForNumber {}, about to return domain application destination details. Configured lvn forwarding to domain: {}, about to return applicationId: {}",
                                number, psipDomainName, forwardAddress);
                    }
                }
            } else {
                forwardAddress = takeFirstIpAddress(alternativeAddresses, reqContent);
                alternatives = alternativeAddresses.length;
                Log.info("getRouteForNumber {}, about to return sip destination details. Configured lvn forwarding to: {}, about to return first forwardAddress: {} alternatives : {}",
                        number, configuredSipDestinations, forwardAddress, alternatives);
            }
        } else if (hasCallbackType(metadata, CallbackType.APPLICATION)) {
            forwardsTo = CallbackType.APPLICATION.getType();
            forwardAddress = metadata.getCallbackAddress(CallbackType.APPLICATION);
            alternatives = 1;
        }

        if (alternatives == 0) {
            Log.warn("No suitable CallbackAddresses for shortcode : " + number + " " + reqContent +
                    " about to return " + ProxyError.ERROR_NOTHING_TO_FORWARD);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_NOTHING_TO_FORWARD), timer, command);
            return;
        }

        String apiKey = shortCode.getAccountId();

        Accounts accounts = Accounts.getInstance();
        final SmppAccount account;
        try {
            account = accounts.getSmppAccount(apiKey);

            if (account.isBanned()) {
                Log.info("Account " + apiKey + " is banned, so we will not forward the call.." +
                        " The returned respons would be ProxyError.ERROR_NOTHING_TO_FORWARD in order not to give too much details. " +
                        reqContent);
                respond(reqContent, response, errorResponse(ProxyError.ERROR_NOTHING_TO_FORWARD), timer, command);
                return;
            }
        } catch (Exception ex) {
            Log.error("Failed to check ban status of account " + apiKey +
                    " " + reqContent +
                    " number " + number, ex.getMessage());
            respond(reqContent, response, errorResponse("Failed to check ban status of account"), timer, command);
            return;
        }

        if (Log.isDebugEnabled()) {
            Log.debug("getRouteForNumber: account: {}, number {}, forwardsTo {} forwardAddress {} alternatives {}",
                    account.getSysId(), number, forwardsTo, forwardAddress, alternatives);
        }

        //SIP-1168: If the LVN is configured to direct the call to PSTN, we need to verify this PSTN is 
        // whitelisted or in the account's permitted destination list
        if (CallbackType.TEL.getType().equals(forwardsTo)) {

            WhitelistedNumbersConfig whitelistConfig = Core.getInstance().getConfig().getWhitelistedNumbersConfig();
            if (whitelistConfig != null && whitelistConfig.isEnabled() && whitelistConfig.isWhitelisted(forwardAddress)) {
                if (Log.isDebugEnabled())
                    Log.debug("Allowing whitelisted number " + forwardAddress + " for " + apiKey);
            } else {
                JSONObject errorResponse = checkValidityAndPermittedDestinations(forwardAddress, account);
                if (errorResponse != null) {
                    Log.warn("An attempt of call by customer {} to {} which is not on their permitted destinations list. Call is rejected ",
                            account.getSysId(), forwardAddress);
                    respond(reqContent, response, errorResponse, timer, command);
                    return;
                }
            }
        }

        if (!AsteriskAGIServer.isVoiceSkipQuota(account) && !hasMinimumBalance(reqContent, response, apiKey, timer, command, account))
            return;

        boolean hasCapacity = true;

        //The below was changed to return always true, as we had issues with the capacity 
        //calculation, so just take it out for now.
        /**
         if (!Core.getInstance().getCallThrottler().isThrottled(apiKey, account.getMaxMtPerSecond())) {

             Log.warn("Failed to accept the request for accountId: " + apiKey +
             " , exceed max number " + account.getMaxMtPerSecond() + " of calls sent. request "+
             reqContent);

             hasCapacity = false;
         }
         else {
             try {
                 //At the moment, this always return true, as <check-concurrent-capacity enabled="false>
                 //hasCapacity = checkCapacity(apiKey); //this is just taking time to query the DB and eventually always return true.
                 hasCapacity = true;

             } catch (Exception ex) {
                 Log.error("Failed to check capacity status of account " + apiKey + " " + reqContent , ex.getMessage());
                 respond(reqContent, response, errorResponse(ex.getMessage()));
                 return;
             }
         } **/

        JSONObject jsonResponse = new JSONObject();
        try {
            jsonResponse.put("status", "ok");
            jsonResponse.put("api_key", shortCode.getAccountId());
            jsonResponse.put("has_capacity", hasCapacity);
            if (hasCapacity) {
                jsonResponse.put("forwards_to", forwardsTo);
                jsonResponse.put("carrier", shortCode.getSupplierId());
                jsonResponse.put("alternatives", alternatives);
                if (StringUtils.isNotBlank(forwardAddress))
                    jsonResponse.put("forward_address", forwardAddress);
                if (destinationGeos != null)
                    addDestinationGeosToResponse(jsonResponse, destinationGeos);
                if (hasCallbackType(metadata, CallbackType.APPLICATION)) {
                    addApplicationParamsToResponse(jsonResponse, metadata.getCallbackAddress(CallbackType.APPLICATION));
                }
            }
            jsonResponse.put("host_name", HOST_NAME);
            jsonResponse.put("siptrunk_region", siptrunk_region);
            addTrafficSteeringAccountCapability(account, jsonResponse);
        } catch (JSONException ex) {
            Log.error("Failed to create json response for " + reqContent, ex.getMessage());
            throw new ServletException("Failed to create json response", ex);
        }

        respond(reqContent, response, jsonResponse, timer, command);
    }

    private static void addDestinationGeosToResponse(JSONObject jsonResponse, GroupConfig groupConfig) {
        if (groupConfig == null)
            return;

        final List<String> destinationGeos = groupConfig.getGeos();
        JSONArray geos = new JSONArray();
        if (destinationGeos != null) {
            for (String geoName : destinationGeos) {
                geos.put(geoName);
            }
        }
        jsonResponse.put("permitted_regions", geos);
        jsonResponse.put("strict_region", groupConfig.isStrict());
    }


    // SIP-1657: Kamailio need this info in order to know to which DC to route the call
    //
    // supported sip addresses:
    // sip:<EMAIL>
    // sip:<EMAIL>:5062
    //
    // and optional prefixes : either both or one of them, in any order
    // [sip address];timeout=xxxxx
    // [sip address];transport=tls
    //
    // we are just interested in the ip of the requested server (i.e. example.com)
    public static String takeFirstIpAddress(String[] alternativeAddresses, String reqContent) {
        String ipOfAddress = "";

        if (alternativeAddresses.length > 0) {
            String address = extractServer(alternativeAddresses, reqContent);

            if (Objects.isNull(address) || address.isEmpty())
                return ipOfAddress;

            if (IPV4Util.isIpAddressValid(address))
                return address;

            try {
                ipOfAddress = InetAddress.getByName(address).getHostAddress();
            } catch (UnknownHostException e) {
                Log.error("Failed to talk to the dns provider. so cannot resovle {} for {} due to {}", address, reqContent, e);
            }
        }

        return ipOfAddress;
    }

    protected static String extractServer(String[] alternativeAddresses, String reqContent) {
        String address = alternativeAddresses[0];

        String[] splitAddress = address.split("@");

        if (splitAddress.length < 2) {
            Log.error("Request: {} the first configured SIP address of this number does not include @: {}  - is it really a sip destination? will return empty string.",
                    reqContent, address);
            return "";
        }

        address = splitAddress[1];
        // At this point address will refer to the first sip destination on the list, and will include either of:
        // example.com
        // example.com:5062
        // or one of the above with the additional suffix ;timeout=xxxxx ;transport=tls or both
        address = address.split(":")[0];
        // At this point the address will be either:
        // example.com
        // example.com;[suffix - one or more]
        address = address.split(";")[0];
        // At this point the address will be
        // example.com
        return address;
    }


    /**
     * Input parameters in the request:
     * cmd: get-route-for-application
     * application_id: the requested application Id (this is used by VBC incoming calls)
     * <p>
     * Response json object includes:
     * status: ok
     * api_key: AccountId
     */
    private static void getRouteForApplication(HttpServletRequest request, HttpServletResponse response,
                                               String reqContent, long timer, String command) throws ServletException, IOException {

        String appId = request.getParameter("application_id");

        if (Objects.isNull(StringUtils.stripToNull(appId))) {
            respond(reqContent, response, errorResponse("Missing parameter: application_id"), timer, command);
            return;
        }

        appId = appId.trim();

        if (!Core.getInstance().isInitialised()) {
            respond(reqContent, response, errorResponse("ProvisioningApplicationsClient is not initialized!"), timer, command);
            return;
        }

        Application application;
        try {
            long reqTime = System.currentTimeMillis();
            application = Core.getInstance().getApplication(appId);
            Log.info("Provisioning getApplication {} took {} ms", appId, System.currentTimeMillis() - reqTime);
        } catch (Exception e) {
            Log.warn("Failed to get the application details of appId {} due to {}", appId, e.getMessage());
            respond(reqContent, response, errorResponse("Failed to get the application details of application_id"), timer, command);
            return;
        }

        if (Objects.isNull(application)) {
            Log.warn("Application of appid {} not found. about to return {}", appId, ProxyError.ERROR_APPLICATION_NOT_FOUND);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_APPLICATION_NOT_FOUND), timer, command);
            return;
        }

        String apiKey = application.getApiKey();

        Accounts accounts = Accounts.getInstance();
        final SmppAccount account;
        try {
            account = accounts.getSmppAccount(apiKey);

            if (account.isBanned()) {
                Log.info("Account " + apiKey + " is banned, so we will not forward the call..  " + reqContent);
                respond(reqContent, response, errorResponse(ProxyError.ERROR_ACCOUNT_IS_BANNED), timer, command);
                return;
            }
        } catch (Exception ex) {
            Log.error(
                    "Failed to check ban status of account " + apiKey + " " + reqContent + " appId " + appId,
                    ex.getMessage());
            respond(reqContent, response, errorResponse("Failed to check ban status of account"), timer, command);
            return;
        }

        if (!AsteriskAGIServer.isVoiceSkipQuota(account) && !hasMinimumBalance(reqContent, response, apiKey, timer, command, account))
            return;

        JSONObject jsonResponse = new JSONObject();
        try {
            jsonResponse.put("status", "ok");
            jsonResponse.put("api_key", apiKey);
            if (application.getPaymentEnabled() != null) {
                boolean payment = application.getPaymentEnabled();
                jsonResponse.put("payment_enabled", payment);
            }
            if (application.getRegion() != null) {
                String region = application.getRegion();
                jsonResponse.put("region", region);
            }
            jsonResponse.put("host_name", HOST_NAME);
            addTrafficSteeringAccountCapability(account, jsonResponse);
        } catch (JSONException ex) {
            Log.error("Failed to create json response for " + reqContent, ex.getMessage());
            throw new ServletException("Failed to create json response", ex);
        }

        respond(reqContent, response, jsonResponse, timer, command);
    }


    /**
     * Input parameters in the request:
     * cmd: can-accept-request
     * api_key: the customer account Id
     * <p>
     * For AUTHENTICATION ONLY there are no more parameters.
     * For AUTHENTICATION and AUTHORISATION (prior to sip-dial-in) additional parameters are required:
     * to: the destination number
     * whitelisted: true / false.   True means no need for the MD5
     *                              False means md5 is required in the response
     * <p>
     * AUTHENTICATION ONLY  response:
     * status: ok
     * api_key: AccountId
     * md5: The account's MD5
     * <p>
     * AUTHENTICATION and AUTHORISATION response:
     * status: ok
     * api_key: AccountId
     * has_capacity: true
     * max_cps: the max cps for this account
     * md5: The account's MD5  (optional)
     */
    private static void canAcceptRequest(HttpServletRequest request, HttpServletResponse response, String reqContent, long timer, String command)
            throws IOException, ServletException {

        String apiKey = request.getParameter("api_key");
        if (Objects.isNull(StringUtils.stripToNull(apiKey))) {
            respond(reqContent, response, errorResponse("Missing parameter: api_key"), timer, command);
            return;
        }

        apiKey = apiKey.trim();

        Accounts accounts = Accounts.getInstance();
        SmppAccount smppAccount = null;
        try {
            long start = System.currentTimeMillis();

            smppAccount = accounts.getSmppAccount(apiKey);

            if (Log.isTraceEnabled())
                Log.trace("TIMED ::: (canAcceptRequest) fetching account " + apiKey + " took (ms): " + (System.currentTimeMillis() - start));
        } catch (AccountsException ex) {
            Log.error("Failed to retrieve account apiKey: " + apiKey + " " + reqContent, ex.getMessage());
        }

        if (smppAccount == null) {
            if (Log.isTraceEnabled())
                Log.trace("smppAccount is null for apiKey: " + apiKey + "  " + reqContent);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_MISSING_ACCOUNT), timer, command);
            return;
        }

        if (smppAccount.isBanned()) {
            Log.info("Account " + apiKey + " is banned, so we will not forward the call... " + reqContent);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_MISSING_ACCOUNT), timer, command);
            return;
        }

        String to = request.getParameter("to");
        String authRealm = request.getParameter("auth_realm");
        String md5ApiSecret = null;

        if (Objects.isNull(to)) { //This is the REGISTER type of can-accept-request
            if (Log.isDebugEnabled())
                Log.debug("can-accept-request:  for REGISTER for " + apiKey);

            if(Objects.isNull(authRealm)) {
                md5ApiSecret = generateMD5ApiSecret(smppAccount);
            } else {
                md5ApiSecret = generateMD5ApiSecret(smppAccount, authRealm);
            }

            if (md5ApiSecret == null) {
                respond(reqContent, response, errorResponse("Failed generate MD5"), timer, command);
            } else {
                respond(reqContent, response, successResponse(smppAccount, md5ApiSecret, null, null, null, null), timer, command);
            }
            return; //End of AUTHENTICATION ONLY type of can-accept-request
        }

        // This is the AUTHENTICATION and AUTHORISATION type of can-accept-request (used by pre-sip-dial-in scenarios)
        // will be removed in the future once the sip-dial-in scenarios will be fully supported by
        // all parts

        //Validate the TO number correctness: 
        //It should be in the phone-numbers white list or verify
        WhitelistedNumbersConfig whitelistConfig = Core.getInstance().getConfig().getWhitelistedNumbersConfig();
        if (whitelistConfig != null && whitelistConfig.isEnabled() && whitelistConfig.isWhitelisted(to)) {
            if (Log.isDebugEnabled())
                Log.debug("Allowing whitelisted number " + to + " for " + apiKey);
        } else {
            JSONObject errorResponse = checkValidityAndPermittedDestinations(to, smppAccount);
            if (errorResponse != null) {
                respond(reqContent, response, errorResponse, timer, command);
                return;
            }
        }
        //At this point, the TO number is valid.

        // check balance (in case of false, the method will return the suitable response)
        if (!AsteriskAGIServer.isVoiceSkipQuota(smppAccount) && !hasMinimumBalance(reqContent, response, apiKey, timer, command, smppAccount))
            return;

        // check max cps (allowance to start x calls in a second)
        int maxCPS = getMaxCPS(smppAccount);

        // check capacity (allowance to have x concurrent calls in any given time)
        boolean hasCapacity = true;
        //The below was changed to return always true, as we had issues with the capacity 
        //calculation. 
        /**
             if (!Core.getInstance().getCallThrottler().isThrottled(apiKey, maxCPS)) {
             Log.warn("Failed to accept the request for accountId: " + apiKey + ", exceed max number " + maxCPS + " of calls sent." + reqContent);
             hasCapacity = false;
         } else {
             try {
                 long start = System.currentTimeMillis();

                 //At the moment, this always return true, as <check-concurrent-capacity enabled="false>
                 //hasCapacity = checkCapacity(apiKey); //this is just taking time to query the DB and eventually always return true.
                 hasCapacity = true;

                 if (Log.isDebugEnabled())
                    Log.debug("TIMED ::: (canAcceptRequest) checking capacity for account " + apiKey + " took (ms): " + (System.currentTimeMillis() - start));
             } catch (Exception ex) {
                 Log.error("Failed to check capcity for account " + apiKey + " " + reqContent + " due to " + reqContent );
                    respond(reqContent, response, errorResponse(ex.getMessage()));
                 return;
             }
         } **/

        // if there is a parent account, we also want to be able to return that account's maxCPS
        SmppAccount masterAccount = null;
        if ((smppAccount.getMasterAccountId() != null) && (!smppAccount.getMasterAccountId().isEmpty()) && (!smppAccount.getMasterAccountId().equals(apiKey))) {
            // if the masterAccountId is this account's apiKey, there is no parent account 
            try {
                masterAccount = accounts.getSmppAccount(smppAccount.getMasterAccountId());
            } catch (Exception ex) {
                Log.error("Failed to obtain master account for " + apiKey, ex.getMessage());
                // should this fail the request or should it fall back to the apiKey?
                respond(reqContent, response, errorResponse("Failed to obtain master account"), timer, command);
                return;
            }

            if (Log.isDebugEnabled()) {
                Log.debug("canAcceptRequest: account: {}, masterAccountId: {}",
                        smppAccount.getSysId(), masterAccount.getSysId());
            }
        }

        // Find out if we need to calculate the MD5
        // If &whitelisted = true skip the md5 calculation. In any other value - calculate it
        boolean isAccountWhiteListed = Boolean.valueOf(request.getParameter("whitelisted"));
        if (!isAccountWhiteListed) {
            if(Objects.isNull(authRealm)) {
                md5ApiSecret = generateMD5ApiSecret(smppAccount);
            } else {
                md5ApiSecret = generateMD5ApiSecret(smppAccount, authRealm);
            }
            if (md5ApiSecret == null) {
                respond(reqContent, response, errorResponse("Failed generate MD5"), timer, command);
                return;
            }
        }
        respond(reqContent, response, successResponse(smppAccount, md5ApiSecret, hasCapacity, maxCPS, null, null, masterAccount), timer, command);

    }

    /**
     * Input parameters in the request: Used for INVITE as of support of sip-dial-in
     * cmd: get-route-for-sipcall
     * api_key: the customer accout Id
     * to: the destination number, it might be an LVN or a generic PSTN number
     * get_md5: true / false. True md5 is required in the response
     * False means md5 is NOT required in the response
     * <p>
     * Response:
     * status: ok
     * api_key: AccountId
     * has_capacity: true
     * max_cps: the max cps for this account
     * md5: The account's MD5  (optional)
     * to_type: the TO destination type: PSTN / LVN
     * redirect_type:   if to_type is an LVN, redirect_type indicates the LVN redirection:
     *                  LVN_TO_PSTN / LVN_TO_SIP / LVN_TO_APPLICATION / NONE
     * redirect_destination: based on the redirect_type, it will include PSTN, SIP, or ApplicationId that the
     * application is using.
     */
    private static void getRouteForSipCall(HttpServletRequest request, HttpServletResponse response, String reqContent, long timer, String command)
            throws IOException, ServletException {

        String apiKey = request.getParameter("api_key");
        if (Objects.isNull(StringUtils.stripToNull(apiKey))) {
            respond(reqContent, response, errorResponse("Missing parameter: api_key"), timer, command);
            return;
        }

        String to = request.getParameter("to");
        if (Objects.isNull(StringUtils.stripToNull(to))) {
            respond(reqContent, response, errorResponse("Missing parameter: to"), timer, command);
            return;
        }

        boolean isTrunkingCall = Boolean.valueOf(request.getParameter("is_trunking_call"));

        apiKey = apiKey.trim();

        // Determine if this is a call to an emergency service number. We will skip some of the checks if it is.
        // if we do not get the from number to know locale, will need to compare against a list of possible emergency
        // service numbers
        boolean isEmergencyNumber = false;
        boolean allowCallToEmergencyNumberFromBannedAccount = false;
        boolean allowCallToEmergencyNumberWithoutQuota = false;
        boolean allowCallToEmergencyNumberWithoutParentAccountCheck = false;
        EmergencyCallingConfig emergencyCallingConfig = Core.getInstance().getConfig().getEmergencyCallingConfig();
        if (emergencyCallingConfig != null) {
            isEmergencyNumber = emergencyCallingConfig.isEmergencyServiceNumber(to);
            if (isEmergencyNumber) {
                Log.info("Account " + apiKey + " call to emergency number: " + to);
            }
            allowCallToEmergencyNumberFromBannedAccount = emergencyCallingConfig.isAllowFromBannedAccount();
            allowCallToEmergencyNumberWithoutQuota = emergencyCallingConfig.isSkipQuota();
            allowCallToEmergencyNumberWithoutParentAccountCheck = emergencyCallingConfig.isSkipParentAccountLookup();
        } else {
            Log.warn("EmergencyCallingConfig is null");
        }

        Accounts accounts = Accounts.getInstance();
        SmppAccount smppAccount = null;
        try {
            long start = System.currentTimeMillis();
            smppAccount = accounts.getSmppAccount(apiKey);
            if (Log.isTraceEnabled())
                Log.trace("TIMED ::: (getRouteForSipCall) fetching account " + apiKey + " took (ms): " + (System.currentTimeMillis() - start));
        } catch (AccountsException ex) {
            Log.error("Failed to retrieve account apiKey: " + apiKey + " " + reqContent, ex.getMessage());
        }

        if (smppAccount == null) {
            if (Log.isTraceEnabled())
                Log.trace("smppAccount is null for apiKey: " + apiKey + "  " + reqContent);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_MISSING_ACCOUNT), timer, command);
            return;
        }

        if (isEmergencyNumber && allowCallToEmergencyNumberFromBannedAccount) {
            Log.info("Account " + apiKey + " call to emergency number: skipping banned account check");
        } else if (smppAccount.isBanned()) {
            Log.info("Account " + apiKey + " is banned, so we will not allow the call... " + reqContent);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_MISSING_ACCOUNT), timer, command);
            return;
        }


        ToNumberDetails toNumberDetails = null;
        GroupConfig destinationGeos = null;
        SmppAccount masterAccount = null;
        if (isEmergencyNumber) {
            Log.info("Account " + apiKey + " call to emergency number: skipping number detail check");
            toNumberDetails = new ToNumberDetails(ToNumberDetails.Type.PSTN, ToNumberDetails.RedirectType.NONE, null);
        } else {
            //Handle the "to"
            if(isTrunkingCall) {
                Log.info("Account " + apiKey + " trunking call: assuming PSTN destination");
                toNumberDetails = new ToNumberDetails(ToNumberDetails.Type.PSTN, null, null);
            } else {
                toNumberDetails = getToNumberDetails(to, apiKey);
            }
            if (Objects.isNull(toNumberDetails)) {
                respond(reqContent, response, errorResponse(ProxyError.ERROR_FAILED_TO_IDENTIFY_THE_TO_NUMBER, apiKey), timer, command);
                return;
            }
            if (Log.isDebugEnabled())
                Log.debug("get-route-for-sipcall for " + apiKey + " destination details: " + toNumberDetails);

            //If the TO number is PSTN we want to verify that it is either white-listed number or valid
            if (ToNumberDetails.Type.PSTN.equals(toNumberDetails.getType())) {
                WhitelistedNumbersConfig whitelistConfig = Core.getInstance().getConfig().getWhitelistedNumbersConfig();
                if (whitelistConfig != null && whitelistConfig.isEnabled() && whitelistConfig.isWhitelisted(to)) {
                    if (Log.isDebugEnabled())
                        Log.debug("Allowing whitelisted number " + to + " for " + apiKey);
                } else {
                    JSONObject errorResponse = checkValidityAndPermittedDestinations(to, smppAccount);
                    if (errorResponse != null) {
                        respond(reqContent, response, errorResponse, timer, command);
                        return;
                    }
                }
            }
        }

        if (isEmergencyNumber) {
            Log.info("Account " + apiKey + " call to emergency number: skipping DC routing lookup");
        } else {
            // Check for DC routing information
            if (ToNumberDetails.Type.LVN.equals(toNumberDetails.getType())) {
                destinationGeos = Core.getInstance().getConfig().getLVNMappingsConfig().getDestinationGeos(to);
            }
        }

        if (isEmergencyNumber && allowCallToEmergencyNumberWithoutQuota) {
            Log.info("Account " + apiKey + " call to emergency number: skipping quota check");
        } else {
            // check balance (in case of false, the method will return the suitable response)
            if (!AsteriskAGIServer.isVoiceSkipQuota(smppAccount) && !hasMinimumBalance(reqContent, response, apiKey, timer, command, smppAccount))
                return;
        }

        if (isEmergencyNumber && allowCallToEmergencyNumberWithoutParentAccountCheck) {
            Log.info("Account " + apiKey + " call to emergency number: skipping parent account check");
        } else {
            // if there is a parent account, we also want to be able to return that account's maxCPS
            if ((smppAccount.getMasterAccountId() != null) && (!smppAccount.getMasterAccountId().isEmpty()) && (!smppAccount.getMasterAccountId().equals(apiKey))) {
                // if masterAccountId is the current account's apiKey, there is no parent account
                try {
                    masterAccount = accounts.getSmppAccount(smppAccount.getMasterAccountId());
                } catch (Exception ex) {
                    Log.error("Failed to obtain master account for " + apiKey, ex.getMessage());
                    // should this fail the request or should it fall back to the apiKey?
                    respond(reqContent, response, errorResponse("Failed to obtain master account"), timer, command);
                    return;
                }

                if (Log.isDebugEnabled()) {
                    Log.debug("getRouteForSipCall: account: {}, masterAccountId: {}",
                            smppAccount.getSysId(), smppAccount.getMasterAccountId());
                }
            }
        }

        // check max cps (allowance to start x calls in a second)
        int maxCPS = getMaxCPS(smppAccount);

        // check capacity (allowance to have x concurrent calls in any given time)
        // at the moment this is always true
        boolean hasCapacity = true;

        // Find out if we need to calculate the MD5
        String md5ApiSecret = null;
        boolean isMD5Required = Boolean.valueOf(request.getParameter("get_md5"));
        if (isMD5Required) {
            md5ApiSecret = generateMD5ApiSecret(smppAccount);
            if (md5ApiSecret == null) {
                respond(reqContent, response, errorResponse("Failed to generate MD5"), timer, command);
                return;
            }
        }
        respond(reqContent, response, successResponse(smppAccount, md5ApiSecret, hasCapacity, maxCPS, toNumberDetails, destinationGeos, masterAccount), timer, command);
    }


    private static JSONObject checkValidityAndPermittedDestinations(String to, SmppAccount smppAccount) throws ServletException {
        if (Log.isDebugEnabled()) {
            Log.debug("About to checkValidityAndPermittedDestinations for account {} and destination {}", smppAccount.getSysId(), to);
        }


        to = to.trim().replaceFirst("^00", "");

        String formattedTo;
        try {
            formattedTo = PhoneNumberTool.formatToInternationalNumber(to, null);
        } catch (BadlyFormattedNumberException e) {
            Log.warn("BadlyFormattedNumberException while checking if number is in account " + smppAccount.getSysId() + " permitted destinations", e.getMessage());
            return errorResponse(ProxyError.ERROR_NUMBER_NOT_VALID);
        }

        if (formattedTo == null) {
            Log.warn("PhoneNumberTool returned a null number for account {} and dest {} ", smppAccount.getSysId(), to);
            return errorResponse(ProxyError.ERROR_NUMBER_NOT_VALID);
        }

        try {
            smppAccount.destinationMatchesPermittedDestinationsWhitelist(formattedTo);
        } catch (PermittedDestinationMisMatchException e) {
            Log.warn("PermittedDestinationMisMatchException while checking if number {} is in account {} permitted destinations", to, smppAccount.getSysId());
            return errorResponse(ProxyError.ERROR_DESTINATION_NOT_PERMITTED);
        }

        // Check for +806 prefix and reject
        PrefixMapConfig prefixMapConfig = Core.getInstance().getConfig().getPrefixMapConfig();
        if ((prefixMapConfig != null) && formattedTo.startsWith(prefixMapConfig.getCountryCode())) {
            Log.warn("Not allowed to call Vonagistan: number {} matches virtual country code {}", to, prefixMapConfig.getCountryCode());
            return errorResponse(ProxyError.ERROR_DESTINATION_NOT_PERMITTED);
        }

        if (Log.isDebugEnabled()) {
            Log.debug("checkValidityAndPermittedDestinations for account {} and destination {} is valid to continue", smppAccount.getSysId(), to);
        }

        return null;
    }

    private static String generateMD5ApiSecret(SmppAccount smppAccount) {
        return generateMD5ApiSecret(smppAccount, DEFAULT_AUTH_REALM);
    }

    private static String generateMD5ApiSecret(SmppAccount smppAccount, String realm) {
        String apiSecret = smppAccount.getPassword();
        //TODO Tally: That should be in the configuration file

        String md5 = null;
        try {
            md5 = MD5Util.calculateMd5(smppAccount.getSysId() + ":" + realm + ":" + apiSecret);
        } catch (NoSuchAlgorithmException ex) {
            Log.warn("Failed to generate md5 hash! for " + smppAccount.getSysId());
        }

        return md5;
    }

    private static boolean hasMinimumBalance(String reqContent, HttpServletResponse response, String apiKey, long timer, String command, SmppAccount account) throws IOException, ServletException {
        InternalApiConfig internalApiConfig = Core.getInstance().getConfig().getInternalApiConfig();

        if (internalApiConfig == null) {
            respond(reqContent, response, errorResponse("Failed to load the InternalApiConfig. It was null!"), timer, command);
            return false;
        }

        if (!internalApiConfig.isBalanceCheckEnabled())
            return true;

        BigDecimal minBalanceRequired = internalApiConfig.getMinBalance();
        //Internal API servlet will be using QuotaClient with reduced timeout to prevent Kamailio from terminating the call,
        // if the pre-balance check doesn’t receive response from quota API within a second
        QuotaClient quotaClient = Core.getInstance().getQuotaClient();
        if (Core.getInstance().isAsyncQuotaFlag()) {
            String remoteHost = Core.getInstance().getConfig().getQuotaAPIConfig().getRemoteHost();
            String serviceId = Core.getInstance().getConfig().getQuotaAPIConfig().getServiceId();
            String remotePort = Core.getInstance().getConfig().getQuotaAPIConfig().getRemotePort();
            String userAgent = Core.getInstance().getConfig().getQuotaAPIConfig().getUserAgent();
            boolean ssl = Core.getInstance().getConfig().getQuotaAPIConfig().isSsl();
            QuotaApiClientConfig asyncConfig = new QuotaApiClientConfig(serviceId, remoteHost, remotePort, asyncTimeout, asyncTimeout, ssl, userAgent);
            quotaClient = new QuotaClient(asyncConfig, Core.getInstance().getConfig().getExtendedQuotaAPIConfig());
        }
        if (quotaClient == null) {
            respond(reqContent, response, errorResponse("Could not check for balance restrictions. QuotaClient was null!"), timer, command);
            return false;
        }

        AccountBalance balance = null;
        try {
            if(VQuotaService.isVpricingEnabled(account)) {
                VQuotaService vQuotaService = Core.getInstance().getVQuotaService();
                minBalanceRequired = Core.getInstance().getConfig().getVQuotaServiceConfig().getCurrentBalanceApiConfig().getMinBalance();
                balance = vQuotaService.getCurrentBalance(apiKey, minBalanceRequired, ""); // we have no sessionId here
            } else {
                balance = quotaClient.checkAccountBalance(apiKey, minBalanceRequired);
            }
        } catch (QuotaException | QuotaUnderMaintenanceException e) {
            if (Core.getInstance().isAsyncQuotaFlag()) {
                Log.warn("AsyncQuota Failed to check the account balance for " + reqContent + " due to " + e.getMessage());
                return true;
            } else {
                Log.error("Failed to check the account balance for " + reqContent + " due to " + e.getMessage());
                respond(reqContent, response, errorResponse("Could not check balance for account: " + apiKey), timer, command);
                return false;
            }
        } catch (AccountNotFoundException | IllegalOperationOnSubAccountException e) {
            Log.error("Failed to check the account balance for " + reqContent + " due to " + e.getMessage());
            respond(reqContent, response, errorResponse("Could not check balance for account: " + apiKey), timer, command);
            return false;
        } catch (QuotaDisabledException e) {
            Log.info("Quota is disabled for account: " + apiKey + " " + reqContent, e.getMessage());
            // if the quota is disabled, the account is not charged for calls so all good
            return true;
        }

        if (!balance.isRequiredFreeBalanceAvailable()) {
            Log.info("Account: " + apiKey + " does not have the required balance. " + reqContent);
            respond(reqContent, response, errorResponse(ProxyError.ERROR_OUT_OF_BALANCE, apiKey), timer, command);
            return false;
        }

        return true;
    }


    private static void respond(String reqContent, HttpServletResponse response, JSONObject jsonResponse, long timer, String command) throws IOException {
        String respContent = dumpResponseDetails(jsonResponse);
        Log.info(reqContent + " " + respContent);


        try {
            response.setContentType("application/json");
            response.setHeader("Cache-Control", "max-age=1");

            PrintWriter out = response.getWriter();
            out.print(jsonResponse.toString());
            INTERNAL_API_REQUESTS_LATENCY.labels(command, jsonResponse.getString("status")).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        } catch (Exception e) {
            Log.error("Failed to send the response: " + respContent + " due to " + e.getMessage());
            ERROR_INTERNAL_API_REQUESTS_LATENCY.labels(command).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        }
    }


    private static JSONObject successResponse(
            SmppAccount smppAccount, String md5ApiSecret,
            Boolean hasCapacity, Integer maxCPS, ToNumberDetails toNumberDetails,
            GroupConfig destinationGeos) throws ServletException {
        return successResponse(smppAccount, md5ApiSecret, hasCapacity, maxCPS, toNumberDetails, destinationGeos, null);
    }

    private static JSONObject successResponse(
                SmppAccount smppAccount, String md5ApiSecret,
                Boolean hasCapacity, Integer maxCPS, ToNumberDetails toNumberDetails,
                GroupConfig destinationGeos, SmppAccount parentAccount) throws ServletException {

        JSONObject jsonResponse = new JSONObject();
        try {
            jsonResponse.put("api_key", smppAccount.getSysId());
            jsonResponse.put("status", "ok");
            if (Objects.nonNull(hasCapacity))
                jsonResponse.put("has_capacity", hasCapacity);
            if (Objects.nonNull(maxCPS))
                jsonResponse.put("max_cps", maxCPS);
            if (Objects.nonNull(md5ApiSecret))
                jsonResponse.put("md5", md5ApiSecret);
            if (Objects.nonNull(toNumberDetails)) {
                jsonResponse.put("to_type", toNumberDetails.getType().name());
                if (Objects.nonNull(toNumberDetails.getRedirectType()))
                    jsonResponse.put("redirect_type", toNumberDetails.getRedirectType().name());
                if (Objects.nonNull(toNumberDetails.getRedirectDestination()))
                    jsonResponse.put("redirect_destination", toNumberDetails.getRedirectDestination());
            }
            if (Objects.nonNull(destinationGeos)) {
                addDestinationGeosToResponse(jsonResponse, destinationGeos);
            }
            if (Objects.nonNull(toNumberDetails)
                    && ToNumberDetails.RedirectType.LVN_TO_APPLICATION.equals(toNumberDetails.getRedirectType())) {
                addApplicationParamsToResponse(jsonResponse, toNumberDetails.getRedirectDestination());
            }
            if (Objects.nonNull(parentAccount)) {
                jsonResponse.put("main_api_key", parentAccount.getSysId());
                jsonResponse.put("main_max_cps", getMaxCPS(parentAccount));
            }
            jsonResponse.put("host_name", HOST_NAME);
            addTrafficSteeringAccountCapability(smppAccount, jsonResponse);
        } catch (JSONException ex) {
            Log.error("Failed to create json success response for apiKey: " + smppAccount.getSysId() +
                    " has_capacity: " + hasCapacity + " max_cps: " + maxCPS + " due to " + ex.getMessage());
            throw new ServletException("Failed to create json success response ", ex);
        }

        return jsonResponse;
    }

    private static JSONObject errorResponse(String reason) throws ServletException {
        return errorResponse("failed", reason, null, null);
    }

    private static JSONObject errorResponse(ProxyError error) throws ServletException {
        return errorResponse(error, null);
    }

    private static JSONObject errorResponse(ProxyError error, String apiKey) throws ServletException {
        return errorResponse("error", error.getMessage(), String.valueOf(error.getCode()), apiKey);
    }

    private static JSONObject errorResponse(String status, String reason, String code, String apiKey) throws ServletException {
        JSONObject response = new JSONObject();
        try {
            response.put("status", status);
            if (apiKey != null)
                response.put("api_key", apiKey);
            response.put("reason", reason);
            if (code != null)
                response.put("code", code);
            response.put("host_name", HOST_NAME);
        } catch (JSONException ex) {
            Log.error("Failed to create json error response for apiKey: " + apiKey +
                    " status: " + status + " reason: " + reason + " code: " + code + " due to " + ex.getMessage());
            throw new ServletException("Failed to create json error response ", ex);
        }

        return response;
    }


    private static String dumpRequestDetails(HttpServletRequest request) {

        StringBuilder sb = new StringBuilder();
        sb.append("Request: ");
        sb.append(" url: ").append(request.getRequestURL());
        sb.append(" uri: ").append(request.getRequestURI());
        sb.append(" params: ");
        Map<String, String[]> paramsMap = request.getParameterMap();
        paramsMap.entrySet().stream().forEach(e -> dumpEntry(e, sb));
        if (Log.isDebugEnabled()) {
            dumpRequestHeaders(request, sb);
        }
        String reqContent = sb.toString();
        Log.info(reqContent);
        return reqContent;
    }


    private static void dumpEntry(Entry<String, String[]> entry, StringBuilder sb) {

        sb.append(entry.getKey());
        sb.append("=");

        String[] values = entry.getValue();
        if (Objects.isNull(values)) {
            sb.append("NULL");
        } else {
            Arrays.asList(values).stream().forEach(v -> sb.append(v).append(", "));
        }
    }


    private static void dumpRequestHeaders(HttpServletRequest request, StringBuilder sb) {
        sb.append(" headers: ");
        Enumeration<String> requestHeaders = request.getHeaderNames();
        while (requestHeaders.hasMoreElements()) {
            String header = requestHeaders.nextElement();
            sb.append(header).append("=").append(request.getHeader(header)).append(", ");
        }
    }

    private static String dumpResponseDetails(JSONObject jsonResponse) {

        StringBuilder sb = new StringBuilder();
        sb.append("Response: ");

        Iterator<String> keys = jsonResponse.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            String value;
            try {
                value = jsonResponse.getString(key);
                if ("md5".equals(key))
                    value = SipAppUtils.obfuscate(value, 4, 4);
                sb.append(key).append(" = ").append(value).append(", ");

            } catch (JSONException e) {
                Log.warn("Failed to fetch the jsonObject content for " + key + " due to: " + e.getMessage());
            }
        }

        return sb.toString();
    }


    private static int getMaxCPS(SmppAccount smppAccount) {

        //int maxCPS = smppAccount.getMaxMtPerSecond(); This was used prior to SIP-93
        int maxCPS = smppAccount.getMaxCallsPerSecond();
        Log.debug("Account: " + smppAccount.getSysId() + " max CPS: " + maxCPS);
        return maxCPS;
    }

    private static ToNumberDetails getToNumberDetails(String number, String callerApiKey) {
        ShortCode shortCode = getShortCodeForNumber(number);
        if (Objects.isNull(shortCode)) {
            Log.info("Failed to find TO number {} details for {}. Assuming PSTN", number, callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("not_found").inc();
            });
            //For PSTN we do not have any more details
            return new ToNumberDetails(ToNumberDetails.Type.PSTN, null, null);
        }

        if (shortCode.getShortCodeType() == ShortCodeType.VERIFIED_CLI) {
            Log.warn("'To' number {} is an LVN of {} which is configured as verified cli, returning response as if no lvn found, caller account {}", number, shortCode.getAccountId(), callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("verified_cli").inc();
            });
            return new ToNumberDetails(ToNumberDetails.Type.PSTN, null, null);
        }

        if((shortCode.getAccountId() != null) && !shortCode.getAccountId().equals(callerApiKey)) {
            Log.info("'To' number {} is an LVN of {} which is different than caller account {}", number, shortCode.getAccountId(), callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("different_account").inc();
            });
            // if we decide to return PSTN type, or other qualification, do it here
        }

        if ((shortCode.getFeaturesSet() != null) && !shortCode.getFeaturesSet().contains(VOICE_FEATURE)) {
            Log.warn("'To' number {} is an LVN of {} which doesn't have {} in features set {}, caller account {}", number, shortCode.getAccountId(), VOICE_FEATURE, shortCode.getFeaturesSet(), callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("not_voice").inc();
            });
            // if we decide to return PSTN type, do it here
        }

        //As we found the shortCode, we know it is an LVN. Find out its definition:
        ShortCodeMetaData scMetadata = shortCode.getMetaData();
        if (Objects.isNull(scMetadata)) {
            Log.warn("'To' number {} is an LVN of {} which does not have scMetadata definition, caller account {}", number, shortCode.getAccountId(), callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("no_metadata").inc();
            });
            return new ToNumberDetails(ToNumberDetails.Type.LVN, ToNumberDetails.RedirectType.NONE, "");
        }


        String telCallbackAddress = scMetadata.getCallbackAddress(CallbackType.TEL);
        if (Objects.nonNull(telCallbackAddress) && StringUtils.isNotEmpty(telCallbackAddress)) {
            if (Log.isDebugEnabled())
                Log.debug("'To' number {} is an LVN of {} configured to redirect to a PTSN {}, caller account {}", number, shortCode.getAccountId(), telCallbackAddress, callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("lvn_to_pstn").inc();
            });
            return new ToNumberDetails(ToNumberDetails.Type.LVN, ToNumberDetails.RedirectType.LVN_TO_PSTN, telCallbackAddress);
        }

        String sipCallbackAddress = scMetadata.getCallbackAddress(CallbackType.SIP);
        if (Objects.nonNull(sipCallbackAddress) && StringUtils.isNotEmpty(sipCallbackAddress)) {
            if (Log.isDebugEnabled())
                Log.debug("'To' number {} is an LVN of {} configured to redirect to SIP destination(s) {}, caller account {}", number, shortCode.getAccountId(), sipCallbackAddress, callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("lvn_to_sip").inc();
            });
            return new ToNumberDetails(ToNumberDetails.Type.LVN, ToNumberDetails.RedirectType.LVN_TO_SIP, sipCallbackAddress);
        }

        String applicationId = scMetadata.getCallbackAddress(CallbackType.APPLICATION);
        if (Objects.nonNull(applicationId) && StringUtils.isNotEmpty(applicationId)) {
            if (Log.isDebugEnabled())
                Log.debug("'To' number {} is an LVN of {} configured to redirect to NCCO application {}, caller account {}", number, shortCode.getAccountId(), applicationId, callerApiKey);
            CompletableFuture.runAsync(() -> {
                TO_NUMBER_DETAIL_TYPES.labels("lvn_to_application").inc();
            });
            return new ToNumberDetails(ToNumberDetails.Type.LVN, ToNumberDetails.RedirectType.LVN_TO_APPLICATION, applicationId);
        }

        //If we are here, the LVN is not configured in any way
        Log.warn("'To' number {} is an LVN of {} which is not configured to forward the call, caller account {}", number, shortCode.getAccountId(), callerApiKey);
        CompletableFuture.runAsync(() -> {
            TO_NUMBER_DETAIL_TYPES.labels("lvn_to_none").inc();
        });
        return new ToNumberDetails(ToNumberDetails.Type.LVN, ToNumberDetails.RedirectType.NONE, "");
    }

    public static ShortCode getShortCodeForNumber(String number) {
        if (Log.isDebugEnabled())
            Log.debug("About to look for shortcode for number {} ", number);
        number = number.trim().replace("+", "");

        ShortCodes shortCodes = Core.getInstance().getConfig().getShortCodes();

        if (shortCodes == null) {
            Log.info("Could not check if number {} belongs to ShortCodes. ShortCodes is not initialized! - assuming PSTN", number);
            return null;
        }

        final long shortCodesDBtimer = System.nanoTime();
        ShortCode shortCode = null;
        try {
            shortCode = shortCodes.getFirstShortCodeMatchForNumber(number);
            if (Log.isDebugEnabled())
                Log.debug("shortcode {} found for number {} ", shortCode, number);
        } catch (ShortCodeException ex) {
            Core.getInstance().incrementShortCodesDBErrorCounter(ex);
            Log.info("Failed to retrieve shortcode for: {} due to {} . Assuming PSTN. ", number, ex.getMessage());
        }
        Core.getInstance().updateShortCodesDBLatencyMetrics((System.nanoTime() - shortCodesDBtimer)/ NANOSECONDS_PER_SECOND);
        return shortCode;
    }

    //These are not really callbacks, this is the link between LVN to PSTN, SIP Destination or Application
    private static boolean hasCallbackType(ShortCodeMetaData scMetadata, CallbackType type) {
        return scMetadata.getCallbackAddresses() != null
                && scMetadata.getCallbackAddresses().containsKey(type)
                && StringUtils.isNotBlank(scMetadata.getCallbackAddress(type));
    }


    private static enum ProxyError {
        ERROR_MISSING_ACCOUNT(1, "Failed to retrieve account for the given key"),
        ERROR_OUT_OF_BALANCE(2, "The account is out of balance"),
        ERROR_NUMBER_NOT_VALID(3, "Failed to validate the given number"),
        ERROR_DESTINATION_NOT_PERMITTED(4, "The given number is not in the permitted destination list"),
        ERROR_NOTHING_TO_FORWARD(5, "Given number has nothing to forward to"),
        ERROR_NUMBER_NOT_OWNED(6, "Failed to retrieve that number"),
        ERROR_APPLICATION_NOT_FOUND(7, "Application Id not found"),
        ERROR_ACCOUNT_IS_BANNED(8, "Account is banned"),
        ERROR_FAILED_TO_IDENTIFY_THE_TO_NUMBER(9, "Failed to identify the TO number");

        private final int code;
        private final String message;

        private ProxyError(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return this.code;
        }

        public String getMessage() {
            return this.message;
        }
    }


    private static String formatNumber(String number) {
        return number.trim().replace("+", "").replaceFirst("^00", "");
    }

    private static void addApplicationParamsToResponse(JSONObject jsonResponse, String applicationId) {
        Application application = Application.lookup(applicationId);
        if (application != null) {
            if (application.getPaymentEnabled() != null) {
                boolean payment = application.getPaymentEnabled();
                jsonResponse.put("payment_enabled", payment);
            }
            if (application.getRegion() != null) {
                String region = application.getRegion();
                jsonResponse.put("region", region);
            }
        }
    }

    private static void addTrafficSteeringAccountCapability(SmppAccount smppAccount, JSONObject jsonResponse) {
        TRAFFIC_STEERING_CAPABILITY.forEach(entry -> jsonResponse.put(entry.getKey(), String.valueOf(smppAccount.hasCapability(entry.getCapability()))));
    }

}
