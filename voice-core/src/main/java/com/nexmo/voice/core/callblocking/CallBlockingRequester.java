package com.nexmo.voice.core.callblocking;


import static org.apache.http.entity.ContentType.APPLICATION_JSON;

import java.io.IOException;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

import com.thepeachbeetle.common.http.HttpRequester.ConnectionRefusedException;
import com.thepeachbeetle.common.http.HttpRequester.ConnectionTimeoutException;
import com.thepeachbeetle.common.http.HttpRequester.HttpException;
import com.thepeachbeetle.common.http.HttpRequester.TimeoutException;


class CallBlockingRequester {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }

    private final HttpClient client;

    CallBlockingRequester(CallblockingServiceConfig callblockingServiceConfig) {
        int timeout = Integer.parseInt(String.valueOf(callblockingServiceConfig.getTimeout()));
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        HttpClientBuilder builder = HttpClientBuilder.create().setDefaultRequestConfig(config);
        this.client = builder.build();
    }


    public String executePost(String url, CallblockingClientJson requestBody, Map<String, String> headers, CallblockingServiceConfig callblockingServiceConfig) throws CallBlockingLookupException, ClientException, ConnectionRefusedException, HttpException, TimeoutException, ConnectionTimeoutException, JsonProcessingException {
        HttpPost request = new HttpPost(url);
        String jsonBody = OBJECT_MAPPER.writeValueAsString(requestBody);

        request.setEntity(new StringEntity(jsonBody, APPLICATION_JSON));
        int timeout = Integer.parseInt(String.valueOf(callblockingServiceConfig.getTimeout()));
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        request.setConfig(config);
        return executeRequest(request, headers);
    }

    private String executeRequest(HttpUriRequest request, Map<String, String> headers) throws CallBlockingLookupException, ClientException {
        addHeaders(request, headers);

        final HttpResponse resp;
        try {
            resp = this.client.execute(request);
        } catch (IOException e) {
            String err = "Failed to execute the request";
            throw new ClientException(err, e);
        }

        int statusCode = resp.getStatusLine().getStatusCode();

        String responseBody = null;
        try {
            responseBody = EntityUtils.toString(resp.getEntity());
        } catch (Exception e) {
            // It is expected that not all bodies are readable
        }

        if (statusCode == 404) {
            throw new NotFoundException(responseBody);
        } else if (statusCode >= 300) {
            throw new CallBlockingLookupException("HTTP " + Integer.valueOf(statusCode) + ": " + responseBody);
        }

        // if successful return the response
        return responseBody;
    }

    private void addHeaders(HttpUriRequest request, Map<String, String> headers) {
        for (Map.Entry<String, String> header : headers.entrySet()) {
            request.addHeader(header.getKey(), header.getValue());
        }
    }

    static class ClientException extends Exception {
        private ClientException(String msg) {
            super(msg);
        }

        private ClientException(String msg, Throwable t) {
            super(msg, t);
        }
    }

    static class NotFoundException extends ClientException {
        private NotFoundException(String msg) {
            super(msg);
        }
    }
}
