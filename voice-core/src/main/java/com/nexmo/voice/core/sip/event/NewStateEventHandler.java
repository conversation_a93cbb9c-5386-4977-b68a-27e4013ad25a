package com.nexmo.voice.core.sip.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.NewStateEvent;
import org.asteriskjava.util.AstState;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.pdd.PDDCalculationException;

/**
 * Created on 05/01/15.
 *
 * <AUTHOR>
 */
public class NewStateEventHandler extends AsteriskVoiceEventHandler<NewStateEvent> {

    private static final Logger Log = LogManager.getLogger(NewStateEventHandler.class);

    public NewStateEventHandler() {
        super(NewStateEvent.class);
    }

    @Override
    public void handle(NewStateEvent event) throws VoiceEventHandlerException {
        if (Log.isDebugEnabled())
            Log.debug("Processing SIP NewState Event ['" + event + "'] hashCode=" + event.hashCode());

        if (event.getChannelState().equals(AstState.AST_STATE_PRERING) || event.getChannelState().equals(AstState.AST_STATE_RING) || event.getChannelState().equals(AstState.AST_STATE_RINGING)) {

            long currentTime = System.currentTimeMillis();
            final String uniqueId = event.getUniqueId();
            if (uniqueId == null || uniqueId.isEmpty())
                throw new VoiceEventHandlerException("Error handling NewState Event. No origUniqueId found. event ['" + event + "']");

            try {
                Core.getInstance().getPddCalculator().storeEndTime(uniqueId, currentTime);
            } catch (PDDCalculationException e) {
                Log.info("PDD can't be calculated for the call with unique id " + uniqueId, e);
            }

            Log.trace("SIP NewState Event MTA RING UNIQUE ID " + uniqueId + " time " + currentTime);
        }
    }
}
