package com.nexmo.voice.core.application;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import io.prometheus.client.Histogram;
import org.apache.http.client.config.RequestConfig;
import org.json.JSONException;
import org.json.JSONObject;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.applications.ApplicationsServiceConfig;
import com.nexmo.voice.config.applications.InterServiceAuthConfig;
import com.thepeachbeetle.core.serviceauth.ServiceAuth;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

public class ApplicationsServiceClient {
    private final static Logger Log = LogManager.getLogger(ApplicationsServiceClient.class);
    private final ApplicationsServiceConfig config;
    private final Requester requester;
    private final ServiceAuth serviceAuth;

    private static final Histogram APPLICANT_REQUESTS_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_application_requests_latency").help("Http Requests Latency to Fetch Applications").labelNames("status").register();

    public ApplicationsServiceClient(ApplicationsServiceConfig config) {
        this.config = config;
        this.requester = new Requester(config);

        InterServiceAuthConfig authConfig = config.getInterServiceAuthConfig();
        Properties props = System.getProperties();
        props.setProperty("JWT_INTER_SERVICE_AUTH_MY_PRIVATE_KEY", authConfig.getPrivateKeyFile());
        props.setProperty("JWT_INTER_SERVICE_AUTH_PUBLIC_KEYS", authConfig.getPublicKeysPath());
        // TODO: Check these paths?
        this.serviceAuth = new ServiceAuth(authConfig.getIssuer());
    }

    public Application getApplication(String applicationId) throws ApplicationLookupException {
        final long timer = System.nanoTime();
        String url = config.constructUri(applicationId);
        int retryCount = config.getRetryCount();

        String response = "";
        boolean success = false;
        int attempt = 0;
        while (!success) {
            try {
                attempt++;
                Log.info("Requesting application from [ {} ]", url);
                final String jwt = this.serviceAuth.getTokenForAuthenticatingWithRemoteService("devapi");
                response = this.requester.executeGet(url, addJwtHeader(jwt));
                Log.info("Application Service response for {}: {}", applicationId, response);
                success = true;
                APPLICANT_REQUESTS_LATENCY.labels("ok").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            } catch (NotFoundException exception) {
                Log.info("Application {} not found - deleted", applicationId);
                APPLICANT_REQUESTS_LATENCY.labels("not_found").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                return null;
            } catch (RetryableException exception) {
                Log.warn("Failed to get application", exception);
                success = false;
                if (attempt == retryCount) {
                    Log.info("Reached maximum attempts, trying first failover host");
                    APPLICANT_REQUESTS_LATENCY.labels("retry_exceeded").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                    url = config.constructUri(config.getFailoverFirstHost(), applicationId); //use first failover host
                } else if (attempt == retryCount + 1) {
                    Log.info("Reached maximum attempts exceeded, trying second failover host");
                    APPLICANT_REQUESTS_LATENCY.labels("retry_exceeded").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                    url = config.constructUri(config.getFailoverSecondHost(), applicationId); //use second failover host
                } else if (attempt > retryCount + 1) {
                    throw new ApplicationLookupException("Retry limit (" + retryCount + ") exceeded, also tried failover hosts", exception);
                }
                APPLICANT_REQUESTS_LATENCY.labels("retry").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            } catch (ClientException exception) {
                Log.warn("Failed to get application", exception);
                APPLICANT_REQUESTS_LATENCY.labels("client_failure").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                throw new ApplicationLookupException("Non-retryable failure", exception);
            } catch (ServiceAuth.PrivateKeyRetrievalFailedException exception) {
                Log.error("Failed to retrieve private key for inter service authentication", exception);
                APPLICANT_REQUESTS_LATENCY.labels("pki_failure").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
                throw new ApplicationLookupException("Failed to load JWT signing key", exception);
            }
        }

        Application result;
        try {
            JSONObject json = new JSONObject(response);
            result = ApplicationsServiceResponseParser.fromJSON(json);
        } catch (JSONException e) {
            Log.warn("Failed to parse json application response", e);
            throw new ApplicationLookupException("Failed to parse JSON response", e);
        }
        return result;
    }

    private static Map<String, String> addJwtHeader(String jwt) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + jwt);
        return headers;
    }

    private static class Requester {
        private final HttpClient client;

        private Requester(ApplicationsServiceConfig config) {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(config.getTimeout())
                    .setConnectionRequestTimeout(config.getTimeout())
                    .setSocketTimeout(config.getTimeout()).build();
            HttpClientBuilder builder = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig);
            client = builder.build();
        }

        public String executeGet(String url) throws ClientException {
            return executeGet(url, Collections.<String, String>emptyMap());
        }

        public String executeGet(String url, Map<String, String> headers) throws ClientException {
            HttpGet get = new HttpGet(url);
            return executeRequest(get, headers, url);
        }

        private String executeRequest(HttpUriRequest request, Map<String, String> headers, String url) throws ClientException {
            // Add headers
            for (Map.Entry<String, String> header : headers.entrySet()) {
                request.addHeader(header.getKey(), header.getValue());
            }

            final HttpResponse resp;
            try {
                resp = this.client.execute(request);
            } catch (IOException e) {
                String err = "Failed to execute the request";
                if (e instanceof SocketTimeoutException)
                    throw new RetryableException(err, e);
                else
                    throw new ClientException(err, e);
            }

            int statusCode = resp.getStatusLine().getStatusCode();

            String responseBody = null;
            try {
                responseBody = EntityUtils.toString(resp.getEntity());
            } catch (Exception e) {
                // It is expected that not all bodies are readable
            }

            if (statusCode == 404) {
                throw new NotFoundException(responseBody);
            } else if (statusCode == 500 || statusCode == 502 || statusCode == 503 || statusCode == 504) {
                throw new RetryableException("HTTP " + Integer.valueOf(statusCode) + ": " + responseBody);
            } else if (statusCode >= 300) {
                throw new ClientException("HTTP " + Integer.valueOf(statusCode) + ": " + responseBody);
            }

            // if successful return the response
            return responseBody;
        }

    }

    private static class ClientException extends Exception {
        private ClientException() {
            super();
        }

        private ClientException(String msg) {
            super(msg);
        }

        private ClientException(String msg, Throwable t) {
            super(msg, t);
        }
    }

    private static class RetryableException extends ClientException {
        private RetryableException() {
            super();
        }

        private RetryableException(String msg) {
            super(msg);
        }

        private RetryableException(String msg, Throwable t) {
            super(msg, t);
        }
    }

    private static class NotFoundException extends ClientException {
        private NotFoundException() {
            super();
        }

        private NotFoundException(String msg) {
            super(msg);
        }
    }

}
