package com.nexmo.voice.core.sip.event;

import java.util.concurrent.locks.Lock;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.ShutdownEvent;

import com.nexmo.voice.core.Core;

//This class is used during by the SIPApp

public class ShutdownEventHandler extends AsteriskVoiceEventHandler<ShutdownEvent> {

    private final static Logger Log = LogManager.getLogger(ShutdownEventHandler.class);

    public ShutdownEventHandler() {
        super(ShutdownEvent.class);
    }

    @Override
    public void handle(ShutdownEvent event) throws VoiceEventHandlerException {
        Log.warn("Processing SIP Shutdown Event ['" + event + "'] hashCode=" + event.hashCode());

        String shutdownType = event.getShutdown();

        Log.warn("Asterisk is shutting down ungracefully with status: " + shutdownType);

        Lock shutdownLock;
        try {
            shutdownLock = Core.getInstance().acquireShutdownLock();
        } catch (InterruptedException ex) {
            throw new VoiceEventHandlerException("FAILED TO ACQUIRE SHUTDOWN LOCK ::: SOMETHING IS WRONG!", ex);
        }

        try {
            Log.info("App shutdown or restart scheduled.");
            Core.getInstance().setAsteriskShutdown(true);
        } finally {
            if (shutdownLock != null)
                shutdownLock.unlock();
        }
    }

}
