package com.nexmo.voice.core.gateway.asterisk;

import java.io.IOException;

import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.ManagerConnection;
import org.asteriskjava.manager.PingThread;
import org.asteriskjava.manager.TimeoutException;
import org.asteriskjava.manager.action.PingAction;
import org.asteriskjava.manager.response.ManagerResponse;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

/**
 * This class is instantiated during the SIPApp server startup.
 * The default timeout is 5 seconds.
 */
public class AsteriskPingThread extends PingThread {

    private final static Logger Log = LogManager.getLogger(AsteriskPingThread.class);

    //The PingThread class has a private timeout element, but there is no getter for it
    //so we need to shadow it here so it can be used in the ping method
    private static final long DEFAULT_TIMEOUT = 0L;
    private long timeout = DEFAULT_TIMEOUT;

    private static final Histogram ASTERISK_PING_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_asterisk_ping_thread_latency").help("Time taken to ping Asterisk").labelNames("status").register();
    private static final Counter ASTERISK_PING_ERRORS = Counter.build().name("sipapp_asterisk_ping_thread_errors").help("Total asterisk ping errors").labelNames("errors").register();

    public AsteriskPingThread(ManagerConnection managerConnection) {
        super(managerConnection);
    }

    @Override
    public void setTimeout(long timeout) {
        super.setTimeout(timeout);
        this.timeout = timeout;
        Log.info("AsteriskPingThread setting timeout to: {}", this.timeout);
    }

    @Override
    protected void ping(ManagerConnection managerConnection) {
        final long timer = System.nanoTime();
        try {

            if (this.timeout <= 0)
                managerConnection.sendAction(new PingAction(), null);
            else {
                final ManagerResponse response;
                response = managerConnection.sendAction(new PingAction(), this.timeout);
                if (Log.isTraceEnabled())
                    Log.trace("Ping response '" + response + "' for " + managerConnection.toString());
            }
            ASTERISK_PING_LATENCY.labels("ok").observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
        } catch (TimeoutException e) {
            Log.error("Ping Thread timed out waiting for response from Asterisk's manager connection ['" + managerConnection.toString() + "']", e);
            ASTERISK_PING_ERRORS.labels("timeout").inc();
            AsteriskAMIProcessor.handleDirtyShutdown();
        } catch (IllegalArgumentException | IllegalStateException | IOException e) {
            Log.warn("Exception on sending Ping to " + managerConnection.toString(), e);
            ASTERISK_PING_ERRORS.labels("failed").inc();
        }
    }

}
