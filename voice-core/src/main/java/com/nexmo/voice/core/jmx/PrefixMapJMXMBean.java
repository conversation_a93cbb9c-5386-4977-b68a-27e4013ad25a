package com.nexmo.voice.core.jmx;

/**
 * <AUTHOR>
 */
public interface PrefixMapJMXMBean {

    // Will be treated as a R/O variable called 'size'
    public int getSize();

    // Will be treated as a R/W variable called 'enabled'
    public boolean getEnabled();

    public void setEnabled(boolean enabled);

    public String viewPrefixMapMetadata();

    public String loadPrefixMapFromFile(String filename);

    public String prefixMapLookup(String number);

}
