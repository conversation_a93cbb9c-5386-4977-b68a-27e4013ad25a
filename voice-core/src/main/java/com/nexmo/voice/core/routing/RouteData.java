package com.nexmo.voice.core.routing;

import com.thepeachbeetle.messaging.hub.core.routing.GenericMTRouter.Route;


public class RouteData {
    public enum RouteExceptionType {
        NONE,
        ROUTING_UNROUTABLE_RANDOMPOOL,
        DROPMESSAGE,
        ROUTENULL
    }
    private Route route;
    private RouteExceptionType routeExceptionType;
    private Exception exc;

    public Route getRoute() { return route; }
    public void setRoute(Route route) { this.route = route; }

    public RouteExceptionType getRouteExceptionType() { return routeExceptionType; }
    public void setRouteExceptionType(RouteExceptionType type) { this.routeExceptionType = type; }

    public Exception getExc() { return exc; };
    public void setExc(Exception e) { this.exc = e; }

    public static class Builder {
        private Route route;
        private RouteExceptionType routeExceptionType;
        private Exception exc;

        public Builder withRoute(Route route) {
            this.route = route;
            return this;
        }

        public Builder withExceptionType(RouteExceptionType type) {
            this.routeExceptionType = type;
            return this;
        }

        public Builder withExc(Exception exc) {
            this.exc = exc;
            return this;
        }

        public RouteData build() {
            RouteData r = new RouteData();
            r.routeExceptionType = routeExceptionType;
            r.route = route;
            r.exc = exc;
            return r;
        }
    }
}

