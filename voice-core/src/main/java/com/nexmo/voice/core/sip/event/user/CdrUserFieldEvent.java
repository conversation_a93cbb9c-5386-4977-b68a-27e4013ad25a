package com.nexmo.voice.core.sip.event.user;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.UserEvent;

// Define your custom UserEvent class
// Note: custom user event must match format in UserEvent(name,body) in asterisk dialplan
// ie: UserEvent(CdrUserField,key1:value,key2:value2) would have class name CdrUserFieldEvent
// also you MUST create getters and setters for each field in UserEvent body
public class CdrUserFieldEvent extends UserEvent {
    private String destinationChannel;
    private String hangupCause;
    private String dialStatus;
    private String leg2ID;
    private String gws;
    private String gw;
    private String attempt;
    private String nexmoUUID;
    private String toInps;
    private String answeredTimeMs;
    private String progressTimeMs;
    private String dialedTimeMs;

    private static final Logger Log = LogManager.getLogger(CdrUserFieldEvent.class);

    public CdrUserFieldEvent(Object source) {
        super(source);
        Log.info("CdrUserFieldEvent: " + source);
    }

    public String getDestinationChannel() {
        return destinationChannel;
    }

    public void setDestinationChannel(String destinationChannel) {
        this.destinationChannel = destinationChannel;
    }

    public String getHangupCause() {
        return hangupCause;
    }

    public void setHangupCause(String hangupCause) {
        this.hangupCause = hangupCause;
    }

    public String getDialStatus() {
        return dialStatus;
    }

    public void setDialStatus(String dialStatus) {
        this.dialStatus = dialStatus;
    }

    public String getLeg2ID() {
        return leg2ID;
    }

    public void setLeg2ID(String leg2ID) {
        this.leg2ID = leg2ID;
    }

    public String getGws() {
        return gws;
    }

    public void setGws(String gws) {
        this.gws = gws;
    }

    public String getGw() {
        return gw;
    }

    public void setGw(String gw) {
        this.gw = gw;
    }

    public String getAttempt() {
        return attempt;
    }

    public void setAttempt(String attempt) {
        this.attempt = attempt;
    }

    public String getNexmoUUID() {
        return nexmoUUID;
    }

    public void setNexmoUUID(String nexmoUUID) {
        this.nexmoUUID = nexmoUUID;
    }

    public String getToInps() {
        return toInps;
    }

    public void setToInps(String toInps) {
        this.toInps = toInps;
    }

    public String getAnsweredTimeMs() {
        return answeredTimeMs;
    }

    public void setAnsweredTimeMs(String answeredTimeMs) {
        this.answeredTimeMs = answeredTimeMs;
    }

    public String getProgressTimeMs() {
        return progressTimeMs;
    }

    public void setProgressTimeMs(String progressTimeMs) {
        this.progressTimeMs = progressTimeMs;
    }

    public String getDialedTimeMs() {
        return dialedTimeMs;
    }

    public void setDialedTimeMs(String dialedTimeMs) {
        this.dialedTimeMs = dialedTimeMs;
    }
}