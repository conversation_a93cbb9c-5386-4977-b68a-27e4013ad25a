package com.nexmo.voice.core.cache;

import com.nexmo.voice.core.sip.event.CdrEventHandler;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.sip.event.cdr.PartialCdrEvent;
import org.apache.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;

import java.util.concurrent.ConcurrentHashMap;

/**
 * This class is a cache of Partial Cdr Events contain CDR events and CDR event user data
 *
 * Will store a hashmap using a Map<uniqueId, Map<channelId, PartialCdrEvent>>
 *
 * <AUTHOR>
 */
public class PartialCdrEventCache {
    private static final Logger Log = Logger.getLogger(PartialCdrEventCache.class.getName());

    private ConcurrentHashMap<String, ConcurrentHashMap<String, PartialCdrEvent>> partialCdrEventCache;

    private CdrEventHandler cdrEventHandler;



    public PartialCdrEventCache() {
        partialCdrEventCache = new ConcurrentHashMap<String, ConcurrentHashMap<String, PartialCdrEvent>>();
        this.cdrEventHandler = new CdrEventHandler();
        if (Log.isDebugEnabled()) {
            Log.debug("PartialCdrEvent Cache initialization completed");
        }
    }

    public void addEvent(String uniqueId, String channelId, PartialCdrEvent event) {
        partialCdrEventCache.computeIfAbsent(uniqueId, key -> new ConcurrentHashMap<>());
        partialCdrEventCache.get(uniqueId).put(channelId, event);
        Log.info("Adding UniqueID: " + uniqueId + "; Channel: " + channelId + "; event='[" + event + "'] to PartialCdrEvent Cache...");
    }

    public boolean containsEvent(String uniqueId, String channelId){
        if (partialCdrEventCache.containsKey(uniqueId)) {
            return partialCdrEventCache.get(uniqueId).containsKey(channelId);
        }

        return false;
    }

    public boolean deleteEvent(String uniqueId, String channelId) {
        if (!partialCdrEventCache.containsKey(uniqueId)) {
            return true;
        }

        if (!partialCdrEventCache.get(uniqueId).containsKey(channelId)) {
            return true;
        }

        // Remove nested channelId from Hangup Cache
        if (partialCdrEventCache.get(uniqueId).remove(channelId) == null) {
            Log.warn("Could not find channel ID ['" + channelId + "'] in PartialCdrEvent Cache...");
            return false;
        };

        Log.info("Removed Unique ID: " + uniqueId + "; channel ID: " + channelId + " from PartialCdrEvent Cache...");
        if (Log.isDebugEnabled()) {
            this.logHashMap();
        }

        return true;
    }

    public boolean deleteUniqueId(String uniqueId) {
        if (!partialCdrEventCache.containsKey(uniqueId)) {
            Log.info("Unique ID: " + uniqueId + " already removed from PartialCdrEvent Cache...");
            return true;
        }

        if (partialCdrEventCache.remove(uniqueId) == null) {
            Log.warn("Could not find unique ID ['" + uniqueId + "'] in PartialCdrEvent Cache...");
            return false;
        }

        Log.info("Removed Unique ID: " + uniqueId + " from PartialCdrEvent Cache...");
        if (Log.isDebugEnabled()) {
            this.logHashMap();
        }

        return true;
    }

    public void logHashMap() {
        Log.debug("Hash Map: " + this.partialCdrEventCache.toString());
    }

    public boolean processPartialCdr(String uniqueId, String channelID) {
        if (Log.isDebugEnabled()) {
            Log.debug("Processing partial CDR event for uniqueID: " + uniqueId + "; channelID: " + channelID);
        }
        if (!containsEvent(uniqueId, channelID)) {
            Log.error("Unique ID: " + uniqueId + "; channel ID: " + channelID + " not found in Partial CDR Event Cache!!!");
            return false;
        }

        PartialCdrEvent partialCdrEvent = partialCdrEventCache.get(uniqueId).get(channelID);
        CdrEvent cdrEvent = partialCdrEvent.getCdrEvent();
        String eventCanonicalName = cdrEvent.getClass().getCanonicalName();
        try {
            cdrEventHandler.handle(cdrEvent);
        } catch (VoiceEventHandlerException ex) {
            Log.error("Error occurred while handling event [className: " + eventCanonicalName + " hashCode=" + cdrEvent.hashCode() + " ] " + ex.getMessage());
        }

        Log.info("Processed partial CDR event for uniqueID: " + uniqueId + "; channelID: " + channelID);

        return true;
    }
}
