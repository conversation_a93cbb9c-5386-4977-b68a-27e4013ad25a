package com.nexmo.voice.core.types;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.thepeachbeetle.common.util.StringUtil;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Message;
import com.thepeachbeetle.messaging.hub.core.Product;
import com.thepeachbeetle.messaging.hub.core.exceptions.NoPriceFoundException;


public class EffectiveCost extends EffectiveAmount implements Serializable {

    private static final Logger Log = LogManager.getLogger(EffectiveCost.class);

    public EffectiveCost(final BigDecimal amount, final String prefix) {
        super(USAGE.COST, -1L, scale(amount), prefix, null, null);
    }

    public EffectiveCost(final Price cost) {
        super(USAGE.COST, timestampFromPricingRule(cost), scale(cost.getPrice()), cost.getPrefix(), null, cost.getSenderPrefix());
    }

    public BigDecimal getCost() {
        return this.getAmount();
    }

    @Override
    public String toString() {
        return "EffectiveCost: " + super.toString();
    }

    //The name of the object representing the cost is PriceMatrixList, but it is containing the cost.
    //Its methods are also referring to the word price, but it is just about cost.
    public static EffectiveCost getCostFromMatrixOrDefault(final PriceMatrixList costMatrixList,
                                                           final SmppAccount account,
                                                           final String to,
                                                           final String from,
                                                           final String appId,
                                                           final String network,
                                                           final BigDecimal defaultCost,
                                                           final boolean isVAPIOutboundToVBC,
                                                           final String purposeDesc) {
        return getCostFromMatrixOrDefault(costMatrixList,
                                          account,
                                          to,
                                          from,
                                          appId,
                                          network,
                                          defaultCost,
                                          isVAPIOutboundToVBC,
                                          purposeDesc,
                                          false);
    }

    public static EffectiveCost getCostFromMatrixOrDefault(final PriceMatrixList costMatrixList,
                                                           final SmppAccount account,
                                                           final String to,
                                                           final String from,
                                                           final String appId,
                                                           final String network,
                                                           final BigDecimal defaultCost,
                                                           final boolean isVAPIOutboundToVBC,
                                                           final String purposeDesc,
                                                           final boolean isEmergencyCall) {

        if(isEmergencyCall) {
            if (Log.isDebugEnabled())
                Log.debug("Cost zero on emergency calls.");
            return new EffectiveCost(BigDecimal.ZERO, "emergency-call-cost");
        }

        //appId has value when it is an inbound call with no LVN. The call request will refer directly to applicationId (VBC)
        if (Objects.nonNull(appId) && !appId.trim().isEmpty()) {
            if (Log.isDebugEnabled())
                Log.debug("Cost zero on non LVN inbound calls. appId: " + appId + " purposeDesc: " + purposeDesc);
            return new EffectiveCost(BigDecimal.ZERO, "default-inbound-to-application-cost");
        }

        //isVAPIOutboundToVBC is true only for VAPI-to-VBC outbound calls. In this case the cost is Zero
        if (isVAPIOutboundToVBC) {
            if (Log.isDebugEnabled())
                Log.debug("Cost zero on VAPI-to-VBC OUTBOUND calls. purposeDesc: " + purposeDesc);
            return new EffectiveCost(BigDecimal.ZERO, "default-outbound-to-vbc-cost");
        }

        if (!StringUtil.containsOnlyNumbers(to)) {
            //For inbound (not VBC) calls that would be the LVN and then it will include numbers
            //For outbound calls that would be the Sip address or the application_id which do not include numbers
            //SIP-287: For outbound sip destination, we now use the sip outbound price, but the cost is still zero
            //SIP-259: For outbound sip destination, if the voice product is SIP - the price is zero, if the voice product is API we now use the sip outbound price
            if (Log.isDebugEnabled())
                Log.debug("Cost zero on non numeric destination (to) " + to + " purposeDesc: " + purposeDesc);
            return new EffectiveCost(BigDecimal.ZERO, "default-sip-destination-cost");
        }

        EffectiveCost ret = null;

        if (costMatrixList != null) {
            try {
                // Create a fake Message to hold the 'from' number
                Message message = null;
                if (from != null) {
                    message = new Message(Message.TYPE_OTHER, Product.PRODUCT_VOICE_CALL);
                    message.oa = from;
                }
                //The object is called Price, but it holds the cost
                Price costForNumber = costMatrixList.getPriceRule(Product.PRODUCT_VOICE_CALL,
                        to,
                        network, // network
                        message, // message
                        account);
                ret = new EffectiveCost(costForNumber);
            } catch (Exception ex) {
                Log.warn("Could not find cost rule for destination " + to + " account " + account.getSysId() + " network " + network + " purposeDesc: " + purposeDesc, ex);
            }
        }

        // wrap defaultCost
        if (ret == null) {
            Log.warn("Using DEFAULT cost rule for destination " + to + " account  " + account.getSysId() + " network " + network + " purposeDesc: " + purposeDesc);
            ret = new EffectiveCost(defaultCost, "default-cost");
        }

        if (Log.isDebugEnabled()) {
            BigDecimal cost = ret.getCost();
            String costStr = Objects.nonNull(cost) ? cost.toPlainString() : "null";
            String prefix = ret.getPrefix();
            Log.debug("Account: " + account.getSysId() +
                      " to: " + to +
                      " appId: " + appId +
                      " network: " + network +
                      " about to return cost: " + costStr + " prefix: " + prefix + " purposeDesc: " + purposeDesc);
        }
        return ret;
    }

}
