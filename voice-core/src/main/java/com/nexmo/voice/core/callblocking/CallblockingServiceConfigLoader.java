package com.nexmo.voice.core.callblocking;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;


public class CallblockingServiceConfigLoader extends NestedXmlHandler {
    public static final String ROOT_NODE = "callblocking-service";
    private static final String DEFAULT_PATH = "/blocking-rule/internal";

    private String host;
    private String basePath;
    private int timeout;
    private String username;
    private String password;
    private int retryCount;
    private int retryTimeout;
    private String fallbackHost;

    private CallblockingServiceConfig config;

    public CallblockingServiceConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public CallblockingServiceConfig getConfig() {
        return config;
    }


    @Override
    public void startNode(String node, XmlContent content) throws LoaderException {
        if (getNodeName().equals(node)) { // <callblocking-service>
            this.host = content.getAttribute("service-host", true, this.host);
            this.basePath = content.getAttribute("base-path", false, DEFAULT_PATH);
            this.timeout = parseInt(content.getAttribute("timeout", false, String.valueOf(this.timeout)));
            this.username = content.getAttribute("username", true, this.username);
            this.password = content.getAttribute("password", true, this.password);
            this.retryCount = parseInt(content.getAttribute("retry-count", false, String.valueOf(this.retryCount)));
            this.retryTimeout = parseInt(content.getAttribute("retry-timeout", false, String.valueOf(this.retryTimeout)));
            this.fallbackHost = content.getAttribute("fallback-host", true, this.fallbackHost);
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new CallblockingServiceConfig(this.host, this.basePath, this.timeout, this.username, this.password, retryCount, retryTimeout, fallbackHost);
            notifyComplete();
        }
    }

}
