package com.nexmo.voice.core.gateway.asterisk.task;

import java.util.Collection;
import java.util.HashSet;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;


public class AsteriskTaskExecutor {

    private final static Logger Log = LogManager.getLogger(AsteriskTaskExecutor.class);

    protected static final int PURGE_INTERVAL = 30;
    protected static final TimeUnit PURGE_INTERVAL_UNIT = TimeUnit.SECONDS;

    private final ExecutorService noConnectionIdThreadPool;
    private final ConcurrentHashMap<String, AsteriskSingleThreadedPool> poolMap; // <connectionId, pool>

    public AsteriskTaskExecutor() {
        this.poolMap = new ConcurrentHashMap<>();
        this.noConnectionIdThreadPool = Executors.newSingleThreadExecutor();
    }

    public void startPurging() {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleWithFixedDelay(new PurgingThread(this),
                                        PURGE_INTERVAL,
                                        PURGE_INTERVAL,
                                        PURGE_INTERVAL_UNIT);
    }

    public void execute(AsteriskTask task) {
        String connectionId = task.getConnectionId();

        if (connectionId == null) {
            if (Log.isDebugEnabled())
                Log.debug("AsteriskTask: " + task + " without contentionId will be executed on the no-connection pool");

            this.noConnectionIdThreadPool.execute(task);
            return;
        }

        AsteriskSingleThreadedPool pool = this.poolMap.get(connectionId);
        if (pool == null) {
            if (Log.isDebugEnabled())
                Log.debug("AsteriskTask: " + task + " connectionId: " + connectionId + " not in the poolMap");

            pool = new AsteriskSingleThreadedPool(connectionId + "-" + task.getTaskContentCode());
            AsteriskSingleThreadedPool existing = this.poolMap.putIfAbsent(connectionId, pool);
            if (existing != null) {
                if (Log.isDebugEnabled())
                    Log.debug("AsteriskTask: " + task + " connectionId: " + connectionId + " appeared in the poolMap just now");
                pool.shutdown();
                pool = existing;
            } else {
                if (Log.isDebugEnabled())
                    Log.debug("AsteriskTask: " + task + " connectionId: " + connectionId + " added to poolMap");
            }
        }

        if (Log.isDebugEnabled())
            Log.debug("Executing task on specific pool " + pool + " for connection id: " + connectionId + " with content code=" + task.getTaskContentCode());

        pool.execute(task);
    }

    /**
     * For performance reasons, the pool is governed by the first leg's connectionId.
     * This could be changed for the sessionId if needed.
     */
    public void shutdownPoolAndRemove(String connectionId) {
        AsteriskSingleThreadedPool pool = this.poolMap.get(connectionId);
        if (pool != null) {
            //As it is not locked, it is essential to check again! Otherwise there might be N.P.E
            AsteriskSingleThreadedPool removedPool = this.poolMap.remove(connectionId);
            if (removedPool != null)
                removedPool.shutdown();
        }
    }

    public void shutdown() {
        for (String connectionId : this.poolMap.keySet())
            shutdownPoolAndRemove(connectionId);
        this.noConnectionIdThreadPool.shutdown();

        boolean shutdownOk = false;
        try {
            shutdownOk = this.noConnectionIdThreadPool.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Log.error("Something really bad happened while trying to shutdown no connectionId thread pool!", ex);
        }

        if (!shutdownOk)
            Log.error("FAILED TO SHUTDOWN noConnectionIdThreadPool GRACEFULLY!!");
    }

    protected void purge() throws Exception {
        // Previously all threadpools would be purged here, causing them to be dropped between the
        // BridgeEvent and CdrEvent for calls lasting longer than ~30 seconds. For some reason, any
        // pools purged in this way were *not* garbage collected, causing them to accumulate in the
        // JVM until an emergency GC following allocation failure. -- Russ, 2021-02-04

        final long startTime = System.currentTimeMillis();

        // Get all active calls
        VoiceContextCache contextCache = Core.getInstance().getVoiceContextCache();
        Collection<VoiceContext> contexts = contextCache.getAllContexts();
        Set<String> ids = new HashSet<String>();
        for (VoiceContext ctx : contexts) {
            ids.add(ctx.getConnectionId());
        }

        // Purge stale pools
        for (Entry<String, AsteriskSingleThreadedPool> entry : this.poolMap.entrySet()) {
            if (ids.contains(entry.getKey()))
                continue;
            // This pool no longer corresponds to an active VoiceContext, so try purging it
            boolean purged = entry.getValue().shutdownIfIdle();
            if (purged) {
                AsteriskSingleThreadedPool purgedPool = this.poolMap.remove(entry.getKey());
                if (purgedPool != null)
                    Log.info("Purged pool: {}", purgedPool.toString());
            }
        }
        final long endTime = System.currentTimeMillis();
        Log.info("Purging thread: Took {} ms. {} pools remain in the map, for {} voice contexts",
                (endTime - startTime), this.poolMap.entrySet().size(), contexts.size());
    }

    private static final class PurgingThread implements Runnable {

        private final AsteriskTaskExecutor parent;

        public PurgingThread(AsteriskTaskExecutor parent) {
            this.parent = parent;
        }

        @Override
        public void run() {
            try {
                this.parent.purge();
            } catch (Exception e) {
                Log.error("Unexpected error while purging", e);
            }
        }

    }

    private static final class AsteriskSingleThreadedPool {

        //First observation: this class is NOT thread safe: it is possible to have
        //both methods "execute" and "shutdown" to run at the same time, the first is using the pool and the
        //other shutting it down at the same time.

        private final ExecutorService pool;
        private volatile boolean touchedInInterval;
        private final String threadNamePrefix;

        public AsteriskSingleThreadedPool(String threadNamePrefix) {
            this.threadNamePrefix = threadNamePrefix;
            ThreadFactory customThreadfactory = new CustomThreadFactoryBuilder()
                    .setNamePrefix(threadNamePrefix).build();

            this.pool = Executors.newSingleThreadExecutor(customThreadfactory);
            this.touchedInInterval = true;
            if (Log.isTraceEnabled())
                Log.trace("Pool {} was created. in AsteriskSingleThreadedPool {} ", pool, this);
        }

        public void execute(AsteriskTask task) {
            if (Log.isTraceEnabled())
                Log.trace("About to use Pool {} for task {}. ", pool, task);
            this.pool.execute(task);
            this.touchedInInterval = true;
        }

        public boolean shutdownIfIdle() {
            if (!this.touchedInInterval) {
                this.shutdown();
                return true;
            }
            this.touchedInInterval = false;
            return false;
        }

        public void shutdown() {
            if (Log.isDebugEnabled())
                Log.debug("About to shutdown Pool {}", pool);
            this.pool.shutdown();
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("AsteriskSingleThreadedPool [pool=");
            builder.append(pool);
            builder.append(", touchedInInterval=");
            builder.append(touchedInInterval);
            builder.append(", threadNamePrefix=");
            builder.append(threadNamePrefix);
            builder.append("]");
            return builder.toString();
        }


/**

        This was used as part of the special deployment in ass4.sng1.
        It was clarifying that the GC is clearing the Executors as needed.
        It is not suitable for regular operation as it slows down the system


        @Override
        public void finalize() {
            Log.info("About to finalize AsteriskSingleThreadedPool {}",this);
        } **/

    }

}
