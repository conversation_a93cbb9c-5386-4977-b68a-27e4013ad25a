package com.nexmo.voice.core.sip.event.cdr;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.nexmo.voice.core.sip.event.*;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.CarrierPlatform;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.types.SIPCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;


/**
 * This class is a parser and container of the userField content arriving with
 * the CDREvent
 * <p>
 * Typical OUTBOUND structure would be:
 * HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=ibasis;GW=ibasis;ATTEMPT=1#1
 * <p>
 * In case the customer's funds are too low to allow the call, the call is not
 * established, and there are no gws details:
 * HANGUPCAUSE=21;DIALSTATUS=NO_MONEY;LEG2ID=;GWS=;GW=;ATTEMPT=
 * <p>
 * <p>
 * Typical INBOUND structure would be:
 * HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;FALLBACKATTEMPT=2;FALLBACKALTERNATIVES=3;NEXMOUUID=658b960eb38dd0eb91931f1ee94e8428
 * <p>
 * SIP-1764: On Asterisk 16, additional params(sample shown below) would be included
 * ANSWEREDTIME_MS=3000;PROGRESSTIME_MS=3000;
 * ANSWEREDTIME_MS is time from answer to hangup in milliseconds
 * Being in milliseconds allow us to round it up to the nearest 1000 when converting it to seconds, making it >= billableSeconds of the CdrEvent
 * Then the BillingInfo's reportedBillableSeconds, is calculated as the the maximum  between the round up seconds above and billableSeconds of the CdrEvent
 * PROGRESSTIME_MS is time in milliseconds between creation of the dialing channel and receiving the first PROGRESS signal for calculating PDD
 *
 * <AUTHOR>
 */
abstract class AbstractCdrEventUserData implements CdrEventUserData {

    private static final Logger Log = LogManager.getLogger(AbstractCdrEventUserData.class.getName());
    protected final Integer cdrEventBillableSeconds;


    // userField elements
    protected HangupCause hangupCause = null;
    protected String dialStatus = null;
    protected String channel1Id = null;
    protected String channel2Id = null;
    protected String nexmoUUID = null;
    protected String gatewaysList = null;
    protected String currentGateway = null;
    protected String gwsAttemptStr = null;
    protected int currentGWAttempt = 0;
    protected int availableGWAttempts = 0;
    protected String fallbackAttemptStr = null;
    protected String fallbackAlternativesStr = null;
    protected int currentFallbackAttempt = 0;
    protected int availableFallbackAlternatives = 0;
    protected CarrierPlatform carrierPlatform = null;
    protected long answeredDuration = -1;
    protected long answeredDurationMillis = -1;
    protected long pddTimeMillis = -1;
    protected boolean ignoreEvent;

    protected CdrEvent event;

    // Holder for any other element
    protected final HashMap<String, String> additionalParams = new HashMap<>();

    public AbstractCdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
        if (Objects.isNull(event)) {
            Log.error("Invalid attempt to extract userData from NULL CdrEvent");
            throw new VoiceEventHandlerException("Error handling CdrEventUserData. NULL event");
        }
        this.event = event;
        cdrEventBillableSeconds = event.getBillableSeconds();
        ignoreEvent = false;
        channel1Id = event.getChannel();
        String userField = event.getUserField();
        // If the userField is empty - verify if it is the cancel-during-invite case
        if (Objects.isNull(userField) || userField.trim().isEmpty()) {
            try {
                handleMissingUserFieldEvent(event, channelUniqueId);
            }
            catch (IgnoredVoiceEventHandlerException e){
                if(Log.isDebugEnabled()){
                    Log.debug("{} Ignoring cdr event {} with missing userField", channelUniqueId, event);
                }
                ignoreEvent = true;
            }
            return;
        }

        try {
            parseUserField(userField, event.getUniqueId());
            amendMissingDetails();
            validateUserData(event.getUniqueId());
        } catch (Exception e) {
            if (attemptFixingUserFieldData(event, channelUniqueId))
                return;
            else {
                Log.error("CdrEvent userField invalid data. No CANCEL-during-INVITE indicators. channelUniqueId: {}",
                        channelUniqueId);
                throw e;
            }
        } finally {
            if (Log.isDebugEnabled())
                Log.debug("Final CdrEventUserData: {}", this);
        }
    }


    protected abstract void parseUserField(String userField, String channelUniqueId) throws VoiceEventHandlerException;

    // SIP-222 ; When CANCEL is sent before the INVITE processing is completed,
    // there are cases
    // where the CDR Event userField is missing or include only partial details.
    // If we can verify that the call was probably cancelled we will reconstruct the
    // proper userField
    protected abstract void handleMissingUserFieldEvent(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException;

    @Override
    public SIPCode calculateCallSIPCodeAndDialStatus(String channelUniqueId, Integer billableSeconds, VoiceContext channelContext) {
        SIPCode sipCode;
        //In case the caller is cancelling the call before it started:
        //  If there is only one GW, the received HANGUPCAUSE=0 and DIALSTATUS=CANCEL.
        //  If there are several gws which failover to the current gw and then the caller cancelled the call,
        //     the received HANGUPCAUSE=[previous attempt cause] and DIALSTATUS=CANCEL.
        if (Log.isDebugEnabled()) {
            Log.debug("Starting calculateCallSIPCodeAndDialStatus()...");
        }
        if (CdrEventHandler.DIAL_STATUS_CANCEL.equals(this.getDialStatus()))
            sipCode = SIPCode.REQUEST_TERMINATED;
        else
            sipCode = AsteriskVoiceEventHandler.translateOrDefault(this.getHangupCause(), SIPCode.UNKNOWN);

        //Additional other amendments to the SIP Code regarding the reasons of failed/stopped call
        //due to insufficient funds or missing route details
        if (Log.isDebugEnabled()) {
            Log.debug("Checking other sip codes...");
        }

        if (sipCode == SIPCode.FORBIDDEN) {
            // Call didn't start because we couldn't find a route
            if (CdrEventHandler.DIAL_STATUS_NO_ROUTE.equals(this.getDialStatus()))
                sipCode = SIPCode.NOT_FOUND;
            // Call didn't start because client didn't have balance
            if (CdrEventHandler.DIAL_STATUS_NO_MONEY.equals(this.getDialStatus()))
                sipCode = SIPCode.PAYMENT_REQUIRED;
        } else if (this.getHangupCause() == HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED)
            sipCode = SIPCode.PAYMENT_REQUIRED;
        else if (this.getHangupCause() == HangupCause.AST_CAUSE_WRONG_CALL_STATE)
            sipCode = SIPCode.BANNED_CALL_ENDED;

        // if we blocked the call for the CLI being unknown or due to Enforcer Callblocking rules, override  behavior of calculateCallSIPCodeAndDialStatus()
        // and set the sipCode to 403
        if (Log.isDebugEnabled()) {
            Log.debug("Checking unknown CLI ...");
        }
        if(channelContext != null && channelContext.getInternalFlags() != null) {
            Log.info("{} call was blocked due to internal flag {}. Setting the SIPCode to 403", channelUniqueId, channelContext.getInternalFlags().toString());
            if (channelContext.getInternalFlags().contains(CallInternalFlag.BLOCKED_ON_CALLID_ENFORCER))
                sipCode = SIPCode.FORBIDDEN;
            if (channelContext.getInternalFlags().contains(CallInternalFlag.BLOCKED_ON_CALLID_UNKNOWN))
                sipCode = SIPCode.FORBIDDEN_UNKNOWN_CLI;
        }

        //SIP-273: When the customer reject a re-invite, (the call has already started), we create an OUTBOUND cdr, as the call
        // took place for a while, and it is chargeable. We do not want to use REASON=500, so we change here the SIPCode to 430
        // The CDREvent details in such scenario are: HANGUPCAUSE=38; DIALSTATUS=ANSWER; billableseconds=271
        //In Asterisk: HANGUPCAUSE=38 => AST_CAUSE_NETWORK_OUT_OF_ORDER => AST_CAUSE_FAILURE
        if (Log.isDebugEnabled()) {
            Log.debug("Checking reinvites status...");
        }
        if ((this.getHangupCause() == HangupCause.AST_CAUSE_FAILURE) &&
                (CdrEventHandler.DIAL_STATUS_ANSWER.equals(this.getDialStatus())) &&
                (Objects.nonNull(billableSeconds) && billableSeconds != 0))
            sipCode = SIPCode.FLOW_FAILED;

        //Additional other amendments to the DIALSTATUS regarding the reasons of failed/stopped call
        //due to gw issues
        if (Log.isDebugEnabled()) {
            Log.debug("Checking remaining fail/stopped sip codes...");
        }
        String newDialStatus = this.getDialStatus();
        if (sipCode == SIPCode.BAD_GATEWAY && CdrEventHandler.DIAL_STATUS_CONGESTION.equals(this.getDialStatus()))
            newDialStatus = CdrEventHandler.DIAL_STATUS_BAD_GATEWAY;
        if (sipCode == SIPCode.NOT_FOUND && CdrEventHandler.DIAL_STATUS_CHANUNAVAIL.equals(this.getDialStatus()))
            newDialStatus = CdrEventHandler.DIAL_STATUS_NOT_FOUND;
        if (sipCode == SIPCode.FORBIDDEN && CdrEventHandler.DIAL_STATUS_CHANUNAVAIL.equals(this.getDialStatus()))
            newDialStatus = CdrEventHandler.DIAL_STATUS_DECLINED;

        this.updateDialStatus(newDialStatus, channelUniqueId);

        if (Log.isDebugEnabled()) {
            Log.debug("Updated dial status to newDialStatus=" + newDialStatus);
        }

        return sipCode;
    }

    protected void setDialStatus(String channelUniqueId, SIPCode sipCode) {
        //Additional other amendments to the DIALSTATUS regarding the reasons of failed/stopped call
        //due to gw issues
        String newDialStatus = this.getDialStatus();
        if (sipCode == SIPCode.BAD_GATEWAY && CdrEventHandler.DIAL_STATUS_CONGESTION.equals(this.getDialStatus())) {
            newDialStatus = CdrEventHandler.DIAL_STATUS_BAD_GATEWAY;
        }
        if (sipCode == SIPCode.NOT_FOUND && CdrEventHandler.DIAL_STATUS_CHANUNAVAIL.equals(this.getDialStatus()))
            newDialStatus = CdrEventHandler.DIAL_STATUS_NOT_FOUND;
        if (sipCode == SIPCode.FORBIDDEN && CdrEventHandler.DIAL_STATUS_CHANUNAVAIL.equals(this.getDialStatus()))
            newDialStatus = CdrEventHandler.DIAL_STATUS_DECLINED;

        this.updateDialStatus(newDialStatus, channelUniqueId);
    }

    @Override
    public boolean isLastRetryGateway() {
        return currentGWAttempt == availableGWAttempts;
    }

    @Override
    public boolean isLastSipDestination() {
        // If there are no sip destination alternatives, this is the single destination,
        // hence the last one.
        if (availableFallbackAlternatives == 0)
            return true;

        // If there are potential alternatives - verify which alternative we are on for
        // this event
        return (currentFallbackAttempt == availableFallbackAlternatives);
    }

    @Override
    public HangupCause getHangupCause() {
        return hangupCause;
    }

    @Override
    public String getDialStatus() {
        return dialStatus;
    }

    @Override
    public String getChannel2Id() {
        return channel2Id;
    }

    @Override
    public String getNexmoUUID() {
        return nexmoUUID;
    }

    @Override
    public String getGatewaysList() {
        return gatewaysList;
    }

    @Override
    public String getCurrentGateway() {
        return currentGateway;
    }

    @Override
    public String getGatewaysAttemptStr() {
        return gwsAttemptStr;
    }

    @Override
    public int getCurrentGWAttempt() {
        return currentGWAttempt;
    }

    @Override
    public int getAvailableGWAttempts() {
        return availableGWAttempts;
    }

    @Override
    public String getFallbackAttemptStr() {
        return fallbackAttemptStr;
    }

    @Override
    public String getFallbackAlternativesStr() {
        return fallbackAlternativesStr;
    }

    @Override
    public int getCurrentFallbackAttempt() {
        return currentFallbackAttempt;
    }

    @Override
    public int getAvailableFallbackAlternatives() {
        return availableFallbackAlternatives;
    }

    @Override
    public CarrierPlatform getCarrierPlatform() {
        return carrierPlatform;
    }

    @Override
    public long getAnsweredDuration() {
        return answeredDuration;
    }

    @Override
    public long getPddTimeMillis() {
        return pddTimeMillis;
    }

    @Override
    public long getAnsweredDurationMillis() {
        return answeredDurationMillis;
    }

    @Override
    public boolean ignoreEvent() {
        return ignoreEvent;
    }

    protected Map<String, String> extractUserField(String userField, String channelUniqueId) {
        HashMap<String, String> userFieldsMap = new HashMap<String, String>();
        String[] pairs = userField.split(";");

        for (String pair : pairs) {
            String[] parts = pair.split("=", 2); // Max two parts
            if (parts.length < 2 || parts[1].trim().isEmpty()) {
                if (Log.isDebugEnabled())
                    Log.debug("CdrEvent of " + channelUniqueId + " userField include a param with no value: " + pair
                            + " it will be ignored");
                continue;
            }
            userFieldsMap.put(parts[0].trim(), parts[1].trim());
        }
        return userFieldsMap;
    }

    protected void setGatewayAttemptDetails(String attemptDetails, String channelUniqueId) throws VoiceEventHandlerException {

        // Attempts will be provided only for outbound calls which actually made an attempt to start
        // If the customer's balance is too low, the call wont start
        if (isNoMoneyBlockingCallSituation())
            return;

        if (Objects.isNull(attemptDetails) || attemptDetails.trim().isEmpty()) {
            Log.error("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " content is missing.  channelUniqueId: "
                    + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " not found.");
        }

        gwsAttemptStr = attemptDetails;

        String[] entry = attemptDetails.split("#", 2); // Max two parts
        if (entry.length < 2) {
            Log.error("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " content is invalid: " + attemptDetails
                    + " channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " not found.");
        }

        // Current attempt number
        try {
            currentGWAttempt = Integer.parseInt(entry[0]);
        } catch (Exception e) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid current attempt: " + entry[0]
                    + " channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid value.");
        }
        // Total available attempts
        try {
            availableGWAttempts = Integer.parseInt(entry[1]);
        } catch (Exception e) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid total number of available attempts: "
                    + entry[1] + " channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid value.");
        }
    }

    // TODO Tally: investigate why we need to change the dial status "NOANSWER" in such way
    protected void setDialStatus(String value, String channelUniqueId) throws VoiceEventHandlerException {
        if (Objects.isNull(value) || value.trim().isEmpty()) {
            Log.warn("CdrEvent userField " + CdrEventUserData.DIAL_STATUS + " is empty or missing. channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.DIAL_STATUS + " invalid value.");
        }

        if ("NOANSWER".equals(value))
            dialStatus = "NO_ANSWER";
        else
            dialStatus = value;
    }

    protected void setHangupCause(String value, String channelUniqueId) throws VoiceEventHandlerException {
        int code = 0; // In org.asteriskjava.live.HangupCause: AST_CAUSE_NOTDEFINED(0),
        if (Log.isDebugEnabled()) {
            Log.debug("Setting hangup cause for value=" + value + "; uniqueID=" + channelUniqueId);
        }
        try {
            code = Integer.parseInt(value);
        } catch (Exception e) {
            Log.warn("CdrEvent userField " + CdrEventUserData.HANGUP_CAUSE + " invalid value " + value + " channelUniqueId: "
                    + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.HANGUP_CAUSE + " invalid value.");
        }
        hangupCause = HangupCause.getByCode(code);
        if (Objects.isNull(hangupCause)) {
            Log.warn("CdrEvent userField " + CdrEventUserData.HANGUP_CAUSE + " for value " + value + " is null.  channelUniqueId: "
                    + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.HANGUP_CAUSE + " invalid value.");
        }

        if (Log.isDebugEnabled()) {
            Log.debug("Set hangup cause for value=" + value + "; uniqueID=" + channelUniqueId + "; to hangupCause=" + hangupCause.getCode());
        }
    }

    protected void setTargetFallbackAlternatives(String value, String channelUniqueId) throws VoiceEventHandlerException {
        fallbackAlternativesStr = value;
        try {
            availableFallbackAlternatives = Integer.parseInt(value);
        } catch (Exception e) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ALTERNATIVES + " invalid value " + value
                    + " channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException("Error handling CdrEventUserData. userField "
                    + CdrEventUserData.SIP_TARGET_FALLBACK_ALTERNATIVES + " invalid value.");
        }
    }

    protected void setTargetFallbackAttemptNo(String value, String channelUniqueId) throws VoiceEventHandlerException {
        fallbackAttemptStr = value;
        try {
            currentFallbackAttempt = Integer.parseInt(value);
        } catch (Exception e) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO + " invalid value " + value
                    + " channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException(
                    "Error handling CdrEventUserData. userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO + " invalid value.");
        }
    }

    protected void validateUserData(String channelUniqueId) throws VoiceEventHandlerException {

        if (isNoMoneyBlockingCallSituation())
            return; // If this CDR is about a call which didnt started due to customer no-money the
        // other details are not provided

        boolean isValid = true;

        // Validate gateway fail over details. All details should be provided (for OUTBAOUND) or not provided (for INBOUND)
        isValid = isValid && validateGatewaysFailOver(channelUniqueId);

        // Validate sip target fail over details. All details should be provided (for INBAOUND) or not provided (for OUTBOUND)
        isValid = isValid && validateSipTargetsFallback(channelUniqueId);

        if (!isValid) {
            Log.warn("CdrEvent userField invalid data. channelUniqueId: " + channelUniqueId);
            throw new VoiceEventHandlerException("Error creating CdrEventUserData. userField invalid data.");
        }
    }

    protected boolean validateGatewaysFailOver(String channelUniqueId) {
        boolean isValid = true;

        if (!((Objects.isNull(gatewaysList) && Objects.isNull(currentGateway) && Objects.isNull(gwsAttemptStr))
                || (Objects.nonNull(gatewaysList) && Objects.nonNull(currentGateway)
                && Objects.nonNull(gwsAttemptStr)))) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAYS_LIST + " and " + CdrEventUserData.CURRENT_GATEWAY + " and "
                    + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT
                    + " mismatching values. All should be provided or null. channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if (Objects.nonNull(gatewaysList) && Objects.nonNull(currentGateway)
                && !gatewaysList.contains(currentGateway)) {

            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAYS_LIST + " should include " + CdrEventUserData.CURRENT_GATEWAY + " channelUniqueId: "
                    + channelUniqueId);
            isValid = false;
        }

        if (currentGWAttempt < 0) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT
                    + "current attempt number unknown value. channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if (availableGWAttempts < 0) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT
                    + "total available attempts number unknown value. channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if (currentGWAttempt > availableGWAttempts) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + "current attempt number " + currentGWAttempt
                    + " higher then available number of attempts " + availableGWAttempts + " channelUniqueId: "
                    + channelUniqueId);
            isValid = false;
        }

        if ((currentGWAttempt == 0 && availableGWAttempts != 0)
                || (currentGWAttempt != 0 && availableGWAttempts == 0)) {
            Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + "current attempt number " + currentGWAttempt
                    + " higher then available number of attempts " + availableGWAttempts + " channelUniqueId: "
                    + channelUniqueId);
            isValid = false;
        }

        if (Objects.nonNull(gatewaysList)) {

            String[] gws = gatewaysList.split(",");
            if (gws.length != availableGWAttempts) {
                Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAYS_LIST + " does not match the total available attempts number "
                        + " channelUniqueId: " + channelUniqueId);
                isValid = false;
            } else if (currentGWAttempt <= availableGWAttempts && currentGWAttempt >= 1) {
                if (!gws[currentGWAttempt - 1].equals(currentGateway)) {
                    Log.warn("CdrEvent userField " + CdrEventUserData.GATEWAYS_LIST + " and " + CdrEventUserData.CURRENT_GATEWAY + " and "
                            + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " valuse do not match " + " channelUniqueId: "
                            + channelUniqueId);
                    isValid = false;
                }
            }
        }

        return isValid;
    }

    protected boolean validateSipTargetsFallback(String channelUniqueId) {
        boolean isValid = true;

        if (!((Objects.isNull(fallbackAttemptStr) && Objects.isNull(fallbackAlternativesStr))
                || (Objects.nonNull(fallbackAttemptStr) && Objects.nonNull(fallbackAlternativesStr)))) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO + " and " + CdrEventUserData.SIP_TARGET_FALLBACK_ALTERNATIVES
                    + " mismatching values. Both should be provided or null. channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if (currentFallbackAttempt < 0) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO
                    + "current sip target attempt number unknown value. channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if (availableFallbackAlternatives < 0) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ALTERNATIVES
                    + "total available sip fall back alternatives number unknown value. channelUniqueId: "
                    + channelUniqueId);
            isValid = false;
        }

        if (currentFallbackAttempt > availableFallbackAlternatives) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO + "current sip target attempt number "
                    + currentFallbackAttempt + " higher then available number of alternatives "
                    + availableFallbackAlternatives + " channelUniqueId: " + channelUniqueId);
            isValid = false;
        }

        if ((currentFallbackAttempt == 0 && availableFallbackAlternatives != 0)
                || (currentFallbackAttempt != 0 && availableFallbackAlternatives == 0)) {
            Log.warn("CdrEvent userField " + CdrEventUserData.SIP_TARGET_FALLBACK_ATTEMPT_NO + "current attempt number "
                    + currentFallbackAttempt + " higher then available number of alternatives "
                    + availableFallbackAlternatives + " channelUniqueId: " + channelUniqueId);
            isValid = false;
        }
        return isValid;
    }

    @Override
    public void updateDialStatus(String updatedDialStatus, String channelId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Start updateDialStatus - dialStatus: " + Objects.isNull(dialStatus));
        }
        if (dialStatus.equals(updatedDialStatus)) {
            if (Log.isDebugEnabled()) {
                Log.debug("Dial status already updated...");
            }
            return;
        }
        Log.warn("ChannelId=" + channelId + " dialStatus has been updated from " + dialStatus + " to "
                + updatedDialStatus);
        dialStatus = updatedDialStatus;
    }

    protected boolean isNoMoneyBlockingCallSituation() {
        if ("NO_MONEY".equals(getDialStatus()) && HangupCause.AST_CAUSE_CALL_REJECTED.equals(getHangupCause())) {
            return true;
        } else {
            return false;
        }
    }

    protected boolean isNoRouteCallSituation() {
        if ("NO_ROUTE".equals(getDialStatus()) && HangupCause.AST_CAUSE_CALL_REJECTED.equals(getHangupCause())) {
            return true;
        } else {
            return false;
        }
    }

    protected boolean isNotFoundCallSituation() {
        if ("NOT_FOUND".equals(getDialStatus()) && HangupCause.AST_CAUSE_FAILURE.equals(getHangupCause())) {
            return true;
        } else {
            return false;
        }
    }

    protected void amendMissingDetails() {
        // TODO Tally: At the moment, for VAPI calls. on the OUTBOUND part for SIP
        // destination, we do not receive:
        // GW, GW fail over attempt number, SIP fail over attempt number.
        // The GWS includes the value "default".
        //
        // In order to mitigate this and allow the call to be completed properly,
        // the following "bypass" was added.
        //
        // The whole values will be sent once we will handle the dial plan re-factoring
        if (Objects.nonNull(gatewaysList) && "default".equalsIgnoreCase(gatewaysList) && Objects.isNull(currentGateway)
                && Objects.isNull(gwsAttemptStr)) {

            currentGateway = gatewaysList;
            gwsAttemptStr = "1#1";
            currentGWAttempt = 1;
            availableGWAttempts = 1;

            if (Objects.isNull(fallbackAttemptStr) && Objects.isNull(fallbackAlternativesStr)) {

                fallbackAttemptStr = "1";
                fallbackAlternativesStr = "1";
                currentFallbackAttempt = 1;
                availableFallbackAlternatives = 1;
            }
        }

        // SIP-409 : Domains Service
        // A new case of rejecting the call due to NO-MONEY during the AGI request.
        if (isSipAppFailoverBlocked()) {
            if (Objects.nonNull(gwsAttemptStr) && gwsAttemptStr.contains("#")) {
                fallbackAttemptStr = "0";
                fallbackAlternativesStr = "0";
                currentFallbackAttempt = 0;
                availableFallbackAlternatives = 0;
            } else {
                fallbackAttemptStr = "1";
                fallbackAlternativesStr = "1";
                currentFallbackAttempt = 1;
                availableFallbackAlternatives = 1;
            }
        }
    }


    // SIP-222 ; When CANCEL is sent before the INVITE processing is completed,
    // there are cases
    // where the CDR Event userField is missing or include only partial details.
    // If we can verify that the call was probably cancelled we will reconstruct the
    // proper userField
    public boolean attemptFixingUserFieldData(CdrEvent event, String channelUniqueId) {

        boolean userFieldRestored = false;
        if (Log.isDebugEnabled()) {
            Log.debug("Attempting to fix user field data for unique ID: " + channelUniqueId);
        }

        // Depending on the INVITE handling and the CANCEL handling race condition, we
        // might be able to identify the
        // sessionId and the callId. Lets try and log it if possible:
        String origSessionDetails = getOriginSessionDetails(channelUniqueId);

        // Verify that indeed the call hasn't started and that there is no potential for
        // missing on chargeable call
        if ("AGI".equals(event.getLastApplication()) && "NO ANSWER".equals(event.getDisposition())
                && (0 == event.getDuration())) {

            Log.warn("CdrEvent is missing the userField or userField is partialy available. Assuming CANCEL. "
                    + origSessionDetails + " event: " + event);

            this.hangupCause = HangupCause.AST_CAUSE_NOANSWER;
            this.dialStatus = CdrEventHandler.DIAL_STATUS_CANCEL;
            this.channel2Id = null;
            this.nexmoUUID = null;

            this.gatewaysList = "Unknown-due-to-fast cancel";
            this.currentGateway = gatewaysList;
            this.gwsAttemptStr = "1#1";
            this.currentGWAttempt = 1;
            this.availableGWAttempts = 1;

            this.fallbackAttemptStr = "1";
            this.fallbackAlternativesStr = "1";
            this.currentFallbackAttempt = 1;
            this.availableFallbackAlternatives = 1;

            userFieldRestored = true;
        } else {
            Log.warn(
                    "CdrEvent is missing the userField or userField is partialy available. The event does not include the specific CANCEL-during-INVITE indicators. "
                            + origSessionDetails + " event: " + event
                            + " PLEASE VERIFY IN HEPIC WHAT IS GOING ON WITH THIS CALL.");
        }

        return userFieldRestored;
    }

    protected String getOriginSessionDetails(String channelUniqueId) {
        VoiceContext originChannelContext = Core.getInstance().getVoiceContextCache().getFirstContextWithConnectionId(channelUniqueId);

        String originSessionId = null;
        String originCallId = null;
        if (Objects.nonNull(originChannelContext)) {
            originSessionId = originChannelContext.getSessionId();
            if (Objects.nonNull(originChannelContext.getApplicationContext())
                    && originChannelContext.getApplicationContext() instanceof SIPAsteriskContext) // YAK!!!! update the
                // interface...
                originCallId = ((SIPAsteriskContext) (originChannelContext.getApplicationContext())).getClientCallId();
        }
        String origSessionDetails = " originSessionId: " + originSessionId + " originCallId: " + originCallId;
        return origSessionDetails;
    }

    //In case we return 402 (No money), 403 (No Route), 404 (Not found) There is no attempt to try
    //other SIPApp instance, and the CDR is the final event about this call.
    protected boolean isSipAppFailoverBlocked() {
        if (isNoMoneyBlockingCallSituation() ||
                isNoRouteCallSituation() ||
                isNotFoundCallSituation())
            return true;
        else
            return false;
    }


    @Override
    public String toString() {
        return "CdrEventUserData [hangupCause=" + hangupCause + ", dialStatus=" + dialStatus + ", channel2Id="
                + channel2Id + ", nexmoUUID=" + nexmoUUID + ", gatewaysList=" + gatewaysList + ", currentGateway="
                + currentGateway + ", gwsAttemptStr=" + gwsAttemptStr + ", currentGWAttempt=" + currentGWAttempt
                + ", availableGWAttempts=" + availableGWAttempts + ", fallbackAttemptStr=" + fallbackAttemptStr
                + ", fallbackAlternativesStr=" + fallbackAlternativesStr + ", currentFallbackAttempt="
                + currentFallbackAttempt + ", availableFallbackAlternatives=" + availableFallbackAlternatives
                + ", additionalParams=" + additionalParams + ", carrierPlatform=" + carrierPlatform
                + ", cdrEventBillableSeconds=" + cdrEventBillableSeconds + ", pddTimeMillis=" + pddTimeMillis
                + ", answeredDurationMillis=" + answeredDurationMillis + ", answeredDuration=" + answeredDuration + "]";
    }
}
