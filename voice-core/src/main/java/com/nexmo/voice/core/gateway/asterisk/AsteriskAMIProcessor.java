package com.nexmo.voice.core.gateway.asterisk;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import io.prometheus.client.Histogram;
import io.prometheus.client.Counter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.asteriskjava.manager.ManagerConnection;
import org.asteriskjava.manager.ManagerEventListener;
import org.asteriskjava.manager.SendActionCallback;
import org.asteriskjava.manager.TimeoutException;
import org.asteriskjava.manager.action.AbstractManagerAction;
import org.asteriskjava.manager.action.PingAction;
import org.asteriskjava.manager.event.ManagerEvent;
import org.asteriskjava.manager.response.ManagerResponse;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.billing.BillingManager;
import com.nexmo.voice.core.cache.ApplicationContext;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.gateway.asterisk.task.AsteriskTask;
import com.nexmo.voice.core.gateway.asterisk.task.AsteriskTaskExecutor;
import com.nexmo.voice.core.sip.event.AsteriskVoiceEventHandler;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.SIPCode;
import com.nexmo.voice.core.types.VoiceDirection;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;


public class AsteriskAMIProcessor implements ManagerEventListener, SendActionCallback {

    private final static Logger Log = LogManager.getLogger(AsteriskAMIProcessor.class);

    private final ManagerConnection managerConnection;

    private final AsteriskTaskExecutor executor;

    private final Map<String, AsteriskVoiceEventHandler<? extends ManagerEvent>> eventHandlerLookupMap;

    private static final Histogram EVENTS_SUMMARY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_ami_events_latency").help("Processing time for the AMI events").labelNames("event").register();
    private static final Counter DIRTY_SHUTDOWN_ACTIVE_CALLS = Counter.build().name("sipapp_dirty_shutdown_active_calls").help("Number of active calls during dirty shutdown").register();
    private static final Counter DIRTY_SHUTDOWNS = Counter.build().name("sipapp_dirty_shutdowns").help("Number of dirty shutdowns called").labelNames("request").register();

    private static final ExecutorService DIRTY_SHUTDOWN_POOL = Executors.newSingleThreadExecutor();

    public AsteriskAMIProcessor(final ManagerConnection managerConnection,
                                final AsteriskTaskExecutor executor,
                                Set<AsteriskVoiceEventHandler<? extends ManagerEvent>> eventHandlers) {
        this.managerConnection = managerConnection;
        this.managerConnection.addEventListener(this);

        Map<String, AsteriskVoiceEventHandler<? extends ManagerEvent>> handlersMap = new HashMap<>();
        if (eventHandlers != null)
            Log.info("Building the events handlers map:");
        for (AsteriskVoiceEventHandler<?> handler : eventHandlers) {
            Log.info(handler.getEventClass().getCanonicalName() + " => " + handler.getClass().getCanonicalName());
            handlersMap.put(handler.getEventClass().getCanonicalName(), handler);
        }
        Log.info("END Of Building the events handlers map:");

        this.eventHandlerLookupMap = Collections.unmodifiableMap(handlersMap);

        this.executor = executor;
    }

    public final ManagerConnection getManagerConnection() {
        return this.managerConnection;
    }

    public void shutdown() {
        Log.info("Shutdown request. Starting the ManagerConnection logoff");
        try {
            this.managerConnection.logoff();
            Log.info("ManagerConnection logoff completed");
        } catch (IllegalStateException ex) {
            Log.error("Error while shutting down the Asterisk connection!", ex);
        }
        Log.info("About to shutdown the executer");
        this.executor.shutdown();
        Log.info("Executer shutdown completed");
    }

    @Override
    public void onManagerEvent(ManagerEvent event) {
        String connectionId = getEventConnectionId(event);
        if(event.getClass().getCanonicalName().toLowerCase().contains("hangupevent")){
            Log.info("Received onManagerEvent {} for connectionId {} ", event, connectionId);
        }
        if (event != null && this.eventHandlerLookupMap.containsKey(event.getClass().getCanonicalName())) {

            Log.info("Received onManagerEvent {} for connectionId {} ", event, connectionId);
            if (Log.isDebugEnabled())
                Log.debug("Processing onManagerEvent {} for connectionId {} ", event, connectionId);

            if (connectionId == null)
                if (Log.isDebugEnabled())
                    Log.debug("Could not retrieve a connectionId associated with event ['" + event + "']");
            try {
                AsteriskEventTask eventTask = new AsteriskEventTask(connectionId, event);
                this.executor.execute(eventTask);
            } catch (Exception e) {
                Log.error("Processing onManagerEvent {} for connectionId {} rejected due to {}",
                        event, connectionId, e);
            }

        } else if (Log.isTraceEnabled())
            Log.trace("Ignoring event ['" + event + "']");
    }

    // ACTIONS

    public void tryExecuting(AbstractManagerAction action) throws Exception {
        tryExecuting(action, this);
    }

    public void tryExecuting(AbstractManagerAction action, SendActionCallback responseListener) throws Exception {
        if (responseListener == null)
            responseListener = this;
        String connectionId = getChannelInAction(action);
        if (connectionId == null) {
            connectionId = "0";
            Log.info("Could not retrieve a connectionId associated with action ['" + action + "']. Assigning general ['0']");
        }
        try {
            AsteriskActionTask actionTask = new AsteriskActionTask(connectionId, action, responseListener);
            this.executor.execute(actionTask);
        } catch (Exception e) {
            Log.error("Processing tryExecuting  for connectionId {} rejected due to {}", connectionId, e);
        }
    }

    @Override
    public void onResponse(ManagerResponse managerResponse) {
        // This receives asynchronous responses for the executed actions...
        Log.info("Response callback received ['" + managerResponse + "']");
        String uniqueId = managerResponse.getActionId(); // the custom actionId has been set to the originator channel's uniqueId

        String responseMessage = managerResponse.getResponse();
        if (!responseMessage.equals("Success"))
            Log.warn("Asterisk could not perform an action corresponding to the channel ['" + uniqueId + "']");

        //This is probably never happening, I do not think we have nexmoid as an attribute
        String nexmoIdAttribute = managerResponse.getAttribute("nexmoid");
        if (nexmoIdAttribute != null)
            Log.info("New session id ['" + nexmoIdAttribute + "'] assigned to channel ['" + uniqueId + "']");
    }

    //This method is called when there are connectivity issues between SIPApp and Asterisk.
    //I think this deserves a separate investigation, maybe as part of the Asterisk upgrade.
    //For now, I will just amend it to use the new BillingManager in the same way it used to it
    //with the old ChargableContext (R.I.P)
    //In any case - this was not handling the API product class - so added here as well


    //After the incident IM-720 this section is clearer now:
    //
    //SIPApp has a ping thread that check on Asterisk: AsteriskPingThread
    //When it detects that Asterisk is not responding, it should stop the not-started/on-going calls as we might never get their relevant
    //BridgeEvent and CDREvent. If we do not stop them, we will continue to bill the customer and CDRs will not be created.
    //Calls which are already ended and on final stages of concluding, can continue this process as they do not need anything more from Asterisk.
    //
    //If Asterisk is really down, then new calls will not arrive and eventually the number of handled calls
    //by this SIPApp instance will go down to zero.
    //When Asterisk will be up again, the calls that it handled on the previous run are gone, and their events will not appear again.
    //But if this was a false positive, Asterisk will continue to send BridgeEvents and CDREvents of such calls that their billing already
    //stopped - this is handled in the BridgeEvent and CDREvent

    public static synchronized void handleDirtyShutdown(){
        handleDirtyShutdown(CallInternalFlag.TERMINATED_ON_DIRTY_SHUTDOWN);
    }

    public static synchronized Future<?> handleDirtyShutdown(CallInternalFlag callInternalFlag) {
        Log.warn("==================== DIRTY SHUTDOWN TRIGGERED ====================");
        return DIRTY_SHUTDOWN_POOL.submit(() -> handleDirtyShutdownHelper(callInternalFlag));
    }

    private static synchronized void handleDirtyShutdownHelper(CallInternalFlag callInternalFlag) {
        Log.warn("==================== HANDLING DIRTY SHUTDOWN ====================");

        //Just as it is going to be bad anyway.. lets ping asterisk to see what is going on
        Log.info("During HANDLING DIRTY SHUTDOWN: about to ping Asterisk");
        pingAsterisk();

        VoiceContextCache contextCache = Core.getInstance().getVoiceContextCache();

        if (contextCache == null) {
            Log.error("Could not handle a dirty shutdown. VoiceContextCache was null!");

            // Increment number of dirty shutdown nullVoiceContext counter
            DIRTY_SHUTDOWNS.labels("nullVoiceContext").inc();

            return;
        }

        BillingManager billingManager = Core.getInstance().getBillingManager();

        //We need all the contexts - SIP, API, Verify, TTS - all the on-going calls
        //For each call we need each leg VoiceContext
        Collection<VoiceContext> contexts = contextCache.getAllContexts();
        //Find all the sessionIds:
        Set<String> ids = new HashSet<String>();
        for (VoiceContext ctx : contexts) {
            Log.info("DIRTY SHUTDOWN context: {}", ctx.getDebugString());
            ids.add(ctx.getSessionId());
        }
        Log.info("DIRTY SHUTDOWN: Active calls found: {} ", ids.size());

        //For each sessionId - find its one or two legs and handle them together
        for (String sessionId : ids) {
            Log.info("{} Handling DIRTY SHUTDOWN for all legs", sessionId);
            Collection<VoiceContext> contextsInSession = Core.getInstance().getVoiceContextCache().
                    getInnerValues(sessionId);

            for (VoiceContext contextInSession : contextsInSession) {
                Log.info("{} {} in DIRTY SHUTDOWN", contextInSession.getSessionId(), contextInSession.getConnectionId());
                if (!billingManager.emergencyStopCharging(contextInSession)) {
                    Log.info("{} {} direction {} emergency stop skipped", contextInSession.getSessionId(), contextInSession.getConnectionId(), contextInSession.getVoiceDirection().name());
                    continue;
                } else {
                    Log.info("{} {} direction {} emergency stop DONE", contextInSession.getSessionId(), contextInSession.getConnectionId(), contextInSession.getVoiceDirection().name());
                }

                generateCDRPerChannel(contextInSession, contextsInSession.size(), callInternalFlag);

            }

            // consider purging ...
            AsteriskVoiceEventHandler.cleanUpSession(sessionId);
        }

        // Increment prometheus dirty shutdown active calls counter by current active call count
        DIRTY_SHUTDOWN_ACTIVE_CALLS.inc(ids.size());

        // Increment dirty shutdown success counter
        DIRTY_SHUTDOWNS.labels("success").inc();
        Log.warn("==================== HANDLING DIRTY SHUTDOWN [finished] ====================");
    }


    private static void generateCDRPerChannel(VoiceContext contextInSession, int numberOfChannelsInSession, CallInternalFlag callInternalFlag) {

        SIPAsteriskContext appContext;
        ApplicationContext apctx = contextInSession.getApplicationContext();
        if (Objects.nonNull(apctx) && apctx instanceof SIPAsteriskContext) {
            appContext = (SIPAsteriskContext) contextInSession.getApplicationContext();
        } else {
            Log.warn("{} {} Generate CDR in DIRTY SHUTDOWN. SIPAsteriskContext not found. CDR will not be created. {} ",
                    contextInSession.getSessionId(),
                    contextInSession.getConnectionId(),
                    contextInSession.getDebugString());
            return;
        }

        Log.info("{} {} Generate CDR in DIRTY SHUTDOWN.  number of channels in session: {} SIPAsteriskContext: {}",
                contextInSession.getSessionId(),
                contextInSession.getConnectionId(),
                numberOfChannelsInSession, appContext.getDebugString());
        
        contextInSession.addInternalFlag(callInternalFlag);
        //Check the regular logic of generating CDR at the CDREventHandler:
        //
        //If !auxiliary:
        //
        // If we are looking at the FIRST leg:
        //                     OUTBOUND => auxiliary=true - Do not create CDR 
        //                                                  (??!! - I suspect this never happens as we never start a call without first
        //                                                   getting a request from customer or NCCO and then it wont be the first leg to
        //                                                   be the outbound call)
        //                     INBOUND => auxiliary=false - Create CDR
        // 
        // If we are looking at the SECOND leg:
        //            the first leg was NOT routing to an application => auxiliary=false  - Create CDR                                    
        //            the first leg had routing to an application => auxiliary=true - Do not create CDR
        //
        Log.info("{} {} DIRTY SHUTDOWN: appContext.isAuxiliary={} numberOfChannelsInSession={}",
                contextInSession.getSessionId(), contextInSession.getConnectionId(), appContext.isAuxiliary(), numberOfChannelsInSession);

        if (!appContext.isAuxiliary() || numberOfChannelsInSession == 1) {

            // generating a CDR 
            if (contextInSession.getVoiceDirection() == VoiceDirection.OUTBOUND) {
                Log.info("{} {} DIRTY SHUTDOWN: About to log OUTBOUND CDR", contextInSession.getSessionId(), contextInSession.getConnectionId());
                Core.getInstance().getOutboundCallLoggerController(contextInSession.getVoiceProduct()).logEmergencyStoppedCall(contextInSession,
                        "ANSWER",
                        SIPCode.REQUEST_TERMINATED);
            } else {
                Log.info("{} {} DIRTY SHUTDOWN: About to log INBOUND CDR", contextInSession.getSessionId(), contextInSession.getConnectionId());
                Core.getInstance().getInboundCallLoggerController(contextInSession.getVoiceProduct()).logEmergencyStoppedCall(contextInSession,
                        "ANSWER",
                        SIPCode.REQUEST_TERMINATED);
            }

        } else {
            Log.info("{} {} DIRTY SHUTDOWN SKIP CDR Creation",
                    contextInSession.getSessionId(), contextInSession.getConnectionId());
        }
    }

    private static String getEventConnectionId(ManagerEvent event) {
        String uniqueId = null;
        Method getUniqueIdMethod = null;
        Method[] methods = event.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("getUniqueId")) {
                getUniqueIdMethod = method;
                break;
            }
        }
        if (getUniqueIdMethod == null) {
            Log.warn("Could not find a method starting by getUniqueId for event ['" + event + "']");
            return null;
        }

        try {
            uniqueId = (String) getUniqueIdMethod.invoke(event);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
            Log.error("Something went wrong trying to execute getUniqueId() method for event ['" + event + "']");
            return null;
        }
        return uniqueId;
    }

    private static String getChannelInAction(AbstractManagerAction action) {
        String channelId = null;
        Method getChannelMethod = null;
        try {
            getChannelMethod = action.getClass().getMethod("getChannel");
        } catch (NoSuchMethodException e) {
            if (Log.isDebugEnabled())
                Log.debug("Action ['" + action + "'] has no getChannel method.", e);
            return null;
        } catch (SecurityException e) {
            Log.error("Could not access to getChannel method.", e);
            return null;
        }
        if (getChannelMethod != null)
            try {
                channelId = (String) getChannelMethod.invoke(action);
            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                Log.error("Something went wrong trying to execute getChannel() method for action ['" + action + "']");
                return null;
            }
        return channelId;
    }

    private class AsteriskEventTask extends AsteriskTask {

        private final ManagerEvent event;

        public AsteriskEventTask(String connectionId, ManagerEvent event) {
            super(connectionId, String.valueOf(event.hashCode()));
            this.event = event;
            if (Log.isDebugEnabled())
                Log.debug("EventTask was created with connectionId: " + connectionId + " and event " + event);
        }

        @Override
        public void run() {
            final String eventCanonicalName = this.event.getClass().getCanonicalName();
            final long timer = System.nanoTime();
            if (Log.isTraceEnabled())
                Log.trace("Received AMI Event [className: " + eventCanonicalName +
                        " hashCode=" + this.event.hashCode() + " ]");

            try {
                AsteriskVoiceEventHandler<?> handler = AsteriskAMIProcessor.this.eventHandlerLookupMap.get(eventCanonicalName);
                if (handler == null) {
                    if (Log.isDebugEnabled())
                        Log.debug("Unsupported AMI Event [className: " + eventCanonicalName +
                                " hashCode=" + this.event.hashCode() + " ] - will be ignored");
                    return;
                }
                if (Log.isDebugEnabled())
                    Log.debug("Handling AMI Event [className: " + eventCanonicalName +
                            " hashCode=" + this.event.hashCode() + " ]");
                handler.tryHandling(this.event);
            } catch (VoiceEventHandlerException ex) {
                Log.error("Error occurred while handling event [className: " + eventCanonicalName + " hashCode=" + this.event.hashCode() + " ] " + ex.getMessage());
            }
            finally {
                EVENTS_SUMMARY.labels(this.event.getClass().getSimpleName()).observe((System.nanoTime() - timer)/ NANOSECONDS_PER_SECOND);
            }
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("AsteriskEventTask [event=");
            builder.append(event);
            builder.append(", ");
            builder.append(super.toString());
            builder.append("]");
            return builder.toString();
        }


    }

    public class AsteriskActionTask extends AsteriskTask {

        private final AbstractManagerAction action;
        private final SendActionCallback responseListener;

        public AsteriskActionTask(String connectionId,
                                  AbstractManagerAction action,
                                  SendActionCallback responseListener) {
            super(connectionId);
            this.action = action;
            this.responseListener = responseListener;
            if (Log.isDebugEnabled())
                Log.debug("ActionTask was created with connectionId: " + connectionId + " and action " + action.getAction());
        }

        @Override
        public void run() {
            if (Log.isDebugEnabled())
                Log.debug("AsteriskAMIProcessor: Sending action: " + this.action);
            try {
                AsteriskAMIProcessor.this.managerConnection.sendAction(this.action, this.responseListener);
            } catch (IllegalArgumentException | IllegalStateException | IOException e) {
                Log.error("Error trying to send action ['" + this.action + "'] to Asterisk through Manager Connection");
            }
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("AsteriskActionTask [action=");
            builder.append(action);
            builder.append(", responseListener=");
            builder.append(responseListener);
            builder.append(", ");
            builder.append(super.toString());
            builder.append("]");
            return builder.toString();
        }

    }

    //Added here for debug purposes
    private static void pingAsterisk() {
        ManagerConnection managerConnection = Core.getInstance().getManagerConnection();
        if (Objects.isNull(managerConnection)) {
            Log.info("During HANDLING DIRTY SHUTDOWN: the ManagerConnection is null - no ping...");
            return;
        }
        try {
            final ManagerResponse response = managerConnection.sendAction(new PingAction(), 3000);
            Log.info("During HANDLING DIRTY SHUTDOWN: Ping response '" + response + "' for " + managerConnection.toString());
        } catch (TimeoutException e) {
            Log.info("During HANDLING DIRTY SHUTDOWN: Ping Thread timed out waiting for response from Asterisk's manager connection ['" + managerConnection.toString() + "']", e);
        } catch (IllegalArgumentException | IllegalStateException | IOException e) {
            Log.info("During HANDLING DIRTY SHUTDOWN: Exception on sending Ping to " + managerConnection.toString(), e);
        }
    }


}
