package com.nexmo.voice.core.billing;

import java.math.BigDecimal;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * This class include the information required for the quota update, or an indication that
 * quota update is not required.
 * <p>
 * If quota update is required, the operation would be either CONSUME or REFUND, and the amount would be
 * the amount to use for this operation.
 */

public final class QuotaUpdateDetails {

    private static final Logger Log = LogManager.getLogger(QuotaUpdateDetails.class);


    private final BigDecimal amount;
    private final Operation operation;
    private final Long duration;


    public QuotaUpdateDetails(Operation operation, BigDecimal amount, Long duration) {
        this.operation = operation;
        this.amount = amount;
        this.duration = duration;
        if (Log.isDebugEnabled())
            Log.debug("QuotaUpdateDetails was created with operation={} amount={} duration={}",
                    operation,
                    Objects.isNull(amount) ? "null" : amount.toPlainString(),
                    Objects.isNull(duration) ? "null" : duration);
    }


    public static QuotaUpdateDetails skipQuotaUpdate() {
        return new QuotaUpdateDetails(Operation.SKIP_QUOTA_UPDATE, null, null);
    }

    public boolean shouldSkipQuotaUpdate() {
        return Operation.SKIP_QUOTA_UPDATE.equals(this.operation);
    }

    public boolean shouldRefund() {
        return Operation.REFUND.equals(this.operation);
    }

    public boolean shouldConsume() {
        return Operation.CONSUME.equals(this.operation);
    }


    public BigDecimal getAmount() {
        return this.amount;
    }

    public Long getDuration() {
        return this.duration;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("QuotaUpdateDetails [operation=");
        builder.append(operation.name());
        builder.append(", amount=");
        builder.append(Objects.nonNull(amount) ? amount.toPlainString() : "null");
        builder.append(", duration=");
        builder.append(Objects.nonNull(duration) ? duration : "null");
        builder.append("]");
        return builder.toString();
    }

    /***
     * QuotaUpdateDetails required operation
     */

    public static enum Operation {
        SKIP_QUOTA_UPDATE("skip-quota-update"), //Quota update is not required
        CONSUME("consume"), //The provided amount should be consumed
        REFUND("refund"), //The provided amount should be refunded
        QUERY("query"); //Quota query

        private final String value;

        Operation(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}