package com.nexmo.voice.core.callinfo.dynamodb;

import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.Instant;
import java.util.Objects;
import java.util.concurrent.Future;


import com.amazonaws.ClientConfiguration;
import com.amazonaws.handlers.AsyncHandler;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBAsyncClientBuilder;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBAsync;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.*;
import com.nexmo.voice.config.dynamodb.DynamoDbConfig;
import com.nexmo.voice.core.Core;
import io.prometheus.client.Gauge;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


// Class to abstract access to DynamoDb
public class DynamoDB {
    private static final Logger Log = LogManager.getLogger(DynamoDB.class);

    private final AmazonDynamoDB client = AmazonDynamoDBClientBuilder.defaultClient();
    // Since we use aws sdk 1.12 we cannot override configuration on query level, instead we need dedicated client
    // for it -> adjust after upgrade sdk.
    private AmazonDynamoDB findItemClient;
    private final AmazonDynamoDBAsync asyncClient = AmazonDynamoDBAsyncClientBuilder.defaultClient();
    protected Region region = Regions.getCurrentRegion();
    protected String table = "rtc-voice-callinfo-cli"; // get it from config or env
    protected String APIKEY = "apikey_cli";
    protected String SESSION_ID = "session_id";
    protected String EXTENSION = "extension";

    protected String APPLICATION_ID = "application_id";
    protected String CREATE_DATE = "create_datetime";
    protected String UPDATE_DATE = "update_datetime";
    protected String EXPIRES = "expires";
    protected long ttl;

    private static final Gauge DYNAMO_DB_LATENCY = Gauge.build()
            .name("rtc_dependency_dynamodb_latency")
            .help("Latency of Dynamo DB read operations in seconds")
            .labelNames("query")
            .register();

    // default constructor
    public DynamoDB() {
        DynamoDbConfig dynamodbConfig = Core.getInstance().getConfig().getDynamoDbConfig();
        this.ttl = dynamodbConfig.getTimeToLive();
        int findItemTimeout = dynamodbConfig.getFindItemTimeout();
        this.findItemClient = buildTimeoutClient(findItemTimeout);
        Log.info("DynamoDB ttl " + this.ttl + ", deleteAfterFind:" + dynamodbConfig.getDeleteAfterFindItem() + ", " +
                "finItemRequestTimeout:" + findItemTimeout);
    }

    // Supports passing a DynamoDB region (for using the public AWS DynamoDB API)
    public DynamoDB(String tableName, long timeToLive, int findItemTimeoutTimeout) {
        this.table = tableName;
        this.ttl = timeToLive;
        this.findItemClient = buildTimeoutClient(findItemTimeoutTimeout);
    }

    private AmazonDynamoDB buildTimeoutClient(int requestTimeout) {
        ClientConfiguration timeoutConfiguration = new ClientConfiguration();
        // in contrast to request timeout, execution timeout allows to control the overall execution time of an
        // application-level operation (end-to-end)
        // NOTE: sdk doesn't have strict guarantees on how quickly a request is aborted in some rare cases it may
        // take several seconds
        timeoutConfiguration.setClientExecutionTimeout(requestTimeout);
        return AmazonDynamoDBClientBuilder.standard()
                .withClientConfiguration(timeoutConfiguration)
                .build();
    }

    // Shut down the client
    public void shutdown() {
        this.client.shutdown();
        this.asyncClient.shutdown();
    }

    public boolean putItem(String apikeyCli, String extension, String applicationId, String sessionId) {
        boolean res = true;
        // tries to insert an item with apikeyCli value in async mode
        // if a records exists with that key, it will update other attributes
        Log.debug("DynamoDB inserting record:" + apikeyCli);
        HashMap<String, AttributeValue> itemValues = new HashMap<String, AttributeValue>();

        LocalDateTime dateTime = LocalDateTime.now();
        Instant instant = Instant.now();
        long timeStampMillis = instant.toEpochMilli();
        // Add all content to the table
        itemValues.put(APIKEY, new AttributeValue().withS(apikeyCli));
        itemValues.put(EXTENSION, new AttributeValue().withS(extension));
        itemValues.put(SESSION_ID, new AttributeValue().withS(applicationId));
        itemValues.put(SESSION_ID, new AttributeValue().withS(sessionId));
        itemValues.put(CREATE_DATE, new AttributeValue().withS(dateTime.toString()));
        itemValues.put(UPDATE_DATE, new AttributeValue().withS(dateTime.toString()));
        itemValues.put(EXPIRES, new AttributeValue().withN(String.valueOf(timeStampMillis + (this.ttl * 1000))));

        PutItemRequest request = new PutItemRequest(this.table, itemValues);

        try {
            PutItemResult result = this.client.putItem(request);
            Log.info("PutItemRequest {} successfully added", request.toString());
            Log.info("PutItem result is: {}", result.toString());

        } catch (ResourceNotFoundException e) {
            Log.error("Error: The Amazon DynamoDB table {} can't be found", this.table);
            res = false;
        } catch (AmazonDynamoDBException e) {
            Log.error("Error in adding item: {}", e.getMessage());
            res = false;
        }
        return res;
    }

    public boolean deleteItem(String apikeyCli) {
        // tries to insert an item with apikeyCli value in async mode
        // if a records exists with that key, it will update other attributes
        HashMap<String,AttributeValue> itemValues = new HashMap<String,AttributeValue>();

        // Add all content to the table
        itemValues.put("apikey_cli", new AttributeValue().withS(apikeyCli));

        DeleteItemRequest request = new DeleteItemRequest(this.table, itemValues);

        try {
            DeleteItemResult result = this.client.deleteItem(request);
            Log.info("DeleteItemRequest {} successfully added", request.toString());
            Log.info("DeleteItem result is: {}", result.toString());

        } catch (ResourceNotFoundException e) {
            Log.error("Error: The Amazon DynamoDB table {} can't be found", this.table);
            return(false);
        } catch (AmazonDynamoDBException e) {
            Log.error("Error: error in deleting item: {}", e.getMessage());
            return(false);
        }
        return true;
    }

    /**
     * Gets one or more items as a List per the query
     *
     * @return {@link GetItemResult} or null if nothing is found
     * @throws GetItemException in case of query timeout or any other DB exceptions
     */
    public GetItemResult findItem(String key, String sessionId) {
        HashMap<String, AttributeValue> itemValues = new HashMap<>();
        itemValues.put(APIKEY, new AttributeValue().withS(key));

        GetItemRequest request = new GetItemRequest(table, itemValues, true);
        Gauge.Timer timer = DYNAMO_DB_LATENCY.labels("findItem").startTimer();
        try {
            GetItemResult result = this.findItemClient.getItem(request);
            Log.info("GetItem result is: {}", result.toString());
            return result;
        } catch (ResourceNotFoundException e) {
            Log.error("SESSION-ID ['{}'] Error: The Amazon DynamoDB table {} can't be found", sessionId, this.table);
            return null;
        } catch (Exception e) {
            Log.error("SESSION-ID ['{}'] Error: error in querying item: {}", sessionId, e.getMessage());
            throw new GetItemException(e);
        } finally {
            timer.setDuration();
        }
    }

    // Get the extension from item
    public String getExtension(GetItemResult result) {

        if (Objects.isNull(result) || Objects.isNull(result.getItem()) || result.getItem().isEmpty()) {
            return null;
        }
        Log.info("DynamoDB GetItem result is: {}", result.toString());
        java.util.Map<String,AttributeValue> returnedItem = result.getItem();
        java.util.Set<String> fields = returnedItem.keySet();

        for (String field : fields) {
            if (field.equals(EXTENSION) && Objects.nonNull(returnedItem.get(field)))
                Log.info("returning extension: {}", returnedItem.get(field).getS().toString());
                return returnedItem.get(field).getS().toString();
        }
        return null;
    }

    // Get the session_id from item
    public String getSessionId(GetItemResult result) {
        if (Objects.isNull(result) || Objects.isNull(result.getItem()) || result.getItem().isEmpty()) {
            return null;
        }
        Log.info("DynamoDB GetItem result is: {}", result.toString());
        java.util.Map<String,AttributeValue> returnedItem = result.getItem();
        java.util.Set<String> fields = returnedItem.keySet();

        for (String field : fields) {
            if (field.equals(SESSION_ID) && Objects.nonNull(returnedItem.get(field)))
                Log.info("returning sessionId: {}", returnedItem.get(field).getS().toString());
            return returnedItem.get(field).getS().toString();
        }
        return null;
    }

    public String getApplicationId(GetItemResult result) {
        if (Objects.isNull(result) || Objects.isNull(result.getItem()) || result.getItem().isEmpty()) {
            return null;
        }
        Log.info("DynamoDB GetItem result is: {}", result.toString());
        java.util.Map<String,AttributeValue> returnedItem = result.getItem();
        java.util.Set<String> fields = returnedItem.keySet();

        for (String field : fields) {
            if (field.equals(APPLICATION_ID) && Objects.nonNull(returnedItem.get(field)))
                Log.info("returning applicationId: {}", returnedItem.get(field).getS().toString());
            return returnedItem.get(field).getS().toString();
        }
        return null;
    }

    public boolean putItemAsync(String apikeyCli, String extension, String applicationId, String sessionId) {
        // tries to insert an item with apikeyCli value in async mode
        // if a records exists with that key, it will update other attributes
        HashMap<String,AttributeValue> itemValues = new HashMap<String,AttributeValue>();

        LocalDateTime dateTime = LocalDateTime.now();
        Instant instant = Instant.now();
        long numberTimeStamp = instant.getEpochSecond() + this.ttl;

        // Add all content to the table
        itemValues.put(APIKEY, new AttributeValue().withS(apikeyCli));
        itemValues.put(EXTENSION, new AttributeValue().withS(extension));
        itemValues.put(SESSION_ID, new AttributeValue().withS(applicationId));
        itemValues.put(SESSION_ID, new AttributeValue().withS(sessionId));
        itemValues.put(CREATE_DATE, new AttributeValue().withS(dateTime.toString()));
        itemValues.put(UPDATE_DATE, new AttributeValue().withS(dateTime.toString()));
        itemValues.put(EXPIRES, new AttributeValue().withN(Long.toString(numberTimeStamp)));

        PutItemRequest request = new PutItemRequest(this.table, itemValues);
        try {
            java.util.concurrent.Future<PutItemResult> result = this.asyncClient.putItemAsync(
                    request, new AsyncHandler<PutItemRequest, PutItemResult>() {
                @Override
                public void onError(Exception exception) {
                    Log.error("putItemAsync::onError " + exception.getMessage());
                }

                @Override
                public void onSuccess(PutItemRequest request, PutItemResult putItemResult) {
                    Log.info("putItemAsync::onSuccess result:" + putItemResult.toString());
                }
            });
            Log.info(request + " was successfully sent");

        } catch (ResourceNotFoundException e) {
            Log.error("Error: The Amazon DynamoDB table {} can't be found.", this.table);
            return(false);
        } catch (AmazonDynamoDBException e) {
            Log.error(e.getMessage());
            return(false);
        }
        return true;
    }

    public boolean deleteItemAsync(String apikeyCli) {

        DynamoDbConfig config = Core.getInstance().getConfig().getDynamoDbConfig();
        if (!config.getDeleteAfterFindItem()) {
            Log.info("deleteItemAsync: delete is disabled, ttl will take care of record deletion apiKeyCli {}", apikeyCli);
            return false;
        }

        // tries to insert an item with apikeyCli value in async mode
        // if a records exists with that key, it will update other attributes
        HashMap<String,AttributeValue> itemValues = new HashMap<String,AttributeValue>();

        // Add all content to the table
        itemValues.put(APIKEY, new AttributeValue().withS(apikeyCli));
        DeleteItemRequest request = new DeleteItemRequest(this.table, itemValues);
        try {
            Future<DeleteItemResult> result = this.asyncClient.deleteItemAsync(request, new AsyncHandler<DeleteItemRequest, DeleteItemResult>() {

                public void onError(Exception exception) {
                    Log.error("deleteItemAsync::onError " + exception.getMessage());
                }

                @Override
                public void onSuccess(DeleteItemRequest request, DeleteItemResult putItemResult) {
                    Log.info("deleteItemAsync::onSuccess result:" + putItemResult.toString());
                }
            });
            Log.info(request + " was successfully submitted to be deleted");

        } catch (ResourceNotFoundException e) {
            Log.error("Error: The Amazon DynamoDB table {} can't be found.", this.table);
            return(false);
        } catch (AmazonDynamoDBException e) {
            Log.error("Error: error in deleting item: {}", e.getMessage());
            return(false);
        }
        return true;
    }
}
