package com.nexmo.voice.core.jmx;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.caches.CacheControlConfig;
import com.nexmo.voice.config.caches.CacheType;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.application.Application;
import com.nexmo.voice.core.cache.ApplicationCache;

import com.thepeachbeetle.common.xml.XmlUtil;


/**
 * <AUTHOR>
 */
public class CachesJMX implements CachesJMXMBean {

    private static final Logger Log = Logger.getLogger(CachesJMX.class.getName());

    @Override
    public boolean isEnabled() {
        final Config config = Core.getInstance().getConfig();
        final CacheControlConfig cachesConfig = config.getCachesConfig();

        return cachesConfig.isEnabled();
    }

    @Override
    public void setEnabled(boolean value) {
        Log.info("About to change cache config");

        final Core core = Core.getInstance();
        final Config config = core.getConfig();
        final CacheControlConfig cachesConfig = config.getCachesConfig();

        CacheControlConfig newConfig = cachesConfig.cloneWithEnabled(value);
        core.getConfig().setCachesConfig(newConfig);
        core.initializeCaches();

        Log.info("Cache config changed");
    }

    @Override
    public String[] getCaches() {
        final Core core = Core.getInstance();
        final Config config = core.getConfig();
        final CacheControlConfig cachesConfig = config.getCachesConfig();

        final Set<CacheType> caches = cachesConfig.getCachesPresent();
        final String[] ret = new String[caches.size()];
        int idx = 0;
        for (CacheType type : caches) {
            ret[idx++] = type.toString();
        }
        return ret;
    }

    @Override
    public String viewCachesConfig() {
        Log.info("About to fetch cache config");

        final Config config = Core.getInstance().getConfig();
        final CacheControlConfig cachesConfig = config.getCachesConfig();

        final StringBuilder sb = new StringBuilder();
        sb.append("<br><br>\n<pre>\n");
        String xml = cachesConfig.toString();
        String escaped = XmlUtil.XMLClean(xml);
        sb.append(escaped);
        sb.append("</pre>\n");

        return sb.toString();
    }

    @Override
    public String viewCacheEntries(String cacheType) {
        Log.info("About to fetch cache contents for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        final Map<String, Application> applications = cache.getApplicationsCache();
        if (applications != null) {
            sb.append("<table id=\"cache-entries\" style=\"width:100%\">\n");
            sb.append("<tr><th>Key</th><th>Value</th></tr>\n");
            for (Map.Entry<String, Application> e : applications.entrySet()) {
                sb.append("<tr><td>");
                sb.append(e.getKey());
                sb.append("</td><td>");
                sb.append(e.getValue());
                sb.append("</td></tr>\n");
            }
            sb.append("</table>");
        }
        return sb.toString();
    }

    @Override
    public String flushCache(String cacheType) {
        Log.info("About to flush cache contents for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            cache.flushCache();
            sb.append("SUCCESS!");
        } catch (Exception ex) {
            Log.warn("Unable to flush " + type + " cache", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String updateCacheSize(String cacheType, int size) {
        Log.info("About to update cache size for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            cache.updateSize(size);
            sb.append("SUCCESS!");
        } catch (Exception ex) {
            Log.warn("Unable to change " + type + " cache size", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String updateCacheExpiry(String cacheType, String period) {
        Log.info("About to change cache expiry for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            cache.updateExpiry(period);
            sb.append("SUCCESS!");
        } catch (Exception ex) {
            Log.warn("Unable to change " + type + " cache expiry period", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String updateCacheRefresh(String cacheType, String period) {
        Log.info("About to change cache refresh for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            cache.updateRefresh(period);
            sb.append("SUCCESS!");
        } catch (Exception ex) {
            Log.warn("Unable to change " + type + " cache refresh period", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String viewCacheMode(String cacheType) {
        Log.info("About to view cache mode for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            sb.append(type);
            sb.append(" mode is ");
            sb.append(cache.getMode());
        } catch (Exception ex) {
            Log.warn("Unable to change " + type + " mode", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String updateCacheMode(String cacheType, String modeType) {
        Log.info("About to change cache mode for: " + cacheType);

        CacheType type = CacheType.from(cacheType);
        if (type != CacheType.APPLICATION)
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        try {
            cache.setMode(modeType);
            sb.append("SUCCESS!");
        } catch (Exception ex) {
            Log.warn("Unable to change " + type + " mode", ex);
            sb.append("ERROR: ");
            sb.append(ex.toString());
        }
        return sb.toString();
    }

    @Override
    public String lookupApplication(String id) {
        Log.info("About to do an Application Lookup for ID: " + id);

        final ApplicationCache cache = Core.getInstance().getApplicationsCache();

        final StringBuilder sb = new StringBuilder();
        sb.append(id);
        sb.append("=>");
        try {
            final Application app = cache.getApplication(id); // Return from cache *or* make calls
            sb.append(app);
        } catch (Exception ex) {
            Log.warn("Exception during Application lookup", ex);
            sb.append("EXCEPTION! ");
            sb.append(ex);
        }
        return sb.toString();
    }

}
