package com.nexmo.voice.core.domains;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.CallbackType;

public class DomainRoutingResponse {

    public final static String DOMAIN_SERVICE_RETURN_CODE_SUCCESS = "0";
    public final static String DOMAIN_SERVICE_RETURN_CODE_UNKNOWN_FAILURE = "999";

    private String returnCode;
    private String codeDescription;
    private List<String> uris;
    private String domainName;
    private String siptrunkRegion;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getCodeDescription() {
        return codeDescription;
    }

    public void setCodeDescription(String codeDescription) {
        this.codeDescription = codeDescription;
    }

    public List<String> getUris() {
        return uris;
    }

    public void setUris(List<String> uris) {
        this.uris = uris;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getDomainName() {
        return domainName;
    }

    public String getSiptrunkRegion() {
        return siptrunkRegion;
    }

    public void setSiptrunkRegion(String siptrunkRegion) {
        this.siptrunkRegion = siptrunkRegion;
    }

    public static DomainRoutingResponse fromJSON(JSONObject json) {
        String returnCode = "";
        String codeDescription = "";
        String siptrunkRegion = "";
        List<String> uris = new ArrayList<>();
        if (json.has("returnCode"))
            returnCode = json.getString("returnCode");
        if (json.has("codeDescription"))
            codeDescription = json.getString("codeDescription");
        if (json.has("uris")) {
            JSONArray uri_array = json.getJSONArray("uris");
            for(int i = 0; i < uri_array.length(); i++) {
                uris.add(uri_array.getString(i));
            }

        }
        if (json.has("siptrunk_region")) {
            siptrunkRegion = json.getString("siptrunk_region");
        }
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setReturnCode(returnCode);
        response.setCodeDescription(codeDescription);
        response.setUris(uris);
        response.setSiptrunkRegion(siptrunkRegion);
        return response;
    }

    public static DomainRoutingResponse fromJSON(JSONObject json, String domain) {
        DomainRoutingResponse response = fromJSON(json);
        response.setDomainName(domain);
        return response;
    }

    public static DomainRoutingResponse unspecifiedFailure() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setReturnCode(DOMAIN_SERVICE_RETURN_CODE_UNKNOWN_FAILURE);
        response.setCodeDescription("Unknown Failure");
        return response;
    }

    public boolean isSuccess() {
        return DOMAIN_SERVICE_RETURN_CODE_SUCCESS.equals(getReturnCode());
    }

    @Override
    public String toString() {
        return "DOMAINS SERVICE:: Routing[" +
                "domainName='" + domainName + '\'' +
                ", returnCode='" + returnCode + '\'' +
                ", codeDescription='" + codeDescription + '\'' +
                ", uris=" + uris +
                ", siptrunkRegion=" + siptrunkRegion +
                ']';
    }

    public CallbackType getForwardsTo() {
        if(!uris.isEmpty()) {
            if(uris.get(0).startsWith("sip:")) {
                return CallbackType.SIP;
            }
            if(uris.get(0).startsWith("sips:")) {
                return CallbackType.SIP;
            }
            if(uris.get(0).startsWith("app://")) {
                return CallbackType.APPLICATION;
            }
        }
        return CallbackType.UNKNOWN;
    }

    /**
     * Returns the list of URIs with the sip or sips scheme prefix removed
     * @return list of cleaned URIs
     */
    public List<String> getUrisWithoutScheme() {
        if(uris != null) {
            return uris.stream().map(this::cleanSchemePrefix).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private String cleanSchemePrefix(String uri) {
        if(uri == null) {
            return null;
        }
        if (uri.startsWith("sips:")) {
            return uri.substring(5);
        }
        if (uri.startsWith("sip:")) {
            return uri.substring(4);
        }
        if(uri.startsWith("app://")) {
            return uri.substring(6);
        }
        return uri;
    }

}
