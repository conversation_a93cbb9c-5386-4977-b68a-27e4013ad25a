package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class PriceImpactApiConfigLoader extends NestedXmlHandler {
    private String host;
    private String basePath;
    private int timeout;

    private PriceImpactApiConfig config;

    public PriceImpactApiConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public PriceImpactApiConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) {
            this.host = xmlContent.getAttribute(PriceImpactApiConfig.API_HOST_URL_ATTR, true);
            this.basePath = xmlContent.getAttribute(PriceImpactApiConfig.API_BASE_PATH_ATTR, true);
            this.timeout = parseInt(xmlContent.getAttribute(PriceImpactApiConfig.API_TIMEOUT_ATTR, true));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            config = new PriceImpactApiConfig(this.host, this.basePath, this.timeout);
            notifyComplete();
        }
    }
}
