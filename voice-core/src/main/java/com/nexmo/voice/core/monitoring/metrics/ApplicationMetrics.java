package com.nexmo.voice.core.monitoring.metrics;

import java.util.Calendar;
import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.VoiceContext;

public class ApplicationMetrics {

    private static final Logger Log = Logger.getLogger(ApplicationMetrics.class);

    private final ConcurrentMap<String, GatewayMetrics> metricByGateway;

    private final GlobalMetrics globalMetrics;

    private final ScheduledExecutorService metricUpdaterPool;

    public ApplicationMetrics() {
        this.metricByGateway = new ConcurrentHashMap<>();
        this.globalMetrics = new GlobalMetrics();
        this.metricUpdaterPool = Executors.newScheduledThreadPool(1);
    }

    public void init() {
        this.metricUpdaterPool.scheduleAtFixedRate(new UpdateChecker(this.metricByGateway, this.globalMetrics),
                                                   0,
                                                   15,
                                                   TimeUnit.SECONDS);
    }

    public void shutdown() {
        final boolean shutdownOk;
        try {
            this.metricUpdaterPool.shutdown();
            shutdownOk = this.metricUpdaterPool.awaitTermination(1, TimeUnit.SECONDS);
            if (!shutdownOk)
                Log.error("FAILED to shutdown the metrics updater pool properly...");
        } catch (InterruptedException e) {
            Log.error("Something horribly wrong happened", e);
            return;
        }
    }

    public GatewayMetrics getGatewayMetrics(String gatewayName) {
        if (gatewayName == null)
            return null;

        GatewayMetrics gatewayMetrics = this.metricByGateway.get(gatewayName);
        if (gatewayMetrics == null) {
            gatewayMetrics = new GatewayMetrics(gatewayName);
            GatewayMetrics existing = this.metricByGateway.putIfAbsent(gatewayName, gatewayMetrics);
            if (existing != null)
                gatewayMetrics = existing;
        }
        return gatewayMetrics;
    }

    // This is to prevent any monitoring commands from creating non-existent gateway metrics
    public boolean containsGatewayMetrics(String gatewayName) {
        return this.metricByGateway.containsKey(gatewayName);
    }

    public Collection<GatewayMetrics> getAllGatewayMetrics() {
        return this.metricByGateway.values();
    }

    public GlobalMetrics getGlobalMetrics() {
        return this.globalMetrics;
    }

    public void logCall() {
        this.globalMetrics.logCall();
    }

    public void logErrorCall() {
        this.globalMetrics.logErrorCall();
    }

    public void logSuccess(VoiceContext ctx) {
        logCall();
        GatewayMetrics metrics = Core.getInstance().getApplicationMetrics().getGatewayMetrics(ctx.getCurrentGateway());
        if (metrics != null)
            metrics.logSuccess();
    }

    public void logMachine(VoiceContext ctx) {
        logErrorCall();
        GatewayMetrics metrics = Core.getInstance().getApplicationMetrics().getGatewayMetrics(ctx.getCurrentGateway());
        if (metrics != null)
            metrics.logMachine();
    }

    public void logError(VoiceContext ctx) {
        logErrorCall();
        GatewayMetrics metrics = Core.getInstance().getApplicationMetrics().getGatewayMetrics(ctx.getCurrentGateway());
        if (metrics != null)
            metrics.logError();
    }

    private static final class UpdateChecker implements Runnable {

        private int lastMinuteUpdateTime;
        private int lastHourUpdateTime;
        private int lastDayUpdateTime;

        private final ConcurrentMap<String, GatewayMetrics> metrics;
        private final GlobalMetrics globalMetrics;

        public UpdateChecker(ConcurrentMap<String, GatewayMetrics> metrics,
                             GlobalMetrics globalMetrics) {
            Calendar now = Calendar.getInstance();
            this.metrics = metrics;
            this.globalMetrics = globalMetrics;
            this.lastMinuteUpdateTime = now.get(Calendar.MINUTE);
            this.lastHourUpdateTime = now.get(Calendar.HOUR);
            this.lastDayUpdateTime = now.get(Calendar.DAY_OF_WEEK);
        }

        @Override
        public void run() {
            Calendar now = Calendar.getInstance();
            int minute = now.get(Calendar.MINUTE);
            int hour = now.get(Calendar.HOUR);
            int day = now.get(Calendar.DAY_OF_WEEK);

            if (minute != this.lastMinuteUpdateTime) {
                this.globalMetrics.doMinuteUpdates();
                for (GatewayMetrics gatewayMetrics : this.metrics.values())
                    gatewayMetrics.doMinuteUpdates();
                this.lastMinuteUpdateTime = minute;
            }

            if (hour != this.lastHourUpdateTime) {
                this.globalMetrics.doHourUpdates();
                for (GatewayMetrics gatewayMetrics : this.metrics.values())
                    gatewayMetrics.doHourUpdates();
                this.lastHourUpdateTime = minute;
            }

            if (day != this.lastDayUpdateTime) {
                this.globalMetrics.doDayUpdates();
                for (GatewayMetrics gatewayMetrics : this.metrics.values())
                    gatewayMetrics.doDayUpdates();
                this.lastDayUpdateTime = minute;
            }
        }
    }

}
