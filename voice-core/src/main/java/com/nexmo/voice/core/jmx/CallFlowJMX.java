package com.nexmo.voice.core.jmx;

import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.cache.LegFlowContext;


/**
 * <AUTHOR>
 */
public class CallFlowJMX implements CallFlowJMXMBean {

    private static final Logger Log = Logger.getLogger(CallFlowJMX.class.getName());

    @Override
    public String dumpCallsFlowCache() {
        Log.info("About to fetch all the CallFlow cached entries");
        String msg = "List of current used legs and their status. <br><br>"
                + "START_S : Processing the start call event is taking place <br><br>"
                + "START_E : Processing the start call event has completed <br><br>"
                + "STOP_P  : Stop event has arrived before or during the start event <br><br><br><br> "
                + "Upon CDR creation the leg flow status is removed from the cache <br><br><br><br> ";

        return Core.getInstance().getLegFlowCache().getLegsStatus()
                    .entrySet()
                    .stream()
                    .map(e -> e.getKey() + " : " + e.getValue().toReportString())
                    .collect(Collectors.joining("<br><br>"));
    }

    @Override
    public String dumpStoppedWaitingForStartCalls() {
        Log.info("About to fetch all the CallFlow cached entries which are in STOP_P status");
        String msg = "List of current used legs in pending stop status. <br><br>"
                + "The longer they are pending the higher chance the start was missed or something is realy wrong <br><br><br><br>";

        return msg + Core.getInstance().getLegFlowCache().getLegsStatus()
                .entrySet()
                .stream()
                .filter(legEntry -> LegFlowContext.LEG_STATUS.STOP_P.equals(legEntry.getValue().getLegStatus()))
                .map(e -> e.getKey() + " : " + e.getValue().toReportString())
                .collect(Collectors.joining("<br><br>"));
    }

    @Override
    public String dumpStartedCalls() {
        Log.info("About to fetch all the CallFlow cached entries which are on going now");
        String msg = "List of current used legs in started status. <br><br>"
                + "As long as the leg is on this status, the customer's quota is decremented. <br><br><br><br>";

        return msg + Core.getInstance().getLegFlowCache().getLegsStatus()
                .entrySet()
                .stream()
                .filter(legEntry -> (LegFlowContext.LEG_STATUS.START_S.equals(legEntry.getValue().getLegStatus()) ||
                        LegFlowContext.LEG_STATUS.START_E.equals(legEntry.getValue().getLegStatus())))
                .map(e -> e.getKey() + " : " + e.getValue().toReportString())
                .collect(Collectors.joining("<br><br>"));
    }

    @Override
    public String dumpUnExpectedStatusCalls() {
        Log.info("About to fetch all the CallFlow cached entries which ended up as unknown due to unexpected order of events");
        String msg = "List of current used legs in unknown status. <br><br>"
                + "This list should always be empty. Any entry here is an indication for something really wrong <br><br><br><br>";

        return msg + Core.getInstance().getLegFlowCache().getLegsStatus()
                .entrySet()
                .stream()
                .filter(legEntry -> (LegFlowContext.LEG_STATUS.UNKNOWN.equals(legEntry.getValue().getLegStatus())))
                .map(e -> e.getKey() + " : " + e.getValue().toReportString())
                .collect(Collectors.joining("<br><br>"));
    }

    @Override
    public String removeLegOnlyIfYouKnowWhatYouAreDoing(String legId) {
        LegFlowContext removed = Core.getInstance().getLegFlowCache().removeLegContext(legId);
        if (Objects.isNull(removed))
            return legId + " Not found";
        else
            return legId + removed.toReportString() + " Removed  -  PLEASE NOTICE: THIS WILL NOT STOP THE CHARGING IF IT IS STILL HAPPENING";
    }


}
