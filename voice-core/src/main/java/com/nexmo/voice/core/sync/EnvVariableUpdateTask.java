package com.nexmo.voice.core.sync;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.jmx.AsyncQuotaJMX;
import com.thepeachbeetle.common.jmx.JMXUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.management.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.nexmo.voice.core.SipAppUtils.getValueFromEnvProperties;

/**
 * This class is activated as a task which runs every 1 second and used to get the updated value from environment variables
 * and this should be used only for emergency purpose
 */

public class EnvVariableUpdateTask implements Runnable {

    private static final Logger Log = LogManager.getLogger(EnvVariableUpdateTask.class);
  
    public EnvVariableUpdateTask() {
        if (Log.isDebugEnabled())
            Log.debug("AsyncQuota EnvVariableUpdateTask created ");
    }

    @Override
    public void run() {
        final AtomicBoolean executed = new AtomicBoolean(false);
        if (executed.compareAndSet(false, true)) {
            try {
                String blockUnknownCLIFlagValue = getValueFromEnvProperties("block_unknown_cli_flag");
                if (StringUtils.isNotBlank(blockUnknownCLIFlagValue)) {
                    boolean blockUnknownCLIFlag = Boolean.parseBoolean(blockUnknownCLIFlagValue);
                    boolean initialUnknownCLIFlagValue = Core.getInstance().isBlockUnknownCLIFlag();
                    if (blockUnknownCLIFlag != initialUnknownCLIFlagValue) {
                        Core.getInstance().updateBlockUnknownCLIFlag(blockUnknownCLIFlag);
                    }
                }
              
                String quotaFlagValue = getValueFromEnvProperties("quota_async_flag");
                //Read env.properties file and if there is a change to variable values,initiate/destroy QuotaQueue thread and related items based on flag
                if (StringUtils.isNotBlank(quotaFlagValue)) {
                    boolean asyncQuotaFlag = Boolean.parseBoolean(quotaFlagValue);
                    boolean initialQuotaFlagValue = Core.getInstance().isAsyncQuotaFlag();
                    if (asyncQuotaFlag != initialQuotaFlagValue) {
                        Core.getInstance().updateQuotaFlag(asyncQuotaFlag);
                        if (asyncQuotaFlag) {
                            Config config = Core.getInstance().getConfig();
                            if (Core.getInstance().getQuotaQueueExecuter() == null) {
                                Core.getInstance().initializeAsyncQuotaExecutor();
                            }
                            AsyncQuotaJMX asyncQuotaJMX = new AsyncQuotaJMX();
                            try {
                                ObjectName name = new ObjectName("com.nexmo.voice.core.asyncQuota:hub=core");
                                MBeanServer mbeanServer = JMXUtil.startJMXConnector(config.getJMXConfig());
                                if (!mbeanServer.isRegistered(name)) {
                                    mbeanServer.registerMBean(asyncQuotaJMX, name);
                                }
                            } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
                                Log.error("Failed to set up AsyncQuota mbean ...", e);
                            }
                        }
                    }
                }

                // Read properties from env.properties
                String domesticRoutingFlagValue = getValueFromEnvProperties("domestic_routing_flag");
                if (StringUtils.isNotBlank(domesticRoutingFlagValue)) {
                    boolean domesticRoutingFlag = Boolean.parseBoolean(domesticRoutingFlagValue);
                    boolean initialDomesticRoutingFlagValue = Core.getInstance().isDomesticRoutingFlag();
                    if (domesticRoutingFlag != initialDomesticRoutingFlagValue) {
                        Core.getInstance().updateDomesticRoutingFlag(domesticRoutingFlag);
                    }
                }

                String randomPoolJMXNotificationFlagValue = getValueFromEnvProperties("random_pool_jmx_notification_flag");
                if (StringUtils.isNotBlank(randomPoolJMXNotificationFlagValue)) {
                    boolean randompoolJMXNotificationFlag = Boolean.parseBoolean(randomPoolJMXNotificationFlagValue);
                    boolean initialRandompooJMXNotificationFlaglValue = Core.getInstance().isRandomPoolJMXNotificationFlag();
                    if (randompoolJMXNotificationFlag != initialRandompooJMXNotificationFlaglValue) {
                        Core.getInstance().updateRandomPoolJMXNotificationFlag(randompoolJMXNotificationFlag);
                    }
                }

            } catch (Exception e) {
                Log.error("Something went terribly wrong!", e);
            }
        }
    }
}
