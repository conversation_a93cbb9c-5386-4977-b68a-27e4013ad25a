package com.nexmo.voice.core.logger;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceClient;
import com.nexmo.voice.core.emergency.address.EmergencyAddressServiceException;
import com.nexmo.voice.core.emergency.address.EmergencyNumberDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.types.CDRType;
import com.nexmo.voice.core.types.VoiceDirection;
import com.thepeachbeetle.messaging.hub.core.Product;

import static com.nexmo.voice.core.sip.AsteriskAGIServer.CUSTOMER_DOMAIN_TYPE_TRUNKING;

/**
 * This class Hold the shared methods and objects for all the specific loggers
 * 
 * 
 * <AUTHOR>
 *
 */


public class SIPAppLoggerController {
    private final static Logger Log = LogManager.getLogger(SIPAppLoggerController.class);

    private final static String host = SipAppUtils.getHostName();

    private Set<SIPAppLogger> sipAppLoggers = new HashSet();
    
    //A note regarding file names.
    //key-value file names are as it used to be: sip-outbound.[date].log etc.
    //json file names are sip-json-outbound.[date].log
    //
    //while creating json style files, we can choose to generate a BACKUP CDRs file which will still be in the
    //key-value structure. It is important to use a different file name for this BACKUP, so they wont be consumed by LogConverter while
    //we are on the transit period. This is needed only when we need ONLY JSON cdrs for the regular process.
    public SIPAppLoggerController(String logDir, String pfx, CdrsConfig cdrsConfig) throws Exception {
        
        CDRType cdrType = cdrsConfig.getCDRType();
        boolean backupCdrs = cdrsConfig.shouldBackupCDRs();
        
        switch (cdrType) {
        case KEY_VALUE:
            sipAppLoggers.add(new SIPAppLogger(logDir, pfx, CDRType.KEY_VALUE));
            break;
        case JSON:
            sipAppLoggers.add(new SIPAppLogger(logDir, SIPAppLogger.getJsonLogPrefix(pfx), CDRType.JSON));
            if (backupCdrs)
                sipAppLoggers.add(new SIPAppLogger(logDir, SIPAppLogger.getBackupLogPrefix(pfx), CDRType.KEY_VALUE));
             break;
        case BOTH:
            sipAppLoggers.add(new SIPAppLogger(logDir, pfx, CDRType.KEY_VALUE));
            sipAppLoggers.add(new SIPAppLogger(logDir, SIPAppLogger.getJsonLogPrefix(pfx), CDRType.JSON));
            break;
        default:
            sipAppLoggers.add(new SIPAppLogger(logDir, pfx, CDRType.KEY_VALUE));
        }
        
    }

    protected LinkedHashMap<String, String> buildBaseCDRData(
            final VoiceContext ctx,
            final String carrierCallId,
            final String idSuffix, 
            final int truncationLength,
            final CdrEventUserData eventUserData) {

        final LinkedHashMap<String, String> cdrDetails = new LinkedHashMap<>();

        cdrDetails.put("PRODUCT", String.valueOf(Product.PRODUCT_VOICE_CALL));

        String directionStr = Objects.nonNull(ctx.getVoiceDirection()) ? ctx.getVoiceDirection().getValue() : "null";
        cdrDetails.put("DIRECTION", directionStr);

        // for PREQ-4422 update network fields in all types of CDRs
        cdrDetails.put("NET", ctx.getNetwork());
        cdrDetails.put("NETWORK-NAME", ctx.getNetworkName());
        cdrDetails.put("NETWORK-TYPE", ctx.getNetworkType());

        String productClass =  SipAppUtils.getProductClass(ctx);
        if(ctx.isOutboundNCCOConnectToDomain()) {
            if(VoiceDirection.OUTBOUND.getValue().equals(ctx.getVoiceDirection().getValue())) {
                cdrDetails.put("NET", productClass);
                cdrDetails.put("NETWORK-NAME", productClass);
                cdrDetails.put("NETWORK-TYPE", productClass);
            }
        } else if ((StringUtils.isNotBlank(ctx.getCustomerDomain()) && !CUSTOMER_DOMAIN_TYPE_TRUNKING.equalsIgnoreCase(ctx.getCustomerDomainType()))
                ||  ctx.isSipOriginToLVNToApplication()) {

            // PREQ-4422 populate Network cdr fields for psip calls
            if (Objects.nonNull(ctx.getVoiceDirection())) {
                String netStr = ctx.getVoiceDirection().getValue().equals(VoiceDirection.OUTBOUND.getValue()) ? "api" : productClass;
                cdrDetails.put("NET", netStr);
                cdrDetails.put("NETWORK-NAME", netStr);
                cdrDetails.put("NETWORK-TYPE", netStr);
            }
        }
        cdrDetails.put("PRODUCT-CLASS", productClass);

        if (Objects.nonNull(ctx.getProductClassVersion()))
            cdrDetails.put("PRODUCT-VERSION", ctx.getProductClassVersion());

        cdrDetails.put("HOST", host);
        
        cdrDetails.put("LEG2-ID", carrierCallId);
        cdrDetails.put("SUPERHUB-ACC", ctx.getMasterAccountId());
        cdrDetails.put("ACC", ctx.getAccountId());
        
        String from = SIPAppLogger.getValueOrEmpty(ctx.getFrom());
        cdrDetails.put("FROM", from);
        cdrDetails.put("PREFIX-FROM", SipAppUtils.extractPrefix(from, truncationLength));

        String decodedTo = ctx.getTo();
        try {
            decodedTo = Objects.nonNull(ctx.getTo()) ? URLDecoder.decode(ctx.getTo(), StandardCharsets.UTF_8.name()) : "";
        } catch (UnsupportedEncodingException e) {
            Log.warn("Failed to decode the TO param " + ctx.getTo() + " due to " + e.getMessage());
        }

        cdrDetails.put("TO", decodedTo);
        cdrDetails.put("PREFIX-TO", SipAppUtils.extractPrefix(decodedTo, truncationLength));

        String forcedSender = SIPAppLogger.getValueOrNullStr(ctx.getForcedSender());
        cdrDetails.put("FORCED_SENDER", forcedSender);
        cdrDetails.put("PREFIX-FORCED_SENDER", SipAppUtils.extractPrefix(forcedSender, truncationLength));
        
        String sipDestAttempt = "1#1";
        if (Objects.nonNull(eventUserData) && Objects.nonNull(eventUserData.getFallbackAttemptStr())) {
            sipDestAttempt = eventUserData.getFallbackAttemptStr() + "#" + eventUserData.getFallbackAlternativesStr();
        }
        cdrDetails.put("SIP-DEST-ATTEMPT", sipDestAttempt);

        cdrDetails.put("COUNTRY", ctx.getCountryCode());

        if (Objects.nonNull(eventUserData) && Objects.nonNull(eventUserData.getCurrentGateway())) {
            cdrDetails.put("GW", eventUserData.getCurrentGateway());
            cdrDetails.put("GWS", eventUserData.getGatewaysList());
            cdrDetails.put("GW_ATTEMPT", eventUserData.getGatewaysAttemptStr());
        } else {
            //This might happen in 2 scenarios:
            //1. This is the case of outbound CDR with no userField in the event. This
            //   will happen when there is an inbound call to an application and not to a real
            //   number (i.e. VAPI call to use NCCO)
            //
            //2. This is INBOUND CDR - there is no meaning to gateways fall back in INBOUND context
            //
            cdrDetails.put("GW", ctx.getCurrentGateway());
            cdrDetails.put("GWS", ctx.getCurrentGateway());
            if (ctx.getVoiceDirection() == VoiceDirection.INBOUND)
                cdrDetails.put("CALL_RETRIES", String.valueOf(ctx.getNoRetries()));
            else 
                cdrDetails.put("GW_ATTEMPT", "1#1");
        }

        Long sequenceNumber = ctx.getSequenceNumber();
        String seqStr = sequenceNumber != null ? sequenceNumber.toString() : "0";
        cdrDetails.put("ROUTING_SEQ", seqStr);
        cdrDetails.put("ROUTING_GROUP", SIPAppLogger.getValueOrEmpty(ctx.getRoutingGroup()));
        cdrDetails.put("ROUTING_OA", SIPAppLogger.getValueOrEmpty(ctx.getRoutingOa()));
        cdrDetails.put("ROUTING_BIND_ID", SIPAppLogger.getValueOrEmpty(ctx.getRoutingBindId()));


        cdrDetails.put("REQUEST_IP", SIPAppLogger.getValueOrEmpty(ctx.getRequestIp()));
        cdrDetails.put("CALL_ORIGIN", SIPAppLogger.getValueOrEmpty(ctx.getCallOrigin()));
        cdrDetails.put("CALL_TERMINATION", SIPAppLogger.getValueOrEmpty(ctx.getCallTermination()));
        cdrDetails.put("CUSTOMER_DOMAIN", SIPAppLogger.getValueOrEmpty(ctx.getCustomerDomain()));

        if (Objects.nonNull(ctx.getTtsContext())) {
            cdrDetails.put("TTS_XTRACE_ID", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().ttsXTRaceId));
            cdrDetails.put("CALL_BACK_URL", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().callbackUrl));
            cdrDetails.put("CALL_BACK_METHOD", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().callbackMethod));
            cdrDetails.put("CLIENT_REFERENCE", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().clientReference));
            cdrDetails.put("REPEAT", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().repeat));
            cdrDetails.put("MACHINE_DETECTION_TYPE", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().machineDetectionType));
            cdrDetails.put("MACHINE_TIMEOUT", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().machineTimeout));
            cdrDetails.put("LANGUAGE_NAME", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().languageName));
            cdrDetails.put("MB_STYLE", SIPAppLogger.getValueOrEmpty(ctx.getTtsContext().mbStyle));
        }

        cdrDetails.put("CDR_UUID", UUID.randomUUID().toString());
        cdrDetails.put("STIR_SHAKEN", SIPAppLogger.getValueOrNullStr(ctx.getStirShaken()));

        if (VoiceDirection.OUTBOUND.equals(ctx.getVoiceDirection()) && Objects.nonNull(eventUserData)
                && Objects.nonNull(eventUserData.getCarrierPlatform())) {
            cdrDetails.put("CARRIER_PLATFORM", String.valueOf(eventUserData.getCarrierPlatform()));
        }
        
        if (Objects.nonNull(ctx.getInternalFlags()) && !ctx.getInternalFlags().isEmpty()) {
            cdrDetails.put("INTERNAL_FLAG", SIPAppLogger.concatInternalFlags(ctx.getInternalFlags()));
        }

        if (ctx.isSrtpEnabled()) {
            cdrDetails.put("SRTP", String.valueOf(ctx.isSrtpEnabled()));
        }

        cdrDetails.put("SIP_TRANSPORT", ctx.getSipTransport());

        cdrDetails.put("SOURCE_COUNTRY", ctx.getSourceCountryCode());

        cdrDetails.put("RULE_ID", ctx.getId());
        cdrDetails.put("BLOCKING_SUBSYSTEM", SIPAppLogger.getValueOrEmpty(ctx.getBlockingSubsystem()));

        if (Objects.nonNull(ctx.getAsteriskVersion())) {
            cdrDetails.put("ASTERISK_VERSION", ctx.getAsteriskVersion().getCdrVersion());
        }

        //SIP-2105 Add HangupCause to CDR
        if (Objects.nonNull(eventUserData) && Objects.nonNull(eventUserData.getHangupCause())) {
                cdrDetails.put("HANGUP_CAUSE", String.valueOf(eventUserData.getHangupCause()));
        }

        cdrDetails.put("PRODUCT-PATH", SIPAppLogger.getValueOrEmpty(ctx.getProductPath()));
        cdrDetails.put("NUMBER_TYPE", SIPAppLogger.getValueOrNullStr(ctx.getNumberType()));
        cdrDetails.put("CALL_TYPE", SIPAppLogger.getValueOrNullStr(ctx.getCallType()));
        cdrDetails.put("VPRICING-ENABLED", String.valueOf(ctx.isVpricingEnabled()));

        if(ctx.isEmergencyCall()) {
            cdrDetails.put("EMERGENCY_CALL", String.valueOf(ctx.isEmergencyCall()));

            if(ctx.getEmergencyCallFailoverReason() != null) {
                cdrDetails.put("EMERGENCY_CALL_FAILOVER", SIPAppLogger.formatSipCodeMap(ctx.getEmergencyCallFailoverReason()));
            }

            cdrDetails.put("EMERGENCY_CALL_LOCATION_ID", getEmergencyCallLocationId(ctx));
        }

        return cdrDetails;
    }
    
    protected void logCDR(CDRData cdrData, String sessionId, String[] CDROrder, String message) {
        sipAppLoggers.stream().forEach(logger -> {
            logger.logCDR(cdrData, CDROrder);
            if (Log.isDebugEnabled())
                Log.debug("{} at logger {} for sessionId: {}", message, logger.toString(), sessionId);
        });

    }
    
    public Set<SIPAppLogger> getSIPAppLoggers() {
        return sipAppLoggers;
    }

    private String getEmergencyCallLocationId(VoiceContext context) {
        Log.debug("{} Attempting to find addressId for emergency call from apiKey: {}, fromNumber: {}", context.getSessionId(), context.getAccountId(), context.getFrom());
        if(Core.getInstance().getConfig().getViamAuthConfig() == null) {
            Log.warn("{} VIAM not configured; cannot invoke emergency address service", context.getSessionId());
            return "";
        }
        if(Core.getInstance().getConfig().getEmergencyAddressServiceConfig() == null) {
            Log.warn("{} Emergency address service not configured", context.getSessionId());
            return "";
        }
        try {
            EmergencyNumberDetail details = new EmergencyAddressServiceClient(
                    Core.getInstance().getConfig().getEmergencyAddressServiceConfig(),
                    Core.getInstance().getConfig().getViamAuthConfig()
            ).getEmergencyNumberDetail(context.getFrom(), context.getAccountId());
            if((details != null) && (details.getAddress() != null)) {
                Log.debug("{} Emergency service addressId is {} for apiKey: {}, fromNumber:{},", context.getSessionId(), details.getAddress().getId(), context.getAccountId(), context.getFrom());
                return details.getAddress().getId();
            }
        } catch(EmergencyAddressServiceException e) {
            Log.warn("{} Could not get addressId for apiKey: {}, fromNumber: {}, reason: {}",
                    context.getSessionId(), context.getAccountId(), context.getFrom(), e.getMessage());
        }
        return "";
    }
}
