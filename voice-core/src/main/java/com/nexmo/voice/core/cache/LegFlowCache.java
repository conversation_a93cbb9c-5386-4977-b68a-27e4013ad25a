package com.nexmo.voice.core.cache;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import com.nexmo.voice.core.Core;
import org.apache.log4j.Logger;
import org.asteriskjava.manager.event.CdrEvent;
/**
 * This class handle the status of each leg in the call
 *
 * The 'start' (BridgeEvent) and 'stop' (CDREvent) update and use this table in order to guarntee
 * proper order of events processing.
 *
 * The leg status might be any of :
 *  START_S, //Processing of BridgeEvent (start) has started
 *  START_E, //Processing of BridgeEvent (start) has ended
 *  STOP_P,  //CDREvent (stop) has arrived; waiting to be processed
 *  UNKNOWN  //Unknown status - really bad situation, not yet supported
 *
 * This is a mid-term solution to the over-charging issue:  SIP-67
 *
 * <AUTHOR>
 */
public class LegFlowCache {

    private static final Logger Log = Logger.getLogger(LegFlowCache.class.getName());

    private ConcurrentHashMap<String, LegFlowContext> legs;

    public LegFlowCache() {
        legs = new ConcurrentHashMap<String, LegFlowContext>();
        Log.info("LegFlowCache initializaton completed");
    }

    /*
     * Handle the STOP event arrival, the returned leg status will be:
     *
     * STOP_P - wait for later events before processing the stop
     * START_E - the start event processing has completed - continue with the stop processing
     * UNKNOWN - really bad things are happening (not expected to happen)
     */
    public LegFlowContext stopEventArrived(String legId, CdrEvent cdrEvent) {
        LegFlowContext legFlowContext = legs.compute(legId, (k, v) -> setStopArrived(k, v, cdrEvent));
        Log.info("stopEventArrived for leg " + legId + " returned " + legFlowContext);
        return legFlowContext;
    }

    private LegFlowContext setStopArrived(String legId, LegFlowContext legContext, CdrEvent cdrEvent) {
        if (Objects.isNull(legContext) ||
                LegFlowContext.LEG_STATUS.START_S.equals(legContext.getLegStatus()))
            return new LegFlowContext(LegFlowContext.LEG_STATUS.STOP_P, cdrEvent); //Set as stop-pending

        else if (LegFlowContext.LEG_STATUS.START_E.equals(legContext.getLegStatus()))
            return legContext; //No changes - the start processing has completed already, 
            //ok to process the stop
        else {
            Log.warn("Leg " + legId + " Stop event has arrived while the leg status is " + legContext + " - unsupported ");
            return new LegFlowContext(LegFlowContext.LEG_STATUS.UNKNOWN, null); //This is bad!
        }
    }

    /*
     * Handle the START event arrival, the returned leg status will be:
     *
     * START_S - the start event processing has started - continue with the start processing
     * STOP_P -  Stop event arrived earlier, handle the start and then continue with the stop
     * UNKNOWN - really bad things are happening (not expected to happen)
     */
    public LegFlowContext startEventArrived(String legId) {
        LegFlowContext legFlowContext = legs.compute(legId, (k, v) -> setStartArrived(k, v));
        Log.info("startEventArrived for leg " + legId + " returned " + legFlowContext);
        return legFlowContext;
    }

    private LegFlowContext setStartArrived(String legId, LegFlowContext legContext) {
        if (Objects.isNull(legContext))
            return new LegFlowContext(LegFlowContext.LEG_STATUS.START_S, null); // Set as started

        else if (LegFlowContext.LEG_STATUS.STOP_P.equals(legContext.getLegStatus()))
            return legContext; // No changes - execute the start and then immediately after the stop

        else {
            Log.warn(
                    "Leg " + legId + " Start event has arrived while the leg status is " + legContext + " - unsupported ");
            return new LegFlowContext(LegFlowContext.LEG_STATUS.UNKNOWN, null); //This is bad!
        }
    }

    /*
     * Handle the START event processing completion, the returned leg status will be:
     *
     * STOP_P - while processing the start event, the stop has arrived - it is time to process the stop
     * START_E - the start event processing has completed - do nothing
     * UNKNOWN - really bad things are happening (not expected to happen)
     */
    public LegFlowContext startEventProcessingCompleted(String legId) {
        LegFlowContext legFlowContext = legs.compute(legId, (k, v) -> setStartCompleted(k, v));
        Log.info("startEventProcessingCompleted for leg " + legId + " returned " + legFlowContext);
        return legFlowContext;
    }

    private LegFlowContext setStartCompleted(String legId, LegFlowContext legContext) {
        if (Objects.isNull(legContext)) {
            Log.warn(
                    "Leg " + legId + " Start event has completed while the leg context is null  - unsupported ");
            return new LegFlowContext(LegFlowContext.LEG_STATUS.UNKNOWN, null); //This is bad!
        } else if (LegFlowContext.LEG_STATUS.START_S.equals(legContext.getLegStatus()))
            return new LegFlowContext(LegFlowContext.LEG_STATUS.START_E, null); // Set as start processing completed

        else if (LegFlowContext.LEG_STATUS.STOP_P.equals(legContext.getLegStatus()))
            return legContext; // We have pending stop event - process it

        else {
            Log.warn(
                    "Leg " + legId + " Start event has completed while the leg status is " + legContext + " - unsupported ");
            return new LegFlowContext(LegFlowContext.LEG_STATUS.UNKNOWN, null); //This is bad!
        }
    }

    /*
     * Remove the leg after CDR creation
     */
    public LegFlowContext removeLegContext(String legId) {
        if (Log.isDebugEnabled()) {
            Log.debug("Started removeLegContext for legId: " + legId);
        }

        LegFlowContext removed = null;
        if (Objects.nonNull(legId)) {
            if (Log.isDebugEnabled()) {
                Log.debug("Try removing leg for legId: " + legId);
            }
            removed = legs.remove(legId);
        }

        if (Objects.nonNull(removed))
            Log.info("LegFlowContext removed " + legId + " at status " + removed.getLegStatus().name());

        return removed;
    }

    public Map<String, LegFlowContext> getLegsStatus() {
        return Collections.unmodifiableMap(legs);
    }

}
