package com.nexmo.voice.core.monitoring;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.thepeachbeetle.common.app.monitoring.AbstractMonitoringServlet.MonitoringCommand;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.monitoring.metrics.ApplicationMetrics;
import com.nexmo.voice.core.monitoring.metrics.GatewayMetrics;


public class GatewayMetricsCommand extends MonitoringCommand {

    private final Core core;

    public GatewayMetricsCommand(final Core core) {
        this.core = core;
    }

    @Override
    protected String getMetrics(HttpServletRequest request, Map<String, String> metrics) {
        String gatewayId = request.getParameter("gateway");

        if (gatewayId == null) {
            menu(request, metrics);
            return "ALL-GATEWAYS";
        }

        ApplicationMetrics applicationMetrics = this.core.getApplicationMetrics();
        if (applicationMetrics.containsGatewayMetrics(gatewayId)) {
            final GatewayMetrics gatewayMetrics = applicationMetrics.getGatewayMetrics(gatewayId);
            metrics.putAll(gatewayMetrics.getMetricsOutputMap());
        }

        return "GATEWAY-" + gatewayId;
    }

    private void menu(HttpServletRequest request, Map<String, String> metrics) {
        String baseUrl = request.getRequestURL().toString();
        if (baseUrl.indexOf("?") > 0)
            baseUrl = baseUrl + "&";
        else
            baseUrl = baseUrl + "?";

        for (GatewayMetrics metric : this.core.getApplicationMetrics().getAllGatewayMetrics()) {
            String url = baseUrl + "cmd=gateway-metrics&gateway=" + metric.getGatewayName();
            metrics.put(metric.getGatewayName(), url);
        }
    }

}
