package com.nexmo.voice.core.cdr;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/***
 * CDR values should be transfered to the json "as is" except for the dates
 * which should be reformatted:
 * 
 * 
 * CDR date related keys/valuse 
 * ============================
 * "PRICE_TIMESTAMP=04/09/2020 09:00:01 (000)", 
 * "COST_TIMESTAMP=10/08/2018 13:04:10 (000)", 
 * "START=10/10/2021 23:59:35 (299)",
 * "END=10/11/2021 00:00:01 (299)", 
 * "CALL_DATE=10/10/2021 23:59:35 (299)",
 * 
 * The CDR creation time stamp: 10/11/2021 00:00:00 (716)
 * 
 * 
 * 
 * JSON date related keys/valuse 
 * ============================ 
 * The priceTimestamp and costTimestamp in the json files have different date format because they are not defined in
 * the logConverter as dates. 
 * That will be fixed as part of the transition to using json CDRs.
 * "priceTimestamp":"04/09/2020 09:00:01 (000)", can also be "null"
 * "costTimestamp":"10/08/2018 13:04:10 (000)", can also be "null"
 * 
 * "start":"2021-10-10T23:59:35.299+0000",
 * "end":"2021-10-11T00:00:01.299+0000",
 * "callDate":"2021-10-10T23:59:35.299+0000",
 * 
 * The CDR creation time stamp: 
 * "@date":"2021-10-11T00:00:00.716+0000",
 */

public class CDRValuesConverter {

    private static final Logger Log = LogManager.getLogger(CDRKeysConverter.class);

    public static HashSet<String> cdrDateKeys = new HashSet();

    public static final String CDR_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss (SSS)";
    public static final String JSON_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

    public static final DateTimeFormatter FROM_CDR_TO_DATE = DateTimeFormatter.ofPattern(CDR_DATE_FORMAT);
    public static final DateTimeFormatter FROM_DATE_TO_JSON = DateTimeFormatter.ofPattern(JSON_DATE_FORMAT);

    static {
        //cdrDateKeys.add("PRICE_TIMESTAMP"); This is kept in Kibana as a string and not as a date
        //cdrDateKeys.add("COST_TIMESTAMP"); This is kept in Kibana as a string and not as a date
        cdrDateKeys.add("START");
        cdrDateKeys.add("END");
        cdrDateKeys.add("CALL_DATE");
        cdrDateKeys.add("@date");
    }

    public static String convertValue(String cdrKey, String cdrValue) {
        if (!cdrDateKeys.contains(cdrKey))
            return cdrValue;

        if (Objects.isNull(cdrValue) || cdrValue.equalsIgnoreCase("null") || cdrValue.isEmpty())
            return cdrValue;

        String jsonFormattedValue = cdrValue;
        try {
            jsonFormattedValue = formatCDRCreationTimeForJson(cdrValue);
        } catch (Exception e) {
            Log.warn("Failed to translate value {} of key {} due to {}. Will use orignal value ", cdrValue, cdrKey,
                    e.getMessage());
        }
        if (Log.isDebugEnabled())
            Log.debug("Converting Key {} from cdrValue {} to jsonValue {}", cdrKey, cdrValue, jsonFormattedValue);

        return jsonFormattedValue;

    }  
    
    
    public static String formatCDRCreationTimeForJson(String cdrClassicCreationDate) {
        //The current CDR dates are in UTC, The time zone is not part of the text, so we need to explicitly mark it as such
        // before we can use the format back with the +0000 as written in the json files, so later Kibana can translate it back to
        // something like: 2021-10-18T16:40:00.008Z

        LocalDateTime cdrDateTime = LocalDateTime.parse(cdrClassicCreationDate, FROM_CDR_TO_DATE);
        ZonedDateTime cdrZonedDateTime = cdrDateTime.atZone(ZoneId.of("UTC"));

        return cdrZonedDateTime.format(FROM_DATE_TO_JSON);

    }
    

}
