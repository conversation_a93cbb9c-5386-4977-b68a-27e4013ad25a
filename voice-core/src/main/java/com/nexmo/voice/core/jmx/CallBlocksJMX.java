package com.nexmo.voice.core.jmx;

import com.nexmo.voice.config.callblocks.CallBlocksConfig;
import com.nexmo.voice.config.callblocks.CallBlocksConfigSource;
import com.nexmo.voice.config.callblocks.CallBlocksConfigUtils;
import com.nexmo.voice.core.Core;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class CallBlocksJMX implements CallBlocksJMXMBean {

    private static final Logger Log = Logger.getLogger(CallBlocksJMX.class.getName());

    @Override
    public String listCallBlocks() {
        Log.info("About to fetch all call blocks from config");

        StringBuilder sb = new StringBuilder();
        sb.append("<br><br>");

        final CallBlocksConfig callBlocksConfig = Core.getInstance().getConfig().getCallBlocksConfig();
        sb.append("New Call Block:<br><br>");
        for (HashMap.Entry<String, HashMap<String, ArrayList<String>>> callBlock : callBlocksConfig.getCallBlockMap().entrySet()) {
            sb.append("account=" + callBlock.getKey() + "<br>");
            for (HashMap.Entry<String, ArrayList<String>> destination : callBlock.getValue().entrySet()) {
                sb.append("- destination=" + destination.getKey() + "<br>");
                for (String callId : destination.getValue()) {
                    sb.append("-- call-id=" + callId + "<br>");
                }
            }
        }
        sb.append("<br><br>");

        return sb.toString();
    }

    @Override
    public String loadCallBlockListFromFile(String filename) {
        Log.info("About to load new call blocks config from " + filename);

        if (filename == null) {
            return "ERROR: No filename provided.";
        }

        final File f = new File(filename);
        if (!f.exists()) {
            return "ERROR: File \"" + filename + "\" does not exist";
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("Loading from file: \"");
        sb.append(filename);
        sb.append("\" => ");

        final CallBlocksConfigSource newSource = new CallBlocksConfigSource(filename);
        final CallBlocksConfig newConfig = CallBlocksConfigUtils.loadConfigFromXMLFile(filename);
        if (newConfig != null) {
            sb.append("SUCCESS!<br><br>");

            final CallBlocksConfig oldConfig = Core.getInstance().getConfig().getCallBlocksConfig();
            Log.info("CallBlocksJmx.loadCallBlockListFromFile(): checking for call blocks");
            if (newConfig.hasCallBlocks()) {
                // Compare old/new configs and report the difference
                Log.info("CallBlocksJmx.loadCallBlockListFromFile(): has call blocks");
                final String diffs = CallBlocksConfigUtils.compareConfigs(oldConfig, newConfig);
                Log.info("CallBlocksJmx.loadCallBlockListFromFile(): checked differences");
                sb.append(diffs);

                // Overwrite old config. Atomic operation, no lock needed.
                Core.getInstance().getConfig().setCallBlocksConfig(newConfig, newSource);
                Log.info("CallBlocksJmx.loadCallBlockListFromFile(): set new config");
            } else {
                sb.append("New config contains no call blocks: REJECTED!");
            }
        } else {
            sb.append("FAILED!");
        }

        return sb.toString();
    }
}

