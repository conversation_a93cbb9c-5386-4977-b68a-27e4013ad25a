package com.nexmo.voice.core.types;


import java.io.Serializable;

public class ToNumberDetails implements Serializable {

    public static enum Type {
        PSTN,
        LVN;
    }

    public static enum RedirectType {
        LVN_TO_PSTN,
        LVN_TO_SIP,
        LVN_TO_APPLICATION,
        NONE;
    }

    private Type toNumberType;
    private RedirectType toRedirectType;
    private String redirectDestination;

    public ToNumberDetails(Type toNumberType, RedirectType toRedirectType, String redirectDestination) {
        this.toNumberType = toNumberType;
        this.toRedirectType = toRedirectType;
        this.redirectDestination = redirectDestination;
    }

    public Type getType() {
        return toNumberType;
    }

    public RedirectType getRedirectType() {
        return toRedirectType;
    }

    public String getRedirectDestination() {
        return redirectDestination;
    }

}
