package com.nexmo.voice.core.callblocking;

import io.prometheus.client.Histogram;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.thepeachbeetle.common.http.HttpRequester.ConnectionRefusedException;
import com.thepeachbeetle.common.http.HttpRequester.ConnectionTimeoutException;
import com.thepeachbeetle.common.http.HttpRequester.HttpException;
import com.thepeachbeetle.common.http.HttpRequester.TimeoutException;

import static com.nexmo.voice.config.metrics.MetricsConfig.DEFAULT_DURATION_BUCKETS;
import static io.prometheus.client.Collector.NANOSECONDS_PER_SECOND;

import com.fasterxml.jackson.core.JsonProcessingException;

public class CallBlockingServiceClient {
    private static final Logger Log = LogManager.getLogger(CallBlockingServiceClient.class);

    private static CallblockingServiceConfig config;
    private final CallBlockingRequester requester;

    private static final Histogram CALLBLOCK_REQUESTS_LATENCY = Histogram.build().buckets(DEFAULT_DURATION_BUCKETS).name("sipapp_callblocking_requests_latency").help("Http Requests Latency to Fetch Callblocking rules").labelNames("status").register();
    private static final double NANOSECONDS_PER_MILLISECOND = 1E6;


    public CallBlockingServiceClient(CallblockingServiceConfig config) {
        this.config = config;
        this.requester = new CallBlockingRequester(config);
    }

    public CallBlockingResponse getCallBlockingRules(String apiKey, String product, String from, String to, String nexmoUUID, int numRetries, final long executionStartTime) throws CallBlockingLookupException, JsonProcessingException {
        boolean isFallbackAttempt = numRetries > 0;
        String baseUrl = config.constructUri(isFallbackAttempt);
        Log.info("config value:{}, and baseUrl:{}", config.toString(), baseUrl);

        CallblockingClientJson requestBody = new CallblockingClientJson(apiKey, product, from, to, Collections.singletonList(nexmoUUID));

        Log.info("Requesting callblocking from [ {} ] for the nexmoUUID {}:", baseUrl, nexmoUUID);

        String response = "";

        try {
            response = this.requester.executePost(baseUrl, requestBody, addHeaders(), config);
            Log.info("Callblocking Service response for {}: {} and for nexmouuid:{}", apiKey, response, nexmoUUID);
            CALLBLOCK_REQUESTS_LATENCY.labels("ok").observe((System.nanoTime() - executionStartTime) / NANOSECONDS_PER_SECOND);
        } catch (CallBlockingRequester.NotFoundException exception) {
            final long executionTimeDifference = System.nanoTime() - executionStartTime;
            if (hasReachedExecutionLimits(executionTimeDifference, numRetries)) {
                Log.warn("Callblocking {} not found - deleted for nexmouuid:{} with exception: {} and response time is: {} ms, so failing.", apiKey, nexmoUUID, exception, executionTimeDifference / NANOSECONDS_PER_MILLISECOND);
                CALLBLOCK_REQUESTS_LATENCY.labels("not_found").observe((executionTimeDifference) / NANOSECONDS_PER_SECOND);
                return null;
            }
            Log.error("Retrying with fallback url as error upon calling callblocking url {} for api key {}, product {}, from {}, to {}, nexmo uuid {}, error: {}",
                      baseUrl, apiKey, product, from, to, nexmoUUID, exception.getMessage());
            return getCallBlockingRules(apiKey, product, from, to, nexmoUUID, numRetries + 1, executionStartTime);
        } catch (CallBlockingRequester.ClientException exception) {
            final long executionTimeDifference = System.nanoTime() - executionStartTime;
            if (hasReachedExecutionLimits(executionTimeDifference, numRetries)) {
                Log.warn("Failed to get callblock for nexmouuid:{} with exception: {} and response time is: {} ms, so failing.", nexmoUUID, exception, executionTimeDifference / NANOSECONDS_PER_MILLISECOND);
                CALLBLOCK_REQUESTS_LATENCY.labels("client_failure").observe((executionTimeDifference) / NANOSECONDS_PER_SECOND);
                return null;
            }
            Log.error("Retrying with fallback url as error upon calling callblocking url {} for api key {}, product {}, from {}, to {}, nexmo uuid {}, error: {}",
                      baseUrl, apiKey, product, from, to, nexmoUUID, exception.getMessage());
            return getCallBlockingRules(apiKey, product, from, to, nexmoUUID, numRetries + 1, executionStartTime);
        } catch (ConnectionRefusedException | ConnectionTimeoutException | TimeoutException | HttpException exception) {
            final long executionTimeDifference = System.nanoTime() - executionStartTime;
            if (hasReachedExecutionLimits(executionTimeDifference, numRetries)) {
                Log.warn("Timeout exception from callblocking api for nexmouuid:{} with exception: {} and response time is: {} ms, so failing.", nexmoUUID, exception, executionTimeDifference / NANOSECONDS_PER_MILLISECOND);
                CALLBLOCK_REQUESTS_LATENCY.labels("http_timeout").observe((executionTimeDifference) / NANOSECONDS_PER_SECOND);
                return null;
            }
            Log.error("Retrying with fallback url as error upon calling callblocking url {} for api key {}, product {}, from {}, to {}, nexmo uuid {}, error: {}",
                      baseUrl, apiKey, product, from, to, nexmoUUID, exception.getMessage());
            return getCallBlockingRules(apiKey, product, from, to, nexmoUUID, numRetries + 1, executionStartTime);
        } catch (Exception exception) {
            final long executionTimeDifference = System.nanoTime() - executionStartTime;
            if (exception.getMessage().contains("HTTP 503") && !hasReachedExecutionLimits(executionTimeDifference, numRetries)) {
                Log.warn("Http 503 error while finding blocking rule for product voice, response time is: {} ms and exception: {}, so retrying", executionTimeDifference / NANOSECONDS_PER_MILLISECOND, exception.getMessage());
                return getCallBlockingRules(apiKey, product, from, to, nexmoUUID, numRetries + 1, executionStartTime);
            }
            throw exception;
        }

        CallBlockingResponse result = null;
        try {
            JSONObject json = new JSONObject(response);
            result = CallBlockingServiceResponseParser.fromJSON(json);
            Log.info("CB json:{}", result.toString());
        } catch (JSONException e) {
            Log.warn("Failed to parse json callblocking response", e);
        }
        return result;
    }


    private static Map<String, String> addHeaders() {
        Map<String, String> headers = new HashMap<>();
        String header = "Basic ";
        String headerValue = config.getUsername() + ":" + config.getPassword();
        String encodedHeaderValue = Base64.encodeBase64String(headerValue.getBytes());
        String headerBasic = header + encodedHeaderValue;
        headers.put("Authorization", headerBasic);
        return headers;
    }

    private boolean hasReachedExecutionLimits(final long executionTimeDifference, final int numRetries) {

        // check if the execution time difference exceeds the threshold
        boolean timeThresholdExceeded = executionTimeDifference >= (config.getRetryTimeout() *  NANOSECONDS_PER_MILLISECOND);

        // check if the retry count exceeds the threshold
        boolean retryThresholdExceeded = numRetries >= config.getRetryCount();
        return timeThresholdExceeded || retryThresholdExceeded;
    }
}
