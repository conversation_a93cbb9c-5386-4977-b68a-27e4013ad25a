package com.nexmo.voice.core.billing;

import io.prometheus.client.Counter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

import com.leansoft.bigqueue.BigQueueImpl;

public class QuotaQueueSyncTask implements Runnable {

    private static final Logger Log = LogManager.getLogger(QuotaQueueSyncTask.class);
    private final ConcurrentLinkedQueue<QuotaItem> quotaQueue;
    private BigQueueImpl persistedQuotaQueue;
    private static final Counter QUOTA_QUEUE_SYNC_FAILURE = Counter.build().name("quota_queue_sync_failure").labelNames("queue_sync_failure").help(" ").register();


    public QuotaQueueSyncTask(ConcurrentLinkedQueue<QuotaItem> queue, BigQueueImpl persistedQueue) {
        this.quotaQueue = queue;
        this.persistedQuotaQueue = persistedQueue;
    }

    @Override
    public void run() {
        // if there is previous thread running don't do another processing
        final AtomicBoolean executed = new AtomicBoolean(false);
        if (executed.compareAndSet(false, true)) {
            // Sync task runs once every 6 seconds ; where it clears all the current items in the persistent queue
            // and any items that's still in the concurrent queue (which hasn't invoked the Quota API calls) will be added to this persistent queue.
            try {
                final long startTime = System.currentTimeMillis();
                // remove current items
                this.persistedQuotaQueue.removeAll();

                // add all items
                for (QuotaItem item : this.quotaQueue) {
                    this.persistedQuotaQueue.enqueue(QuotaItem.serializeToByteArray(item));
                }

                if (Log.isDebugEnabled())
                    Log.debug("Took {} milliseconds to process {} items", System.currentTimeMillis() - startTime, this.persistedQuotaQueue.size());
            } catch (Exception ex) {
                Log.error("Got Exception in doSync", ex);
                CompletableFuture.runAsync(() -> {
                    QUOTA_QUEUE_SYNC_FAILURE.labels("sync_failure").inc();
                });
            }
        }
    }

}