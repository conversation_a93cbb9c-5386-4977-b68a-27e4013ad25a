package com.nexmo.voice.core.billing.vquota;

import com.nexmo.voice.core.types.ProductType;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.types.VoiceDirection;
import org.apache.commons.lang3.StringUtils;
import java.util.Objects;
import static com.nexmo.voice.core.sip.AsteriskAGIServer.CUSTOMER_DOMAIN_TYPE_TRUNKING;

public class ProductTypeResolver {
    public ProductType resolveProductType(final VoiceContext ctx) {
        if (isPsipInbound(ctx)) {
            return ProductType.PSIP_INBOUND;
        } else if (isPsipOutbound(ctx)) {
            return ProductType.PSIP_OUTBOUND;
        } else if (isVoiceInbound(ctx)) {
            return ProductType.VOICE_INBOUND;
        } else if (isVoiceOutbound(ctx)) {
            return ProductType.VOICE_OUTBOUND;
        } else {
            throw new IllegalArgumentException("Unknown product-type voice-context: " + ctx);
        }
    }

    private boolean isPsipInbound(final VoiceContext ctx) {
        return Objects.nonNull(ctx.getVoiceDirection()) && VoiceDirection.INBOUND.equals(ctx.getVoiceDirection()) &&
                ((StringUtils.isNotBlank(ctx.getCustomerDomain()) && !CUSTOMER_DOMAIN_TYPE_TRUNKING.equalsIgnoreCase(ctx.getCustomerDomainType()))
                        || ctx.isSipOriginToLVNToApplication());
    }

    private boolean isPsipOutbound(final VoiceContext ctx) {
        return ctx.isOutboundProgrammableSip();
    }

    private boolean isVoiceInbound(final VoiceContext ctx) {
        return Objects.nonNull(ctx.getVoiceDirection()) && VoiceDirection.INBOUND.equals(ctx.getVoiceDirection());
    }

    private boolean isVoiceOutbound(final VoiceContext ctx) {
        return Objects.nonNull(ctx.getVoiceDirection()) && VoiceDirection.OUTBOUND.equals(ctx.getVoiceDirection());
    }
}
