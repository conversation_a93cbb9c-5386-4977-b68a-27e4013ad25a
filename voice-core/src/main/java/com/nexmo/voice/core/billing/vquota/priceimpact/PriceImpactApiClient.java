package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.nexmo.voice.core.billing.exceptions.NegativeBalanceRefundException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.IllegalOperationOnSubAccountException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import static org.apache.http.entity.ContentType.APPLICATION_JSON;

public class PriceImpactApiClient {
    private final static Logger Log = LogManager.getLogger(PriceImpactApiClient.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final PriceImpactApiConfig config;
    private final String authValue;
    protected HttpClient client;
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAILURE = "failure";

    public PriceImpactApiClient(PriceImpactApiConfig config, String authValue) {
        this.config = config;
        this.authValue = authValue;
        int timeout = config.getTimeout();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        this.client = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).build();
    }

    public PriceImpactApiSuccessResponse invokePriceImpactApi(PriceImpactApiRequest requestBody, String sessionId) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        String priceImpactApiUrl = config.constructUri();
        HttpPost request = new HttpPost(priceImpactApiUrl);
        String jsonBody;
        try {
            jsonBody = OBJECT_MAPPER.writeValueAsString(requestBody);
        } catch (JsonProcessingException e) {
            throw new QuotaException("Failed to serialize request body to JSON", e);
        }

        request.setEntity(new StringEntity(jsonBody, APPLICATION_JSON));
        int timeout = config.getTimeout();
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        request.setConfig(config);
        addAuthenticationHeaders(request);
        String apiKey = requestBody.getGroupId();
        if (Log.isDebugEnabled())
            Log.debug("Calling vQuota price-impact API with URL [{}] and body [{}] for api-key [{}], sessionId [{}]", priceImpactApiUrl, jsonBody, apiKey, sessionId);
        return executeRequest(request, apiKey, sessionId);
    }

    private PriceImpactApiSuccessResponse executeRequest(HttpPost request, String apiKey, String sessionId) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        final HttpResponse resp;
        try {
            resp = this.client.execute(request);
        } catch (Exception e) {
            String err = "Failed to execute the request";
            throw new QuotaException(err, e);
        }

        int statusCode = resp.getStatusLine().getStatusCode();
        String responseBody = null;
        try {
            responseBody = EntityUtils.toString(resp.getEntity());
        } catch (Exception e) {
            // Not all bodies are expected to be readable
        }
        Log.info("Received response from price-impact API: status code [{}], response body [{}], api-key [{}], sessionId [{}]", statusCode, responseBody, apiKey, sessionId);
        return handleResponse(responseBody, apiKey);
    }

    private PriceImpactApiSuccessResponse handleResponse(String responseBody, String apiKey) throws QuotaException, QuotaUnderMaintenanceException,
            IllegalOperationOnSubAccountException, QuotaDisabledException, AccountNotFoundException, NotEnoughBalanceException, NegativeBalanceRefundException {
        if (responseBody == null || responseBody.isEmpty()) {
            throw new QuotaException("Empty or null response body received");
        }

        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(responseBody);
            String status = jsonNode.get("status").asText();

            if (STATUS_SUCCESS.equals(status)) {
                return OBJECT_MAPPER.readValue(responseBody, PriceImpactApiSuccessResponse.class);
            } else if (STATUS_FAILURE.equals(status)) {
                PriceImpactApiFailureResponse failureResponse = OBJECT_MAPPER.readValue(responseBody, PriceImpactApiFailureResponse.class);
                failureResponse.handleResponse(apiKey);
                return null;
            } else {
                throw new QuotaException("Request for price-impact API failed: " + responseBody);
            }
        } catch (JsonProcessingException | NullPointerException e) {
            throw new QuotaException("Failed to parse price-impact API JSON response: " + responseBody);
        }
    }

    private void addAuthenticationHeaders(HttpPost request) {
        String authHeader = "Basic " + this.authValue;
        request.setHeader("Authorization", authHeader);
    }
}
