package com.nexmo.voice.core.emergency.address;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlContent;

public class EmergencyAddressServiceConfigLoader extends NestedXmlHandler {

    private String primaryServiceUrl;

    private String secondaryServiceUrl;

    private int serviceTimeout;

    private boolean primaryUseBearerToken;

    private boolean secondaryUseBearerToken;

    private EmergencyAddressServiceConfig config;

    public EmergencyAddressServiceConfigLoader(final String nodeName) {
        super(nodeName);
    }

    public EmergencyAddressServiceConfig getConfig() {
        return config;
    }

    @Override
    public void startNode(String node, XmlContent xmlContent) throws LoaderException {
        if (getNodeName().equals(node)) { // <locale>
            this.primaryServiceUrl = xmlContent.getAttribute(EmergencyAddressServiceConfig.ATTR_PRIMARY_SERVICE_URL, true);
            this.primaryUseBearerToken = Boolean.parseBoolean(xmlContent.getAttribute(EmergencyAddressServiceConfig.ATTR_PRIMARY_USE_BEARER_TOKEN, false, EmergencyAddressServiceConfig.PRIMARY_USE_BEARER_TOKEN_DEFAULT_VALUE));
            this.secondaryServiceUrl = xmlContent.getAttribute(EmergencyAddressServiceConfig.ATTR_SECONDARY_SERVICE_URL, false);
            this.secondaryUseBearerToken = Boolean.parseBoolean(xmlContent.getAttribute(EmergencyAddressServiceConfig.ATTR_SECONDARY_USE_BEARER_TOKEN, false, EmergencyAddressServiceConfig.SECONDARY_USE_BEARER_TOKEN_DEFAULT_VALUE));
            this.serviceTimeout = Integer.parseInt(xmlContent.getAttribute(EmergencyAddressServiceConfig.ATTR_SERVICE_TIMEOUT, false, EmergencyAddressServiceConfig.SERVICE_TIMEOUT_DEFAULT_VALUE));
        }
    }

    @Override
    public void endNode(String node, String contentData) throws LoaderException {
        if (getNodeName().equals(node)) {
            try {
                config = new EmergencyAddressServiceConfig();
                config.setPrimaryServiceUrl(this.primaryServiceUrl);
                config.setPrimaryUseBearerToken(this.primaryUseBearerToken);
                config.setServiceTimeout(this.serviceTimeout);
                if ((this.secondaryServiceUrl != null) && !this.secondaryServiceUrl.isEmpty()) {
                    config.setSecondaryServiceUrl(this.secondaryServiceUrl);
                    config.setSecondaryUseBearerToken(this.secondaryUseBearerToken);
                }
            } catch(EmergencyAddressServiceException e) {
                throw new LoaderException(e.getMessage(), e);
            }
            notifyComplete();
        }
    }
}
