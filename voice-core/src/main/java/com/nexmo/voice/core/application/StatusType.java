package com.nexmo.voice.core.application;

public enum StatusType {

    ENABLED("enabled", true, false),
    DISABLED("disabled", false, false),
    BANNED("banned", false, true);

    private final String key;
    private final boolean enabled;
    private final boolean banned;

    StatusType(final String key, final boolean enabled, final boolean banned) {
        this.key = key;
        this.enabled = enabled;
        this.banned = banned;
    }

    public String getKey() {
        return this.key;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public boolean isBanned() {
        return this.banned;
    }

    public static StatusType getMatching(final String key) {
        if (key != null)
            for (final StatusType type : StatusType.values())
                if (type.getKey().equals(key))
                    return type;
        return StatusType.ENABLED;
    }

    public static boolean isValidKey(final String key) {
        for (final StatusType type : StatusType.values())
            if (key != null && type.getKey().equals(key))
                return true;
        return false;
    }

}