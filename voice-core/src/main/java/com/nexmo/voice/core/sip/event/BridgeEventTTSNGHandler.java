package com.nexmo.voice.core.sip.event;

import java.math.BigDecimal;
import java.util.Objects;

import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.asteriskjava.manager.event.BridgeEvent;

import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.ChargingConfig.CountrySpecificInfo;
import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.SipAppUtils;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cache.VoiceContextCache;
import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.pdd.PDDCalculator;
import com.nexmo.voice.core.types.CallInternalFlag;
import com.nexmo.voice.core.types.EffectiveCost;
import com.nexmo.voice.core.types.EffectivePrice;
import com.nexmo.voice.core.types.VoiceDirection;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.accounts.Accounts;
import com.thepeachbeetle.messaging.hub.core.exceptions.AccountsException;

public final class BridgeEventTTSNGHandler {

    private static final Logger Log = LogManager.getLogger(BridgeEventTTSNGHandler.class);

    protected static void handleBridgeEvent(BridgeEvent event, String sessionId,
                                            VoiceContext origContext) throws VoiceEventHandlerException {

        Log.info("{}: Processing TTSNG Bridge Event ", sessionId);

        VoiceContext destContext;
        SIPAsteriskContext originApplicationContext = (SIPAsteriskContext) origContext.getApplicationContext();
        String to = origContext.getTo(); //This is the originally requested "to"
        String from = origContext.getFrom();
        String forceSender = origContext.getForcedSender();
        String sourceCountryCode = origContext.getSourceCountryCode();

        // CLI to use for Caller ID ("From:"), P-Asserted-Identity and source-prefix pricing
        String cli = from;
        if (Objects.nonNull(forceSender))
            cli = forceSender;

        //A note for when I will be here for the new TTSNG:
        //For outbound calls, we already fetched the out leg price for checking the
        //balance during the AGI request - there is no need to fetch it here again - 
        // take it from the first leg (if possible)

        String gateway = originApplicationContext.getOutboundGateway();
        String network = origContext.getNetwork();
        String countryCode = origContext.getCountryCode();
        String callTermination = origContext.getCallTermination();

        VoiceProduct origVoiceProduct = origContext.getVoiceProduct();

        String accountId = origContext.getAccountId();

        SmppAccount account = getAccount(accountId, sessionId, origContext);

        //Price
        EffectivePrice matrixPrice = getMatrixPrice(sessionId, origContext);
        BigDecimal forcedPrice = getFrocedPrice(sessionId, origContext);

        // Cost ..
        EffectiveCost unitCost = getCost(sessionId, account, network, gateway, to, cli, origVoiceProduct);

        //Quota updates frequency 
        ChargingConfig chargingConfig = Core.getInstance().getConfig().getChargingConfig();
        final CountrySpecificInfo specificInfo = chargingConfig.getCountrySpecificInfo(origContext.getCountryCode());
        long minIncrement = specificInfo.getMinIncrement();
        long recurringIncrement = specificInfo.getRecurringIncrement();

        boolean isAuxiliary = false; //The call is not for an LVN which is directed to an application.

        SIPAsteriskContext applicationContext = new SIPAsteriskContext(event.getChannel2(),
                                                                       originApplicationContext.getClientCallId(),
                                                                       originApplicationContext.getRedirectionCallbackAddress(),
                                                                       gateway,
                                                                       isAuxiliary,
                                                                       originApplicationContext.getRemainingAttempts(),
                                                                       originApplicationContext.getApplicationId());

        boolean isChargeable = !origContext.isVoiceSkipQuota() && !SipAppUtils.isNotChargeablePricePrefix(matrixPrice.getPrefix());

        boolean isVpricingEnabled = origContext.isVpricingEnabled();
        if (isChargeable && isVpricingEnabled && forcedPrice != null && BigDecimal.ZERO.compareTo(forcedPrice) == 0) {
            isChargeable = false;
        }

        destContext = new VoiceContext.Builder().withVoiceProduct(origVoiceProduct)
                                                .withFrom(from)
                                                .withTo(to)
                                                .withAccountId(accountId)
                                                .withMasterAccountId(origContext.getMasterAccountId())
                                                .withAccountPricingGroup(origContext.getAccountPricingGroup())
                                                .withMasterAccountPricingGroup(origContext.getMasterAccountPricingGroup())
                                                .withGateway(gateway)
                                                .withNetwork(network)
                                                .withNetworkType(origContext.getNetworkType())
                                                .withNetworkName(origContext.getNetworkName())
                                                .withCountryCode(countryCode)
                                                .withVoiceDirection(VoiceDirection.OUTBOUND)
                                                .withApplicationContext(applicationContext)
                                                .withProductClass(origContext.getProductClass())
                                                .withProductClassVersion(origContext.getProductClassVersion())
                                                .withPricePerMinute(matrixPrice.getPrice())
                                                .withPricePrefix(matrixPrice.getPrefix())
                                                .withPricePrefixGroup(matrixPrice.getPrefixGroup())
                                                .withPriceSenderPrefix(matrixPrice.getSenderPrefix())
                                                .withPriceTimestamp(matrixPrice.getTimestamp())
                                                .withCostPerMinute(unitCost.getCost())
                                                .withCostPrefix(unitCost.getPrefix())
                                                .withCostPrefixGroup(unitCost.getPrefixGroup())
                                                .withCostSenderPrefix(unitCost.getSenderPrefix())
                                                .withCostTimestamp(unitCost.getTimestamp())
                                                .withFirstChargedSeconds(minIncrement)
                                                .withQuotaUpdatesInterval(recurringIncrement)
                                                .withCallbackMethod(origContext.getCallbackMethod())
                                                .withCallbackUrl(origContext.getCallbackUrl())
                                                .withInternalCallbackMethod(origContext.getInternalCallbackMethod())
                                                .withInternalCallbackUrl(origContext.getInternalCallbackUrl())
                                                .withSequenceNumber(origContext.getSequenceNumber())
                                                .withForcedPrice(forcedPrice)
                                                .withRequestIp((origContext.getRequestIp()))
                                                .withForcedSender(forceSender)
                                                .withCallTermination(callTermination)
                                                .withCallOrigin(origContext.getCallOrigin())
                                                .withQuotaRef(SipAppUtils.generateQuotaReference(sessionId, false))
                                                .withAGIRequestContext(origContext.getAGIRequestContext())
                                                .withTTSContext(origContext.getTtsContext())
                                                .withStirShaken(origContext.getStirShaken())
                                                .withInternalFlags(origContext.getInternalFlags())
                                                .withVoiceSkipQuota(origContext.isVoiceSkipQuota())
                                                .withAsteriskVersion(origContext.getAsteriskVersion())
                                                .withClientCallId(origContext.getClientCallId())
                                                .withId(origContext.getId())
                                                .withBlockingSubsystem(origContext.getBlockingSubsystem())
                                                .withProductPath(origContext.getProductPath())
                                                .withSourceCountryCode(sourceCountryCode)
                                                .withCallType(origContext.getCallType())
                                                .withNumberType(origContext.getNumberType())
                                                .withRoutingGroup(origContext.getRoutingGroup())
                                                .withRoutingOa(origContext.getRoutingOa())
                                                .withRoutingBindId(origContext.getRoutingBindId())
                                                .withChargeable(isChargeable)
                                                .withVpricingEnabled(isVpricingEnabled)
                                                .build();

        destContext.setSessionId(sessionId);
        destContext.setConnectionId(event.getUniqueId2());

        setPDDStop(sessionId, event.getUniqueId1(), event.getUniqueId2(), applicationContext);

        //This is the second leg caching, the charging will not start yet...
        VoiceContextCache cache = Core.getInstance().getVoiceContextCache();
        cache.storeContext(sessionId, event.getUniqueId2(), destContext);
        if (Log.isDebugEnabled())
            Log.debug("{}: TTSNG Bridge Event - building and storing context {} ", sessionId, destContext.getDebugString());


        if (Objects.isNull(cache.getContext(sessionId, event.getUniqueId2()))) {
            Log.info("{}: TTSNG Bridge Event: context of {} has been already removed - don't charge ",
                    sessionId, event.getUniqueId2());
            return;
        }

        // Start charging both legs
        BridgeEventHandler.startChargingContext(origContext,
                                event.getChannel1(),
                                event.getChannel2(),
                                event);

        BridgeEventHandler.startChargingContext(destContext,
                                event.getChannel1(),
                                event.getChannel2(),
                                event);
    }

    private static BigDecimal getFrocedPrice(String sessionId, VoiceContext origContext) {
        BigDecimal forcedPrice = null;
        if (Objects.nonNull(origContext.getAGIRequestContext().getForcedPrice()))
            forcedPrice = origContext.getAGIRequestContext().getForcedPrice().getPrice();

        if (Log.isDebugEnabled())
            Log.debug("{}: forcedPrice as asked during the call initiation: {}", sessionId, forcedPrice);
        return forcedPrice;
    }


    private static SmppAccount getAccount(String accountId, String sessionId, VoiceContext origContext) throws VoiceEventHandlerException {
        SmppAccount account = null;
        try {
            account = Accounts.getInstance().getSmppAccount(accountId);
            if (Log.isDebugEnabled())
                Log.debug("Account found for accountId {} sessionId {}", accountId, sessionId);
        } catch (AccountsException e) {
            Log.error("{} Failed to retrieve account {} due to {}.", sessionId, accountId, e.getMessage());
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            throw new VoiceEventHandlerException("Account not found");
        }

        if (Objects.isNull(account)) {
            Log.error("{} Account {} not found.", sessionId, accountId);
            origContext.addInternalFlag(CallInternalFlag.BLOCKED_ON_ACCOUNT_ISSUES);
            throw new VoiceEventHandlerException("Account not found");
        }
        return account;
    }

    private static EffectivePrice getMatrixPrice(String sessionId, VoiceContext origContext) {
        //The price of the second leg as was already calculated in the AGI Request
        EffectivePrice matrixPrice = origContext.getAGIRequestContext().getOutboundPrice();
        if (Log.isDebugEnabled()) {
            Log.debug("{}: second leg matrix price: {}", sessionId, matrixPrice);
        }
        return matrixPrice;
    }


    private static EffectiveCost getCost(String sessionId, SmppAccount account, String network, String gateway, String to, String from, VoiceProduct origVoiceProduct) {
        GatewayInfoMatrixConfig gwInfoMatrixConfig = Core.getInstance().getConfig().selectGatewayInfoMatrixConfig(origVoiceProduct);
        ChargingConfig chargingConfig = Core.getInstance().getConfig().getChargingConfig();

        SupplierMappingConfig gwInfo = null;
        PriceMatrixList costMatrix = null;
        if (!"default".equals(gateway)) {
            gwInfo = gwInfoMatrixConfig.getGatewayInfo(gateway);
            costMatrix = gwInfo == null ? null : gwInfo.getCostMatrix();
        }

        if (gwInfo == null) {
            if ("default".equals(gateway)) {
                Log.info("{}: Outbound gateway is 'default'. WILL USE DEFAULT COST for account {}",
                        sessionId, account.getSysId());
            } else {
                if (!account.getSysId().equals(gateway)) //SIP-221
                    Log.error("{}: Could not find gateway info for outbound gateway {} WILL USE DEFAULT COST for account {} ",
                            sessionId, account.getSysId());
            }
        }
        EffectiveCost unitCost = EffectiveCost.getCostFromMatrixOrDefault(costMatrix,
                                                                            account,
                                                                            to,
                                                                            from,
                                                                            null, //originApplicationContext.getApplicationId(),
                                                                            network,
                                                                            chargingConfig.getDefaultSipCost(),
                                                                            false, //origContext.isVAPIOutboundToVBC(),
                                                                            "BridgeEvent-cost");

        if (Log.isDebugEnabled())
            Log.debug("{}: leg2 cost: {}", sessionId, unitCost.toString());
        return unitCost;
    }


    private static void setPDDStop(String sessionId, String uniqueId1, String uniqueId2, SIPAsteriskContext appCtx) {
        PDDCalculator pddCalculator = Core.getInstance().getPddCalculator();
        try {
            appCtx.setPdd(pddCalculator.calculate(uniqueId1, uniqueId2));
        } catch (PDDCalculationException e) {
            Log.warn("{}: PDD can't be calculated for the call with {} and {} due to {} ",
                    sessionId, uniqueId1, uniqueId2, e.getMessage());
        }

    }


}
