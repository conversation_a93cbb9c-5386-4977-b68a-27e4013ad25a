package com.nexmo.voice.core.randomize;

import java.util.Map;

import com.nexmo.voice.core.Core;
import com.thepeachbeetle.messaging.hub.config.randomize.RandomPool;
import com.thepeachbeetle.messaging.hub.config.randomize.SenderRandomizerConfig;
import com.thepeachbeetle.messaging.hub.core.provisioning.exceptions.ProvisioningException;
import com.thepeachbeetle.messaging.hub.core.provisioning.exceptions.RandomPoolNotFoundException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RandomPoolNotificationUpdateTask implements Runnable{
    private static final Logger Log = LogManager.getLogger(RandomPoolNotificationUpdateTask.class);

    public RandomPoolNotificationUpdateTask() {
        Log.debug("RandomPoolNotification update task created");
    }

    @Override
    public void run() {
        // go through all the cached pool and check to see if the timestamp is greater than two minutes
        Map<String, Long> poolIds = Randomizer.getInstance().getAllPoolIdsCacheWithNotifications();
        if (poolIds.isEmpty()) {
            Log.info("RandomPoolNotificationUpdateTask: no poolIds in the cache");
            return;
        }

        for (Map.Entry<String,Long> entry : poolIds.entrySet()) {
            String poolId = entry.getKey();
            Long timestamp = entry.getValue(); // milliseconds
            Log.debug("For poolId: " + poolId + " timestamp:" + timestamp + " currentTime" + System.currentTimeMillis());
            if ((System.currentTimeMillis() - timestamp) > 2*60*1000) {
                try
                {
                    // longer than 2 minutes call provisioning client and to our cache
                    RandomPool randomPool = Randomizer.getRandomPoolFromProvisioningClient(poolId);
                    SenderRandomizerConfig senderRandomizerConfig = Core.getInstance().getConfig().getSenderRandomizerConfig();
                    if (senderRandomizerConfig != null)
                        senderRandomizerConfig.addRandomPool(randomPool);

                    Randomizer.getInstance().addRandomPool(randomPool);
                    Log.info("RandomPoolNotificationUpdateTask: new RandomPool is available: " + poolId);
                    Log.debug("RandomPoolNotificationUpdateTask: new RandomPool is available: " + poolId + "  :" + randomPool.toJSON(true).toString());
                    // remove from our cache
                    Randomizer.getInstance().removeNotificationsCacheRandomPoolId(poolId);
                } catch (ProvisioningException e) {
                    Log.error("RandomPoolNotificationUpdateTask [ " + poolId + " ] error while inserting/saving to db", e);
                    //todo Shall we still remove it from our random Pool cache
                } catch (RandomPoolNotFoundException e) {
                    // Possibly the random pool is deleted
                    Log.debug("RandomPoolNotificationUpdateTask [ " + poolId + " ] is removed.", e);
                    Randomizer.getInstance().removeRandomPool(poolId);
                    // remove from our cache
                    Randomizer.getInstance().removeNotificationsCacheRandomPoolId(poolId);
                }
            }
        }
    }
}