package com.nexmo.voice;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.management.MBeanServerConnection;
import javax.management.Notification;
import javax.management.NotificationListener;
import javax.management.ObjectName;
import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Logger;

import com.thepeachbeetle.common.logging.LoggingUtil;
import com.thepeachbeetle.common.sys.PID;
import com.thepeachbeetle.common.xml.LoaderException;

import com.thepeachbeetle.deployment.check.AppStartupDeploymentCheck;
import com.thepeachbeetle.deployment.check.StartupDeploymentCheck;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;

/**
 * <AUTHOR>
 */
public class Stop {

    private static final Logger Log = Logger.getLogger(Stop.class.getName());

    public static void main(String[] args) {

        BasicConfigurator.configure();
        String pid = PID.getPid();
        Log.info("====== PID :: " + pid + " =========");

        if (args == null || args.length < 1) {
            Log.error("ERROR :: must specify config.xml file of the service to terminate on the command line ..........");
            System.exit(1);
            throw new RuntimeException("ERROR :: must specify config.xml file of the service to terminate on the command line ..........");
        }

        LoggingUtil.setRootLogLevelToInfo();

        for (String arg : args) {
            String configXmlFileLocation = arg;
            configXmlFileLocation = configXmlFileLocation.replaceAll("\r", "");
            Log.info("... terminating log-tailer for config [ " + configXmlFileLocation + " ] .....");

            Config config = null;

            try {
                boolean skipDB = true;
                ConfigReader reader = new ConfigReader(skipDB);
                reader.read(new File(configXmlFileLocation));
                config = reader.getConfig();
            } catch (LoaderException e) {
                Log.error("Unable to load configuration from [ " + configXmlFileLocation + " ] file. --- ABORTING", e);
                System.exit(1);
                throw new RuntimeException("Unable to load configuration from [ " + configXmlFileLocation + " ] file. --- ABORTING", e);
            }

            try {
                // Are we allowed to start in this environment ?
                StartupDeploymentCheck startupDeploymentCheck = new AppStartupDeploymentCheck("voice", config);
                startupDeploymentCheck.checkStartupPermitted(config.getDeploymentCheckConfig());
            } catch (Exception e) {
                Log.error("........ cannot shut-down this instance.. failed deployment matrix check ........", e);
                System.exit(1);
            }

            if (config.getJMXConfig() == null || !config.getJMXConfig().isEnabled()) {
                Log.error("..... cannot shut-down this instance .. no jmx listener configured .....");
                System.exit(1);
            }

            // We need a JMX client for the running connector ....

            MBeanServerConnection mbsc = null;
            try {
                Log.info("\nCreate an RMI connector client and connect it to the RMI connector server");
                //JMXServiceURL url = new JMXServiceURL("service:jmx:rmi://localhost:" + config.getJmxListenPort() + "/jndi/rmi");

                int jmxPort = config.getJMXConfig().getListenPort();
                int rmiRegistryPort = config.getJMXConfig().getListenPort();
                JMXServiceURL url = new JMXServiceURL("service:jmx:rmi://localhost:" + jmxPort + "/jndi/rmi://localhost:" + rmiRegistryPort + "/jmxrmi");

                Map<String, String> h = new HashMap<>();

                //Specify the user ID and password for the server if security is enabled on server.

                // String[] credentials = new String[] { username, password };
                // h.put("jmx.remote.credentials", credentials);

                JMXConnector jmxc = JMXConnectorFactory.connect(url, h);

                mbsc = jmxc.getMBeanServerConnection();
            } catch (Exception e) {
                Log.error("... failed to establish an rmi connection to the remote jmx port for the connector running config [ " + configXmlFileLocation + " ] ..", e);
                System.exit(1);
                throw new RuntimeException("... failed to establish an rmi connection to the remote jmx port for the connector running config [ " + configXmlFileLocation + " ] ..", e);
            }

            // grab a handle to the Core Mbean on the running connector ...
            ObjectName coreMbean = null;
            try {
                String query = "com.nexmo.voice.core:hub=core,*";
                ObjectName queryName = new ObjectName(query);
                Set<ObjectName> s = mbsc.queryNames(queryName, null);
                if (!s.isEmpty())
                    coreMbean = s.iterator().next();
                else {
                    Log.error("Connector core MBean was not found for config [ " + configXmlFileLocation + " ] ");
                    System.exit(-1);
                }
            } catch (Exception e) {
                Log.error(e);
                System.exit(-1);
            }

            // Invoke the 'shutdown' jmx function ...

            try {
                String[] signature = null;
                String[] params = null;

                String timeStarted = (String) mbsc.getAttribute(coreMbean, "TimeStarted");
                String upTime = (String) mbsc.getAttribute(coreMbean, "Uptime");
                mbsc.invoke(coreMbean, "dumpXmlToLog", params, signature);
                Log.info("Log-Tailer was started at [ " + timeStarted + " ] and ran for [ " + upTime + " ] ");
            } catch (Exception e) {
                Log.error("Exception talking to jmx", e);
                System.exit(-1);
            }

            String[] signature = {"java.lang.String"};
            String[] params = {"Shutting Down"};
            try {
                Log.info("..... starting shutdown of logtailer-instance [ " + config.getInstanceId() + " ] ");
                mbsc.invoke(coreMbean, "shutdown", params, signature);
                Log.info("..... finished shutdown of logtailer-instance [ " + config.getInstanceId() + " ] ");
            } catch (Exception e) {
                Log.error("Exception invoking remote shutdown() call ", e);
                System.exit(-1);
            }
        }

        File pidFile = new File("pid/running.pid");
        if (!pidFile.delete())
            Log.error("Failed to remove pid file [ " + pidFile.getAbsolutePath() + " ] ");

        System.exit(0);
    }

    public static class ClientListener implements NotificationListener {

        @Override
        public void handleNotification(Notification notification, Object handback) {
            Log.info("***************************************************");
            Log.info("* Notification received at " + new Date().toString());
            Log.info("* type      = " + notification.getType());
            Log.info("* message   = " + notification.getMessage());
            Log.info("* source    = " + notification.getSource());
            Log.info("* seqNum    = " + Long.toString(notification.getSequenceNumber()));
            Log.info("* timeStamp = " + new Date(notification.getTimeStamp()));
            Log.info("* userData  = " + notification.getUserData());
            Log.info("***************************************************");
        }

    }

}
