package com.nexmo.voice;

import java.io.File;

import javax.management.InstanceAlreadyExistsException;
import javax.management.MBeanRegistrationException;
import javax.management.MBeanServer;
import javax.management.MalformedObjectNameException;
import javax.management.NotCompliantMBeanException;
import javax.management.ObjectName;
import javax.servlet.http.HttpServlet;

import com.nexmo.voice.core.jmx.*;
import org.apache.log4j.Logger;

import com.thepeachbeetle.common.app.ServerStartException;
import com.thepeachbeetle.common.app.boot.BootConfigReader;
import com.thepeachbeetle.common.app.jmx.LoggingJMX;
import com.thepeachbeetle.common.app.logging.AppStartupLogger;
import com.thepeachbeetle.common.app.signals.Signals;
import com.thepeachbeetle.common.cluster.Cluster;
import com.thepeachbeetle.common.cluster.jmx.ClusterJMX;
import com.thepeachbeetle.common.http.HttpServer;
import com.thepeachbeetle.common.http.servlet.PingServlet;
import com.thepeachbeetle.common.jmx.JMXUtil;
import com.thepeachbeetle.common.ssl.SSLClientCore;
import com.thepeachbeetle.common.sys.PID;
import com.thepeachbeetle.common.xml.LoaderException;

import com.thepeachbeetle.deployment.check.AppStartupDeploymentCheck;
import com.thepeachbeetle.deployment.check.StartupDeploymentCheck;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.ConfigReader;
import com.nexmo.voice.config.api.InternalApiConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.Shutdown;
import com.nexmo.voice.core.monitoring.MonitoringServlet;
import com.nexmo.voice.core.sip.api.InternalApiServlet;


/**
 * <AUTHOR> Cook
 */
public class Server {

    private static final Logger Log = Logger.getLogger(Server.class.getName());

    public static void main(String[] args) {

        if (args == null || args.length < 1) {
            System.err.println("ERROR :: must specify configs on the command line ..........");
            throw new RuntimeException("ERROR :: must specify configs on the command line ..........");
        }

        String configXmlFileLocation = args[0];
        if (configXmlFileLocation == null || configXmlFileLocation.trim().equals(""))
            configXmlFileLocation = "dummy.xml";
        configXmlFileLocation = configXmlFileLocation.replaceAll("\r", "");
        System.out.println("... kicking off config [ " + configXmlFileLocation + " ] .....");

        try {
            new Server(configXmlFileLocation);
        } catch (ServerStartException e) {
            Log.error("Failed to start Server Instance for 'Some Reason' ...", e);
            System.err.println("Failed to start Server Instance for 'Some Reason' ...");
            e.printStackTrace(System.err);
            System.exit(0);
        }

        String pid = PID.getPid();
        System.out.println("====== PID :: " + pid + " =========");

        for (; ; ) {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
            }
        }
    }

    public Server(String configXmlFileLocation) throws ServerStartException {

        try {
            BootConfigReader reader = new BootConfigReader(ConfigReader.ROOT_NODE);
            reader.read(new File(configXmlFileLocation));
        } catch (LoaderException e) {
            throw new ServerStartException("Unable to load configuration from [ " + configXmlFileLocation + " ] file. --- ABORTING", e);
        }

        // Write to boot.log
        new AppStartupLogger(this);

        // Load Config
        ConfigReader configReader = new ConfigReader();
        try {
            try {
                configReader.read(new File(configXmlFileLocation));
            } catch (LoaderException e) {
                Log.error("Unable to load dummy-server configuration from [ " + configXmlFileLocation + " ] file. ", e);
                throw e;
            }
        } catch (LoaderException e) {
            Log.fatal("..... Failed to read config file [ " + configXmlFileLocation + " ] ... .. Aborting", e);
            throw new ServerStartException("..... Failed to read config file [ " + configXmlFileLocation + " ] ... .. Aborting", e);
        }
        Config config = configReader.getConfig();
        config.dumpXmlToLog();

        // Check for fatal configuration errors
        if ((config == null) ||
                (config.getSIPGatewayInfoMatrixConfig() == null) ||
                !config.getSIPGatewayInfoMatrixConfig().hasGateways()) {
            Log.fatal("Config file [ " + configXmlFileLocation + " ] contains no sip gateways - aborting");
            throw new ServerStartException("Config file [ " + configXmlFileLocation + " ] contains no sip gateways - aborting");
        }

        if ((config == null) ||
                (config.getTTSGatewayInfoMatrixConfig() == null) ||
                !config.getTTSGatewayInfoMatrixConfig().hasGateways()) {
            Log.fatal("Config file [ " + configXmlFileLocation + " ] contains no tts gateways - aborting");
            throw new ServerStartException("Config file [ " + configXmlFileLocation + " ] contains no tts gateways - aborting");
        }

        // Are we allowed to start in this environment ?
        StartupDeploymentCheck startupDeploymentCheck = new AppStartupDeploymentCheck("voice", config);
        startupDeploymentCheck.checkStartupPermitted(config.getDeploymentCheckConfig());

        // by default, DNS cacheing ignores ttl's and retains cached lookups for ever,  this is a load of old bollocks,   change this default behaviour!!
        java.security.Security.setProperty("networkaddress.cache.ttl", "900");

        // If we have a custom SSL CA-Certa trust-store configured, then register it with the https client ...
        SSLClientCore.getInstance().init(config.getSSLClientConfig());

        // Install some signal handlers to catch things like SIG-TERM and turn them into a clean shutdown ....
        Signals.installHandlers(Shutdown.getInstance());

        // Initialize the Cluster-Core (this has to be done early before anything that is likely to use it is initialized)
        try {
            if (config.getClusterConfig() != null && config.getClusterConfig().isEnabled())
                Cluster.getInstance().initialize(config.getClusterConfig());
        } catch (Exception e) {
            Log.error("Failed to initialize the cluster-core ...", e);
            throw new ServerStartException("Failed to initialize the cluster-core ...", e);
        }

        // Expose the Metrics API very early so that binding can quickly take place
        if (config.getMetricsConfig() != null) {
            config.getMetricsConfig().init();
        }

        // Initialize the core ...
        try {
            Core.getInstance().init(config);
        } catch (Exception e) {
            Log.fatal("Failed to initialize the dummy-server core ....... aborting .......", e);
            throw new ServerStartException("Failed to initialize the dummy-server core ....... aborting .......", e);
        }

        // JMX Monitor
        MBeanServer mbeanServer = null;
        try {
            mbeanServer = JMXUtil.startJMXConnector(config.getJMXConfig());
        } catch (Exception e) {
            Log.fatal("Failed to set up jmx listener on port [ " + config.getJMXConfig() + " ] ", e);
            throw new ServerStartException("Failed to set up jmx listener on port [ " + config.getJMXConfig() + " ] ", e);
        }

        CoreJMX mbean = new CoreJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core:hub=core");
            mbeanServer.registerMBean(mbean, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up core mbean ...", e);
            throw new ServerStartException("Failed to set up core mbean ...", e);
        }

        ReloadJMX reloadJMX = new ReloadJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.reload:hub=core");
            mbeanServer.registerMBean(reloadJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up reload mbean ...", e);
            throw new ServerStartException("Failed to set up reload mbean ...", e);
        }

        DebugJMX debugJMX = new DebugJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.debug:hub=core");
            mbeanServer.registerMBean(debugJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up debug mbean ...", e);
            throw new ServerStartException("Failed to set up debug mbean ...", e);
        }

        ClusterJMX clusterMbean = new ClusterJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.cluster:hub=core");
            mbeanServer.registerMBean(clusterMbean, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up cluster mbean ...", e);
            throw new ServerStartException("Failed to set up cluster mbean ...", e);
        }

        // Now bind the Logging Manager MBean to JMX
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.logging:hub=" + config.getInstanceId());
            LoggingJMX loggingManager = new LoggingJMX();
            mbeanServer.registerMBean(loggingManager, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up mbean for Logging Manager ...", e);
            throw new ServerStartException("Failed to set up mbean for Logging Manager ...", e);
        }

        CallFlowJMX callFlowJMX = new CallFlowJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.callsflow:hub=core");
            mbeanServer.registerMBean(callFlowJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up CallFlow mbean ...", e);
            throw new ServerStartException("Failed to set up CallFlow mbean ...", e);
        }

        SuppliersJMX suppliersJMX = new SuppliersJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.suppliers:hub=core");
            mbeanServer.registerMBean(suppliersJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up Suppliers mbean ...", e);
            throw new ServerStartException("Failed to set up Suppliers mbean ...", e);
        }

        final TTSSuppliersJMX ttsSuppliersJMX = new TTSSuppliersJMX();
        try {
            mbeanServer.registerMBean(ttsSuppliersJMX, new ObjectName("com.nexmo.voice.core.tts.suppliers:hub=core"));
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up TTS Suppliers mbean ...", e);
            throw new ServerStartException("Failed to set up TTS Suppliers mbean ...", e);
        }

        NumbersJMX numbersJMX = new NumbersJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.mappings:hub=core");
            mbeanServer.registerMBean(numbersJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up Numbers mbean ...", e);
            throw new ServerStartException("Failed to set up Numbers mbean ...", e);
        }

        CachesJMX CachesJMX = new CachesJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.caches:hub=core");
            mbeanServer.registerMBean(CachesJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up Caches mbean ...", e);
            throw new ServerStartException("Failed to set up Caches mbean ...", e);
        }

        PrefixMapJMX PrefixMapJMX = new PrefixMapJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.prefix:hub=core");
            mbeanServer.registerMBean(PrefixMapJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up PrefixMap mbean ...", e);
            throw new ServerStartException("Failed to set up PrefixMap mbean ...", e);
        }

        CallBlocksJMX callBlocksJMX = new CallBlocksJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.callBlocks:hub=core");
            mbeanServer.registerMBean(callBlocksJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up Call Blocks mbean ...", e);
            throw new ServerStartException("Failed to set up Call Blocks mbean ...", e);
        }

        if (Core.getInstance().isAsyncQuotaFlag()) {
            AsyncQuotaJMX asyncQuotaJMX = new AsyncQuotaJMX();
            try {
                ObjectName name = new ObjectName("com.nexmo.voice.core.asyncQuota:hub=core");
                if (!mbeanServer.isRegistered(name)) {
                    mbeanServer.registerMBean(asyncQuotaJMX, name);
                }
            } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
                Log.fatal("Failed to set up Async Quota mbean ...", e);
                throw new ServerStartException("Failed to set up Async Quota mbean ...", e);
            }
        }

        ShutdownJMX shutdownJMX  = new ShutdownJMX();
        try {
            ObjectName name = new ObjectName("com.nexmo.voice.core.shutdown:hub=core");
            mbeanServer.registerMBean(shutdownJMX, name);
        } catch (MalformedObjectNameException | InstanceAlreadyExistsException | MBeanRegistrationException | NotCompliantMBeanException e) {
            Log.fatal("Failed to set up Shutdown mbean ...", e);
            throw new ServerStartException("Failed to set up Shutdown mbean ...", e);
        }


        // Internal Api Endpoint Servlet
        if (config.getHttpInternalApiEndpointConfig() != null && config.getHttpInternalApiEndpointConfig().isEnabled()) {
            InternalApiConfig internalApiConfig = config.getInternalApiConfig();

            if (internalApiConfig == null) {
                String errorMsg = "Failed to initialize the http-internal-api http interface [InternalApiConfig was null!] .......aborting .....";
                Log.fatal(errorMsg);
                throw new ServerStartException(errorMsg);
            }

            try {
                final String hubName = "core";
                final String httpServerName = "internal-api";
                final String context = internalApiConfig.getEndpointConfig().getContext();
                final String path = internalApiConfig.getEndpointConfig().getJsonPath();

                HttpServer.getInstance().initializeHttpListener(hubName,
                                                                httpServerName,
                                                                config.getHttpInternalApiEndpointConfig());


                HttpServlet internalApiServlet = new InternalApiServlet();
                HttpServlet pingServlet = new PingServlet();
                HttpServer.getInstance().addServletToHttpContext(hubName, httpServerName, context, "/" + path, internalApiServlet, null, null);
                Log.info(">>> PROXY API SERVLET .. add interface at [ /" + context + " ] [ /" + path + " ] ");
                HttpServer.getInstance().addServletToHttpContext(hubName, httpServerName, "sip", "/ping", pingServlet, null, null);
                HttpServer.getInstance().startHttpServer(hubName, httpServerName);
            } catch (Exception e) {
                Log.fatal("Failed to initialize the http-internal-api http interface .......aborting .....", e);
                throw new ServerStartException("Failed to initialize the http-internal-api http interface .......aborting .....", e);
            }
        }

        // Initialize the Monitoring API Listener
        if (config.getMonitoringConfig() != null) {
            HttpServlet monitoringServlet = new MonitoringServlet();
            config.getMonitoringConfig().listen(monitoringServlet);
        }

    }

}

