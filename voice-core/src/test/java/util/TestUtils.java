package util;

import java.io.File;

public class TestUtils {

    private TestUtils() {
        // utility class should not be instantiable
    }

    /**
     * recursively deletes the file and any of it's subfiles.
     * @throws a RuntimeException if the cleanup failed in order to fail the test.
     */
    public static void recursiveFileDelete(File fileToDelete) {
        if (fileToDelete.isDirectory()) {
            File[] files = fileToDelete.listFiles();
            if (files != null)
                for (File file: files)
                    recursiveFileDelete(file);
        }
        if (!fileToDelete.delete())
            throw new RuntimeException("Failed to delete " + fileToDelete.getAbsolutePath());
    }

}
