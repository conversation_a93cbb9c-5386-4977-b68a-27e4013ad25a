<gateway>
    <core hubname = "vodafone-mpay">
        <listen enabled='true' addr="0.0.0.0" port="8860"/>
        <jmx enabled="true" listen-addr="0.0.0.0" listen-port="8863"  http-enabled="true" http-port="8865" http-username="netops" http-password="password"/>
        <persistance-directory>tmp_config/persistence</persistance-directory>
        <assign-local-message-id sequence-range-start="0" sequence-range-end="0x0fffffff">true</assign-local-message-id>
        <enquire-link send="true" interval="30000" />
        <so-timeout>60000</so-timeout>
        <daemon-threads send-window-size="100">4</daemon-threads>
        <http-server-mo enabled="false" addr="0.0.0.0" port="8861" mo-sessions-enabled="false" mo-session-max-life="600000" persistence="true" persistence-dir="mo-sessions-persistence" access-log-location="tmp_config/logs" access-log-prefix="http-mo" min-threads="10" max-threads="100" mo-sessions-clustering-enabled="false">
            <ssl enabled="false" port="8868" keystore="/tmp/keystore.dat" keystore-password="pass" key-password="keypass" />
        </http-server-mo>
        <http-server-client enabled="true" addr="0.0.0.0" port="8866" access-log-location="tmp_config/logs" access-log-prefix="http-client" min-threads="10" max-threads="100">
            <ssl enabled="false" port="8867" keystore="/tmp/keystore.dat" keystore-password="pass" key-password="keypass" />
            <http-client-interface enabled="true" context="http" path="submit" submit-upstream-timeout="15000"/>
        </http-server-client>
        <http-server-tools enabled="true" addr="0.0.0.0" port="8869" access-log-location="tmp_config/logs" access-log-prefix="http-tools" min-threads="10" max-threads="100">
            <ssl enabled="false" port="8869" keystore="/tmp/keystore.dat" keystore-password="pass" key-password="keypass" />
        </http-server-tools>
        <monitoring>
            <listen addr="0.0.0.0" port="8862" access-log-location="" context="monitoring" path="stats" />
        </monitoring>
        <log dir="tmp_config/logs" bind-log-file-prefix="vodafonempay-bind" bind-logging="true" log4j-properties-file="../log4j.xml" />
        <ldap>
            <ldap-hosts>
                <ldap-host host="127.0.0.1" port="389" />
            </ldap-hosts>
            <initial-context-factory>com.sun.jndi.ldap.LdapCtxFactory</initial-context-factory>
            <security-authentication>simple</security-authentication>
            <security-principal>cn=Manager, dc=thepeachbeetle, dc=net</security-principal>
            <security-credentials>82#ei8P</security-credentials>
            <default-base-dns>
                <smpp-account>cn=smppaccounts, ou=connectors, dc=thepeachbeetle, dc=net</smpp-account>
                <mo-routing-rules>cn=mo-routing, ou=connectors, dc=thepeachbeetle, dc=net</mo-routing-rules>
                <mt-routing-rules>cn=mt-routing, ou=connectors, dc=thepeachbeetle, dc=net</mt-routing-rules>
            </default-base-dns>
        </ldap>
        <delivery-notification-matching enabled="false" />
    </core>

    <smpp-accounts enabled="true" db="false" ldap="false" ldap-base-dn="ou=vodafonempay,ou=smppaccounts,ou=connectors,dc=thepeachbeetle,dc=net">
        <!-- standard smpp account -->
        <account sysid="smpp" password="smpp" max-binds="6" max-mt-per-second="5" max-mo-per-second="5" smpp-enabled="true" />
        <!-- standard http account -->
        <account sysid="http" password="http" max-binds="2" max-mt-per-second="1" max-mo-per-second="5" smpp-enabled="false"
                 use-http="true"
                 http-mo-base-url="http://127.0.0.1:9999/mo/local"
                 http-dn-base-url="http://127.0.0.1:9999/dr/local"
                 http-post-username="user1"
                 http-post-password="pass1" />
    </smpp-accounts>

    <mo-routing ldap="false" db="false">
        <match seq="1" gateway="vodafone-mpay" route-to-bind-id="smpp" />
    </mo-routing>

    <mo-queueing enabled="true" always-queue="true" always-attempt-delivery-first-on-smpp-channel='false'>
        <persistence-directory>tmp_config/queues/mo</persistence-directory>
        <max-queue-size>10000</max-queue-size>
        <!-- <shared-threads enabled="true" how-many="2" /> -->
        <retry-schedule max-lifetime="86400">
            <interval>30</interval>
            <interval>300</interval>
            <interval>300</interval>
            <interval>900</interval>
            <interval>3600</interval>
        </retry-schedule>
    </mo-queueing>

    <mt-queueing enabled="true">
        <persistence-directory>tmp_config/queues/mt</persistence-directory>
        <max-queue-size>10000</max-queue-size>
        <unroutable-queue enabled="true" size="5000" />
        <retry-schedule max-lifetime="86400">
            <interval>30</interval>
            <interval>300</interval>
            <interval>300</interval>
            <interval>900</interval>
            <interval>3600</interval>
        </retry-schedule>
    </mt-queueing>

    <target name="vodafone-mpay"
            enabled="true"
            deliverer-config-class="com.thepeachbeetle.messaging.hub.protocols.custom.vodafonempay.VodafoneMpayConfig"
            deliverer-config-loader-class="com.thepeachbeetle.messaging.hub.protocols.custom.vodafonempay.VodafoneMpayConfigLoader">
        <deliver-thread-pool-class>com.thepeachbeetle.messaging.hub.protocols.custom.vodafonempay.VodafoneMpayDelivererThreadPool</deliver-thread-pool-class>
        <deliver-threads>1</deliver-threads>
        <delivery-notification-matching enabled="false" />
        <queue enabled="true" size="10000" always-queue="true" always-queue-mo="true" always-attempt-mo-delivery-first-on-smpp-channel='false'/>
        <log mo-log-file-prefix="vodafonempay-mo" mt-log-file-prefix="vodafonempay-mt" dr-log-file-prefix="vodafonempay-dr" mo-logging="true" mt-logging="true" dr-logging="true"/>
        <http-server />
        <!-- protocol specific config goes here ... -->
        <vodafone-mpay>
            <server url="https://mpay-bill.vodafone.co.uk/app-node-mct/responder" />

            <mpay-params>
                <param name="defaultCurrency" value="GBP" />
                <param name="paymentService" value="2" />
                <param name="paymentGuaranteed" value="false" />
                <param name="handleRefund" value="false" />
            </mpay-params>

            <!-- Merchant Setup -->
            <merchant-setup>
                <merchant name="thepeachbeetle" id="800835" key-id="5103" key-value="ThePeachBeetle" default="true" />
                <merchant name="ea" id="800836" key-id="5104" key-value="Electronic Arts" />
                <merchant name="offerpal" id="800837" key-id="5105" key-value="Offerpal" />
                <merchant name="playfish" id="800838" key-id="5106" key-value="Playfish" />
                <merchant name="playdom" id="800839" key-id="5107" key-value="Playdom" />
            </merchant-setup>

            <!-- Product Setup -->
            <product-setup>
                <product name="default" mpay-product-category="GAMES:WEB-ONLINE" mpay-product-class="2" default="true" />
            </product-setup>

        </vodafone-mpay>
    </target>
</gateway>

