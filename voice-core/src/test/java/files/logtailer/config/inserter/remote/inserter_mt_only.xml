<messaging-logtailer>
    <core instance='logtailer1' company-id="Nexmo">
        <deployment-check enabled='false'>
            <remote-deployment-matrix enabled='false' deployment-matrix-service-host='localhost' deployment-matrix-service-port='3000' connection-timeout='3000' so-timeout='10000'>
                <regular-refresh enabled='true' poll-interval-unit='minute' poll-interval-count='1'/>
            </remote-deployment-matrix>
        </deployment-check>
        <http enabled='false' addr="0.0.0.0" port="16000" access-log-location="logs" access-log-prefix="access" min-threads='50' max-threads='100'>
            <ssl enabled='false' addr="127.0.0.1" port='4443' keystore='/tmp/keystore.dat' keystore-password='pass' key-password='keypass' />
        </http>
        <log dir="tmp_config/logs" log4j-properties-file="../log4j.xml" />
        <jmx enabled="false" listen-addr="0.0.0.0" listen-port="16003" http-enabled='true' http-port='16005' http-username='user1' http-password='password'/>
        <debug system-dump-dir="tmp_config/logs/dumps" />
        <databases>
            <db id='dummy' threads='1' aggregator='false' inserter='true'>
                <db enabled="true" local-write="false" cdr-import="true" />
                <remote-queue-broker enabled='true' broker-url='failover:(tcp://127.0.0.1:6006)?timeout=10000' client-id='inserter-client' insert-batch-size='50'/>
            </db>
        </databases>
    </core>
    <logtailers state-dir='cdr-importer-state'>
        <instance id='mt'
                  enabled='false'
                  log-dir='../connector/logs/mt'
                  complete-file-filename-token="complete"
                  mt-log-prefix='core-mt'
                  mo-log-prefix='connector-mo'
                  dr-log-prefix='connector-dr'
                  final-state-log-prefix='42tele-connector-final-states'
                  mt-enabled='true'
                  mo-enabled='false'
                  dr-enabled='false'
                  final-state-enabled='true'
                  max-time-to-leave-final-file-un-touched-before-adding-complete-tag-count='999'
                  max-time-to-leave-final-file-un-touched-before-adding-complete-tag-unit='day' >
            <db id='dummy' />
            <cdr-aggregator enabled='false' />
            <cdr-inserter enabled='true' />
        </instance>
    </logtailers>
</messaging-logtailer>

