package com.nexmo.test.util;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.NestedXmlHandler;
import com.thepeachbeetle.common.xml.XmlAbstractReader;

public final class SimpleConfigReader<T extends NestedXmlHandler, U> extends XmlAbstractReader {

    private final T configLoader;
    private U config;

    public SimpleConfigReader(T loader) {
        this.configLoader = loader;
        addHandler(this.configLoader);
    }

    public U getConfig() {
        return this.config;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void notifyComplete(NestedXmlHandler childHandler) throws LoaderException {

        try {
            Method getConfig = ((T) childHandler).getClass().getMethod("getConfig");
            this.config = (U)getConfig.invoke(childHandler);
        } catch (NoSuchMethodException |
                 SecurityException |
                 IllegalAccessException |
                 IllegalArgumentException |
                 InvocationTargetException e) {
            throw new LoaderException("Failed to read config.", e);
        }
    }
}
