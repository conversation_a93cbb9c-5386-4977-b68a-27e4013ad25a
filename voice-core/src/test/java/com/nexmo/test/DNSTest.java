package com.nexmo.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Objects;

import org.junit.Ignore;
import org.junit.Test;

/**
 * Test of DNS lookups using the Java defaults, rather than a separate library to
 * do the resolution
 * 
 * <AUTHOR> Williams
 *
 */
public class DNSTest {
    @Test
    public void testNexmo() throws Exception {
        final String actual = lookup("<EMAIL>");
        assertNotNull(actual);
    }

    @Test
    public void testLocalhost() throws Exception {
        final String expected = "127.0.0.1";
        final String actual = lookup("test@localhost");
        assertEquals(expected, actual);
    }

    @Test(expected = UnknownHostException.class)
    public void testGibberish() throws Exception {
        final String expected = null;
        final String actual = lookup("<EMAIL>");
        assertEquals(expected, actual);
    }

    private static String lookup(String uri) throws Exception {
        String ipOfAddress = null;

        String address = extractServer(uri);
        if (Objects.isNull(address) || address.isEmpty())
            return ipOfAddress;

        ipOfAddress = InetAddress.getByName(address).getHostAddress();

        return ipOfAddress;
    }

    private static String extractServer(String address) {
        String[] splitAddress = address.split("@");

        if (splitAddress.length < 2) {
            throw new IllegalArgumentException("The configured SIP address of this number does not include @ - is it really a sip destination?");
        }

        address = splitAddress[1];
        // At this point address will refer to the first sip destination on the list, and will include either of:
        // example.com
        // example.com:5062
        // or one of the above with the additional suffix ;timeout=xxxxx ;transport=tls or both
        address = address.split(":")[0];
        // At this point the address will be either:
        // example.com
        // example.com;[suffix - one or more]
        address = address.split(";")[0];
        // At this point the address will be
        // example.com
        return address;
    }
}