package com.nexmo.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Test;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.Policy;
import com.github.benmanes.caffeine.cache.RemovalCause;

import java.util.concurrent.TimeUnit;

public class CaffeineTest {

    @Test
    public void testAutoLoad() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(1, TimeUnit.MINUTES)
                                                 .build(key -> createValue(key));
        
        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = cache.get(k);
        
        assertEquals(1L, cache.estimatedSize());
        assertTrue(v.toString().startsWith("Created for key badger at"));
    }
    
    @Test
    public void testProvisioningException() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(1, TimeUnit.MINUTES)
                                                 .build(key -> throwInsteadOfCreatingValue(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        try {
            Value v = cache.get(k);
            fail();
        } catch (IllegalArgumentException ex) {
            assertEquals("All arguments are illegal", ex.getMessage());
        }
    }

    @Test
    public void testProvisioningExceptionInRefresh() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(1, TimeUnit.SECONDS)
                                                 .build(key -> throwInsteadOfCreatingValue(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = new Value("badger");
        cache.put(k, v); // Put entry into cache

        // The entry doesn't become eligible for a refresh until after a second, so
        // will still have the original value at this point
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        assertTrue(v.toString().equals("badger"));

        // Wait 3 seconds, to be sure a refresh is eligible
        sleep(3000L);

        // When we issue the read, the refresh will FAIL, so Caffeine should log the exception,
        // and continue to return the old value
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        assertTrue(v.toString().equals("badger"));
    }

    @Test
    public void testAsynchronousRefresh() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(1, TimeUnit.SECONDS)
                                                 .build(key -> createValueSlow(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = new Value("badger");
        cache.put(k, v); // Put entry into cache

        // The entry doesn't become eligible for a refresh until after a second so
        // will still have the original value at this point
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        assertTrue(v.toString().equals("badger"));
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);

        // Wait 3 seconds, to be sure a refresh is eligible
        sleep(3000L);

        // Trigger refresh, and check it returns immediately with old value
        assertEquals(1L, cache.estimatedSize());
        System.out.println("Cache refresh @ "+System.currentTimeMillis()+" in "+Thread.currentThread());
        final long startTime = System.currentTimeMillis();
        v = cache.get(k);
        final long endTime = System.currentTimeMillis();
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        // assertTrue((endTime-startTime) < 10L);
        assertTrue(v.toString().equals("badger"));

        // Wait another 3 seconds
        sleep(3000L);
        System.out.println("After refresh");
        v = cache.get(k);
        System.out.println("Final value is: "+v);
        assertTrue(v.toString().startsWith("Created for key badger at"));
    }

    @Test
    public void testRefresh() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(3, TimeUnit.SECONDS)
                                                 .build(key -> createValue(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = new Value("badger");
        cache.put(k, v); // Put entry into cache

        // The entry doesn't become eligible for a refresh until after 3 seconds so
        // will still have the original value at this point
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        assertTrue(v.toString().equals("badger"));
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);

        // Wait 10 seconds, querying cache to ensure a refresh is attempted
        final long endTime = System.currentTimeMillis() + 20000L;
        while (System.currentTimeMillis() < endTime) {
            sleep(1000L);
            v = cache.get(k);
            System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        }

        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        System.out.println("Final value is: "+v);
        assertTrue(v.toString().startsWith("Created for key badger at"));
    }

    @Test
    public void testRemovalInRefresh() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(1, TimeUnit.SECONDS)
                                                 .removalListener((key,value,cause) -> System.out.println("Entry \""+key+"\"=>\""+value+"\" removed, cause "+cause))
                                                 .build(key -> removeValue(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = new Value("badger");
        cache.put(k, v); // Put entry into cache

        // The entry doesn't become eligible for a refresh until after a second, so
        // will still have the original value at this point
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        assertTrue(v.toString().equals("badger"));

        // Wait 3 seconds, querying cache to ensure a refresh is attempted
        final long endTime = System.currentTimeMillis() + 20000L;
        while (System.currentTimeMillis() < endTime) {
            sleep(1000L);
            v = cache.get(k);
            System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        }

        // When we issue the read, the refresh will return null so Caffeine should remove
        // the entry from the cache
        v = cache.get(k);
        System.out.println("Final value is: "+v);
        assertNull(v);
        assertEquals(0L, cache.estimatedSize());
    }

    @Test
    public void testExpiry() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(2, TimeUnit.SECONDS)
                                                 .refreshAfterWrite(1, TimeUnit.SECONDS)
                                                 .removalListener((key,value,cause) -> System.out.println("Entry \""+key+"\"=>\""+value+"\" removed, cause "+cause))
                                                 .build(key -> createValue(key));

        assertEquals(0L, cache.estimatedSize());

        Key k = new Key("badger");
        Value v = new Value("badger");
        cache.put(k, v); // Put entry into cache

        // The entry doesn't become eligible for a refresh until after a second, so
        // will still have the original value at this point
        assertEquals(1L, cache.estimatedSize());
        v = cache.get(k);
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        assertTrue(v.toString().equals("badger"));

        // Wait 3 seconds, to be sure a refresh is eligible
        sleep(3000L);

        // Entry should have expired and no longer be present in cache

        // FIXME: estimatedSize() will still return 1 here unless an explicit cleanup is triggered:
        // cache.cleanUp();
        // assertEquals(0L, cache.estimatedSize());

        // The old value has expired, so is no longer present
        System.out.println("Fetching value @ "+System.currentTimeMillis());
        v = cache.getIfPresent(k);
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        assertNull(v);

        // If we do a full get(), however, it will create a new entry
        v = cache.get(k);
        System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
        assertEquals(1L, cache.estimatedSize());
        assertTrue(v.toString().startsWith("Created for key badger at"));
    }

    @Test
    public void testSizeShrink() {
        LoadingCache<Key, Value> cache = Caffeine.newBuilder()
                                                 .maximumSize(10)
                                                 .expireAfterWrite(5, TimeUnit.MINUTES)
                                                 .refreshAfterWrite(5, TimeUnit.MINUTES)
                                                 .removalListener((key,value,cause) -> System.out.println("Entry \""+key+"\"=>\""+value+"\" removed, cause "+cause))
                                                 .build(key -> createValue(key));

        assertEquals(0L, cache.estimatedSize());

        // Put 10 items into cache
        Key[] keys = new Key[10];
        for (int i=0;i<10;i++) {
            Key k = new Key("key-"+Integer.toString(i));
            keys[i] = k;
            Value v = new Value("badger");
            cache.put(k, v); // Put entry into cache
            sleep(100L);
        }

        // Shrink cache
        System.out.println("Shrinking cache");
        Policy<Key, Value> policy = cache.policy();
        Policy.Eviction<Key, Value> evictionPolicy = policy.eviction().get(); // Will throw NoSuchElementException if not present
        evictionPolicy.setMaximum(5);

        // Ensure new size is correct
        cache.cleanUp();
        assertEquals(5L, cache.estimatedSize());

        // Check contents
        for (int i=0;i<10;i++) {
            Value v = cache.getIfPresent(keys[i]);
            System.out.println("Value @ "+System.currentTimeMillis()+" = "+v);
            if (i<5)
                assertNull(v);
            else
                assertNotNull(v);
        }
    }


    private static Value createValue(Key key) {
        System.out.println("createValue("+key+")");
        return new Value("Created for key "+key+" at "+System.currentTimeMillis());
    }

    private static Value throwInsteadOfCreatingValue(Key key) {
        System.out.println("throwInsteadOfCreatingValue("+key+")");
        throw new IllegalArgumentException("All arguments are illegal");
    }

    private static Value removeValue(Key key) {
        System.out.println("removeValue("+key+")");
        return null;
    }

    private static Value createValueSlow(Key key) {
        System.out.println("createValueSlow("+key+") @ "+System.currentTimeMillis()+" in "+Thread.currentThread());
        sleep(500L);
        return new Value("Created for key "+key+" at "+System.currentTimeMillis());
    }


    private static class Key {
        private final String name; 
        
        private Key(String name) {
            System.out.println("Key constructor("+name+")");
            if (name == null)
                throw new IllegalArgumentException("Key name may not be null");
            
            this.name = name;
        }
        
        @Override
        public boolean equals(Object o) {
            if (o == this)
                return true;
            if (!(o instanceof Key))
                return false;
            Key other = (Key)o;
            
            return this.name.equals(other.name);
        }

        @Override
        public int hashCode() {
            final int prime = 31;
            int result = 1;
            result = prime * result + name.hashCode();
            return result;
        }
        
        @Override
        public String toString() {
            return name;
        }
    }
    
    private static class Value {
        private final String payload; 
        
        private Value(String payload) {
            System.out.println("Value constructor("+payload+")");
            if (payload == null)
                throw new IllegalArgumentException("Value payload may not be null");
            
            this.payload = payload;
        }
        
        @Override
        public String toString() {
            return payload;
        }
    }

    private static void sleep(long time) {
        if (time <= 0)
            return;

        final long startTime = System.currentTimeMillis();
        final long endTime = startTime + time;
        do {
            try {
                Thread.sleep(endTime - System.currentTimeMillis());
            } catch (InterruptedException ex) {
                System.err.println("Thread interrupted");
            }
        } while (System.currentTimeMillis() < endTime);
    }

}
