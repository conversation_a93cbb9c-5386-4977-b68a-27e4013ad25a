package com.nexmo.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.Objects;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.xbill.DNS.ARecord;
import org.xbill.DNS.Address;
import org.xbill.DNS.Lookup;
import org.xbill.DNS.Record;
import org.xbill.DNS.Resolver;
import org.xbill.DNS.SimpleResolver;
import org.xbill.DNS.TextParseException;
import org.xbill.DNS.Type;

/**
 * Tests for the xbill.org DNS resolver, previously used for IP address lookups
 * during get-route-for-number to determine which DC to use for a call
 * 
 * <AUTHOR> Williams
 *
 */
public class DNSTestOld {
    private static final int DNS_RESOLVER_TIMEOUT_SECS = 2;

    @BeforeClass
    public static void setUp() {
        // Enable extra logging to stderr
        System.setProperty("dnsjava.options","verbose");
    }

    @Test
    public void testNexmo() throws Exception {
        final String actual = lookup("<EMAIL>");
        assertNotNull(actual);
    }

    @Test
    @Ignore("BUG: This doesn't work with the xbill.org resolver")
    public void testLocalhost() throws Exception {
        final String expected = "127.0.0.1";
        final String actual = lookup("test@localhost");
        assertEquals(expected, actual);
    }

    @Test
    public void testGibberish() throws Exception {
        final String expected = null;
        final String actual = lookup("<EMAIL>");
        assertEquals(expected, actual);
    }

    private static String lookup(String uri) throws Exception {
        return lookup(uri, null);
    }

    private static String lookup(String uri, String dns) throws Exception {
        String ipOfAddress = null;

        String address = extractServer(uri);
        if (Objects.isNull(address) || address.isEmpty())
            return ipOfAddress;

        Resolver resolver = (dns != null) ? new SimpleResolver(dns) : new SimpleResolver();
        resolver.setTimeout(DNS_RESOLVER_TIMEOUT_SECS);

        Lookup lookup = new Lookup(address, Type.A);
        lookup.setResolver(resolver);

        Record[] records = lookup.run();

        if (records == null || records.length == 0) {
            return ipOfAddress;
        }

        ARecord aRecord = (ARecord) records[0];
        ipOfAddress = Address.toDottedQuad(aRecord.getAddress().getAddress());

        return ipOfAddress;
    }

    private static String extractServer(String address) {
        String[] splitAddress = address.split("@");

        if (splitAddress.length < 2) {
            throw new IllegalArgumentException("The configured SIP address of this number does not include @ - is it really a sip destination?");
        }

        address = splitAddress[1];
        // At this point address will refer to the first sip destination on the list, and will include either of:
        // example.com
        // example.com:5062
        // or one of the above with the additional suffix ;timeout=xxxxx ;transport=tls or both
        address = address.split(":")[0];
        // At this point the address will be either:
        // example.com
        // example.com;[suffix - one or more]
        address = address.split(";")[0];
        // At this point the address will be
        // example.com
        return address;
    }
}