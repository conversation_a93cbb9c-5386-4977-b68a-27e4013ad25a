package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.thepeachbeetle.messaging.hub.core.quota.client.AccountBalance;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.HttpResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import static org.junit.Assert.*;
import java.math.BigDecimal;
import org.apache.http.StatusLine;

@RunWith(MockitoJUnitRunner.class)
public class CurrentBalanceApiClientTest {

    @Mock
    private HttpClient mockHttpClient;

    @Mock
    private HttpResponse response;

    @Mock
    private HttpEntity entity;

    @Mock
    private StatusLine statusLine;

    @Mock
    private CurrentBalanceApiConfig config;

    @InjectMocks
    private CurrentBalanceApiClient currentBalanceApiClient;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        when(config.getTimeout()).thenReturn(1000);
        when(config.constructUri()).thenReturn("http://mock-api-url.com");
        currentBalanceApiClient = new CurrentBalanceApiClient(config, "mockAuth");
        currentBalanceApiClient.client = mockHttpClient;
    }

    @Test
    public void testGetCurrentBalanceSuccess() throws Exception {
        String responseBody = "{"
                + "\"status\": \"success\","
                + "\"accountBalance\": {"
                + "\"accountId\": \"Aa0a051b\","
                + "\"balance\": 100.0,"
                + "\"creditLimit\": 100.0,"
                + "\"quotaEnabled\": true,"
                + "\"requiredFreeBalance\": 101,"
                + "\"requiredFreeBalanceAvailable\": false"
                + "}"
                + "}";

        InputStream inputStream = new ByteArrayInputStream(responseBody.getBytes());
        when(mockHttpClient.execute(any(HttpGet.class))).thenReturn(response);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        when(response.getEntity()).thenReturn(entity);
        when(entity.getContent()).thenReturn(inputStream);

        AccountBalance balance = currentBalanceApiClient.getCurrentBalance("testApiKey", new BigDecimal(50), "");
        assertNotNull(balance);
        assertEquals("Aa0a051b", balance.getAccountId());
        assertEquals(new BigDecimal("100.0"), balance.getBalance());
        assertEquals(new BigDecimal("100.0"), balance.getCreditLimit());
        assertTrue(balance.isQuotaEnabled());
        assertEquals(new BigDecimal("101"), balance.getRequiredFreeBalance());
        assertFalse(balance.isRequiredFreeBalanceAvailable());
    }

    @Test
    public void testGetCurrentBalanceAccountNotFoundException() throws Exception {
        String errorResponseBody = "{"
                + "\"status\": \"failure\","
                + "\"error\": {"
                + "\"quotaResponse\": {"
                + "\"code\": \"404\","
                + "\"description\": \"Unrecognized Or Missing Account Id\","
                + "\"exceptionName\": \"AccountNotFoundException\""
                + "}"
                + "}"
                + "}";

        InputStream inputStream = new ByteArrayInputStream(errorResponseBody.getBytes());
        when(mockHttpClient.execute(any(HttpGet.class))).thenReturn(response);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        when(response.getEntity()).thenReturn(entity);
        when(entity.getContent()).thenReturn(inputStream);

        Exception exception = assertThrows(AccountNotFoundException.class, () -> currentBalanceApiClient.getCurrentBalance("invalidApiKey", new BigDecimal(50), ""));

        assertEquals("Error code: 404, Description: Unrecognized Or Missing Account Id", exception.getMessage());
    }

    @Test
    public void testGetCurrentBalanceQuotaUnderMaintenanceException() throws Exception {
        String errorResponseBody = "{"
                + "\"status\": \"failure\","
                + "\"error\": {"
                + "\"quotaResponse\": {"
                + "\"code\": \"503\","
                + "\"description\": \"Quota Under Maintenance\","
                + "\"exceptionName\": \"QuotaUnderMaintenanceException\""
                + "}"
                + "}"
                + "}";

        InputStream inputStream = new ByteArrayInputStream(errorResponseBody.getBytes());
        when(mockHttpClient.execute(any(HttpGet.class))).thenReturn(response);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        when(response.getEntity()).thenReturn(entity);
        when(entity.getContent()).thenReturn(inputStream);

        Exception exception = assertThrows(QuotaUnderMaintenanceException.class, () -> currentBalanceApiClient.getCurrentBalance("testApiKey", new BigDecimal(50), ""));

        assertEquals("Error code: 503, Description: Quota Under Maintenance", exception.getMessage());
    }

    @Test
    public void testGetCurrentBalanceUnknownException() throws Exception {
        String errorResponseBody = "{"
                + "\"status\": \"failure\","
                + "\"error\": {"
                + "\"quotaResponse\": {"
                + "\"code\": \"500\","
                + "\"description\": \"Internal Server Error\","
                + "\"exceptionName\": \"UnknownException\""
                + "}"
                + "}"
                + "}";

        InputStream inputStream = new ByteArrayInputStream(errorResponseBody.getBytes());
        when(mockHttpClient.execute(any(HttpGet.class))).thenReturn(response);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        when(response.getEntity()).thenReturn(entity);
        when(entity.getContent()).thenReturn(inputStream);

        Exception exception = assertThrows(QuotaException.class, () -> currentBalanceApiClient.getCurrentBalance("testApiKey", new BigDecimal(50), ""));

        assertEquals("Error code: 500, Description: Internal Server Error", exception.getMessage());
    }

    @Test
    public void testGetCurrentBalanceEmptyResponse() throws Exception {
        when(mockHttpClient.execute(any(HttpGet.class))).thenReturn(response);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);

        InputStream emptyStream = new ByteArrayInputStream("".getBytes());
        when(response.getEntity()).thenReturn(entity);
        when(entity.getContent()).thenReturn(emptyStream);

        Exception exception = assertThrows(QuotaException.class, () -> currentBalanceApiClient.getCurrentBalance("testApiKey", new BigDecimal(50), ""));

        assertEquals("Empty or null response body received", exception.getMessage());
    }
}