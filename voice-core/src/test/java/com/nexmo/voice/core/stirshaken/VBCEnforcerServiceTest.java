package com.nexmo.voice.core.stirshaken;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.stirshaken.impl.VBCEnforcerService;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static com.nexmo.voice.core.stirshaken.Attestation.NONE;
import static com.nexmo.voice.core.types.VoiceProduct.UNKNOWN;
import static com.nexmo.voice.core.types.VoiceProduct.VBC;
import static java.util.UUID.randomUUID;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.doThrow;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.script.*"})
public class VBCEnforcerServiceTest extends EnforcerServiceTest {
    private static final VoiceProduct VOICE_PRODUCT = VBC;


    @BeforeClass
    public static void init() throws ShortCodeException {
        EnforcerServiceTest.init();
        doReturn(new VBCEnforcerService()).when(CORE)
                .getVBCEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getSIPEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getTTSEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getVerifyEnforcerService();
    }

    @Test(expected = IllegalArgumentException.class)
    @PrepareForTest(Core.class)
    public void shouldBeEmptyGivenNonVBCProductClass() {
        EnforcerServiceFactory.byProductClass(UNKNOWN)
                .attestationLevel(
                        AttestationValidationParams.builder()
                                .withAccountId(randomUUID().toString())
                                .withHasKyc(true)
                                .withHasDisableMustOwnLVN(true)
                                .withIsCallerIdE164(true)
                                .withIsOwnedLvn(false)
                                .withDestinationCountry(US)
                                .withGatewayName("vonage")
                                .withForcedSender(null)
                                .withSessionId(randomUUID().toString())
                                .build());
    }


    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassNoneAttestationRegardlessOfTheInput() throws ShortCodeException {
        String accountId = randomUUID().toString();
        assertThat(
                EnforcerServiceFactory.byProductClass(VOICE_PRODUCT)
                        .attestationLevel(
                                AttestationValidationParams.builder()
                                        .withAccountId(accountId)
                                        .withHasKyc(true)
                                        .withHasDisableMustOwnLVN(true)
                                        .withIsCallerIdE164(true)
                                        .withIsOwnedLvn(true)
                                        .withDestinationCountry(US)
                                        .withGatewayName("vonage")
                                        .withForcedSender(null)
                                        .withSessionId(randomUUID().toString())
                                        .build()),
                is(NONE));

    }
}
