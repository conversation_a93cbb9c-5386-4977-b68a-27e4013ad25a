package com.nexmo.voice.core.types;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;

import com.nexmo.voice.core.types.EffectiveAmount.USAGE;


public class EffectiveAmountTest {
    private static BigDecimal DEFAULT_AMOUNT = new BigDecimal("0.01234");
    private static String DEFAULT_PREFIX = "12345";
    private static String DEFAULT_PREFIXGROUP = "860123";
    private static String EXPECTED_STRING = "Usage PRICE. amount=0.01234, prefix=12345 (860123)";


    @Test
    public void testCreation() {
        EffectiveAmount amount = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(amount);
        
        Assert.assertEquals(DEFAULT_AMOUNT, amount.getAmount());
        Assert.assertEquals(DEFAULT_PREFIX, amount.getPrefix());
        Assert.assertEquals(DEFAULT_PREFIXGROUP, amount.getPrefixGroup());
    }

    @Test
    public void testToString() {
        EffectiveAmount amount = new EffectiveAmount(USAGE.PRICE, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(amount);
        
        Assert.assertEquals(EXPECTED_STRING, amount.toString());
    }

    @Test
    @Ignore("No equals() method defined in class")
    public void testSameContents() {
        EffectiveAmount amount1 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, null);
        Assert.assertNotNull(amount1);
        EffectiveAmount amount2 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, null, null);
        Assert.assertNotNull(amount2);
        EffectiveAmount amount3 = new EffectiveAmount(USAGE.COST, 0L, DEFAULT_AMOUNT, null, null, null);
        Assert.assertNotNull(amount3);

        Assert.assertEquals(amount1, amount2);
        Assert.assertEquals(amount2, amount3);
        Assert.assertEquals(amount1, amount3);
    }

    @Test
    @Ignore("No equals() method defined in class")
    public void testDifferentContents() {
        EffectiveAmount amount1 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, DEFAULT_PREFIX);
        Assert.assertNotNull(amount1);
        EffectiveAmount amount2 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(amount2);
        EffectiveAmount amount3 = new EffectiveAmount(USAGE.COST, 0L, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP, null);
        Assert.assertNotNull(amount3);

        Assert.assertNotEquals(amount1, amount2);
        Assert.assertNotEquals(amount2, amount3);
        Assert.assertNotEquals(amount1, amount3);
    }
    
    @Test
    @Ignore("No hashCode() method defined in class")
    public void testHashes() {
        EffectiveAmount amount1 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, DEFAULT_PREFIX);
        Assert.assertNotNull(amount1);
        Assert.assertEquals(0x4F00D4D8, amount1.hashCode());

        EffectiveAmount amount2 = new EffectiveAmount(USAGE.COST, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(amount2);
        Assert.assertEquals(0x4F00D517, amount2.hashCode());

        EffectiveAmount amount3 = new EffectiveAmount(USAGE.COST, 0L, DEFAULT_AMOUNT, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP, null);
        Assert.assertNotNull(amount3);
        Assert.assertEquals(0x4F00D517, amount3.hashCode());
    }

}
