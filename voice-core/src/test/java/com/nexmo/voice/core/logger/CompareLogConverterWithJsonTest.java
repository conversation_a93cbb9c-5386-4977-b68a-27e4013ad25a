package com.nexmo.voice.core.logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.HashMap;
import java.util.Map.Entry;

import org.junit.Test;

import com.nexmo.voice.core.cdr.CDRKeysConverter;
import com.nexmo.voice.core.cdr.CDRValuesConverter;
/**
 * This is a special test, which compare the LogConverter results
 * with the SIPApp json CDRs.
 * It is based on snapshot of CDRs and LogConverter results as taken from Production
 * 
 * This test is needed as long as we are on the transition mode and use both key-value and json CDRs.
 * Once LogCOnverter is not used, and the Tests are converted to use the Json CDRs instead of the key-value
 * this test is not required.
 * 
 * The data for the tests here is taken from all potential CDR files
 * 
 * The tests are using the internal conversion utilities, i.e:
 * 
 * prod-key-value-cdr -> using SIPApp internal conversion utility -> compare to prod LogConvertor result
 * 
 * The SIPApp internal conversion utility IS NOT converting the whole CDR, but is used per-value as
 * the CDR is written by SIPApp.
 * 
 * 
 * 
 * <AUTHOR>
 *
 */
public class CompareLogConverterWithJsonTest {

	@Test
	public void sipAttemptCDRsTest() {
		String sipAttemptCDR = "1633961911064 :: 10/11/2021 14:18:31 (064) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=250020a3a17226cf88409ee6297ce287\",\"LEG1-ID=4904154_134207085@**************\",\"LEG2-ID=null\",\"SUPERHUB-ACC=null\",\"ACC=36a1b762\",\"FROM= ***********\",\"TO=***********\",\"PREFIX-FROM=1618243\",\"PREFIX-TO=1701204\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#2\",\"REROUTE-ADDRESS=sip:+***********@************\",\"COUNTRY=US\",\"NET=US-FIXED\",\"GW=vonage\",\"GWS=vonage\",\"CALL_RETRIES=0\",\"STATUS=CONGESTION\",\"REASON=500\",\"REASON_DESC=Internal error occurred.\",\"START=10/11/2021 14:18:28 (248)\",\"END=10/11/2021 14:18:28 (248)\",\"CALL_DATE=10/11/2021 14:18:28 (248)\",\"ROUTING_SEQ=0\",\"PDD=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=pstn\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=532132ea-119a-4474-8c68-7242619e8619\",\"STIR_SHAKEN=null\",";
		String logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"250020a3a17226cf88409ee6297ce287\",\"leg1Id\":\"4904154_134207085@**************\",\"leg2Id\":\"null\",\"superHubAcc\":\"null\",\"acc\":\"36a1b762\",\"from\":\" ***********\",\"to\":\"***********\",\"prefixFrom\":\"1618243\",\"prefixTo\":\"1701204\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#2\",\"rerouteAddress\":\"sip:+***********@************\",\"country\":\"US\",\"net\":\"US-FIXED\",\"gw\":\"vonage\",\"gws\":\"vonage\",\"callRetries\":\"0\",\"status\":\"CONGESTION\",\"reason\":\"500\",\"reasonDesc\":\"Internal error occurred.\",\"start\":\"2021-10-11T14:18:28.248+0000\",\"end\":\"2021-10-11T14:18:28.248+0000\",\"callDate\":\"2021-10-11T14:18:28.248+0000\",\"routingSeq\":\"0\",\"pdd\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"pstn\",\"callTermination\":\"\",\"customerDomain\":\"\",\"cdrUuid\":\"532132ea-119a-4474-8c68-7242619e8619\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T14:18:31.064+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
		verify(sipAttemptCDR, logConvJson);
		
	    sipAttemptCDR = "************* :: 12/08/2021 11:17:32 (845) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=0b1e62021a857caa85e40de8a081c4c4\",\"LEG1-ID=<EMAIL>\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"ACC=1c485038\",\"FROM= ************\",\"TO=sip:************@************:5060\",\"PREFIX-FROM=********\",\"PREFIX-TO=\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#3\",\"REROUTE-ADDRESS=sip:************@************:5060\",\"COUNTRY=null\",\"NET=null\",\"GW=default\",\"GWS=default\",\"GW_ATTEMPT=1#1\",\"STATUS=ANSWER\",\"REASON=408\",\"REASON_DESC=Could not find the recipient in time\",\"START=12/08/2021 11:17:26 (441)\",\"END=12/08/2021 11:17:33 (441)\",\"CALL_DATE=12/08/2021 11:17:26 (439)\",\"ROUTING_SEQ=0\",\"PDD=282\",\"REQUEST_IP=\",\"CALL_ORIGIN=\",\"CALL_TERMINATION=sip\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=a10291e5-316a-4346-b4ef-de5cc2a09c98\",\"STIR_SHAKEN=null\",";
	    logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"0b1e62021a857caa85e40de8a081c4c4\",\"leg1Id\":\"<EMAIL>\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"acc\":\"1c485038\",\"from\":\" ************\",\"to\":\"sip:************@************:5060\",\"prefixFrom\":\"********\",\"prefixTo\":\"\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#3\",\"rerouteAddress\":\"sip:************@************:5060\",\"country\":\"null\",\"net\":\"null\",\"gw\":\"default\",\"gws\":\"default\",\"gwAttempt\":\"1#1\",\"status\":\"ANSWER\",\"reason\":\"408\",\"reasonDesc\":\"Could not find the recipient in time\",\"start\":\"2021-12-08T11:17:26.441+0000\",\"end\":\"2021-12-08T11:17:33.441+0000\",\"callDate\":\"2021-12-08T11:17:26.439+0000\",\"routingSeq\":\"0\",\"pdd\":\"282\",\"requestIp\":\"\",\"callOrigin\":\"\",\"callTermination\":\"sip\",\"customerDomain\":\"\",\"cdrUuid\":\"a10291e5-316a-4346-b4ef-de5cc2a09c98\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-12-08T11:17:32.845+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
	    verify(sipAttemptCDR, logConvJson);
	}
	
    @Test
    public void sipInboundCDRsTest() {
        String sipInboundCDR = "************* :: 10/11/2021 00:00:00 (716) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=api\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=2a1a8fa0c361a1c72327f2c5da52c3bb\",\"SESSION-ID=2a1a8fa0c361a1c72327f2c5da52c3bb\",\"LEG1-ID=<EMAIL>\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=76ab8cb4\",\"ACCOUNT-PRICING-GROUP=\",\"FROM= ********5350\",\"TO=************\",\"PREFIX-FROM=********\",\"PREFIX-TO=********\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=GB\",\"NET=GB-FIXED\",\"NETWORK-NAME=United Kingdom Landline\",\"NETWORK-TYPE=LANDLINE\",\"GW=gamma\",\"GWS=gamma\",\"CALL_RETRIES=0\",\"REROUTE-ADDRESS=c369b3ed-9a0e-4b78-936e-f76e37ac6dd6\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=04/09/2020 09:00:01 (000)\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=44\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=10/08/2018 13:04:10 (000)\",\"START=10/10/2021 23:59:35 (299)\",\"END=10/11/2021 00:00:01 (299)\",\"DURATION=26\",\"CALL_DURATION=26000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/10/2021 23:59:35 (299)\",\"ROUTING_SEQ=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=pstn\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=6ed47cba-e978-4207-a3fb-dcf22256a4d5\",\"STIR_SHAKEN=null\",";
        String logConvJson = "{\"product\":\"5\",\"productClass\":\"api\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"2a1a8fa0c361a1c72327f2c5da52c3bb\",\"sessionId\":\"2a1a8fa0c361a1c72327f2c5da52c3bb\",\"leg1Id\":\"<EMAIL>\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"76ab8cb4\",\"accountPricingGroup\":\"\",\"from\":\" ********5350\",\"to\":\"************\",\"prefixFrom\":\"********\",\"prefixTo\":\"********\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"GB\",\"net\":\"GB-FIXED\",\"networkName\":\"United Kingdom Landline\",\"networkType\":\"LANDLINE\",\"gw\":\"gamma\",\"gws\":\"gamma\",\"callRetries\":\"0\",\"rerouteAddress\":\"c369b3ed-9a0e-4b78-936e-f76e37ac6dd6\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"04/09/2020 09:00:01 (000)\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"44\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"10/08/2018 13:04:10 (000)\",\"start\":\"2021-10-10T23:59:35.299+0000\",\"end\":\"2021-10-11T00:00:01.299+0000\",\"duration\":\"26\",\"callDuration\":\"26000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-10T23:59:35.299+0000\",\"routingSeq\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"pstn\",\"callTermination\":\"\",\"customerDomain\":\"\",\"cdrUuid\":\"6ed47cba-e978-4207-a3fb-dcf22256a4d5\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:00.716+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipInboundCDR, logConvJson);
        
        sipInboundCDR = "************* :: 10/11/2021 00:00:15 (363) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=api\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=34e3bef96dcf4daff3f8540d7ae51754\",\"SESSION-ID=34e3bef96dcf4daff3f8540d7ae51754\",\"LEG1-ID=151924800_62886977@**********\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=83dc1528\",\"ACCOUNT-PRICING-GROUP=\",\"FROM= ***********\",\"TO=1144094\",\"PREFIX-FROM=1513376\",\"PREFIX-TO=114\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=null\",\"NET=null\",\"NETWORK-NAME=null\",\"NETWORK-TYPE=null\",\"GW=default\",\"GWS=default\",\"GW_ATTEMPT=1#1\",\"REROUTE-ADDRESS=e251635c-a77d-4f04-a137-a4364c23395b\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.004000\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=default-sip-originated-price\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=default-sip-originated-cost\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=null\",\"START=10/10/2021 23:59:17 (933)\",\"END=10/11/2021 00:00:15 (933)\",\"DURATION=58\",\"CALL_DURATION=58000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/10/2021 23:59:17 (933)\",\"ROUTING_SEQ=0\",\"REQUEST_IP=**********\",\"CALL_ORIGIN=sip\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=novolabs.sip-us.nexmo.com\",\"CDR_UUID=********-5982-4b4e-beda-c24b21c874fe\",\"STIR_SHAKEN=null\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"api\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"34e3bef96dcf4daff3f8540d7ae51754\",\"sessionId\":\"34e3bef96dcf4daff3f8540d7ae51754\",\"leg1Id\":\"151924800_62886977@**********\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"83dc1528\",\"accountPricingGroup\":\"\",\"from\":\" ***********\",\"to\":\"1144094\",\"prefixFrom\":\"1513376\",\"prefixTo\":\"114\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"null\",\"net\":\"null\",\"networkName\":\"null\",\"networkType\":\"null\",\"gw\":\"default\",\"gws\":\"default\",\"gwAttempt\":\"1#1\",\"rerouteAddress\":\"e251635c-a77d-4f04-a137-a4364c23395b\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.004000\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"default-sip-originated-price\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"default-sip-originated-cost\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"null\",\"start\":\"2021-10-10T23:59:17.933+0000\",\"end\":\"2021-10-11T00:00:15.933+0000\",\"duration\":\"58\",\"callDuration\":\"58000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-10T23:59:17.933+0000\",\"routingSeq\":\"0\",\"requestIp\":\"**********\",\"callOrigin\":\"sip\",\"callTermination\":\"\",\"customerDomain\":\"novolabs.sip-us.nexmo.com\",\"cdrUuid\":\"********-5982-4b4e-beda-c24b21c874fe\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:15.363+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipInboundCDR, logConvJson);
        
        sipInboundCDR = "************* :: 10/11/2021 00:00:16 (275) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=api\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=f37105753730992fe201de02f3129b96\",\"SESSION-ID=f37105753730992fe201de02f3129b96\",\"LEG1-ID=392322055_112053108@************\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=635f5b18\",\"ACCOUNT-PRICING-GROUP=\",\"FROM= ***********\",\"TO=***********\",\"PREFIX-FROM=1270705\",\"PREFIX-TO=1502449\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=310090\",\"NETWORK-NAME=AT&T Mobility\",\"NETWORK-TYPE=MOBILE\",\"GW=vonage\",\"GWS=vonage\",\"CALL_RETRIES=0\",\"REROUTE-ADDRESS=60e477c0-3f1c-4391-be62-29c7938ba63f\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=08/12/2020 03:42:17 (000)\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=default-cost\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=null\",\"START=10/10/2021 23:43:55 (446)\",\"END=10/11/2021 00:00:16 (446)\",\"DURATION=981\",\"CALL_DURATION=981000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/10/2021 23:43:55 (446)\",\"ROUTING_SEQ=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=pstn\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=70053fde-2939-415b-88f3-73bb14e754ed\",\"STIR_SHAKEN=null\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"api\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"f37105753730992fe201de02f3129b96\",\"sessionId\":\"f37105753730992fe201de02f3129b96\",\"leg1Id\":\"392322055_112053108@************\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"635f5b18\",\"accountPricingGroup\":\"\",\"from\":\" ***********\",\"to\":\"***********\",\"prefixFrom\":\"1270705\",\"prefixTo\":\"1502449\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"310090\",\"networkName\":\"AT&T Mobility\",\"networkType\":\"MOBILE\",\"gw\":\"vonage\",\"gws\":\"vonage\",\"callRetries\":\"0\",\"rerouteAddress\":\"60e477c0-3f1c-4391-be62-29c7938ba63f\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"08/12/2020 03:42:17 (000)\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"default-cost\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"null\",\"start\":\"2021-10-10T23:43:55.446+0000\",\"end\":\"2021-10-11T00:00:16.446+0000\",\"duration\":\"981\",\"callDuration\":\"981000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-10T23:43:55.446+0000\",\"routingSeq\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"pstn\",\"callTermination\":\"\",\"customerDomain\":\"\",\"cdrUuid\":\"70053fde-2939-415b-88f3-73bb14e754ed\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:16.275+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipInboundCDR, logConvJson);
        
        sipInboundCDR = "************* :: 10/11/2021 00:01:10 (706) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=e485edccc19890177238f47f927b336a-1\",\"SESSION-ID=e485edccc19890177238f47f927b336a\",\"LEG1-ID=436374_127894315@**************\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=ce0c340f\",\"ACCOUNT-PRICING-GROUP=\",\"FROM=***********\",\"TO=***********\",\"PREFIX-FROM=1786320\",\"PREFIX-TO=1786453\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=US-VIRTUAL-BANDWIDTH\",\"NETWORK-NAME=Bandwidth.com CLEC, LLC\",\"NETWORK-TYPE=VIRTUAL\",\"GW=vonage\",\"GWS=vonage\",\"CALL_RETRIES=0\",\"REROUTE-ADDRESS=***********@************\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=default-cost\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=null\",\"START=10/11/2021 00:00:34 (872)\",\"END=10/11/2021 00:01:10 (872)\",\"DURATION=36\",\"CALL_DURATION=36000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/11/2021 00:00:34 (872)\",\"ROUTING_SEQ=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=pstn\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=248066e8-ef7e-4399-83b1-c4d964a72db1\",\"STIR_SHAKEN=null\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"e485edccc19890177238f47f927b336a-1\",\"sessionId\":\"e485edccc19890177238f47f927b336a\",\"leg1Id\":\"436374_127894315@**************\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"ce0c340f\",\"accountPricingGroup\":\"\",\"from\":\"***********\",\"to\":\"***********\",\"prefixFrom\":\"1786320\",\"prefixTo\":\"1786453\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"US-VIRTUAL-BANDWIDTH\",\"networkName\":\"Bandwidth.com CLEC, LLC\",\"networkType\":\"VIRTUAL\",\"gw\":\"vonage\",\"gws\":\"vonage\",\"callRetries\":\"0\",\"rerouteAddress\":\"***********@************\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"default-cost\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"null\",\"start\":\"2021-10-11T00:00:34.872+0000\",\"end\":\"2021-10-11T00:01:10.872+0000\",\"duration\":\"36\",\"callDuration\":\"36000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-11T00:00:34.872+0000\",\"routingSeq\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"pstn\",\"callTermination\":\"\",\"customerDomain\":\"\",\"cdrUuid\":\"248066e8-ef7e-4399-83b1-c4d964a72db1\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:01:10.706+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipInboundCDR, logConvJson);
    }
    
    @Test
    public void sipOutboundCDRsTest() {
        String sipOutboundCDR = "************* :: 10/11/2021 00:00:16 (551) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=api\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=5f62f7a1-bf71-411b-923a-a500bd610530\",\"SESSION-ID=5f62f7a1-bf71-411b-923a-a500bd610530\",\"LEG1-ID=f73c30c0-a4c8-123a-2494-06de94703c47\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=b8595f81\",\"ACCOUNT-PRICING-GROUP=\",\"FROM=***********\",\"TO=***********\",\"PREFIX-FROM=1602429\",\"PREFIX-TO=1623224\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=310090\",\"NETWORK-NAME=AT&T Mobility\",\"NETWORK-TYPE=MOBILE\",\"GW=vonage-prem\",\"GWS=vonage-prem,ibasis\",\"GW_ATTEMPT=1#2\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=02/27/2019 05:20:02 (000)\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=1623224\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=06/14/2018 17:02:09 (000)\",\"START=10/10/2021 23:59:41 (835)\",\"END=10/11/2021 00:00:16 (835)\",\"DURATION=35\",\"CALL_DURATION=35000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/10/2021 23:59:41 (835)\",\"ROUTING_SEQ=***************\",\"PDD=1585\",\"REQUEST_IP=\",\"CALL_ORIGIN=\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=f1d9bf47-33ff-4b0d-a693-008616b3c220\",\"STIR_SHAKEN=A\",\"PSTN_PLATFORM=0\",";
        String logConvJson = "{\"product\":\"5\",\"productClass\":\"api\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"5f62f7a1-bf71-411b-923a-a500bd610530\",\"sessionId\":\"5f62f7a1-bf71-411b-923a-a500bd610530\",\"leg1Id\":\"f73c30c0-a4c8-123a-2494-06de94703c47\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"b8595f81\",\"accountPricingGroup\":\"\",\"from\":\"***********\",\"to\":\"***********\",\"prefixFrom\":\"1602429\",\"prefixTo\":\"1623224\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"310090\",\"networkName\":\"AT&T Mobility\",\"networkType\":\"MOBILE\",\"gw\":\"vonage-prem\",\"gws\":\"vonage-prem,ibasis\",\"gwAttempt\":\"1#2\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"02/27/2019 05:20:02 (000)\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"1623224\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"06/14/2018 17:02:09 (000)\",\"start\":\"2021-10-10T23:59:41.835+0000\",\"end\":\"2021-10-11T00:00:16.835+0000\",\"duration\":\"35\",\"callDuration\":\"35000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-10T23:59:41.835+0000\",\"routingSeq\":\"***************\",\"pdd\":\"1585\",\"requestIp\":\"\",\"callOrigin\":\"\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"f1d9bf47-33ff-4b0d-a693-008616b3c220\",\"stirShaken\":\"A\",\"pstnPlatform\":\"0\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:16.551+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipOutboundCDR, logConvJson);
         
        sipOutboundCDR = "************* :: 10/11/2021 00:01:10 (082) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=verify\",\"PRODUCT-VERSION=NG\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=b9acf3a6-2de4-4300-a240-b5ed2245b6ec\",\"SESSION-ID=b9acf3a6-2de4-4300-a240-b5ed2245b6ec\",\"LEG1-ID=1511116e-a4c9-123a-6e9f-06d0f6bc7fde\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=55d147c2\",\"ACCOUNT-PRICING-GROUP=VERIFY_LEGACY\",\"FROM=GLOVO\",\"TO=*************\",\"PREFIX-FROM=\",\"PREFIX-TO=*********\",\"FORCED_SENDER=************\",\"PREFIX-FORCED_SENDER=********\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=AR\",\"NET=722070\",\"NETWORK-NAME=Telefonica Moviles\",\"NETWORK-TYPE=MOBILE\",\"GW=bics\",\"GWS=bics\",\"GW_ATTEMPT=1#1\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=0\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=-0.********\",\"COST_PREFIX=549\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=01/20/2020 15:23:18 (000)\",\"START=10/11/2021 00:00:51 (072)\",\"END=10/11/2021 00:01:11 (072)\",\"DURATION=20\",\"CALL_DURATION=20000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/11/2021 00:00:51 (072)\",\"ROUTING_SEQ=***************\",\"PDD=1960\",\"REQUEST_IP=\",\"CALL_ORIGIN=verify\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=a1a70a7c-c115-45df-ac9b-748b6c588707\",\"STIR_SHAKEN=none\",\"PSTN_PLATFORM=0\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"verify\",\"productVersion\":\"NG\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"b9acf3a6-2de4-4300-a240-b5ed2245b6ec\",\"sessionId\":\"b9acf3a6-2de4-4300-a240-b5ed2245b6ec\",\"leg1Id\":\"1511116e-a4c9-123a-6e9f-06d0f6bc7fde\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"55d147c2\",\"accountPricingGroup\":\"VERIFY_LEGACY\",\"from\":\"GLOVO\",\"to\":\"*************\",\"prefixFrom\":\"\",\"prefixTo\":\"*********\",\"forceSender\":\"************\",\"prefixForcedSender\":\"********\",\"sipDestAttempt\":\"1#1\",\"country\":\"AR\",\"net\":\"722070\",\"networkName\":\"Telefonica Moviles\",\"networkType\":\"MOBILE\",\"gw\":\"bics\",\"gws\":\"bics\",\"gwAttempt\":\"1#1\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"0\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"-0.********\",\"costPrefix\":\"549\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"01/20/2020 15:23:18 (000)\",\"start\":\"2021-10-11T00:00:51.072+0000\",\"end\":\"2021-10-11T00:01:11.072+0000\",\"duration\":\"20\",\"callDuration\":\"20000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-11T00:00:51.072+0000\",\"routingSeq\":\"***************\",\"pdd\":\"1960\",\"requestIp\":\"\",\"callOrigin\":\"verify\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"a1a70a7c-c115-45df-ac9b-748b6c588707\",\"stirShaken\":\"none\",\"pstnPlatform\":\"0\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:01:10.082+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipOutboundCDR, logConvJson);
        
        sipOutboundCDR = "************* :: 10/11/2021 11:39:41 (426) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=cbc81c8cd8574bee566d40d567e5d28a-2\",\"SESSION-ID=cbc81c8cd8574bee566d40d567e5d28a\",\"LEG1-ID=3721eabf35f64a8cf4782e847513fb0d@***********\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=1c485038\",\"ACCOUNT-PRICING-GROUP=VOICE_PROXY\",\"FROM=***********\",\"TO=sip:***********@************:5060\",\"PREFIX-FROM=3366122\",\"PREFIX-TO=\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#3\",\"COUNTRY=null\",\"NET=null\",\"NETWORK-NAME=null\",\"NETWORK-TYPE=null\",\"GW=default\",\"GWS=default\",\"GW_ATTEMPT=1#1\",\"REROUTE-ADDRESS=sip:***********@************:5060\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=default-destination-price\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=default-sip-destination-cost\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=null\",\"START=10/11/2021 11:36:40 (891)\",\"END=10/11/2021 11:39:41 (891)\",\"DURATION=181\",\"CALL_DURATION=181000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/11/2021 11:36:40 (891)\",\"ROUTING_SEQ=0\",\"PDD=364\",\"REQUEST_IP=\",\"CALL_ORIGIN=\",\"CALL_TERMINATION=sip\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=ea236d99-8bef-45a5-b2e6-8624a7eb5208\",\"STIR_SHAKEN=null\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"cbc81c8cd8574bee566d40d567e5d28a-2\",\"sessionId\":\"cbc81c8cd8574bee566d40d567e5d28a\",\"leg1Id\":\"3721eabf35f64a8cf4782e847513fb0d@***********\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"1c485038\",\"accountPricingGroup\":\"VOICE_PROXY\",\"from\":\"***********\",\"to\":\"sip:***********@************:5060\",\"prefixFrom\":\"3366122\",\"prefixTo\":\"\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#3\",\"country\":\"null\",\"net\":\"null\",\"networkName\":\"null\",\"networkType\":\"null\",\"gw\":\"default\",\"gws\":\"default\",\"gwAttempt\":\"1#1\",\"rerouteAddress\":\"sip:***********@************:5060\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"default-destination-price\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"default-sip-destination-cost\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"null\",\"start\":\"2021-10-11T11:36:40.891+0000\",\"end\":\"2021-10-11T11:39:41.891+0000\",\"duration\":\"181\",\"callDuration\":\"181000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-11T11:36:40.891+0000\",\"routingSeq\":\"0\",\"pdd\":\"364\",\"requestIp\":\"\",\"callOrigin\":\"\",\"callTermination\":\"sip\",\"customerDomain\":\"\",\"cdrUuid\":\"ea236d99-8bef-45a5-b2e6-8624a7eb5208\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T11:39:41.426+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipOutboundCDR, logConvJson);  
        
        sipOutboundCDR = "************* :: 10/11/2021 11:40:04 (359) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=61f97ee74145115678a80a4ad32fd9ce-2\",\"SESSION-ID=61f97ee74145115678a80a4ad32fd9ce\",\"LEG1-ID=1872df21c175a8c94d031a2d2d86344b@0.0.0.0\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=null\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=c362109e\",\"ACCOUNT-PRICING-GROUP=\",\"FROM=***********\",\"TO=***********\",\"PREFIX-FROM=3376399\",\"PREFIX-TO=1720383\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=US-VIRTUAL-BANDWIDTH\",\"NETWORK-NAME=Bandwidth.com CLEC, LLC\",\"NETWORK-TYPE=VIRTUAL\",\"GW=vonage-prem\",\"GWS=vonage-prem\",\"GW_ATTEMPT=1#1\",\"REROUTE-ADDRESS=***********\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=1720383\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=06/14/2018 17:35:53 (000)\",\"START=10/11/2021 11:40:01 (937)\",\"END=10/11/2021 11:40:04 (937)\",\"DURATION=3\",\"CALL_DURATION=3000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/11/2021 11:40:01 (937)\",\"ROUTING_SEQ=***************\",\"PDD=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=6cf8ecc2-4ffb-4a03-8613-23ab1ddc644a\",\"STIR_SHAKEN=C\",\"CARRIER_PLATFORM=NET\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"61f97ee74145115678a80a4ad32fd9ce-2\",\"sessionId\":\"61f97ee74145115678a80a4ad32fd9ce\",\"leg1Id\":\"1872df21c175a8c94d031a2d2d86344b@0.0.0.0\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"null\",\"masterAccountPricingGroup\":\"\",\"acc\":\"c362109e\",\"accountPricingGroup\":\"\",\"from\":\"***********\",\"to\":\"***********\",\"prefixFrom\":\"3376399\",\"prefixTo\":\"1720383\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"US-VIRTUAL-BANDWIDTH\",\"networkName\":\"Bandwidth.com CLEC, LLC\",\"networkType\":\"VIRTUAL\",\"gw\":\"vonage-prem\",\"gws\":\"vonage-prem\",\"gwAttempt\":\"1#1\",\"rerouteAddress\":\"***********\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"1720383\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"06/14/2018 17:35:53 (000)\",\"start\":\"2021-10-11T11:40:01.937+0000\",\"end\":\"2021-10-11T11:40:04.937+0000\",\"duration\":\"3\",\"callDuration\":\"3000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-11T11:40:01.937+0000\",\"routingSeq\":\"***************\",\"pdd\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"6cf8ecc2-4ffb-4a03-8613-23ab1ddc644a\",\"stirShaken\":\"C\",\"carrierPlatform\":\"NET\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T11:40:04.359+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipOutboundCDR, logConvJson);  
    }
    
    @Test
    public void ttsOutboundCDRsTest() {
        String ttsOutboundCDR = "************* :: 10/11/2021 00:00:05 (421) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=tts\",\"PRODUCT-VERSION=NG\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=d4244977-32d4-414c-9936-66f533057989\",\"SESSION-ID=d4244977-32d4-414c-9936-66f533057989\",\"LEG1-ID=f1e9af0d-a4c8-123a-6e9f-06d0f6bc7fde\",\"LEG2-ID=<EMAIL>\",\"SUPERHUB-ACC=605ab82a\",\"MASTER-ACCOUNT-PRICING-GROUP=\",\"ACC=7a937409\",\"ACCOUNT-PRICING-GROUP=\",\"FROM=***********\",\"TO=************\",\"PREFIX-FROM=1205639\",\"PREFIX-TO=********\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=CO\",\"NET=732101\",\"NETWORK-NAME=Comcel\",\"NETWORK-TYPE=MOBILE\",\"GW=vonage-prem\",\"GWS=vonage-prem\",\"GW_ATTEMPT=1#1\",\"UNIT=6\",\"MIN_UNIT=6\",\"RECURRING_UNIT=6\",\"PRICE=0.********\",\"OVERRIDE_PRICE=null\",\"TOTAL_PRICE=0.********\",\"PRICE_PREFIX=null\",\"PRICE_PREFIX_GROUP=null\",\"PRICE_SENDER_PREFIX=null\",\"PRICE_TIMESTAMP=null\",\"COST=0.********\",\"TOTAL_COST=0.********\",\"CALL_MARGIN=0.********\",\"COST_PREFIX=57321\",\"COST_PREFIX_GROUP=null\",\"COST_SENDER_PREFIX=null\",\"COST_TIMESTAMP=11/10/2020 17:05:10 (000)\",\"START=10/10/2021 23:59:39 (826)\",\"END=10/11/2021 00:00:05 (826)\",\"DURATION=26\",\"CALL_DURATION=26000\",\"STATUS=ANSWER\",\"REASON=200\",\"REASON_DESC=The call was successful.\",\"CALL_DATE=10/10/2021 23:59:39 (826)\",\"ROUTING_SEQ=***************\",\"BACKEND=MB\",\"PDD=1879\",\"REQUEST_IP=\",\"CALL_ORIGIN=api\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"TTS_XTRACE_ID=a0c30ae8-ee56-4c8f-931e-51f216781288\",\"CALL_BACK_URL=\",\"CALL_BACK_METHOD=GET_QUERY_PARAMS\",\"CLIENT_REFERENCE=\",\"REPEAT=3\",\"MACHINE_DETECTION_TYPE=\",\"MACHINE_TIMEOUT=-1\",\"LANGUAGE_NAME=es-ES\",\"MB_STYLE=4\",\"CDR_UUID=4cf0266a-9847-4c49-b56d-56af20f98c85\",\"STIR_SHAKEN=none\",\"PSTN_PLATFORM=0\",";
        String logConvJson = "{\"product\":\"5\",\"productClass\":\"tts\",\"productVersion\":\"NG\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"d4244977-32d4-414c-9936-66f533057989\",\"sessionId\":\"d4244977-32d4-414c-9936-66f533057989\",\"leg1Id\":\"f1e9af0d-a4c8-123a-6e9f-06d0f6bc7fde\",\"leg2Id\":\"<EMAIL>\",\"superHubAcc\":\"605ab82a\",\"masterAccountPricingGroup\":\"\",\"acc\":\"7a937409\",\"accountPricingGroup\":\"\",\"from\":\"***********\",\"to\":\"************\",\"prefixFrom\":\"1205639\",\"prefixTo\":\"********\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"CO\",\"net\":\"732101\",\"networkName\":\"Comcel\",\"networkType\":\"MOBILE\",\"gw\":\"vonage-prem\",\"gws\":\"vonage-prem\",\"gwAttempt\":\"1#1\",\"unit\":\"6\",\"minUnit\":\"6\",\"recurringUnit\":\"6\",\"price\":\"0.********\",\"overridePrice\":\"null\",\"totalPrice\":\"0.********\",\"pricePrefix\":\"null\",\"pricePrefixGroup\":\"null\",\"priceSenderPrefix\":\"null\",\"priceTimestamp\":\"null\",\"cost\":\"0.********\",\"totalCost\":\"0.********\",\"callMargin\":\"0.********\",\"costPrefix\":\"57321\",\"costPrefixGroup\":\"null\",\"costSenderPrefix\":\"null\",\"costTimestamp\":\"11/10/2020 17:05:10 (000)\",\"start\":\"2021-10-10T23:59:39.826+0000\",\"end\":\"2021-10-11T00:00:05.826+0000\",\"duration\":\"26\",\"callDuration\":\"26000\",\"status\":\"ANSWER\",\"reason\":\"200\",\"reasonDesc\":\"The call was successful.\",\"callDate\":\"2021-10-10T23:59:39.826+0000\",\"routingSeq\":\"***************\",\"backend\":\"MB\",\"pdd\":\"1879\",\"requestIp\":\"\",\"callOrigin\":\"api\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"ttsXtraceId\":\"a0c30ae8-ee56-4c8f-931e-51f216781288\",\"callbackUrl\":\"\",\"callbackMethod\":\"GET_QUERY_PARAMS\",\"clientReference\":\"\",\"repeat\":\"3\",\"machineDetectionType\":\"\",\"machineTimeout\":\"-1\",\"languageName\":\"es-ES\",\"mbStyle\":\"4\",\"cdrUuid\":\"4cf0266a-9847-4c49-b56d-56af20f98c85\",\"stirShaken\":\"none\",\"pstnPlatform\":\"0\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:05.421+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(ttsOutboundCDR, logConvJson);
    }
    
    @Test
    public void sipRejectedCDRsTest() {
        String sipRejectedCDR = "************* :: 10/11/2021 00:00:07 (108) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=api\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=f2d90f4f-1258-433f-bc62-a9e88c855501\",\"LEG1-ID=05ed9c0e-a4c9-123a-75b6-06d6125e9399\",\"LEG2-ID=null\",\"SUPERHUB-ACC=null\",\"ACC=f5ab1a77\",\"FROM=************\",\"TO=************\",\"PREFIX-FROM=********\",\"PREFIX-TO=********\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=MX\",\"NET=33402\",\"GW=c3ntro\",\"GWS=c3ntro,vonage-prem\",\"GW_ATTEMPT=1#2\",\"STATUS=BUSY\",\"REASON=486\",\"REASON_DESC=Callee is busy\",\"START=10/11/2021 00:00:02 (001)\",\"END=10/11/2021 00:00:02 (001)\",\"CALL_DATE=10/11/2021 00:00:02 (001)\",\"ROUTING_SEQ=***************\",\"OUTBOUND-GW=c3ntro\",\"REQUEST_IP=\",\"CALL_ORIGIN=api\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=421d9e33-e24d-46d1-8c0d-dcd2f7a24c0c\",\"STIR_SHAKEN=none\",\"PSTN_PLATFORM=0\",";
        String logConvJson = "{\"product\":\"5\",\"productClass\":\"api\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"f2d90f4f-1258-433f-bc62-a9e88c855501\",\"leg1Id\":\"05ed9c0e-a4c9-123a-75b6-06d6125e9399\",\"leg2Id\":\"null\",\"superHubAcc\":\"null\",\"acc\":\"f5ab1a77\",\"from\":\"************\",\"to\":\"************\",\"prefixFrom\":\"********\",\"prefixTo\":\"********\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"MX\",\"net\":\"33402\",\"gw\":\"c3ntro\",\"gws\":\"c3ntro,vonage-prem\",\"gwAttempt\":\"1#2\",\"status\":\"BUSY\",\"reason\":\"486\",\"reasonDesc\":\"Callee is busy\",\"start\":\"2021-10-11T00:00:02.001+0000\",\"end\":\"2021-10-11T00:00:02.001+0000\",\"callDate\":\"2021-10-11T00:00:02.001+0000\",\"routingSeq\":\"***************\",\"outboundGw\":\"c3ntro\",\"requestIp\":\"\",\"callOrigin\":\"api\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"421d9e33-e24d-46d1-8c0d-dcd2f7a24c0c\",\"stirShaken\":\"none\",\"pstnPlatform\":\"0\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:07.108+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipRejectedCDR, logConvJson);
        
        sipRejectedCDR = "************* :: 10/11/2021 00:00:25 (767) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=verify\",\"PRODUCT-VERSION=NG\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=905b7b3c-0ec5-4672-931a-fd94502198ea\",\"LEG1-ID=fe8cbdbe-a4c8-123a-e9a4-06962150ffc4\",\"LEG2-ID=null\",\"SUPERHUB-ACC=null\",\"ACC=f132be6a\",\"FROM=Unknown\",\"TO=***********\",\"PREFIX-FROM=\",\"PREFIX-TO=1908304\",\"FORCED_SENDER=***********\",\"PREFIX-FORCED_SENDER=1972453\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=310090\",\"GW=vonage-prem\",\"GWS=vonage-prem\",\"GW_ATTEMPT=1#1\",\"STATUS=CANCEL\",\"REASON=487\",\"REASON_DESC=Request terminated by BYE or CANCEL\",\"START=10/10/2021 23:59:49 (619)\",\"END=10/10/2021 23:59:49 (619)\",\"CALL_DATE=10/10/2021 23:59:49 (619)\",\"ROUTING_SEQ=****************\",\"PDD=0\",\"OUTBOUND-GW=vonage-prem\",\"REQUEST_IP=\",\"CALL_ORIGIN=verify\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=508f20cf-2ed1-429f-9ad3-dabc1235f8c9\",\"STIR_SHAKEN=C\",\"PSTN_PLATFORM=0\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"verify\",\"productVersion\":\"NG\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"905b7b3c-0ec5-4672-931a-fd94502198ea\",\"leg1Id\":\"fe8cbdbe-a4c8-123a-e9a4-06962150ffc4\",\"leg2Id\":\"null\",\"superHubAcc\":\"null\",\"acc\":\"f132be6a\",\"from\":\"Unknown\",\"to\":\"***********\",\"prefixFrom\":\"\",\"prefixTo\":\"1908304\",\"forceSender\":\"***********\",\"prefixForcedSender\":\"1972453\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"310090\",\"gw\":\"vonage-prem\",\"gws\":\"vonage-prem\",\"gwAttempt\":\"1#1\",\"status\":\"CANCEL\",\"reason\":\"487\",\"reasonDesc\":\"Request terminated by BYE or CANCEL\",\"start\":\"2021-10-10T23:59:49.619+0000\",\"end\":\"2021-10-10T23:59:49.619+0000\",\"callDate\":\"2021-10-10T23:59:49.619+0000\",\"routingSeq\":\"****************\",\"pdd\":\"0\",\"outboundGw\":\"vonage-prem\",\"requestIp\":\"\",\"callOrigin\":\"verify\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"508f20cf-2ed1-429f-9ad3-dabc1235f8c9\",\"stirShaken\":\"C\",\"pstnPlatform\":\"0\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:00:25.767+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipRejectedCDR, logConvJson);

        sipRejectedCDR = "************* :: 10/11/2021 00:04:14 (551) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=in\",\"HOST=ass1.wdc4.internal\",\"ID=ff34b2c0299e1be523c78bc61447318a\",\"LEG1-ID=443194464_123586032@************\",\"LEG2-ID=null\",\"SUPERHUB-ACC=null\",\"ACC=efe9cd03\",\"FROM= ***********\",\"TO=***********\",\"PREFIX-FROM=1626234\",\"PREFIX-TO=1626234\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"REROUTE-ADDRESS=***********@***************\",\"COUNTRY=US\",\"NET=US-FIXED\",\"GW=vonage\",\"GWS=vonage\",\"CALL_RETRIES=0\",\"STATUS=NOT_FOUND\",\"REASON=404\",\"REASON_DESC=Recipient does not exist in the specified domain\",\"START=10/11/2021 00:04:14 (435)\",\"END=10/11/2021 00:04:14 (435)\",\"CALL_DATE=10/11/2021 00:04:14 (435)\",\"ROUTING_SEQ=0\",\"OUTBOUND-GW=default\",\"REQUEST_IP=\",\"CALL_ORIGIN=pstn\",\"CALL_TERMINATION=\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=913d14a7-4333-48a4-8259-b737e70512a3\",\"STIR_SHAKEN=null\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"in\",\"host\":\"ass1.wdc4.internal\",\"id\":\"ff34b2c0299e1be523c78bc61447318a\",\"leg1Id\":\"443194464_123586032@************\",\"leg2Id\":\"null\",\"superHubAcc\":\"null\",\"acc\":\"efe9cd03\",\"from\":\" ***********\",\"to\":\"***********\",\"prefixFrom\":\"1626234\",\"prefixTo\":\"1626234\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"rerouteAddress\":\"***********@***************\",\"country\":\"US\",\"net\":\"US-FIXED\",\"gw\":\"vonage\",\"gws\":\"vonage\",\"callRetries\":\"0\",\"status\":\"NOT_FOUND\",\"reason\":\"404\",\"reasonDesc\":\"Recipient does not exist in the specified domain\",\"start\":\"2021-10-11T00:04:14.435+0000\",\"end\":\"2021-10-11T00:04:14.435+0000\",\"callDate\":\"2021-10-11T00:04:14.435+0000\",\"routingSeq\":\"0\",\"outboundGw\":\"default\",\"requestIp\":\"\",\"callOrigin\":\"pstn\",\"callTermination\":\"\",\"customerDomain\":\"\",\"cdrUuid\":\"913d14a7-4333-48a4-8259-b737e70512a3\",\"stirShaken\":\"null\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T00:04:14.551+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipRejectedCDR, logConvJson);
        
        sipRejectedCDR = "************* :: 12/08/2021 15:39:49 (283) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=out\",\"HOST=ass8.dal13.internal\",\"ID=f6b6ba2b96bf7932cb93975f44d5a659\",\"LEG1-ID=e8c53a7c-d2df-123a-52b8-42010a800005\",\"LEG2-ID=null\",\"SUPERHUB-ACC=null\",\"ACC=f5e5bcce\",\"FROM=***********\",\"TO=***********\",\"PREFIX-FROM=1210368\",\"PREFIX-TO=1281897\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=US\",\"NET=US-FIXED\",\"GW=vonage-prem\",\"GWS=ibasis,vonage-prem\",\"GW_ATTEMPT=2#2\",\"STATUS=CANCEL\",\"REASON=487\",\"REASON_DESC=Request terminated by BYE or CANCEL\",\"START=12/08/2021 15:39:46 (098)\",\"END=12/08/2021 15:39:46 (098)\",\"CALL_DATE=12/08/2021 15:39:46 (098)\",\"ROUTING_SEQ=***************\",\"OUTBOUND-GW=vonage-prem\",\"REQUEST_IP=*************\",\"CALL_ORIGIN=sip\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=8eac8869-5b04-4ce4-a8b6-596c1f6f6333\",\"STIR_SHAKEN=none\",\"CARRIER_PLATFORM=NET\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"out\",\"host\":\"ass8.dal13.internal\",\"id\":\"f6b6ba2b96bf7932cb93975f44d5a659\",\"leg1Id\":\"e8c53a7c-d2df-123a-52b8-42010a800005\",\"leg2Id\":\"null\",\"superHubAcc\":\"null\",\"acc\":\"f5e5bcce\",\"from\":\"***********\",\"to\":\"***********\",\"prefixFrom\":\"1210368\",\"prefixTo\":\"1281897\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"US\",\"net\":\"US-FIXED\",\"gw\":\"vonage-prem\",\"gws\":\"ibasis,vonage-prem\",\"gwAttempt\":\"2#2\",\"status\":\"CANCEL\",\"reason\":\"487\",\"reasonDesc\":\"Request terminated by BYE or CANCEL\",\"start\":\"2021-12-08T15:39:46.098+0000\",\"end\":\"2021-12-08T15:39:46.098+0000\",\"callDate\":\"2021-12-08T15:39:46.098+0000\",\"routingSeq\":\"***************\",\"outboundGw\":\"vonage-prem\",\"requestIp\":\"*************\",\"callOrigin\":\"sip\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"8eac8869-5b04-4ce4-a8b6-596c1f6f6333\",\"stirShaken\":\"none\",\"carrierPlatform\":\"NET\",\"@timestamp\":\"*************\",\"@date\":\"2021-12-08T15:39:49.283+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipRejectedCDR, logConvJson);
        
        sipRejectedCDR = "************* :: 12/08/2021 15:36:06 (772) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=sip\",\"DIRECTION=out\",\"HOST=ass7.lon1.internal\",\"ID=fe12408abae5a6c30dc0d4bb649d3776\",\"LEG1-ID=686975b2-4669-41ab-a9e8-7b22e920f020\",\"LEG2-ID=\",\"SUPERHUB-ACC=null\",\"ACC=4deccd6c\",\"FROM=anonymous\",\"TO=***********\",\"PREFIX-FROM=\",\"PREFIX-TO=8529704\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=HK\",\"NET=45404\",\"GW=bts\",\"GWS=bts,telenor-voice\",\"GW_ATTEMPT=1#2\",\"STATUS=CONGESTION\",\"REASON=500\",\"REASON_DESC=Internal error occurred.\",\"START=12/08/2021 15:36:06 (668)\",\"END=12/08/2021 15:36:06 (668)\",\"CALL_DATE=12/08/2021 15:36:06 (668)\",\"ROUTING_SEQ=***************\",\"OUTBOUND-GW=bts\",\"REQUEST_IP=************\",\"CALL_ORIGIN=sip\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"CDR_UUID=7323e2ae-9f85-43c8-ab8c-3a1d47b01c38\",\"STIR_SHAKEN=none\",\"CARRIER_PLATFORM=NET\",";
        logConvJson = "{\"product\":\"5\",\"productClass\":\"sip\",\"direction\":\"out\",\"host\":\"ass7.lon1.internal\",\"id\":\"fe12408abae5a6c30dc0d4bb649d3776\",\"leg1Id\":\"686975b2-4669-41ab-a9e8-7b22e920f020\",\"leg2Id\":\"\",\"superHubAcc\":\"null\",\"acc\":\"4deccd6c\",\"from\":\"anonymous\",\"to\":\"***********\",\"prefixFrom\":\"\",\"prefixTo\":\"8529704\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"HK\",\"net\":\"45404\",\"gw\":\"bts\",\"gws\":\"bts,telenor-voice\",\"gwAttempt\":\"1#2\",\"status\":\"CONGESTION\",\"reason\":\"500\",\"reasonDesc\":\"Internal error occurred.\",\"start\":\"2021-12-08T15:36:06.668+0000\",\"end\":\"2021-12-08T15:36:06.668+0000\",\"callDate\":\"2021-12-08T15:36:06.668+0000\",\"routingSeq\":\"***************\",\"outboundGw\":\"bts\",\"requestIp\":\"************\",\"callOrigin\":\"sip\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"cdrUuid\":\"7323e2ae-9f85-43c8-ab8c-3a1d47b01c38\",\"stirShaken\":\"none\",\"carrierPlatform\":\"NET\",\"@timestamp\":\"*************\",\"@date\":\"2021-12-08T15:36:06.772+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(sipRejectedCDR, logConvJson);
    }
    
    
    @Test
    public void ttsRejectedCDRsTest() {
        String ttsRejectedCDR = "************* :: 10/11/2021 23:28:50 (100) :: com.nexmo.voice.core.cache.VoiceContext :: \"PRODUCT=5\",\"PRODUCT-CLASS=tts\",\"PRODUCT-VERSION=NG\",\"DIRECTION=out\",\"HOST=ass1.wdc4.internal\",\"ID=ef293a09-5b02-4521-8eda-42a998c79676\",\"LEG1-ID=d499fc58-a58d-123a-4eb1-068131cfda86\",\"LEG2-ID=null\",\"SUPERHUB-ACC=605ab82a\",\"ACC=7a937409\",\"FROM=***********\",\"TO=*************\",\"PREFIX-FROM=1205639\",\"PREFIX-TO=*********\",\"FORCED_SENDER=null\",\"PREFIX-FORCED_SENDER=\",\"SIP-DEST-ATTEMPT=1#1\",\"COUNTRY=null\",\"NET=null\",\"GW=null\",\"GWS=null\",\"STATUS=NO_ROUTE\",\"REASON=404\",\"REASON_DESC=Recipient does not exist in the specified domain\",\"START=10/11/2021 23:28:50 (096)\",\"END=10/11/2021 23:28:50 (096)\",\"CALL_DATE=10/11/2021 23:28:50 (096)\",\"ROUTING_SEQ=***************\",\"BACKEND=MB\",\"PDD=0\",\"REQUEST_IP=\",\"CALL_ORIGIN=api\",\"CALL_TERMINATION=pstn\",\"CUSTOMER_DOMAIN=\",\"TTS_XTRACE_ID=02bef95a-65b7-4ff1-9264-967ac95336ff\",\"CALL_BACK_URL=\",\"CALL_BACK_METHOD=GET_QUERY_PARAMS\",\"CLIENT_REFERENCE=\",\"REPEAT=3\",\"MACHINE_DETECTION_TYPE=\",\"MACHINE_TIMEOUT=-1\",\"LANGUAGE_NAME=en-US\",\"MB_STYLE=0\",\"CDR_UUID=f56f3224-8e04-4457-b02e-6409a73f0030\",\"STIR_SHAKEN=null\",\"INTERNAL_FLAG=97\",";
        String logConvJson = "{\"product\":\"5\",\"productClass\":\"tts\",\"productVersion\":\"NG\",\"direction\":\"out\",\"host\":\"ass1.wdc4.internal\",\"id\":\"ef293a09-5b02-4521-8eda-42a998c79676\",\"leg1Id\":\"d499fc58-a58d-123a-4eb1-068131cfda86\",\"leg2Id\":\"null\",\"superHubAcc\":\"605ab82a\",\"acc\":\"7a937409\",\"from\":\"***********\",\"to\":\"*************\",\"prefixFrom\":\"1205639\",\"prefixTo\":\"*********\",\"forceSender\":\"null\",\"prefixForcedSender\":\"\",\"sipDestAttempt\":\"1#1\",\"country\":\"null\",\"net\":\"null\",\"gw\":\"null\",\"gws\":\"null\",\"status\":\"NO_ROUTE\",\"reason\":\"404\",\"reasonDesc\":\"Recipient does not exist in the specified domain\",\"start\":\"2021-10-11T23:28:50.096+0000\",\"end\":\"2021-10-11T23:28:50.096+0000\",\"callDate\":\"2021-10-11T23:28:50.096+0000\",\"routingSeq\":\"***************\",\"backend\":\"MB\",\"pdd\":\"0\",\"requestIp\":\"\",\"callOrigin\":\"api\",\"callTermination\":\"pstn\",\"customerDomain\":\"\",\"ttsXtraceId\":\"02bef95a-65b7-4ff1-9264-967ac95336ff\",\"callbackUrl\":\"\",\"callbackMethod\":\"GET_QUERY_PARAMS\",\"clientReference\":\"\",\"repeat\":\"3\",\"machineDetectionType\":\"\",\"machineTimeout\":\"-1\",\"languageName\":\"en-US\",\"mbStyle\":\"0\",\"cdrUuid\":\"f56f3224-8e04-4457-b02e-6409a73f0030\",\"stirShaken\":\"null\",\"internalFlag\":\"97\",\"@timestamp\":\"*************\",\"@date\":\"2021-10-11T23:28:50.100+0000\",\"@class\":\"com.nexmo.voice.core.cache.VoiceContext\"}";
        verify(ttsRejectedCDR, logConvJson);
    }



    public static void verify(String kvCDR, String logConvCDR) {
        HashMap <String, String> kvMap = buildCDRMap(kvCDR);
        assertNotNull(kvMap);
        
        HashMap <String, String> jsonMap = buildJsonMap(logConvCDR);
        assertNotNull(jsonMap);

        assertEquals(kvMap.size(), jsonMap.size());
        assertNotEquals(kvMap.size(), 0);
        
        kvMap.entrySet().stream().forEach(e -> verifyEntry(e, jsonMap));
    }
    

    public static void verifyEntry(Entry<String, String> e, HashMap<String, String> jsonMap) {
        String cdrKey = e.getKey();
        String cdrValue = e.getValue();
        
        //handle the special CDR header values: timestamp
        if (cdrKey.equals("@timestamp")) {
            verifyCDRTimestamp(cdrKey, cdrValue, jsonMap);
            return;
        } 
        
        String jsonKey = CDRKeysConverter.getJsonKey(cdrKey);
        assertNotNull(jsonKey);
        
        String jsonValue = jsonMap.get(jsonKey);
        assertNotNull("Failed to find the json value of jsonKey="+jsonKey+" converted from cdrKey="+cdrKey, jsonValue);
        
        String jsonExpectedValue = CDRValuesConverter.convertValue(cdrKey,cdrValue);
        assertNotNull(jsonExpectedValue);
        
        assertEquals("for cdrKey="+cdrKey+" jsonKey="+jsonKey, jsonExpectedValue, jsonValue);
    }


    public static void verifyCDRTimestamp(String cdrKey, String cdrValue, HashMap<String, String> jsonMap) {
        String jsonKey = cdrKey;
        // timestamp is different due to some bug in LogConverter
        // just check it is there on the json object
        assertNotNull("for cdrKey=" + cdrKey + " jsonKey=" + jsonKey, jsonMap.get(jsonKey));
        return;
    }

    public static HashMap<String, String> buildCDRMap(String kvCDR) {
        /**
         * CDR first part:
         * 
         * 1633961911064 :: 10/11/2021 14:18:31 (064) :: com.nexmo.voice.core.cache.VoiceContext :: 
         * 
         * CDR regular key-values (partial list):
         * 
         * "PRODUCT=5","PRODUCT-CLASS=sip","DIRECTION=in","HOST=ass1.wdc4.internal","ID=250020a3a17226cf88409ee6297ce287",
         */
        HashMap<String, String> cdrMap = new HashMap();
        String[] cdrParts = kvCDR.split("::");
        assertEquals(4, cdrParts.length);
        
        String timestamp = cdrParts[0].trim();
        String cdrDate = cdrParts[1].trim();
        String cdrClass = cdrParts[2].trim();
        String cdrData = cdrParts[3].trim();
        
        /**
         * json special attributes taken from the CDR first 3 parts:
         * "@timestamp":"*************",
         * "@date":"2021-10-11T14:18:31.064+0000",
         * "@class":"com.nexmo.voice.core.cache.VoiceContext"
         * 
         */
        
        cdrMap.put("@timestamp", timestamp);
        cdrMap.put("@date", cdrDate);
        cdrMap.put("@class", cdrClass);
        
        //Regular CDR key-value pairs
        //The cdrData starts with "PRODUCT=5... and end with STIR_SHAKEN=null", (just an example)
        //For later proper clean split, we need to remove the first " and the last ,"
        cdrData = cdrData.substring(1,cdrData.length()-2);
        
        String[] cdrPairs = cdrData.split("\",\""); //This will ignore the "," inside values so it wont split
        assertTrue(cdrPairs.length > 0);
        for (int i=0; i<cdrPairs.length; i++) {
            String pair = cdrPairs[i];
            String[] kv = pair.split("=",2); //Avoid the TO=sip:<EMAIL>;transport=tls
            assertTrue("Pair="+pair+" kv="+kv,(kv.length == 2) || (kv.length == 1)) ;
            
            String cdrKey = kv[0];
            String cdrValue;
            cdrValue = (kv.length == 2) ? kv[1] : "";
            cdrMap.put(cdrKey, cdrValue);
            
            //double check the parsing was correct by verifying that the key is indeed a key in the CDR and not
            //a wrong split result.
            //i.e if the key is "direction" check that "direction=out", is in the original cdr
            String reconstructedKeyValue = "\""+cdrKey+"="+cdrValue+"\",";
            assertTrue("reconstructedKeyValue: "+reconstructedKeyValue+" is not in kvCDR",kvCDR.contains(reconstructedKeyValue));
        }
        
        return cdrMap;
    }
    
    public static HashMap<String, String> buildJsonMap(String jsonCDR) {
        /**
         * json CDR from logConverter (Partial example):
         * 
         * {"product":"5","productClass":"sip",
         *  "@timestamp":"*************","@date":"2021-10-11T14:18:31.064+0000",
         *  "@class":"com.nexmo.voice.core.cache.VoiceContext"}         
         * 
         * */
        HashMap<String, String> jsonMap = new HashMap();
 
        String jsonData = jsonCDR.replace("{\"", ""); //remove json prefix
        jsonData = jsonData.replace("\"}", ""); //remove json suffix
        
        String[] jsonPairs = jsonData.split("\",\"");
        assertTrue("jsonPairs.length is: "+jsonPairs.length, jsonPairs.length > 0);
        
        for (int i=0; i<jsonPairs.length; i++) {
            String pair = jsonPairs[i];
            //we cannot just split on ":" because of: "rerouteAddress":"sip:+***********@************"
            String[] kv = pair.split("\":\"", 2);
            
            
            String jsonKey = kv[0];
            String jsonValue;
            jsonValue = (kv.length == 2) ? kv[1] : "";
            jsonMap.put(jsonKey, jsonValue);

            //double check the parsing was correct by verifying that the key is indeed a key in the json and not
            //a wrong split result.
            //i.e if the key is "direction" check that "direction":"out", is in the original json cdr
            String reconstructedKeyValue = "\""+jsonKey+"\":\""+jsonValue+"\"";
            assertTrue("reconstructedKeyValue: "+reconstructedKeyValue+" is not in jsonCDR",jsonCDR.contains(reconstructedKeyValue));
        }
        
        return jsonMap;
     }

}
