package com.nexmo.voice.core.billing;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;

import org.junit.Test;



public class EmergencyStopBillingTest {
    
    
    @Test
    public void emergencyStopBillingNotStartedCallTest() throws Exception {
	
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        String sessionId = "1234";
        String connectionId = "5678";
        
        BillingInfo billingInfo = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, billingInfo.getStatus());
        
        //mimic some time passing, just to make sure the System.currentTimeMillis() return different value for the startTime and endTime,
        //and yet because of the call status, we know it was not started and its length is zero
        Thread.sleep(1000);
        
        assertTrue(billingInfo.shouldHandleEmergencyStopCharging(sessionId,connectionId));
        
    	//Calculate the end of call details as would happened in BillingManager.emergencyStopCharging
        QuotaUpdateDetails quotaUpdateDetails = billingInfo.emergencyStopCharging(sessionId, connectionId);

        assertTrue(quotaUpdateDetails.shouldSkipQuotaUpdate());
        assertTrue(billingInfo.getCallDurationInSeconds() == 0);
        assertEquals(billingInfo.getTotalCallPrice(sessionId, connectionId).longValue(), 0);
    }
    
    @Test
    public void emergencyStopBillingStartedCallTest() throws Exception {
	
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        String sessionId = "1234";
        String connectionId = "5678";
        
        BillingInfo billingInfo = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        billingInfo.startCharging(sessionId, connectionId, System.currentTimeMillis());
        assertEquals(BillingInfo.Status.STARTED, billingInfo.getStatus());
        
        assertTrue(billingInfo.shouldHandleEmergencyStopCharging(sessionId,connectionId));

    	//Calculate the end of call details as would happened in BillingManager.emergencyStopCharging
        QuotaUpdateDetails quotaUpdateDetails = billingInfo.emergencyStopCharging(sessionId, connectionId);

        assertFalse(quotaUpdateDetails.shouldSkipQuotaUpdate());
        assertTrue(quotaUpdateDetails.shouldConsume() ||  quotaUpdateDetails.shouldRefund());
        assertTrue(billingInfo.getCallEndTime() >= billingInfo.getCallStartTime());
        assertTrue(billingInfo.getCallDurationInSeconds() < 2);
        assertTrue(billingInfo.getCallDurationInSeconds() >= 0);
	 
    }
    
    @Test
    public void emergencyStopBillingNoChargesCallTest() throws Exception {
	
	       long minIncrement = 6;
	        long recurringIncrement = 6;
	        BigDecimal forcedPrice = null;
	        BigDecimal configuredPrice = BigDecimal.ZERO;
	        String sessionId = "1234";
	        String connectionId = "5678";
	        
	        BillingInfo billingInfo = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
	        billingInfo.startCharging(sessionId, connectionId, System.currentTimeMillis());
	        assertEquals(BillingInfo.Status.NO_CHARGES, billingInfo.getStatus());
	        
	        assertTrue(billingInfo.shouldHandleEmergencyStopCharging(sessionId,connectionId));
	        
	    	//Calculate the end of call details as would happened in BillingManager.emergencyStopCharging
	        QuotaUpdateDetails quotaUpdateDetails = billingInfo.emergencyStopCharging(sessionId, connectionId);

	        assertTrue(quotaUpdateDetails.shouldSkipQuotaUpdate());
	        assertTrue(billingInfo.getCallEndTime() >= billingInfo.getCallStartTime());
	        
	        assertTrue(billingInfo.getCallDurationInSeconds() < 2);
	        assertTrue(billingInfo.getCallDurationInSeconds() >= 0); 
       
    }
    
    
    @Test
    public void emergencyStopBillingEndedCallTest() throws Exception {
	
	       long minIncrement = 6;
	        long recurringIncrement = 6;
	        BigDecimal forcedPrice = null;
	        BigDecimal configuredPrice = BigDecimal.TEN;
	        String sessionId = "1234";
	        String connectionId = "5678";
	        
	        BillingInfo billingInfo = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
	        billingInfo.startCharging(sessionId, connectionId, System.currentTimeMillis());
	        assertEquals(BillingInfo.Status.STARTED, billingInfo.getStatus());
	        //the "call" is on going
	        Thread.sleep(1000);
	        //the "call" has ended 
	        billingInfo.setEndedStatus(sessionId, connectionId);
	        assertEquals(BillingInfo.Status.ENDED, billingInfo.getStatus());

	        assertFalse(billingInfo.shouldHandleEmergencyStopCharging(sessionId,connectionId));
    }
    
    @Test
    public void emergencyStopBillingErrorCallTest() throws Exception {
	
	       long minIncrement = 6;
	        long recurringIncrement = 6;
	        BigDecimal forcedPrice = null;
	        BigDecimal configuredPrice = BigDecimal.TEN;
	        String sessionId = "1234";
	        String connectionId = "5678";
	        
	        BillingInfo billingInfo = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
	        billingInfo.startCharging(sessionId, connectionId, System.currentTimeMillis());
	        assertEquals(BillingInfo.Status.STARTED, billingInfo.getStatus());
	        //the "call" is on going
	        Thread.sleep(1000);
	        //the "call" has ended 
	        billingInfo.setErrorStatus(sessionId, connectionId);
	        assertEquals(BillingInfo.Status.ERROR, billingInfo.getStatus());

	        assertFalse(billingInfo.shouldHandleEmergencyStopCharging(sessionId,connectionId));
    }




}
