package com.nexmo.voice.core.cache;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.asteriskjava.manager.event.CdrEvent;
import org.junit.Before;
import org.junit.Test;

public class TestLegFlowCache {
    
    private static LegFlowCache legFlowCache;
    
    @Before 
    public void testSetup() {
	legFlowCache = new LegFlowCache();
    }
    
     
    @Test
    public void testStartArrivedFirst() {
	String legId = "leg-1";
	
	LegFlowContext legFlowContext = legFlowCache.startEventArrived(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.START_S, legFlowContext.getLegStatus() );
        assertNull(legFlowContext.getCdrEvent());
    }
    
    @Test
    public void testStartArrivedAfterStartCompleted() {
	String legId = "leg-1";
	
	LegFlowContext legFlowContextS = legFlowCache.startEventArrived(legId);
	LegFlowContext legFlowContextE = legFlowCache.startEventProcessingCompleted(legId);
	
	LegFlowContext legFlowContext = legFlowCache.startEventArrived(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.UNKNOWN, legFlowContext.getLegStatus() );
        assertNull(legFlowContext.getCdrEvent());
    }
    
    @Test
    public void testStartArrivedAfterStop() {
	String cdrEventName = "stop-leg-1";
	CdrEvent cdrEvent = new CdrEvent(cdrEventName);
	String legId = "leg-1";
	
	LegFlowContext legFlowContextStop = legFlowCache.stopEventArrived(legId, cdrEvent);
	
	LegFlowContext legFlowContext = legFlowCache.startEventArrived(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.STOP_P, legFlowContext.getLegStatus() );
        assertNotNull(legFlowContext.getCdrEvent());
        assertEquals(cdrEventName, legFlowContext.getCdrEvent().getSource());
    }
    
    @Test
    public void testStartCompletedArrivedFirst() {
	String legId = "leg-1";
	
	LegFlowContext legFlowContext = legFlowCache.startEventProcessingCompleted(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.UNKNOWN, legFlowContext.getLegStatus() );
        assertNull(legFlowContext.getCdrEvent());
    }
    
    @Test
    public void testStartCompletedArrivedAfterStart() {
	String legId = "leg-1";
	
	LegFlowContext legFlowContextS = legFlowCache.startEventArrived(legId);
	LegFlowContext legFlowContext = legFlowCache.startEventProcessingCompleted(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.START_E, legFlowContext.getLegStatus() );
        assertNull(legFlowContext.getCdrEvent());
    }
    
    @Test
    public void testStartCompletedArrivedAfterStop() {
	String cdrEventName = "stop-leg-1";
	CdrEvent cdrEvent = new CdrEvent(cdrEventName);
	String legId = "leg-1";
	
	LegFlowContext legFlowContextStop = legFlowCache.stopEventArrived(legId, cdrEvent);
	
	LegFlowContext legFlowContext = legFlowCache.startEventProcessingCompleted(legId);
  
        assertEquals(LegFlowContext.LEG_STATUS.STOP_P, legFlowContext.getLegStatus() );
        assertEquals(cdrEventName, legFlowContext.getCdrEvent().getSource());
    }
    
    
    @Test
    public void testStopArrivedFirst() {
	String cdrEventName = "stop-leg-1";
	CdrEvent cdrEvent = new CdrEvent(cdrEventName);
	String legId = "leg-1";
	
	LegFlowContext legFlowContext = legFlowCache.stopEventArrived(legId, cdrEvent);
  
        assertEquals(LegFlowContext.LEG_STATUS.STOP_P, legFlowContext.getLegStatus() );
        assertNotNull(legFlowContext.getCdrEvent());
        assertEquals(cdrEventName, legFlowContext.getCdrEvent().getSource());
    }

    
    @Test
    public void testStopArrivedAfterStartS() {
	String cdrEventName = "stop-leg-1";
	CdrEvent cdrEvent = new CdrEvent(cdrEventName);
	String legId = "leg-1";
	
	LegFlowContext legFlowContextStart = legFlowCache.startEventArrived(legId);
	
	LegFlowContext legFlowContext = legFlowCache.stopEventArrived(legId, cdrEvent);
  
        assertEquals(LegFlowContext.LEG_STATUS.STOP_P, legFlowContext.getLegStatus() );
        assertEquals(cdrEventName, legFlowContext.getCdrEvent().getSource());
    }
    
    @Test
    public void testStopArrivedAfterStartE() {
	String cdrEventName = "stop-leg-1";
	CdrEvent cdrEvent = new CdrEvent(cdrEventName);
	String legId = "leg-1";
	
	LegFlowContext legFlowContextStartS = legFlowCache.startEventArrived(legId);
	LegFlowContext legFlowContextStartE = legFlowCache.startEventProcessingCompleted(legId);
	
	LegFlowContext legFlowContext = legFlowCache.stopEventArrived(legId, cdrEvent);
  
        assertEquals(LegFlowContext.LEG_STATUS.START_E, legFlowContext.getLegStatus() );
        assertNull(legFlowContext.getCdrEvent());
    }
    
    

}
