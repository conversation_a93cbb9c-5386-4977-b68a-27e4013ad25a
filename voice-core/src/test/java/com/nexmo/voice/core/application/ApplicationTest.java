package com.nexmo.voice.core.application;

import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;


public class ApplicationTest {
    private static final String APPLICATION_ONE = "{\"id\":\"f067fcef-792a-4d04-b4ba-f83617f9e4ce\",\"name\":\"smokeapp\",\"api_key\":\"4bcbb82f\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzcHKA7HLY4Ra/3TFRAyS\\n8tP0oWJMA/3f7gfz2/fhL13Iz2CjZaHZGxlZUYXgfFuxXK6kWZyZNIMfuL8z+L1N\\nmBbBS1iDQpwbImOPFC7VfGys63HRiYFHu716KaFI9fUfb8ZQdV8iDmHCPEqVi89K\\nUw77XvDs+g+HoYpMIrBvudlBBSpbzm7mG7K8+uhvLBtSf5+KZ/CJpzrAGUrjDZRy\\nMe/r42uJAohH9DyTgB9VRMOiJ7OI/OQpGOFwsDpq8HqR0tM2cXB0qeO3p/JVl+8V\\nlTCZIjqB+zUeNOUXFvXFAPRN5+gNv/2ybVkIt5lTKUD7Vskiq/mxAmLzvebXbAT9\\npwIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1596188532000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"},\"answer_url\":{\"address\":\"https://answer.url\",\"http_method\":\"GET\"}}},\"rtc\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}},\"messages\":{\"webhooks\":{\"inbound_url\":{\"address\":\"https://answer.url\",\"http_method\":\"POST\"},\"status_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}}},\"created_time\":\"2020-07-31T09:42:12Z\",\"last_update_time\":\"2020-07-31T09:42:12Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/f067fcef-792a-4d04-b4ba-f83617f9e4ce\"}}}";
    private static final String APPLICATION_WITH_REGION = "{\"id\":\"f067fcef-792a-4d04-b4ba-f83617f9e4ce\",\"name\":\"smokeapp\",\"api_key\":\"4bcbb82f\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzcHKA7HLY4Ra/3TFRAyS\\n8tP0oWJMA/3f7gfz2/fhL13Iz2CjZaHZGxlZUYXgfFuxXK6kWZyZNIMfuL8z+L1N\\nmBbBS1iDQpwbImOPFC7VfGys63HRiYFHu716KaFI9fUfb8ZQdV8iDmHCPEqVi89K\\nUw77XvDs+g+HoYpMIrBvudlBBSpbzm7mG7K8+uhvLBtSf5+KZ/CJpzrAGUrjDZRy\\nMe/r42uJAohH9DyTgB9VRMOiJ7OI/OQpGOFwsDpq8HqR0tM2cXB0qeO3p/JVl+8V\\nlTCZIjqB+zUeNOUXFvXFAPRN5+gNv/2ybVkIt5lTKUD7Vskiq/mxAmLzvebXbAT9\\npwIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1596188532000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"},\"answer_url\":{\"address\":\"https://answer.url\",\"http_method\":\"GET\"}},\"region\":\"us-4\"},\"rtc\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}},\"messages\":{\"webhooks\":{\"inbound_url\":{\"address\":\"https://answer.url\",\"http_method\":\"POST\"},\"status_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}}},\"created_time\":\"2020-07-31T09:42:12Z\",\"last_update_time\":\"2020-07-31T09:42:12Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/f067fcef-792a-4d04-b4ba-f83617f9e4ce\"}}}";

    @Test
    public void testCreation() {
        Application app = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", "us-3", null);
        Assert.assertEquals("4bcbb82f", app.getApiKey());
        Assert.assertEquals("us-3", app.getRegion());
        Assert.assertNull(app.getPaymentEnabled());
    }

    @Test
    public void testSameContents() {
        Application app1 = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", null, null);
        Application app2 = ApplicationsServiceResponseParser.fromJSON(new JSONObject(APPLICATION_ONE));
        Assert.assertEquals(app1, app2);

        Application app3 = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", "us-4", null);
        Application app4 =  ApplicationsServiceResponseParser.fromJSON(new JSONObject(APPLICATION_WITH_REGION));
        Assert.assertEquals(app3, app4);
    }

    @Test
    public void testDifferentContents() {
        Application app = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", null, null);
        Application app1 = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", null, Boolean.FALSE);
        Assert.assertNotEquals(app, app1);
        Application app2 = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "john", null, null);
        Assert.assertNotEquals(app, app2);
        Application app3 = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "load_test_09032021153947_1613", "4bcbb82f", null, null);
        Assert.assertNotEquals(app, app3);
        Application app4 = new Application("633703ce-7b8c-4840-a192-cbd6f4bff2ba", "smokeapp", "4bcbb82f", null, null);
        Assert.assertNotEquals(app, app4);
    }

    @Test
    public void testToString() {
        Application app = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", null, null);
        Assert.assertEquals("APP :: f067fcef-792a-4d04-b4ba-f83617f9e4ce :: smokeapp :: API-KEY: 4bcbb82f", app.toString());
    }

    @Test
    public void testHashes() {
        Application app = new Application("f067fcef-792a-4d04-b4ba-f83617f9e4ce", "smokeapp", "4bcbb82f", null, null);
        Assert.assertEquals(0x7DD714DE, app.hashCode());

    }
}
