package com.nexmo.voice.core.stirshaken;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.core.Core;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodes;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import org.mockito.stubbing.Answer;

import static org.powermock.api.mockito.PowerMockito.*;

public abstract class EnforcerServiceTest {

    protected static final String GB_NUMBER = "443434960912";
    protected static final String US_NUMBER = "013434960912";
    protected static final String CA_NUMBER = "017774960912";

    protected static final String GB = "GB";
    protected static final String US = "US";
    protected static final String CA = "CA";

    public static final Core CORE = spy(Core.getInstance());


    public static void init() throws ShortCodeException {
        mockStatic(Core.class);
        when(Core.getInstance()).thenAnswer((Answer<Core>) invocation -> CORE);

        Config config = mock(Config.class);
        doReturn(config).when(CORE).getConfig();

        ShortCodes shortCodes = mock(ShortCodes.class);
        doReturn(shortCodes).when(config).getShortCodes();

        ShortCode gbShortCode = mock(ShortCode.class);
        doReturn(gbShortCode).when(shortCodes).getFirstShortCodeMatchForNumber(GB_NUMBER);
        doReturn(GB).when(gbShortCode).getCountry();

        ShortCode usShortCode = mock(ShortCode.class);

        doReturn(usShortCode).when(shortCodes).getFirstShortCodeMatchForNumber(US_NUMBER);
        doReturn(US).when(usShortCode).getCountry();

        ShortCode caShortCode = mock(ShortCode.class);

        doReturn(caShortCode).when(shortCodes).getFirstShortCodeMatchForNumber(CA_NUMBER);
        doReturn(CA).when(caShortCode).getCountry();

    }
}
