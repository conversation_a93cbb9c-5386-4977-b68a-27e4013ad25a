package com.nexmo.voice.core.emergency;

import com.nexmo.test.util.SimpleConfigReader;
import org.junit.Test;

import java.io.File;
import static org.junit.Assert.*;

public class EmergencyCallingConfigLoaderTest {

    private final String TEST_FILE_ROOT = "test/configs/emergencycalling/";

    @Test
    public void getConfigTest() throws Exception {
        EmergencyCallingConfigLoader loader = new EmergencyCallingConfigLoader("voice.emergency-calling");
        SimpleConfigReader<EmergencyCallingConfigLoader, EmergencyCallingConfig> configReader = new SimpleConfigReader<>(loader);
        configReader.read(new File(getClass().getClassLoader().getResource(TEST_FILE_ROOT + "test-config.xml").getFile()));

        EmergencyCallingConfig config = loader.getConfig();

        assertNotNull(config);
        assertFalse(config.isAllowFromBannedAccount());
        assertTrue(config.isSkipQuota());
        assertTrue(config.isSkipParentAccountLookup());
        assertFalse(config.isRequireCapabilityLocale());
        assertTrue(config.isAllowFromByon());

        assertTrue(config.isEmergencyServiceNumber("911"));
        assertTrue(config.isEmergencyServiceNumber("999"));
        assertTrue(config.isEmergencyServiceNumber("112"));
        assertTrue(config.isEmergencyServiceNumber("988"));

        assertTrue(config.isEmergencyServiceNumberForLocale("US", "911"));
        assertTrue(config.isEmergencyServiceNumberForLocale("US", "988"));
        assertTrue(config.isEmergencyServiceNumberForLocale("CA", "911"));
        assertTrue(config.isEmergencyServiceNumberForLocale("GB", "999"));
        assertTrue(config.isEmergencyServiceNumberForLocale("GB", "112"));

        assertFalse(config.isEmergencyServiceNumberForLocale("US", "999"));
        assertFalse(config.isEmergencyServiceNumberForLocale("US", "112"));
        assertFalse(config.isEmergencyServiceNumberForLocale("CA", "999"));
        assertFalse(config.isEmergencyServiceNumberForLocale("CA", "112"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "911"));
        assertFalse(config.isEmergencyServiceNumberForLocale("CA", "988"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "988"));

        System.out.println(config);

    }


    @Test
    public void getConfigAllDefaults() throws Exception {
        EmergencyCallingConfigLoader loader = new EmergencyCallingConfigLoader("voice.emergency-calling");
        SimpleConfigReader<EmergencyCallingConfigLoader, EmergencyCallingConfig> configReader = new SimpleConfigReader<>(loader);
        configReader.read(new File(getClass().getClassLoader().getResource(TEST_FILE_ROOT + "test-empty-config.xml").getFile()));

        EmergencyCallingConfig config = loader.getConfig();

        assertNotNull(config);
        assertTrue(config.isSkipQuota());
        assertTrue(config.isAllowFromBannedAccount());
        assertTrue(config.isSkipParentAccountLookup());
        assertTrue(config.isRequireCapabilityLocale());
        assertFalse(config.isAllowFromByon());

        // we set no locales in this file
        assertFalse(config.isEmergencyServiceNumber("911"));
        assertFalse(config.isEmergencyServiceNumber("999"));
        assertFalse(config.isEmergencyServiceNumber("112"));
    }

}