package com.nexmo.voice.core.cdr;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class CDRKeysConverterTest {

    @Test
    public void convertOnTheFlyTest() {

        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS"), "status");

        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS_1"), "status1");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS_A"), "statusA");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS_AB"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS_AB_"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS_AB_cd"), "statusAbCd");

        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-1"), "status1");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-A"), "statusA");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-AB"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-AB-"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-AB-cd"), "statusAbCd");

        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS 1"), "status1");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS A"), "statusA");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS AB"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS AB "), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS AB cd"), "statusAbCd");

        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-_ AB"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS-AB _"), "statusAb");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly("STATUS AB-cd_"), "statusAbCd");

        assertEquals(CDRKeysConverter.convertKeyOnTheFly(""), "");
        assertEquals(CDRKeysConverter.convertKeyOnTheFly(null), null);

    }

    @Test
    public void handleUnConfiguredKeyTest() {
        String cdrKey = "NO-SUCH-KEY";
        // Verify that there is no such key
        String jsonKey = CDRKeysConverter.keysDictionary.get(cdrKey);
        assertNull(jsonKey);

        // Convert-on-the-fly which add the new key and json to the in-memory dictionary
        jsonKey = CDRKeysConverter.getJsonKey(cdrKey);
        assertNotNull(jsonKey);

        // verify it is in the dictionary
        String fromDict = CDRKeysConverter.keysDictionary.get(cdrKey);
        assertNotNull(fromDict);
        assertEquals(fromDict, jsonKey);

    }

}
