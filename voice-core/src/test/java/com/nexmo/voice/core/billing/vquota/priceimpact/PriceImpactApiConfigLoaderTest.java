package com.nexmo.voice.core.billing.vquota.priceimpact;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.XmlContent;
import org.junit.Before;
import org.junit.Test;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;

public class PriceImpactApiConfigLoaderTest {
    private PriceImpactApiConfigLoader loader;

    @Before
    public void setUp() {
        loader = new PriceImpactApiConfigLoader("price-impact-api");
    }

    @Test
    public void testValidConfigLoading() throws Exception {
        String host = "https://test.com";
        String basePath = "/vquota/v1/priceImpact";
        int timeout = 5000;

        Map<String, String> configMap = new HashMap<>();
        configMap.put(PriceImpactApiConfig.API_HOST_URL_ATTR, host);
        configMap.put(PriceImpactApiConfig.API_BASE_PATH_ATTR, basePath);
        configMap.put(PriceImpactApiConfig.API_TIMEOUT_ATTR, Integer.toString(timeout));

        XmlContent xmlContent = new PriceImpactApiConfigLoaderTest.MockXmlContent(configMap);
        loader.startNode("price-impact-api", xmlContent);
        loader.endNode("price-impact-api", "");

        PriceImpactApiConfig config = loader.getConfig();

        assertNotNull(config);
        assertEquals(host, config.getHost());
        assertEquals(basePath, config.getBasePath());
        assertEquals(timeout, config.getTimeout());
    }

    @Test(expected = LoaderException.class)
    public void testMissingRequiredAttribute() throws Exception {
        Map<String, String> configMap = new HashMap<>();
        configMap.put(PriceImpactApiConfig.API_HOST_URL_ATTR, "http://test.com");

        XmlContent xmlContent = new PriceImpactApiConfigLoaderTest.MockXmlContent(configMap);
        loader.startNode("price-impact-api", xmlContent);
        loader.endNode("price-impact-api", "");
        loader.getConfig();
    }

    static class MockXmlContent implements XmlContent {

        private final Map<String, String> content;

        public MockXmlContent(Map<String, String> content) {
            this.content = content;
        }

        @Override
        public String getAttribute(String key, boolean required) throws LoaderException {
            String value = this.content.get(key);
            if (value == null && required) {
                throw new LoaderException("Config doesn't contain " + key);
            }
            return value;
        }

        @Override
        public String getAttribute(String key, boolean required, String defaultValue) {
            return this.content.getOrDefault(key, defaultValue);
        }

        @Override
        public File getCurrentFile() {
            return null;
        }
    }
}
