package com.nexmo.voice.core.billing.vquota.currentbalance;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.XmlContent;
import org.junit.Before;
import org.junit.Test;
import java.io.File;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;

public class CurrentBalanceApiConfigLoaderTest {
    private CurrentBalanceApiConfigLoader loader;

    @Before
    public void setUp() {
        loader = new CurrentBalanceApiConfigLoader("current-balance-api");
    }

    @Test
    public void testValidConfigLoading() throws Exception {
        String host = "https://test.com";
        String basePath = "/vquota/v1/currentBalance";
        BigDecimal minBalance = BigDecimal.valueOf(0.1);
        int timeout = 5000;

        Map<String, String> configMap = new HashMap<>();
        configMap.put(CurrentBalanceApiConfig.API_HOST_URL_ATTR, host);
        configMap.put(CurrentBalanceApiConfig.API_BASE_PATH_ATTR, basePath);
        configMap.put(CurrentBalanceApiConfig.API_MIN_BALANCE_VALUE_ATTR, minBalance.toPlainString());
        configMap.put(CurrentBalanceApiConfig.API_TIMEOUT_ATTR, Integer.toString(timeout));

        XmlContent xmlContent = new MockXmlContent(configMap);
        loader.startNode("current-balance-api", xmlContent);
        loader.endNode("current-balance-api", "");

        CurrentBalanceApiConfig config = loader.getConfig();

        assertNotNull(config);
        assertEquals(host, config.getHost());
        assertEquals(basePath, config.getBasePath());
        assertEquals(minBalance, config.getMinBalance());
        assertEquals(timeout, config.getTimeout());
    }

    @Test(expected = LoaderException.class)
    public void testMissingRequiredAttribute() throws Exception {
        Map<String, String> configMap = new HashMap<>();
        configMap.put(CurrentBalanceApiConfig.API_HOST_URL_ATTR, "http://test.com");

        XmlContent xmlContent = new MockXmlContent(configMap);
        loader.startNode("current-balance-api", xmlContent);
        loader.endNode("current-balance-api", "");
        loader.getConfig();
    }

    static class MockXmlContent implements XmlContent {

        private final Map<String, String> content;

        public MockXmlContent(Map<String, String> content) {
            this.content = content;
        }

        @Override
        public String getAttribute(String key, boolean required) throws LoaderException {
            String value = this.content.get(key);
            if (value == null && required) {
                throw new LoaderException("Config doesn't contain " + key);
            }
            return value;
        }

        @Override
        public String getAttribute(String key, boolean required, String defaultValue) {
            return this.content.getOrDefault(key, defaultValue);
        }

        @Override
        public File getCurrentFile() {
            return null;
        }
    }
}
