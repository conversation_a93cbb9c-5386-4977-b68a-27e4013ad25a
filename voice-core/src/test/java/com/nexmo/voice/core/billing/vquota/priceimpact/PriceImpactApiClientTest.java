package com.nexmo.voice.core.billing.vquota.priceimpact;

import static org.junit.Assert.*;
import static org.hamcrest.MatcherAssert.assertThat;
import com.fasterxml.jackson.databind.ObjectMapper;
import static org.hamcrest.Matchers.*;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.NotEnoughBalanceException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaDisabledException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaException;
import com.thepeachbeetle.messaging.hub.core.quota.exceptions.QuotaUnderMaintenanceException;
import com.thepeachbeetle.messaging.hub.core.quota.client.QuotaClient.AccountNotFoundException;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.math.BigDecimal;

public class PriceImpactApiClientTest {

    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testDeserializeSuccessResponse() throws IOException {
        String successJson = "{"
                + "\"status\":\"success\","
                + "\"estimatedPriceImpact\":\"0.004\","
                + "\"message\":\"Price estimated successfully\","
                + "\"currency\":\"EUR\","
                + "\"groupId\":\"DEFAULT\","
                + "\"mediationCdrField\":{"
                + "\"originPrefixGroup\":\"JP Mobile Origin\","
                + "\"originCountryRegion\":\"US\""
                + "}"
                + "}";

        PriceImpactApiSuccessResponse response = objectMapper.readValue(successJson, PriceImpactApiSuccessResponse.class);

        assertNotNull(response);
        assertThat(response.getStatus(), is("success"));
        assertThat(response.getEstimatedPriceImpact(), is(new BigDecimal("0.004")));
        assertThat(response.getMessage(), is("Price estimated successfully"));
        assertThat(response.getCurrency(), is("EUR"));
        assertThat(response.getGroupId(), is("DEFAULT"));
        assertThat(response.getMediationCdrField().getOriginPrefixGroup(), is("JP Mobile Origin"));
        assertThat(response.getMediationCdrField().getOriginCountryRegion(), is("US"));
    }

    @Test
    public void testDeserializeResponseWithAccountBalance() throws IOException {
        String jsonResponse = "{"
                + "\"status\":\"success\","
                + "\"estimatedPriceImpact\":\"0.********\","
                + "\"message\":\"Price estimated successfully\","
                + "\"groupId\":\"testApiKey\","
                + "\"callType\":\"domestic\","
                + "\"numberType\":\"Landline\","
                + "\"country\":\"JP\","
                + "\"networkType\":\"LANDLINE\","
                + "\"mediationCdrField\":{"
                + "\"originCountryRegion\":\"CATCH_ALL\""
                + "},"
                + "\"accountBalance\":{"
                + "\"accountId\":\"test-apiKey-01\","
                + "\"balance\":12.********,"
                + "\"creditLimit\":0.0,"
                + "\"currency\":null,"
                + "\"quotaEnabled\":true,"
                + "\"requiredFreeBalance\":12.79,"
                + "\"requiredFreeBalanceAvailable\":false"
                + "}"
                + "}";

        PriceImpactApiSuccessResponse response = objectMapper.readValue(jsonResponse, PriceImpactApiSuccessResponse.class);

        // Validate the main response fields
        assertNotNull(response);
        assertThat(response.getStatus(), is("success"));
        assertThat(response.getEstimatedPriceImpact(), is(new BigDecimal("0.********")));
        assertThat(response.getMessage(), is("Price estimated successfully"));
        assertThat(response.getGroupId(), is("testApiKey"));
        assertThat(response.getCallType(), is("domestic"));
        assertThat(response.getNumberType(), is("Landline"));
        assertThat(response.getCountry(), is("JP"));
        assertThat(response.getNetworkType(), is("LANDLINE"));

        // Validate MediationCdrField
        assertNotNull(response.getMediationCdrField());
        assertThat(response.getMediationCdrField().getOriginCountryRegion(), is("CATCH_ALL"));

        // Validate AccountBalance
        assertNotNull(response.getAccountBalance());
        assertThat(response.getAccountBalance().getAccountId(), is("test-apiKey-01"));
        assertThat(response.getAccountBalance().getBalance(), is(new BigDecimal("12.********")));
        assertThat(response.getAccountBalance().getCreditLimit(), is(new BigDecimal("0.0")));
        assertThat(response.getAccountBalance().isQuotaEnabled(), is(true));
        assertNull(response.getAccountBalance().getCurrency());
        assertThat(response.getAccountBalance().getRequiredFreeBalance(), is(new BigDecimal("12.79")));
        assertThat(response.getAccountBalance().isRequiredFreeBalanceAvailable(), is(false));
    }

    @Test
    public void testDeserializeResponseWithAccountBalanceAndCurrencyEUR() throws IOException {
        String jsonResponse = "{"
                + "\"status\":\"success\","
                + "\"estimatedPriceImpact\":\"0.********\","
                + "\"message\":\"Price estimated successfully\","
                + "\"groupId\":\"testApiKey\","
                + "\"callType\":\"domestic\","
                + "\"numberType\":\"Landline\","
                + "\"country\":\"JP\","
                + "\"networkType\":\"LANDLINE\","
                + "\"mediationCdrField\":{"
                + "\"originCountryRegion\":\"CATCH_ALL\""
                + "},"
                + "\"accountBalance\":{"
                + "\"accountId\":\"test-apiKey-01\","
                + "\"balance\":12.********,"
                + "\"creditLimit\":0.0,"
                + "\"currency\":\"EUR\","
                + "\"quotaEnabled\":true,"
                + "\"requiredFreeBalance\":12.7,"
                + "\"requiredFreeBalanceAvailable\":true"
                + "}"
                + "}";

        PriceImpactApiSuccessResponse response = objectMapper.readValue(jsonResponse, PriceImpactApiSuccessResponse.class);

        // Validate the main response fields
        assertNotNull(response);
        assertThat(response.getStatus(), is("success"));
        assertThat(response.getEstimatedPriceImpact(), is(new BigDecimal("0.********")));
        assertThat(response.getMessage(), is("Price estimated successfully"));
        assertThat(response.getGroupId(), is("testApiKey"));
        assertThat(response.getCallType(), is("domestic"));
        assertThat(response.getNumberType(), is("Landline"));
        assertThat(response.getCountry(), is("JP"));
        assertThat(response.getNetworkType(), is("LANDLINE"));

        // Validate MediationCdrField
        assertNotNull(response.getMediationCdrField());
        assertThat(response.getMediationCdrField().getOriginCountryRegion(), is("CATCH_ALL"));

        // Validate AccountBalance
        assertNotNull(response.getAccountBalance());
        assertThat(response.getAccountBalance().getAccountId(), is("test-apiKey-01"));
        assertThat(response.getAccountBalance().getBalance(), is(new BigDecimal("12.********")));
        assertThat(response.getAccountBalance().getCreditLimit(), is(new BigDecimal("0.0")));
        assertThat(response.getAccountBalance().getCurrency().getCode(), is("EUR"));
        assertThat(response.getAccountBalance().isQuotaEnabled(), is(true));
        assertThat(response.getAccountBalance().getRequiredFreeBalance(), is(new BigDecimal("12.7")));
        assertThat(response.getAccountBalance().isRequiredFreeBalanceAvailable(), is(true));
    }

    @Test
    public void testDeserializeFailureResponse() throws IOException {
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"priceResponse\":{"
                + "\"code\":\"SystemDown\","
                + "\"description\":\"System is down\""
                + "},"
                + "\"quotaResponse\":{"
                + "\"code\":\"500\","
                + "\"description\":\"Quota is disabled\","
                + "\"exceptionName\":\"QuotaDisabledException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertNotNull(response);
        assertThat(response.getStatus(), is("failure"));
        assertThat(response.getMessage(), is("Unable to estimate price due to system error"));
        assertNotNull(response.getError());
        assertThat(response.getError().getPriceResponse().getCode(), is("SystemDown"));
        assertThat(response.getError().getPriceResponse().getDescription(), is("System is down"));
        assertThat(response.getError().getQuotaResponse().getCode(), is("500"));
        assertThat(response.getError().getQuotaResponse().getDescription(), is("Quota is disabled"));
        assertThat(response.getError().getQuotaResponse().getExceptionName(), is("QuotaDisabledException"));
    }

    @Test
    public void testHandleResponse_withQuotaDisabledException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"500\","
                + "\"description\":\"Quota is disabled\","
                + "\"exceptionName\":\"QuotaDisabledException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertThrows(QuotaDisabledException.class, () -> response.handleResponse(apiKey));
    }

    @Test
    public void testHandleResponse_withSystemDownQuotaException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"priceResponse\":{"
                + "\"code\":\"SystemDown\","
                + "\"description\":\"System is down\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        QuotaException thrownException = assertThrows(QuotaException.class, () -> response.handleResponse(apiKey));
        assertThat(thrownException.getMessage(), containsString("Price Error code: SystemDown"));
    }

    @Test
    public void testHandleResponse_withQuotaUnderMaintenanceException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"503\","
                + "\"description\":\"Quota is under maintenance\","
                + "\"exceptionName\":\"QuotaUnderMaintenanceException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertThrows(QuotaUnderMaintenanceException.class, () -> response.handleResponse(apiKey));
    }

    @Test
    public void testHandleResponse_withAccountNotFoundException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"404\","
                + "\"description\":\"Account not found\","
                + "\"exceptionName\":\"AccountNotFoundException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertThrows(AccountNotFoundException.class, () -> response.handleResponse(apiKey));
    }

    @Test
    public void testHandleResponse_withIllegalOperationOnSubAccountException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"500\","
                + "\"description\":\"Illegal operation on sub-account\","
                + "\"exceptionName\":\"IllegalOperationOnSubAccountException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertThrows(QuotaClient.IllegalOperationOnSubAccountException.class, () -> response.handleResponse(apiKey));
    }

    @Test
    public void testHandleResponse_withNotEnoughBalanceException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"503\","
                + "\"description\":\"Not enough balance\","
                + "\"exceptionName\":\"NotEnoughBalanceException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        assertThrows(NotEnoughBalanceException.class, () -> response.handleResponse(apiKey));
    }

    @Test
    public void testHandleResponse_withGenericQuotaException() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\","
                + "\"error\":{"
                + "\"quotaResponse\":{"
                + "\"code\":\"500\","
                + "\"description\":\"Unknown error\","
                + "\"exceptionName\":\"UnknownException\""
                + "}"
                + "}"
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        QuotaException thrownException = assertThrows(QuotaException.class, () -> response.handleResponse(apiKey));
        assertThat(thrownException.getMessage(), containsString("Quota Error code 500, Description: Unknown error"));
    }

    @Test
    public void testHandleResponse_withMissingErrorDetails() throws IOException {
        String apiKey = "dummyApiKey";
        String failureJson = "{"
                + "\"status\":\"failure\","
                + "\"message\":\"Unable to estimate price due to system error\""
                + "}";

        PriceImpactApiFailureResponse response = objectMapper.readValue(failureJson, PriceImpactApiFailureResponse.class);

        QuotaException thrownException = assertThrows(QuotaException.class, () -> response.handleResponse(apiKey));
        assertThat(thrownException.getMessage(), containsString("Error details are missing in the response"));
    }

    @Test
    public void testSerializePriceImpactApiRequest() throws Exception {
        PriceImpactApiRequest request = new PriceImpactApiRequest.Builder()
                .setGroupId("12345")
                .setProduct("voice_outbound")
                .setProductDetail("sip")
                .setAllowNegativeBalance(true)
                .setRefId("ref123")
                .setConnId("conn567")
                .setDuration(6L)
                .setCmd("consume")
                .setCallType("international")
                .setNumberType("MOBILE")
                .setPrefixTo("44")
                .setPrefixFrom("1")
                .setCountry("GB")
                .setSourceCountry("US")
                .setNetworkType("MOBILE")
                .setToNumber("123")
                .build();

        String jsonString = objectMapper.writeValueAsString(request);
        assertNotNull(jsonString);
        assertThat(jsonString, containsString("\"groupId\":\"12345\""));
        assertThat(jsonString, containsString("\"product\":\"voice_outbound\""));
        assertThat(jsonString, containsString("\"productDetail\":\"sip\""));
        assertThat(jsonString, containsString("\"allowNegativeBalance\":true"));
        assertThat(jsonString, containsString("\"refId\":\"ref123\""));
        assertThat(jsonString, containsString("\"connId\":\"conn567\""));
        assertThat(jsonString, containsString("\"duration\":6"));
        assertThat(jsonString, containsString("\"cmd\":\"consume\""));
        assertThat(jsonString, containsString("\"callType\":\"international\""));
        assertThat(jsonString, containsString("\"numberType\":\"MOBILE\""));
        assertThat(jsonString, containsString("\"prefixTo\":\"44\""));
        assertThat(jsonString, containsString("\"prefixFrom\":\"1\""));
        assertThat(jsonString, containsString("\"country\":\"GB\""));
        assertThat(jsonString, containsString("\"sourceCountry\":\"US\""));
        assertThat(jsonString, containsString("\"networkType\":\"MOBILE\""));
        assertThat(jsonString, containsString("\"toNumber\":\"123\""));
    }
}