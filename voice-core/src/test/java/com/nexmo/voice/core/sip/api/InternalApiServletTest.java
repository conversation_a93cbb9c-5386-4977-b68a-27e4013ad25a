package com.nexmo.voice.core.sip.api;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class InternalApiServletTest {

    @Test
    public void sipDestinationsHappyCasesTest() {
	String[] alternativeAddresses;

	alternativeAddresses = new String[] { "sip:<EMAIL>" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>:5601" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>;timeout=xxxxx" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>;timeout=xxxxx;transport=tls" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>:5601;transport=tls" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>:5601;transport=tls;timeout=xxxxx" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1:5601" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1;timeout=xxxxx" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1;timeout=xxxxx;transport=tls" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1:5601;transport=tls" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@127.0.0.1:5601;transport=tls;timeout=xxxxx" };
	assertEquals("127.0.0.1", InternalApiServlet.extractServer(alternativeAddresses, "req"));

    }

    @Test
    public void sipDestinationsErrorCasesTest() {
	String[] alternativeAddresses;

	alternativeAddresses = new String[] { "sip:1234@" };
	assertEquals("", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234@:5601" };
	assertEquals("", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:1234" };
	assertEquals("", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "" };
	assertEquals("", InternalApiServlet.extractServer(alternativeAddresses, "req"));

    }

    @Test
    public void sipDestinationsListCasesTest() {
	String[] alternativeAddresses;

	alternativeAddresses = new String[] { "sip:<EMAIL>", "sip:<EMAIL>" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "sip:<EMAIL>", "" };
	assertEquals("example.com", InternalApiServlet.extractServer(alternativeAddresses, "req"));

	alternativeAddresses = new String[] { "", "sip:<EMAIL>" };
	assertEquals("", InternalApiServlet.extractServer(alternativeAddresses, "req"));
    }

}
