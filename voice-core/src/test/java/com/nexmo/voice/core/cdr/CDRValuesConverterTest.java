package com.nexmo.voice.core.cdr;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class CDRValuesConverterTest {

    @Test
    public void convertDateValuesTest() {

        assertEquals(CDRValuesConverter.convertValue("START", "09/24/2021 23:59:35 (299)"), "2021-09-24T23:59:35.299+0000");
        assertEquals(CDRValuesConverter.convertValue("END", "10/03/2021 00:13:57 (089)"), "2021-10-03T00:13:57.089+0000");
        assertEquals(CDRValuesConverter.convertValue("CALL_DATE", "10/10/2021 23:59:58 (737)"), "2021-10-10T23:59:58.737+0000");

        //These are not really dates
        assertEquals(CDRValuesConverter.convertValue("PRICE_TIMESTAMP", "07/14/2020 10:11:04 (000)"), "07/14/2020 10:11:04 (000)");
        assertEquals(CDRValuesConverter.convertValue("COST_TIMESTAMP", "08/05/2020 03:50:04 (000)"), "08/05/2020 03:50:04 (000)");
        
        assertEquals(CDRValuesConverter.convertValue("XYZ", " "), " ");
        assertEquals(CDRValuesConverter.convertValue("XYZ", ""), "");
        assertEquals(CDRValuesConverter.convertValue("XYZ", null), null);

        assertEquals(CDRValuesConverter.convertValue("START", " "), " ");
        assertEquals(CDRValuesConverter.convertValue("START", ""), "");
        assertEquals(CDRValuesConverter.convertValue("START", null), null);
    }
    
}
