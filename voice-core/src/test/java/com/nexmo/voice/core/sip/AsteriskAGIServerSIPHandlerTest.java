package com.nexmo.voice.core.sip;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.config.WhitelistedNumbersConfig;
import com.nexmo.voice.core.Core;
import org.apache.log4j.*;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class AsteriskAGIServerSIPHandlerTest {

    private MockedStatic<Core> coreMockedStatic;

    @Before
    public void beforeTest() {
        BasicConfigurator.configure();
        coreMockedStatic = mockStatic(Core.class);
    }

    @After
    public void afterTest() {
        coreMockedStatic.close();
    }

    @Test
    public void testLogCallingNumberValidationSunnyDayNotForcedNotWhitelisted() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "***********";
        String forceSender = null;
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = false;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }

    @Test
    public void testLogCallingNumberValidationNotForcedIsWhitelisted() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "***********";
        String forceSender = null;
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = true;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }

    @Test
    public void testLogCallingNumberValidationIsForcedNotWhitelisted() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "***********";
        String forceSender = "***********";
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = false;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }

    @Test
    public void testLogCallingNumberValidationIsForcedIsWhitelisted() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "***********";
        String forceSender = "************";
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = true;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }

    @Test
    public void testLogCallingNumberValidationNotForcedNotWhitelistedFailsValidation() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "***********";
        String forceSender = null;
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = false;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }

    @Test
    public void testLogCallingNumberValidationNotForcedNotWhitelistedFailsValidationNotNumeric() {
        String sessionId = UUID.randomUUID().toString();
        String direction = "inbound";
        String callerId = "unknown!";
        String forceSender = null;
        String outboundDestination = "************";
        String accountId = UUID.randomUUID().toString();
        boolean isWhitelistedNumber = false;


        Core core = mock(Core.class);
        Config config = mock(Config.class);
        WhitelistedNumbersConfig whitelistedNumbersConfig = mock(WhitelistedNumbersConfig.class);
        Mockito.when(config.getWhitelistedNumbersConfig()).thenReturn(whitelistedNumbersConfig);
        Mockito.when(core.getConfig()).thenReturn(config);
        Mockito.when(whitelistedNumbersConfig.isEnabled()).thenReturn(true);
        Mockito.when(whitelistedNumbersConfig.isWhitelisted(anyString())).thenReturn(isWhitelistedNumber);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        AsteriskAGIServerSIPHandler.logCallingNumberValidation(sessionId, direction, callerId, forceSender, outboundDestination, accountId);
    }
}