package com.nexmo.voice.core.domains;

import org.junit.Test;

import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;

public class DomainsServiceConfigTest {

    @Test
    public void getDomainFromSipUri() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(
                Stream.of("sip-eu.vonage.com", "sip-us.vonage.com", "sip-ap.vonage.com", "sip.vonage.com").collect(Collectors.toSet())
        );

        String sipUri = "mydomain.sip.vonage.com";

        String domain = config.getDomainFromSipUri(sipUri);

        assertNotNull(domain);
        assertEquals("mydomain", domain);

    }

    @Test
    public void extractHostFromSipUri() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "mydomain.sip.vonage.com";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithUserPart() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "<EMAIL>";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithSipScheme() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "sip:mydomain.sip.vonage.com";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithSipsScheme() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "sips:mydomain.sip.vonage.com";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithParameters() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "mydomain.sip.vonage.com;param1=foo;param2=bar";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithHeaders() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "mydomain.sip.vonage.com?x-header1=foo&=x-header2=bar";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithPort() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "mydomain.sip.vonage.com:5061";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void extractHostFromSipUriWithEverything() {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setDomainSuffixes(Collections.singleton("sip.vonage.com"));

        String sipUri = "sips:<EMAIL>:5061;param1=foo;param2=bar?x-header1=foo&=x-header2=bar";

        String host = config.extractHostFromSipUri(sipUri);

        assertNotNull(host);
        assertEquals("mydomain.sip.vonage.com", host);
    }

    @Test
    public void getFqdnWithDomainName() {
        String domainName = "my-domain";

        DomainsServiceConfig config = new DomainsServiceConfig();

        String fqdn = config.domainFqdn(domainName);

        assertNotNull(fqdn);
        assertEquals("my-domain.sip.vonage.com", fqdn);
    }

    @Test
    public void getFqdnWithDomainNameNullValue() {
        String domainName = null;

        DomainsServiceConfig config = new DomainsServiceConfig();

        String fqdn = config.domainFqdn(domainName);

        assertNotNull(fqdn);
        assertEquals("", fqdn);
    }

}