package com.nexmo.voice.core;

import static org.apache.logging.log4j.Level.ALL;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.apache.logging.log4j.core.config.Configurator;
import org.junit.Test;

import java.util.UUID;

public class SipAppUtilsTest {

    @Test
    public void regularPhoneNumberTest() {
	assertEquals("44735219", SipAppUtils.extractPrefix("************", 4));
    }

    @Test
    public void nullPhoneNumberTest() {
	assertNull(SipAppUtils.extractPrefix(null, 4));
    }

    @Test
    public void emptyPhoneNumberTest() {
	assertEquals("", SipAppUtils.extractPrefix("", 4));
    }

    @Test
    public void shortPhoneNumberTest() {
	assertEquals("12", SipAppUtils.extractPrefix("12", 4));
    }
    
    @Test
    public void regularNumberTest() {
	assertEquals("44735219", SipAppUtils.extractPrefix("************", 4));
    }
    
    @Test
    public void regularNumberWithSpecailCharsTest() {
	assertEquals("44735219", SipAppUtils.extractPrefix(" +44-7352 198 767 ", 4));
    }

    @Test
    public void alphaNumericPhoneNumberTest() {
	assertEquals("", SipAppUtils.extractPrefix("<EMAIL>", 4));
    }
    
    @Test
    public void alphaNumericPhoneNumberWithSpecialCharsTest() {
	assertEquals("", SipAppUtils.extractPrefix("+<EMAIL> ", 4));
    }

    @Test
    public void negativeCutTest() {
	assertEquals("", SipAppUtils.extractPrefix("************", -5));
    }

    @Test
    public void zeroCutTest() {
	assertEquals("", SipAppUtils.extractPrefix("************", 0));
    }
    
    @Test
    public void numberLessThanFourTest() {
	assertEquals("447", SipAppUtils.extractPrefix("447", 4));
    }
    
    @Test
    public void numberLessThanFourWithPlusTest() {
	assertEquals("447", SipAppUtils.extractPrefix("+447", 4));
    }
    
    @Test
    public void numberLenFourTest() {
	assertEquals("4475", SipAppUtils.extractPrefix("4475", 4));
    }
    
    @Test
    public void numberLenFourWithPlusTest() {
	assertEquals("4475", SipAppUtils.extractPrefix("+4475", 4));
    }
    
    @Test
    public void shortNumberMoreThanFourTest() {
	assertEquals("8", SipAppUtils.extractPrefix("84751", 4));
    }
    
    @Test
    public void shortNumberMoreThanFourWithPlusTest() {
	assertEquals("8", SipAppUtils.extractPrefix("+84751", 4));
    }
    
    @Test
    public void lettersLenFourTest() {
	assertEquals("", SipAppUtils.extractPrefix("abcd", 4));
    }
    
    @Test
    public void lettersMoreThanFourTest() {
	assertEquals("", SipAppUtils.extractPrefix("abc33333", 4));
    }
    
    @Test
    public void lettersLessThanFourTest() {
	assertEquals("", SipAppUtils.extractPrefix("abc", 4));
    }
    
    @Test
    public void lettersWithPlusAndSpaceTest() {
	assertEquals("", SipAppUtils.extractPrefix("abc  33++ --33b3", 4));
    }
    
    @Test
    public void lettersWithPlusAndSpaceShortTest() {
	assertEquals("", SipAppUtils.extractPrefix("a+b c", 4));
    }
    
    @Test
    public void onlySpecialChars1Test() {
	assertEquals("", SipAppUtils.extractPrefix("+++", 4));
    }
    
    @Test
    public void onlySpecialChars2Test() {
	assertEquals("", SipAppUtils.extractPrefix("+", 4));
    }
    
    @Test
    public void onlySpecialChars3Test() {
	assertEquals("", SipAppUtils.extractPrefix("                ", 4));
    }
    
    @Test
    public void onlySpecialChars4Test() {
	assertEquals("", SipAppUtils.extractPrefix("-+ ", 4));
    }
    
    @Test
    public void numberWithNLTest() {
	assertEquals("777", SipAppUtils.extractPrefix("7777666\n", 4));
    }
    
    @Test
    public void numberWithNL1Test() {
	assertEquals("77776668", SipAppUtils.extractPrefix("7777666\n88888", 4));
    }
    
    @Test
    public void hostNameTest() {
	String host = SipAppUtils.getHostName();
	assertNotNull(host);
	assertFalse(host.trim().isEmpty());
	assertNotEquals("Unknown Host", host);
    }
    
    @Test
    public void gatewayListToSetTest() {
	assertNull(SipAppUtils.convertGatewaysListToSet(null));
	assertEquals(1, SipAppUtils.convertGatewaysListToSet("abcd").size());
	assertEquals(1, SipAppUtils.convertGatewaysListToSet("abcd,").size());
	assertEquals(2, SipAppUtils.convertGatewaysListToSet("abcd,xyz").size());
	assertEquals(3, SipAppUtils.convertGatewaysListToSet("a,b,c,a,b").size());
	assertEquals(1, SipAppUtils.convertGatewaysListToSet(",a").size());
	assertEquals(0, SipAppUtils.convertGatewaysListToSet(",").size());
    }
    
    @Test
    public void getSessionIdTest() {
	assertEquals(SipAppUtils.SESSION_ID_SUFFIX+"1", SipAppUtils.getSessionId("",1));
	assertEquals("abc"+SipAppUtils.SESSION_ID_SUFFIX+"15", SipAppUtils.getSessionId("abc",15));
    }
    
    @Test
    public void getNexmoUUIDTest() {
	assertNull(SipAppUtils.getNexmoUUID(null));
	assertEquals("", SipAppUtils.getNexmoUUID(""));
	assertEquals("aaa", SipAppUtils.getNexmoUUID("aaa"));
	assertEquals("abc", SipAppUtils.getNexmoUUID(SipAppUtils.getSessionId("abc",2)));
	assertEquals("0-1-a-b-", SipAppUtils.getNexmoUUID(SipAppUtils.getSessionId("0-1-a-b-",33)));
    }
    
    @Test
    public void obfuscateTest() {
	assertNull(SipAppUtils.obfuscate(null,1,1));
	assertEquals("", SipAppUtils.obfuscate("",1,1));
	assertEquals("a***a", SipAppUtils.obfuscate("aaa",1,1));
	assertEquals("a***a", SipAppUtils.obfuscate("aaaaaaaaaaaaaaaaaaaaaaa",1,1));
	assertEquals("ab***ba", SipAppUtils.obfuscate("abcba",2,2));
	assertEquals("xyz", SipAppUtils.obfuscate("xyz",2,2));
	assertEquals("***yz", SipAppUtils.obfuscate("xyz",0,2));
	assertEquals("xy***", SipAppUtils.obfuscate("xyz",2,0));
	assertEquals("***", SipAppUtils.obfuscate("ddddddd",0,0));
	assertEquals("***", SipAppUtils.obfuscate("12345",-1,-2));
	assertEquals("12345***", SipAppUtils.obfuscate("12345",5,0));
	assertEquals("1234", SipAppUtils.obfuscate("1234",5,0));
	assertEquals("abc***op", SipAppUtils.obfuscate("abcdefghijklmnop",3,2));
    }
    
    @Test
    public void generateQuotaReferenceTest() {
	assertEquals("aaa-out", SipAppUtils.generateQuotaReference("aaa", false));
	assertEquals("aaa-in", SipAppUtils.generateQuotaReference("aaa", true));
	assertEquals("abc-out", SipAppUtils.generateQuotaReference("abc-SFAN-1", false));
	assertEquals("abc-in", SipAppUtils.generateQuotaReference("abc-SFAN-1", true));
	assertEquals("v-out", SipAppUtils.generateQuotaReference("v-SFAN-1-XYZ", false));
	assertEquals("v-in", SipAppUtils.generateQuotaReference("v-SFAN-1-XYZ", true));
	
	assertNull(SipAppUtils.generateQuotaReference(null, false));
	assertNull(SipAppUtils.generateQuotaReference(null, true));
	assertEquals("", SipAppUtils.generateQuotaReference("", false));
	assertEquals("", SipAppUtils.generateQuotaReference("", true));
    }

    @Test
    public void findTerminationTypeTest() {
        // TODO: Test requestedAppId parameter
        assertNull(SipAppUtils.findTerminationType(null,null,true));
        assertEquals("pstn", SipAppUtils.findTerminationType("01234567890",null,false));
        assertEquals("pstn", SipAppUtils.findTerminationType("+13335550000",null,false));
        assertEquals("pstn", SipAppUtils.findTerminationType(null,null,false)); // FIXME
        assertEquals("pstn", SipAppUtils.findTerminationType("",null,false)); // FIXME
        assertEquals("sip", SipAppUtils.findTerminationType("sip:<EMAIL>",null,false));
        assertEquals("sip", SipAppUtils.findTerminationType("sips:<EMAIL>:5061",null,false));
        assertEquals("websocket", SipAppUtils.findTerminationType("ws://dummycarrier1.example.com:5221/echo",null,false));
        assertEquals("websocket", SipAppUtils.findTerminationType("wss%3A%2F%2F12345abcd123.ngrok.io%2Fnexmo%2Fsocket%2F13335550000",null,false));
        assertEquals("pstn", SipAppUtils.findTerminationType("badger",null,false)); // FIXME
    }

    @Test
    public void testPhoneNumberIsoCountryCode() {
        assertEquals("US", SipAppUtils.phoneNumberIsoCountryCode("17325282600"));
        assertEquals("US", SipAppUtils.phoneNumberIsoCountryCode("+17325282600"));
        assertEquals("GB", SipAppUtils.phoneNumberIsoCountryCode("************"));
        assertEquals("GB", SipAppUtils.phoneNumberIsoCountryCode("+************"));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode("1234"));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode("abcd1234"));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode(null));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode(UUID.randomUUID().toString()));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode("911"));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode("999"));
        assertNull(SipAppUtils.phoneNumberIsoCountryCode("112"));
    }


}
