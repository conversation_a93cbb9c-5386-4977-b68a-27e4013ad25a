package com.nexmo.voice.core;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.Arrays;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.pdd.PDDCalculator;

@RunWith(Parameterized.class)
public class PDDCalculatorTest {

    private static final long MARGIN_ERROR = 10l; //MARGIN ERROR CALCULATION PDD 10MS

    protected static final String ID_LEG1 = "ID1";
    protected static final String ID_LEG2 = "ID2";

    private final long cacheExpiration;
    private final long expectedPdd;
    private PDDCalculator calculator;

    @Parameters(name = "{index}: {0}")
    public static Iterable<Object[]> data() {

        return Arrays.asList(new Object[][] {
            { new Long(1000),  new <PERSON>(0) },
            { new Long(60000), new Long(5000) }
        });
    }

    public PDDCalculatorTest(long cacheExpiration, long pdd) {
        this.cacheExpiration = cacheExpiration;
        this.expectedPdd = pdd;
    }

    @Before
    public void setUp() throws Exception {
        this.calculator = new PDDCalculator(this.cacheExpiration);
    }

    @Test
    public void normalTest() {
        long pdd = 0L;
        try {
            this.calculator.storeStartTime(ID_LEG1, System.currentTimeMillis());
            Thread.sleep(this.expectedPdd);
            this.calculator.storeEndTime(ID_LEG2, System.currentTimeMillis());
            pdd = this.calculator.calculate(ID_LEG1, ID_LEG2);
        } catch (PDDCalculationException e1) {
            fail("Pdd calculation failed with exception " + e1.getMessage());
        } catch (InterruptedException e) {
            fail("Sleep interrupted with exception " + e.getMessage());
        }

        assertTrue(MARGIN_ERROR > ((pdd - this.expectedPdd)*(-1)));
    }

    @Test
    public void endTimeAlreadySet() {

        long pdd = 0L;
        try {
            this.calculator.storeStartTime(ID_LEG1, System.currentTimeMillis());
            Thread.sleep(this.expectedPdd);
            this.calculator.storeEndTime(ID_LEG2, System.currentTimeMillis());
            Thread.sleep(this.expectedPdd);
            this.calculator.storeEndTime(ID_LEG2, System.currentTimeMillis());
            pdd = this.calculator.calculate(ID_LEG1, ID_LEG2);
        } catch (InterruptedException e) {
            fail("Sleep interrupted with exception " + e.getMessage());
        } catch (PDDCalculationException e) {
            fail("Pdd calculation failed with exception " + e.getMessage());
        }

        assertTrue(MARGIN_ERROR > ((pdd - this.expectedPdd)*(-1)));
    }
}
