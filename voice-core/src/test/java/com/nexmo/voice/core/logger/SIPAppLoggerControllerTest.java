package com.nexmo.voice.core.logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.types.CDRType;

public class SIPAppLoggerControllerTest {

    @Test
    public void correctBothLoggersTest() throws Exception {
        // for the cdrTYpe=BOTH, the "backupCDRs" flag is ignored.
        CdrsConfig cdrsConfig = new CdrsConfig(CDRType.BOTH, true);
        SIPAppLoggerController bothLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(2, bothLogger.getSIPAppLoggers().size());
        boolean kvFound = false;
        boolean jsonFound = false;
        for (SIPAppLogger logger : bothLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) {
                kvFound = true;
                assertEquals("sip-b",logger.getlogPrefix()); 
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
                assertEquals("sip-json-b",logger.getlogPrefix());
            }
        }
        assertTrue(kvFound);
        assertTrue(jsonFound);

        cdrsConfig = new CdrsConfig(CDRType.BOTH, false);
        bothLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(2, bothLogger.getSIPAppLoggers().size());
        kvFound = false;
        jsonFound = false;
        for (SIPAppLogger logger : bothLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) {
                kvFound = true;
                assertEquals("sip-b",logger.getlogPrefix()); 
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
                assertEquals("sip-json-b",logger.getlogPrefix());
            }
        }
        assertTrue(kvFound);
        assertTrue(jsonFound);
    }

    @Test
    public void correctKVLoggersTest() throws Exception {
        // for the cdrTYpe=KEY_VALUE, the "backupCDRs" flag is ignored.
        CdrsConfig cdrsConfig = new CdrsConfig(CDRType.KEY_VALUE, true);
        SIPAppLoggerController kvLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(1, kvLogger.getSIPAppLoggers().size());
        boolean kvFound = false;
        boolean jsonFound = false;
        for (SIPAppLogger logger : kvLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) {
                kvFound = true;
                assertEquals("sip-b",logger.getlogPrefix());
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
            }
        }
        assertTrue(kvFound);
        assertFalse(jsonFound);

        cdrsConfig = new CdrsConfig(CDRType.KEY_VALUE, false);
        kvLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(1, kvLogger.getSIPAppLoggers().size());
        kvFound = false;
        jsonFound = false;
        for (SIPAppLogger logger : kvLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) {
                kvFound = true;
                assertEquals("sip-b",logger.getlogPrefix());
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
            }
        }
        assertTrue(kvFound);
        assertFalse(jsonFound);
    }

    @Test
    public void correctJsonLoggersTest() throws Exception {
        // for the cdrTYpe=JSON, the "backupCDRs" flag is NOT ignored.
        CdrsConfig cdrsConfig = new CdrsConfig(CDRType.JSON, true);
        SIPAppLoggerController jsonLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(2, jsonLogger.getSIPAppLoggers().size());
        boolean kvFound = false;
        boolean jsonFound = false;
        for (SIPAppLogger logger : jsonLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) {
                kvFound = true;
                assertEquals("sip-backup-b",logger.getlogPrefix());
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
                assertEquals("sip-json-b",logger.getlogPrefix());
            }
        }
        assertTrue(kvFound);
        assertTrue(jsonFound);

        cdrsConfig = new CdrsConfig(CDRType.JSON, false);
        jsonLogger = new SIPAppLoggerController("./", "sip-b", cdrsConfig);
        assertEquals(1, jsonLogger.getSIPAppLoggers().size());
        kvFound = false;
        jsonFound = false;
        for (SIPAppLogger logger : jsonLogger.getSIPAppLoggers()) {
            if (CDRType.KEY_VALUE.equals(logger.getCDRType())) { 
                kvFound = true;
            } else if (CDRType.JSON.equals(logger.getCDRType())) {
                jsonFound = true;
                assertEquals("sip-json-b",logger.getlogPrefix());
            }
        }
        assertFalse(kvFound);
        assertTrue(jsonFound);
    }

}
