package com.nexmo.voice.core.logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.io.StringReader;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import javax.json.Json;
import javax.json.JsonObject;
import javax.json.JsonReader;

import com.nexmo.voice.core.types.*;
import org.asteriskjava.manager.event.CdrEvent;
import org.junit.BeforeClass;
import org.junit.Test;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.billing.QuotaUpdateTask.VoiceApplicationType;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.thepeachbeetle.common.callback.types.CallbackMethod;

public class CallLoggerControllerTest {

    private static CallLoggerController callLoggerController;
    private static SIPAsteriskContext applicationContext;
    private VoiceContext ctx;

    private static final String FROM = "\"FROM=";
    private static final String TO = "\"TO=";
    private static final String PREFIX_FROM = "\"PREFIX-FROM=";
    private static final String PREFIX_TO = "\"PREFIX-TO=";
    private static final String EOF_PARAM = "\",";

    private static final String channelUniqueId = "1234";

    @BeforeClass
    public static void setUp() throws Exception {
        CdrsConfig cdrsConfig = new CdrsConfig(CDRType.BOTH, true);
        callLoggerController = new CallLoggerController("./", "sip-tmptest", cdrsConfig);
        applicationContext = new SIPAsteriskContext("1", "2", null, null, false, 1);
    }

    @Test
    public void regularPhoneNumberTest() {
        String from = "447352198767";
        String to = "123456789";
        int truncationLength = Core.PHONE_NUMBER_TRUNCATION_LENGTH;

        String prefixedFrom = "44735219";
        String prefixedTo = "12345";

        verifyCDRContent(from, to, truncationLength, prefixedFrom, prefixedTo);
    }

    @Test
    public void UnknownPhoneNumberTest() {
        String from = "Unknown";
        String to = "Unknown";
        int truncationLength = Core.PHONE_NUMBER_TRUNCATION_LENGTH;

        String prefixedFrom = "";
        String prefixedTo = "";

        verifyCDRContent(from, to, truncationLength, prefixedFrom, prefixedTo);
    }

    @Test
    public void shortPhoneNumberTest() {
        String from = "447";
        String to = "1234";
        int truncationLength = Core.PHONE_NUMBER_TRUNCATION_LENGTH;

        String prefixedFrom = "447";
        String prefixedTo = "1234";

        verifyCDRContent(from, to, truncationLength, prefixedFrom, prefixedTo);
    }

    @Test
    public void alphaNumericPhoneNumberTest() {
        String from = "<EMAIL>";
        String to = "1234BBB";
        int truncationLength = Core.PHONE_NUMBER_TRUNCATION_LENGTH;

        String prefixedFrom = "";
        String prefixedTo = "";

        verifyCDRContent(from, to, truncationLength, prefixedFrom, prefixedTo);
    }

    @Test
    public void emergencyStopTest() {
        String from = "<EMAIL>";
        String to = "1234BBB";
        int truncationLength = Core.PHONE_NUMBER_TRUNCATION_LENGTH;

        String prefixedFrom = "";
        String prefixedTo = "";

        verifyCDRContentOfEmergencyStop(from, to, truncationLength, prefixedFrom, prefixedTo);
    }

    @Test
    public void retryGatewayTest() throws Exception {
        String from = "447352198767";
        String to = "123456789";

        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        CdrEventUserData eventUserData = CdrEventUserData.of(AsteriskVersion.V1_8,  event, channelUniqueId);

        verifyCDRContentGatewayFailover(from, to, eventUserData);

    }

    @Test
    public void retryGatewayTestAsterisk16() throws Exception {
        String from = "447352198767";
        String to = "123456789";

        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        CdrEventUserData eventUserData = CdrEventUserData.of(AsteriskVersion.V16,  event, channelUniqueId);

        verifyCDRContentGatewayFailover(from, to, eventUserData);

    }

    @Test
    public void retryGatewayTestAsterisk20() throws Exception {
        String from = "447352198767";
        String to = "123456789";

        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        CdrEventUserData eventUserData = CdrEventUserData.of(AsteriskVersion.V20,  event, channelUniqueId);

        verifyCDRContentGatewayFailover(from, to, eventUserData);

    }

    private void verifyCDRContent(String from, String to, int truncationLength, String prefixedFrom,
            String prefixedTo) {
        String expectedFrom = FROM + from + EOF_PARAM;
        String expectedTo = TO + to + EOF_PARAM;
        String expectedPrefixedFrom = PREFIX_FROM + prefixedFrom + EOF_PARAM;
        String expectedPrefixedTo = PREFIX_TO + prefixedTo + EOF_PARAM;

        ctx = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(from).withTo(to).withAccountId("account").withGateway("inboundGw")
                .withNetwork("inboundNetwork").withCountryCode("inboundCountryCode")
                .withVoiceDirection(VoiceDirection.OUTBOUND).withApplicationContext(applicationContext)
                .withProductClass("productClass").withCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
                .withCallbackUrl("callbackAddress").withPricePerMinute(BigDecimal.ONE).withCostPerMinute(BigDecimal.ONE)
                .withRegion("us-3")
                .build();

        CDRData cdrData = callLoggerController.buildCDRData(ctx, "", SIPCode.OK, "", "", truncationLength, null, false);

        String kvCDR = SIPAppLogger.buildKeyValueCDR(cdrData, CallLoggerController.CallCDROrder);
        String jsonCDR = SIPAppLogger.buildJsonCDR(cdrData, CallLoggerController.CallCDROrder);

        assertTrue(kvCDR.contains(expectedFrom) && kvCDR.contains(expectedTo) && kvCDR.contains(expectedPrefixedFrom)
                && kvCDR.contains(expectedPrefixedTo) && kvCDR.contains("inboundGw") && !kvCDR.contains("INTERNAL=1")
                && kvCDR.contains(CDRData.CDR_CLASS_NAME)
                && kvCDR.contains("us-3")
                && kvCDR.startsWith(String.valueOf(cdrData.getCdrCreationTimestamp())));

        JsonReader jsonReader = Json.createReader(new StringReader(jsonCDR));
        JsonObject jsonCDRObj = jsonReader.readObject();
        assertTrue(Objects.nonNull(jsonCDRObj.getString("from")) && Objects.nonNull(jsonCDRObj.getString("to"))
                && Objects.nonNull(jsonCDRObj.getString("prefixFrom"))
                && Objects.nonNull(jsonCDRObj.getString("prefixTo")) && Objects.nonNull(jsonCDRObj.getString("gw"))
                && !jsonCDRObj.containsKey("internal") && Objects.nonNull(jsonCDRObj.getString("@timestamp"))
                && jsonCDRObj.getString("@timestamp").equals(String.valueOf(cdrData.getCdrCreationTimestamp()))
                && Objects.nonNull(jsonCDRObj.getString("@date")) && jsonCDRObj.getString("@date").contains("+")
                && !jsonCDRObj.getString("@date").contains(")")
                && jsonCDRObj.getString("region").equals("us-3")
                && CDRData.CDR_CLASS_NAME.equals(jsonCDRObj.getString("@class")));

    }

    private void verifyCDRContentOfEmergencyStop(String from, String to, int truncationLength, String prefixedFrom,
            String prefixedTo) {

        ctx = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(from).withTo(to).withAccountId("account").withGateway("inboundGw")
                .withNetwork("inboundNetwork").withCountryCode("inboundCountryCode")
                .withVoiceDirection(VoiceDirection.OUTBOUND).withApplicationContext(applicationContext)
                .withProductClass("productClass").withCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
                .withCallbackUrl("callbackAddress").withPricePerMinute(BigDecimal.ONE).withCostPerMinute(BigDecimal.ONE)
                .build();

        CDRData cdrData = callLoggerController.buildCDRData(ctx, "", SIPCode.OK, "", "", truncationLength, null, true);

        String kvCDR = SIPAppLogger.buildKeyValueCDR(cdrData, CallLoggerController.CallCDROrder);
        String jsonCDR = SIPAppLogger.buildJsonCDR(cdrData, CallLoggerController.CallCDROrder);

        assertTrue(kvCDR.contains("INTERNAL=1"));

        JsonReader jsonReader = Json.createReader(new StringReader(jsonCDR));
        JsonObject jsonCDRObj = jsonReader.readObject();
        assertTrue(Objects.nonNull(jsonCDRObj.getString("internal")));
        assertEquals("1", jsonCDRObj.getString("internal"));
    }

    private void verifyCDRContentGatewayFailover(String from, String to, CdrEventUserData eventUserData) {
        String expectedFrom = FROM + from + EOF_PARAM;
        String expectedTo = TO + to + EOF_PARAM;

        ctx = new VoiceContext.Builder().withVoiceProduct(VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
                .withFrom(from).withTo(to).withAccountId("account").withGateway("inboundGw")
                .withNetwork("inboundNetwork").withCountryCode("inboundCountryCode")
                .withVoiceDirection(VoiceDirection.OUTBOUND).withApplicationContext(applicationContext)
                .withProductClass("productClass").withCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
                .withCallbackUrl("callbackAddress").withPricePerMinute(BigDecimal.ONE).withCostPerMinute(BigDecimal.ONE)
                .build();

        CDRData cdrData = callLoggerController.buildCDRData(ctx, "", SIPCode.OK, "", "",
                Core.PHONE_NUMBER_TRUNCATION_LENGTH, eventUserData, false);

        String kvCDR = SIPAppLogger.buildKeyValueCDR(cdrData, CallLoggerController.CallCDROrder);
        String jsonCDR = SIPAppLogger.buildJsonCDR(cdrData, CallLoggerController.CallCDROrder);

        assertTrue(kvCDR.contains(expectedFrom) && kvCDR.contains(expectedTo) && !kvCDR.contains("inboundGw")
                && kvCDR.contains(eventUserData.getCurrentGateway()) && !kvCDR.contains("us-3"));

        JsonReader jsonReader = Json.createReader(new StringReader(jsonCDR));
        JsonObject jsonCDRObj = jsonReader.readObject();
        assertTrue(Objects.nonNull(jsonCDRObj.getString("from")) && Objects.nonNull(jsonCDRObj.getString("to"))
                && Objects.nonNull(jsonCDRObj.getString("gw")) && Objects.nonNull(jsonCDRObj.getString("gws"))
                && Objects.isNull(jsonCDRObj.get("region")));
        assertEquals("ibasis", jsonCDRObj.getString("gw"));
        assertEquals("bogus,nothing,ibasis", jsonCDRObj.getString("gws"));
    }

    @Test
    public void callInternalFlagsTest() {
        Set<CallInternalFlag> internalFlags = null;
        assertEquals(SIPAppLogger.concatInternalFlags(internalFlags), "");

        internalFlags = new HashSet<CallInternalFlag>();
        assertEquals(SIPAppLogger.concatInternalFlags(internalFlags), "");

        internalFlags.add(CallInternalFlag.BLOCKED_ON_BANNED_ACCOUNT);
        assertEquals("15", SIPAppLogger.concatInternalFlags(internalFlags));

        internalFlags.add(CallInternalFlag.QUOTA_ISSUES_ON_CALL_FINAL_BILLING);
        String values = SIPAppLogger.concatInternalFlags(internalFlags);
        assertTrue(values.contains("15") && values.contains("33"));
    }

}
