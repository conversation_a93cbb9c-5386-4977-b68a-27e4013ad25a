package com.nexmo.voice.core.sip.event;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.nexmo.voice.core.sip.event.cdr.LegacyCdrEventUserData;
import com.nexmo.voice.core.billing.QuotaUpdateTask;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.types.VoiceDirection;
import com.thepeachbeetle.common.callback.types.CallbackMethod;

import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;
import org.junit.Test;

import com.nexmo.voice.core.types.SIPCode;

import java.math.BigDecimal;

/**
 * This test class goal is to make sure the expected result are returned from
 * the internal utility methods: 
 * 
 * isANsweredCall(SIPCode sipCode, CdrEventUserData eventUserData, String sessionId)
 * isOnGoingGwsFailoverCdrEvent(SIPCode sipCode, CdrEventUserData eventUserData)
 * 
 * As it is internal in CdrEventHandler, it is based on the existence and
 * validity of sipCode and eventUserData, hence, the cases where these values
 * are null or invalid are not tested here.
 * 
 * 
 * //Call is answered when it ended in one of the scenarios: 
 * SIPCode.OK and the dialStatus indicate that a call took place. 
 * SIPCode.PAYMENT_REQUIRED && HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED - i.e. the call was stopped in the middle because the customer run out of funds.
 * SIPCode.BANNED_CALL_ENDED  && HangupCause.AST_CAUSE_WRONG_CALL_STATE
 * SIPCode.VERSION_NOT_SUPPORTED && HangupCause.AST_CAUSE_INTERWORKING 
 * 
 * // On going attempts cdr events are those which: 
 * the call has not ended successfully on this attempt, and the call has not ended due to
 * run-out-of-credit, and the call has not answered, and (Added for the
 * SIP-213) the current attempt is not the last, and the caller has not
 * cancelled the call, and the callee has not declined the call
 * 
 * 
 * 
 * <AUTHOR>
 *
 */
public class CdrEventCallStatusTest {

    private static final String channelUniqueId = "1234";
    private static SIPAsteriskContext applicationContext = new SIPAsteriskContext("1", "2", null, null, false, 1);
    private static VoiceContext vctx = new VoiceContext.Builder().withVoiceProduct(QuotaUpdateTask.VoiceApplicationType.SIP_ASTERISK.getVoiceProduct())
            .withFrom("Unknown").withTo("************").withAccountId("account").withGateway("inboundGw")
            .withNetwork("inboundNetwork").withCountryCode("inboundCountryCode")
            .withVoiceDirection(VoiceDirection.OUTBOUND).withApplicationContext(applicationContext)
            .withProductClass("productClass").withCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
            .withCallbackUrl("callbackAddress").withPricePerMinute(BigDecimal.ONE).withCostPerMinute(BigDecimal.ONE)
            .withRegion("us-3").withInternalFlags(null)
            .build();

    @Test
    // HANGUPCAUSE=AST_CAUSE_NORMAL=16
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=2#3
    public void hangupCause16DialStatusAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 2;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NORMAL=16
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=3#3
    public void hangupCause16DialStatusAnswerAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 2;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_OUTGOING_CALL_BARRED=52 (out of money during the call)
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=2#3
    public void hangupCause52DialStatusAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=52;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 2;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_OUTGOING_CALL_BARRED=52 (out of money during the call)
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=3#3
    public void hangupCause52DialStatusAnswerAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=52;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 2;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_OUTGOING_CALL_BARRED, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // This test relates to SIP-246 / SIP-251 / SIP-261
    // HANGUPCAUSE=AST_CAUSE_NORMAL=16
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=2#3
    public void hangupCause16DialStatusNoAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // This test relates to SIP-246 / SIP-251 / SIP-261
    // HANGUPCAUSE=AST_CAUSE_NORMAL=16
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=3#3
    public void hangupCause16DialStatusNoAnswerAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOTDEFINED=0
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=2#3
    public void hangupCause0DialStatusNoAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOTDEFINED, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOTDEFINED=0
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=3#3
    public void hangupCause0DialStatusNoAnswerAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOTDEFINED, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOANSWER=19
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=2#3
    public void hangupCause19DialStatusNoAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=19;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertTrue(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOANSWER=19
    // DIALSTATUS=NOANSWER
    // GW ATTEMPT=3#3
    public void hangupCause19DialStatusNoAnswerAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=19;DIALSTATUS=NOANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("NO_ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_CALL_REJECTED=21
    // DIALSTATUS=NO_MONEY
    // GW ATTEMPT=1#3
    public void hangupCause21DialStatusNoMoneyAttempt1_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=21;DIALSTATUS=NO_MONEY;LEG2ID=;GWS=something,idt,ibasis;GW=something;ATTEMPT=1#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_CALL_REJECTED, eventUserData.getHangupCause());
        assertEquals("NO_MONEY", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOTDEFINED=0
    // DIALSTATUS=CANCEL
    // GW ATTEMPT=2#3
    public void hangupCause0DialStatusCancelAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=CANCEL;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOTDEFINED, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOTDEFINED=0
    // DIALSTATUS=CANCEL
    // GW ATTEMPT=3#3
    public void hangupCause0DialStatusCancelAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=CANCEL;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOTDEFINED, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOANSWER=19
    // DIALSTATUS=CONGESTION
    // GW ATTEMPT=2#3
    public void hangupCause19DialStatusCongestionAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=19;DIALSTATUS=CONGESTION;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CONGESTION", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NOANSWER=19
    // DIALSTATUS=CONGESTION
    // GW ATTEMPT=3#3
    public void hangupCause19DialStatusCongestionAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=19;DIALSTATUS=CONGESTION;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CONGESTION", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_USER_BUSY=17
    // DIALSTATUS=BUSY
    // GW ATTEMPT=2#3
    public void hangupCause17DialStatusBusyAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=17;DIALSTATUS=BUSY;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_BUSY, eventUserData.getHangupCause());
        assertEquals("BUSY", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_USER_BUSY=17
    // DIALSTATUS=BUSY
    // GW ATTEMPT=3#3
    public void hangupCause17DialStatusBusyAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=17;DIALSTATUS=BUSY;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_BUSY, eventUserData.getHangupCause());
        assertEquals("BUSY", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_CALL_REJECTED=21
    // DIALSTATUS=BUSY
    // GW ATTEMPT=1#2
    public void hangupCause21DialStatusBusyAttempt1_2Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=21;DIALSTATUS=BUSY;LEG2ID=;GWS=bics,ibasis;GW=bics;ATTEMPT=1#2";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_CALL_REJECTED, eventUserData.getHangupCause());
        assertEquals("BUSY", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_USER_BUSY=58
    // DIALSTATUS=CHANUNAVAIL
    // GW ATTEMPT=2#3
    public void hangupCause58DialStatusChanunavailAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=58;DIALSTATUS=CHANUNAVAIL;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_BEARERCAPABILITY_NOTAVAIL, eventUserData.getHangupCause());
        assertEquals("CHANUNAVAIL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertEquals(sipCode, SIPCode.NOT_ACCEPTABLE);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertTrue(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_USER_BUSY=58
    // DIALSTATUS=CHANUNAVAIL
    // GW ATTEMPT=3#3
    public void hangupCause58DialStatusChanunavailAttempt3_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=58;DIALSTATUS=CHANUNAVAIL;LEG2ID=;GWS=bogus,idt,ibasis;GW=ibasis;ATTEMPT=3#3";
        event.setUserField(userField);
        Integer billableSeconds = 0;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_BEARERCAPABILITY_NOTAVAIL, eventUserData.getHangupCause());
        assertEquals("CHANUNAVAIL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertEquals(sipCode, SIPCode.NOT_ACCEPTABLE);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_NETWORK_OUT_OF_ORDER=38
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=2#3
    public void hangupCause38DialStatusAnswerAttempt2_3Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=38;DIALSTATUS=ANSWER;LEG2ID=;GWS=bogus,idt,ibasis;GW=idt;ATTEMPT=2#3";
        event.setUserField(userField);
        Integer billableSeconds = 10;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_FAILURE, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
        assertFalse(CdrEventHandler.isOnGoingSipDestinationFailoverCdrEvent(sipCode, eventUserData));
    }

    @Test
    // HANGUPCAUSE=AST_CAUSE_INTERWORKING=127
    // DIALSTATUS=ANSWER
    // GW ATTEMPT=1#1
    public void hangupCause127DialStatusAnswerAttempt1_1Test() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=127;DIALSTATUS=ANSWER;LEG2ID=;GWS=ibasis;GW=ibasis;ATTEMPT=1#1";
        event.setUserField(userField);
        Integer billableSeconds = 10;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_INTERWORKING, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertTrue(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));
        assertFalse(CdrEventHandler.isOnGoingSipDestinationFailoverCdrEvent(sipCode, eventUserData));
    }


    @Test
    // No userField at all - with other CANCEL indicators
    // SIPApp fix the CdrEventUserData
    public void noUserFieldWithCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        event.setUserField(null);
        event.setDuration(0);
        event.setLastApplication("AGI");
        event.setDisposition("NO ANSWER");
        Integer billableSeconds = null;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));

    }

    @Test
    // Partial userField - with other CANCEL indicators
    // SIPApp fix the CdrEventUserData
    public void partialUserFieldWithCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=;LEG2ID=;GWS=bogus,nothing,ibasis;GW=bogus;ATTEMPT=3#3";
        event.setUserField(userField);
        event.setDuration(0);
        event.setLastApplication("AGI");
        event.setDisposition("NO ANSWER");
        Integer billableSeconds = null;
        event.setBillableSeconds(billableSeconds);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());

        SIPCode sipCode = eventUserData.calculateCallSIPCodeAndDialStatus(channelUniqueId, billableSeconds, vctx);
        assertFalse(CdrEventHandler.isAnsweredCall(sipCode, eventUserData, "test"));
        assertFalse(CdrEventHandler.isOnGoingGwsFailoverCdrEvent(sipCode, eventUserData));

    }

    private class LocalCdrEventUserData extends LegacyCdrEventUserData {

        public LocalCdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
            super(event, channelUniqueId);
        }

        @Override
        protected String getOriginSessionDetails(String channelUniqueId) {
            return "junit-test-session";
        }

    }
}
