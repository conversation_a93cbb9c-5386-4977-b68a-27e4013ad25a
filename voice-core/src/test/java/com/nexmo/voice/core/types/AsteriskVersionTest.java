package com.nexmo.voice.core.types;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.util.Arrays;

import static com.nexmo.voice.core.types.AsteriskVersion.V20;
import static com.nexmo.voice.core.types.AsteriskVersion.V16;
import static com.nexmo.voice.core.types.AsteriskVersion.V1_8;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(Parameterized.class)
public class AsteriskVersionTest {

    private final String input;
    private final AsteriskVersion target;

    public AsteriskVersionTest(String input, AsteriskVersion target) {
        this.input = input;
        this.target = target;
    }

    @Parameterized.Parameters(name = "{index}: {0} {1}")
    public static Iterable<Object[]> data() {
        return Arrays.asList(new Object[][]{
                {null, V1_8},
                {"", V1_8},
                {"1.8", V1_8},
                {" 1.8 ", V1_8},
                {"16", V16},
                {" 16 ", V16},
                {"20", V20},
                {" 20 ", V20}
        });
    }

    @Test
    public void shouldBeAbleToCovertIntoAsteriskVersion() {
        assertThat(AsteriskVersion.from("dummy", input), equalTo(target));
    }
}
