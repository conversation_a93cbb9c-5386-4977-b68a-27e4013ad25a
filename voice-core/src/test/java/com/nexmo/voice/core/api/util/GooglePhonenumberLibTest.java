package com.nexmo.voice.core.api.util;

import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.thepeachbeetle.common.msisdn.PhoneNumberTool;

/**
 * This class goal is to verify our ability to upgrade the google libphonenumber library
 * outside of the messaging.jar
 * The SIPApp and TTS<PERSON>pp will continue to use the messagin.jar API : PhoneNumberTool, it is 
 * just that we are forcing it to use a different google library version, so we can
 * upgrade the google libphonenumber library without upgrading the messaging.jar 
 * 
 * The tests here are not to verify the correctness of PhoneNumberTool but to verify that
 * the updated library support the requested numbers
 * 
 * <AUTHOR>
 *
 */

public class GooglePhonenumberLibTest {

    @Test
    public void britishPhoneNumberTest() {
	assertTrue(PhoneNumberTool.isValidInternationalNumber("447352198767"));
    }
    
    @Test
    public void usaPhoneNumberTest() {
	assertTrue(PhoneNumberTool.isValidInternationalNumber("12025550000"));
    }
    
    //Added for SIP-227
    @Test
    public void vietnamPhoneNumberTest() {
	assertTrue(PhoneNumberTool.isValidInternationalNumber("84356822338"));
    }

    //Added for SIP-1806
    @Test
    public void mexicanPhoneNumberTest() {
        assertTrue(PhoneNumberTool.isValidInternationalNumber("525621632936"));
    }
   
}
