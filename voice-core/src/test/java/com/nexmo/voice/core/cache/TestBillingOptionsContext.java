package com.nexmo.voice.core.cache;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.HashMap;

import org.junit.Test;

import com.nexmo.voice.core.sip.AsteriskAGIServer;

public class TestBillingOptionsContext {
    //request-cost: "1" - yes,  If not provided or any other value - no.
    //bypass-permitted_destination: "1" - yes,  If not provided or any other value - no.
    //forced-price: - If provided - use it, if not, or invalid value, ignore it.
    private final String testSessionId="abc";
    
    
    @Test
    public void test_rc1_bpd1_fp3() {
	String billingOptHeader="request-cost=1;force-price=0.003;bypass-permitted-destination=1";
	
	TTSNGBillingInstructions ttsngBillingInstructions = getBillingInstructions(billingOptHeader);
  
        assertTrue(ttsngBillingInstructions.isCostRequiredInCallback() );
        assertTrue(ttsngBillingInstructions.isBypassPermittedDestinationVerification() );
        assertEquals("0.003", ttsngBillingInstructions.getRequestedForcedPrice());
    }
    
    @Test
    public void test_bpd1_fp3() {
	String billingOptHeader="force-price=0.003;bypass-permitted-destination=1";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertTrue(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertEquals("0.003", billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_rc1_fp3() {
	String billingOptHeader="request-cost=1;force-price=0.003";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertTrue(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertEquals("0.003", billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_fp3() {
	String billingOptHeader="force-price=0.003";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertEquals("0.003", billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_rcX_bpdY_fp0() {
	String billingOptHeader="request-cost=X;force-price=0;bypass-permitted-destination=Y";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertEquals("0", billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_rc1_bpd1() {
	String billingOptHeader="request-cost=1;bypass-permitted-destination=1";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertTrue(billingOptionsContext.isCostRequiredInCallback() );
        assertTrue(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_rc1() {
	String billingOptHeader="request-cost=1";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertTrue(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }

    
    @Test
    public void test_empty_header() {
	String billingOptHeader="";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_blank_header() {
	String billingOptHeader="  ";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_null_header() {
	String billingOptHeader=null;
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }

    @Test
    public void test_bpd2_rc1() {
	String billingOptHeader="bypass-permitted-destination=2;request-cost=1";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertTrue(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }
    
    @Test
    public void test_rcEmpty_bpdEmpty_fpEmpty() {
	String billingOptHeader="request-cost=;force-price=;bypass-permitted-destination=";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }

    
    @Test
    public void test_rcEmpty() {
	String billingOptHeader="request-cost=";
	
	TTSNGBillingInstructions billingOptionsContext = getBillingInstructions(billingOptHeader);
  
        assertFalse(billingOptionsContext.isCostRequiredInCallback() );
        assertFalse(billingOptionsContext.isBypassPermittedDestinationVerification() );
        assertNull(billingOptionsContext.getRequestedForcedPrice());
    }






    private TTSNGBillingInstructions getBillingInstructions(String billingOptHeader) {
	HashMap<String, String> inputParams = new HashMap<String, String>();
	inputParams.put(AsteriskAGIServer.SIP_HEADER_P_NEXMO_BILLING_OPTS, billingOptHeader);
	TTSNGBillingInstructions billingOptionsContext = TTSNGBillingInstructions.parseBillingInstructions(testSessionId, inputParams);
	return billingOptionsContext;
    }

    
}
