package com.nexmo.voice.core.types;

import org.junit.Assert;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.nexmo.voice.config.ChargingConfig;
import com.nexmo.voice.config.prefix.MappingConfig;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.MessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.PrefixAndNetworkMapMessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Product;


public class EffectivePriceTest {
    private static int DEFAULT_PRODUCT = Product.PRODUCT_VOICE_CALL;
    private static BigDecimal DEFAULT_PRICE = new BigDecimal("0.********");
    private static BigDecimal SIP_DESTINATION_PRICE = new BigDecimal("0.********");
    private static String DEFAULT_PREFIX = "12345";
    private static String DEFAULT_PREFIXGROUP = "860123";
    private static String EXPECTED_STRING = "EffectivePrice: Usage PRICE. amount=0.********, prefix=12345 (860123)"; // Price not 8dp


    @Test
    public void testCreation() {
        EffectivePrice price = new EffectivePrice(DEFAULT_PRICE, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(price);

        Assert.assertEquals(DEFAULT_PRICE, price.getPrice()); // As-is, not 8dp
        Assert.assertEquals(DEFAULT_PREFIX, price.getPrefix());
        Assert.assertEquals(DEFAULT_PREFIXGROUP, price.getPrefixGroup());
    }

    @Test
    public void testToString() {
        EffectivePrice price = new EffectivePrice(DEFAULT_PRICE, DEFAULT_PREFIX, DEFAULT_PREFIXGROUP);
        Assert.assertNotNull(price);

        Assert.assertEquals(EXPECTED_STRING, price.toString());
    }


    //
    // EffectivePrice.getPriceFromMatrixOrDefault() tests
    //

    @Test
    public void testSimplePricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "***********" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          null /*prefixMap*/,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice());
        Assert.assertEquals("12345", price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testPrefixMapPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "***********" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          prefixMap,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice());
        Assert.assertEquals("12345", price.getPrefix());
        Assert.assertEquals("806002", price.getPrefixGroup());
    }

    @Test
    public void testPrefixMapFallthroughPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "***********" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          prefixMap,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice());
        Assert.assertEquals("1235", price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup());
    }

    @Test
    public void testDefaultPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "***********" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          prefixMap,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice()); // PriceMatrix default, as 8dp
        Assert.assertEquals(null, price.getPrefix()); // No prefix as it didn't match any rules
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testDefaultDefaultPricing() {
        final SmppAccount account = makeSmppAccount();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(null /*priceMatrixList*/,
                                                                          account,
                                                                          "***********" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          null /*prefixMap*/,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice()); // DEFAULT_PRICE as 8dp
        Assert.assertEquals("default-price", price.getPrefix()); // No prefix as it didn't match any rules
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testSIPPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "sip:<EMAIL>" /*to*/,
                                                                          null /*from*/,
                                                                          null /*appId*/,
                                                                          null /*network*/,
                                                                          DEFAULT_PRICE,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          prefixMap,
                                                                          chargingConfig,
                                                                          VoiceProduct.CALL_API,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(SIP_DESTINATION_PRICE, price.getPrice()); // default-sip-destination-price (FIXME: Not 8dp?)
        Assert.assertEquals("default-sip-destination-price", price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testSourcePricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                         account,
                                                                         "***********" /*to*/,
                                                                         "***********" /*from*/,
                                                                         null /*appId*/,
                                                                         null /*network*/,
                                                                         DEFAULT_PRICE,
                                                                         false /*isVAPIOutboundToVBC*/,
                                                                         null /*prefixMap*/,
                                                                         chargingConfig,
                                                                         VoiceProduct.CALL_API,
                                                                         "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice());
        Assert.assertEquals("12345", price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testUnknownSourcePricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        final PrefixMapConfig prefixMap = makePrefixMapConfig();
        final ChargingConfig chargingConfig = makeChargingConfig();
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                         account,
                                                                         "***********" /*to*/,
                                                                         "Unknown" /*from*/,
                                                                         null /*appId*/,
                                                                         null /*network*/,
                                                                         DEFAULT_PRICE,
                                                                         false /*isVAPIOutboundToVBC*/,
                                                                         null /*prefixMap*/,
                                                                         chargingConfig,
                                                                         VoiceProduct.CALL_API,
                                                                         "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("0.********"), price.getPrice());
        Assert.assertEquals("12345", price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }

    /**
     * Unit test added using actual data captured from an integration test, which somehow
     * failed to match...
     * 
     * Turned out to be a bug in NoNetworkOrPrefixMessagePriceMatrix where a sender-prefix
     * rule would only be returned if there were >1 of them found. That could obviously
     * never be true for a situation with a single rule.
     * 
     * SIP-1925:
     * The com.thepeachbeetle.messaging.hub.config.pricing.NoNetworkOrPrefixMessagePriceMatrix
     * class is locally overridden in this project to fix this specific issue. If/when it's fixed
     * in messaging.jar, we can consider updating to the corresponding version or back-porting
     * the fix to the -2020-03-30_114334-ce490f6988de6b947220413b928711b44d9feb9a version.
     */
    @Test
    public void testFailedLookup() {
        // PriceMatrixList
        final Price p = new Price(Product.PRODUCT_VOICE_CALL,
                                  null /*currency*/,
                                  null /*network*/,
                                  null /*prefix*/,
                                  "nexmo-tester" /*senderPrefix*/,
                                  "sip-callee-7596" /*account*/,
                                  null /*pricingGroup*/,
                                  new BigDecimal("5.********") /*price*/,
                                  null,
                                  null /*countryCode*/,
                                  null /*dateCreated*/,
                                  new Date(1626787282000L) /*timeLastModified*/);
        final List<Price> currentPrices = new ArrayList<Price>();
        currentPrices.add(p);
        final Map<Integer, MessagePriceMatrix> matrixMap = new HashMap<>();
        MessagePriceMatrix matrix = new PrefixAndNetworkMapMessagePriceMatrix(DEFAULT_PRODUCT,
                                                                              "mt-price-matrix" /*matrixNodeName*/,
                                                                              true /*performPriceLookup*/,
                                                                              new BigDecimal("0.1") /*this.defaultPrice*/,
                                                                              false /*rejectSubmissionIfNoPriceFound*/,
                                                                              false /*useDb*/,
                                                                              null /*dbMatrixId*/,
                                                                              false /*useLdap*/,
                                                                              null /*ldapBaseDn*/,
                                                                              currentPrices);
        matrixMap.put(DEFAULT_PRODUCT, matrix);
        final PriceMatrixList priceMatrixList = new PriceMatrixList("mt-product-price-matrixes");
        priceMatrixList.setMatrix(matrixMap);

        // Account
        final SmppAccount account = new SmppAccount.Builder()
                                                   .setSysId("sip-callee-7596")
                                                   .build();
        // Default charging config 
        final ChargingConfig chargingConfig = makeChargingConfig();
        // And we're off...
        EffectivePrice price = EffectivePrice.getPriceFromMatrixOrDefault(priceMatrixList,
                                                                          account,
                                                                          "************" /*to*/,
                                                                          "nexmo-tester" /*from*/,
                                                                          null /*appId*/,
                                                                          "GB-FIXED-RESERVED" /*network*/,
                                                                          new BigDecimal("0.000000") /*defaultPrice*/,
                                                                          false /*isVAPIOutboundToVBC*/,
                                                                          null /*prefixMap*/,
                                                                          chargingConfig,
                                                                          VoiceProduct.SIP,
                                                                          "Purpose Description");

        Assert.assertNotNull(price);
        Assert.assertEquals(new BigDecimal("5.********"), price.getPrice());
        Assert.assertEquals(null, price.getPrefix());
        Assert.assertEquals(null, price.getPrefixGroup()); // No Prefix Group possible
    }


    //
    // Helper methods (TODO: move these out?)
    //

    private static PriceMatrixList makePriceMatrixList() {
        final List<Price> currentPrices = new ArrayList<Price>();
        currentPrices.add( makePrice("12345", null, new BigDecimal("0.69")) );
        currentPrices.add( makePrice("1235", null, new BigDecimal("0.099")) );
        currentPrices.add( makePrice("806002", null, new BigDecimal("0.069")) );
        // No price for 806004 so will fall through if number matches prefix 1235
        currentPrices.add( makePrice("12345", "12", new BigDecimal("0.042")) );

        final Map<Integer, MessagePriceMatrix> matrixMap = new HashMap<>();
        MessagePriceMatrix matrix = new PrefixAndNetworkMapMessagePriceMatrix(DEFAULT_PRODUCT,
                                                                              "mt-price-matrix" /*matrixNodeName*/,
                                                                              true /*performPriceLookup*/,
                                                                              new BigDecimal("0.1") /*this.defaultPrice*/,
                                                                              false /*rejectSubmissionIfNoPriceFound*/,
                                                                              false /*useDb*/,
                                                                              null /*dbMatrixId*/,
                                                                              false /*useLdap*/,
                                                                              null /*ldapBaseDn*/,
                                                                              currentPrices);
        matrixMap.put(DEFAULT_PRODUCT, matrix);

        final PriceMatrixList ret = new PriceMatrixList("mt-product-price-matrixes");
        ret.setMatrix(matrixMap);
        return ret;
    }

    private static Price makePrice(final String prefix, final String senderPrefix, final BigDecimal price) {
        return new Price(DEFAULT_PRODUCT,
                         null /*currency*/,
                         null /*network*/,
                         prefix,
                         senderPrefix,
                         null /*account*/,
                         null /*pricingGroup*/,
                         price,
                         "No description",
                         null /*countryCode*/,
                         null /*?*/,
                         null /*?*/);
    }

    private static SmppAccount makeSmppAccount() {
        Set<String> capabilities = new HashSet<String>();
        capabilities.add("use-prefix-group-pricing");
        return new SmppAccount.Builder()
                              .setSysId("Badger")
                              .setCapabilities(capabilities)
                              .build();
    }

    private static PrefixMapConfig makePrefixMapConfig() {
        List<MappingConfig> mappings = new ArrayList<MappingConfig>();
        mappings.add(new MappingConfig("1234", "806001"));
        mappings.add(new MappingConfig("12345", "806002"));
        mappings.add(new MappingConfig("12346", "806003"));
        mappings.add(new MappingConfig("1235", "806004"));
        return new PrefixMapConfig("806", mappings);
    }

    private static ChargingConfig makeChargingConfig() {
        final Map<String, ChargingConfig.CountrySpecificInfo> countrySpecificInfo = new HashMap<>();
        return new ChargingConfig(new BigDecimal("0") /*minPrice*/,
                new BigDecimal("0.00666") /*defaultCost*/,
                new BigDecimal("0") /*defaultSipPrice*/,
                new BigDecimal("0") /*defaultSipCost*/,
                new BigDecimal("0.00400") /*defaultSipDialInPrice*/,
                new BigDecimal("0.00400") /*defaultSipDestinationPrice*/,
                new BigDecimal("0") /*defaultInboundPrice*/,
                new BigDecimal("0") /*defaultInboundCost*/,
                6L /*minIncrement*/,
                6L /*recurringIncrement*/,
                new BigDecimal("0.1") /*ttsNGMinPrice*/,
                countrySpecificInfo);
    }

}
