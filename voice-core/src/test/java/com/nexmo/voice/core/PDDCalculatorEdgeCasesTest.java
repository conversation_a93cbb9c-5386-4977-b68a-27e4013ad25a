package com.nexmo.voice.core;

import static org.hamcrest.number.OrderingComparison.comparesEqualTo;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

import org.junit.Before;
import org.junit.Test;

import com.nexmo.voice.core.pdd.PDDCalculationException;
import com.nexmo.voice.core.pdd.PDDCalculator;

public class PDDCalculatorEdgeCasesTest {

    protected static final String ID_LEG1 = "ID1";
    protected static final String ID_LEG2 = "ID2";
    protected static final long EXPECTED_PDD = 0;

    protected PDDCalculator calculator;

    public PDDCalculatorEdgeCasesTest() {
    }

    @Before
    public void setUp() throws Exception {
        this.calculator = new PDDCalculator(1000);
    }

    @Test
    public void noStartTime() {

        long pdd = 0L;
        try {
            this.calculator.storeEndTime(ID_LEG2, System.currentTimeMillis());
            pdd = this.calculator.calculate(ID_LEG1, ID_LEG2);
        } catch (PDDCalculationException e) {
            fail("Pdd calculation failed with exception " + e.getMessage());
        }

        assertThat(pdd, comparesEqualTo(EXPECTED_PDD));
    }

    @Test
    public void noEndTime() {

        long pdd = 0L;
        try {
            this.calculator.storeStartTime(ID_LEG1, System.currentTimeMillis());
            pdd = this.calculator.calculate(ID_LEG1, ID_LEG2);
        } catch (PDDCalculationException e) {
            fail("Pdd calculation failed with exception " + e.getMessage());
        }

        assertThat(pdd, comparesEqualTo(EXPECTED_PDD));
    }

    @Test(expected=PDDCalculationException.class)
    public void storeStartTimeWithNullId() throws PDDCalculationException {
        this.calculator.storeStartTime(null, System.currentTimeMillis());
    }

    @Test(expected=PDDCalculationException.class)
    public void storeEndTimeWithNullId() throws PDDCalculationException {
        this.calculator.storeEndTime(null, System.currentTimeMillis());
    }

    @Test(expected=PDDCalculationException.class)
    public void calculatePDDWithNullId() throws PDDCalculationException {
        this.calculator.calculate(null, ID_LEG2);
    }

    @Test(expected=PDDCalculationException.class)
    public void calculatePDDWithNullId2() throws PDDCalculationException {
        this.calculator.calculate(ID_LEG1, null);
    }
}
