package com.nexmo.voice.core.logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import com.nexmo.voice.core.sip.event.cdr.LegacyCdrEventUserData;
import com.nexmo.voice.core.types.*;
import org.asteriskjava.manager.event.CdrEvent;
import org.junit.Test;

import com.nexmo.voice.config.cdrs.CdrsConfig;
import com.nexmo.voice.core.billing.BillingInfo.Status;
import com.nexmo.voice.core.cache.SIPAsteriskContext;
import com.nexmo.voice.core.cache.VoiceContext;
import com.nexmo.voice.core.cdr.CDRData;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;

public class SIPAppLoggerTest {

    @Test
    public void createJsonLogPrefixTest() {
        assertEquals("sip-json-outbound", SIPAppLogger.getJsonLogPrefix("sip-outbound"));
        assertEquals("sip-json-inbound", SIPAppLogger.getJsonLogPrefix("sip-inbound"));
        assertEquals("sip-json-rejected", SIPAppLogger.getJsonLogPrefix("sip-rejected"));
        assertEquals("sip-json-attempt", SIPAppLogger.getJsonLogPrefix("sip-attempt"));
        assertEquals("tts-json-outbound", SIPAppLogger.getJsonLogPrefix("tts-outbound"));
        assertEquals("tts-json-rejected", SIPAppLogger.getJsonLogPrefix("tts-rejected"));
        assertEquals("tts-json-attempt", SIPAppLogger.getJsonLogPrefix("tts-attempt"));

        assertEquals("json-xyz-abc", SIPAppLogger.getJsonLogPrefix("xyz-abc"));
    }
    
    @Test
    public void createBackupLogPrefixTest() {
        assertEquals("sip-backup-outbound", SIPAppLogger.getBackupLogPrefix("sip-outbound"));
        assertEquals("sip-backup-inbound", SIPAppLogger.getBackupLogPrefix("sip-inbound"));
        assertEquals("sip-backup-rejected", SIPAppLogger.getBackupLogPrefix("sip-rejected"));
        assertEquals("sip-backup-attempt", SIPAppLogger.getBackupLogPrefix("sip-attempt"));
        assertEquals("tts-backup-outbound", SIPAppLogger.getBackupLogPrefix("tts-outbound"));
        assertEquals("tts-backup-rejected", SIPAppLogger.getBackupLogPrefix("tts-rejected"));
        assertEquals("tts-backup-attempt", SIPAppLogger.getBackupLogPrefix("tts-attempt"));

        assertEquals("backup-xyz-abc", SIPAppLogger.getBackupLogPrefix("xyz-abc"));
    }
    
    
    //TALLY REMOVE THIS TEST - IT IS JUST MANUAL ON ONE SPECIFIC CASE.
    //The number of permutations and options is endless
    @Test
    public void cdrKVOrderTest() throws Exception {
        
        //Prod Outbound CDR:
        /**
         * 
         * ************* :: 01/27/2022 00:00:02 (799) ::
         * com.nexmo.voice.core.cache.VoiceContext :: "PRODUCT=5", "PRODUCT-CLASS=api",
         * "DIRECTION=out", "HOST=ass1.wdc4.internal",
         * "ID=205bb3f3-a9fe-4827-b29a-97f155a4385d",
         * "SESSION-ID=205bb3f3-a9fe-4827-b29a-97f155a4385d",
         * "LEG1-ID=d762e1f5-f9a6-123a-7bb0-068baab34a03",
         * "LEG2-ID=<EMAIL>",
         * "SUPERHUB-ACC=null", "MASTER-ACCOUNT-PRICING-GROUP=", "ACC=3023d4d9",
         * "ACCOUNT-PRICING-GROUP=", "FROM=***********", "TO=***********",
         * "PREFIX-FROM=1415212", "PREFIX-TO=1408550", "FORCED_SENDER=null",
         * "PREFIX-FORCED_SENDER=", "SIP-DEST-ATTEMPT=1#1", "COUNTRY=US", "NET=310090",
         * "NETWORK-NAME=AT&T Mobility", "NETWORK-TYPE=MOBILE", "GW=vonage-prem",
         * "GWS=vonage-prem,ibasis", "GW_ATTEMPT=1#2", "UNIT=6", "MIN_UNIT=6",
         * "RECURRING_UNIT=6", "PRICE=0.********", "OVERRIDE_PRICE=null",
         * "TOTAL_PRICE=0.********", "PRICE_PREFIX=null", "PRICE_PREFIX_GROUP=null",
         * "PRICE_SENDER_PREFIX=null", "PRICE_TIMESTAMP=09/21/2021 19:45:27 (000)",
         * "COST=0.********", "TOTAL_COST=0.********", "CALL_MARGIN=0.********",
         * "COST_PREFIX=1408550", "COST_PREFIX_GROUP=null", "COST_SENDER_PREFIX=null",
         * "COST_TIMESTAMP=06/14/2018 15:27:55 (000)", "START=01/26/2022 23:59:54
         * (413)", "END=01/27/2022 00:00:03 (413)", "DURATION=9", "CALL_DURATION=9000",
         * "STATUS=ANSWER", "REASON=200", "REASON_DESC=The call was successful.",
         * "CALL_DATE=01/26/2022 23:59:54 (413)", "ROUTING_SEQ=***************",
         * "PDD=1848", "REQUEST_IP=", "CALL_ORIGIN=", "CALL_TERMINATION=pstn",
         * "CUSTOMER_DOMAIN=", "CDR_UUID=e473b89e-9513-4409-a401-8cbb2632d3bb",
         * "STIR_SHAKEN=A", "CARRIER_PLATFORM=NET",
         * 
         * 
         */
        
        CdrsConfig cdrsConfig = new CdrsConfig(CDRType.KEY_VALUE, false);
        CallLoggerController callLoggerController = new CallLoggerController("./", "sip-tmptest", cdrsConfig);

        SIPAsteriskContext appCtx = new SIPAsteriskContext("SIP/net1-dal13-********",
                "d762e1f5-f9a6-123a-7bb0-068baab34a03",
                              null,
                              "vonage-prem",
                              false,
                              0,
                              null);
        appCtx.setPdd(1848);
        
        VoiceContext ctx = new VoiceContext.Builder()
                
                .withProductClass("api")
                .withVoiceProduct(VoiceProduct.SIP)
                .withAccountId("3023d4d9")
                .withFrom("***********")
                .withTo("***********")
                .withGateway("vonage-prem")
                .withVoiceDirection(VoiceDirection.OUTBOUND)
                .withApplicationContext(appCtx)
                .withCallTermination("pstn")
                .withIsVAPIOutboundToVBC(false)
                .withQuotaRef("205bb3f3-a9fe-4827-b29a-97f155a4385d-out")
                .withStirShaken("A")
                .withVoiceSkipQuota(false)
                .withSrtpEnabled(false)
                .withClientCallId("d762e1f5-f9a6-123a-7bb0-068baab34a03")
                .withPricePerMinute(BigDecimal.valueOf(0.********))
                .withCostPerMinute(BigDecimal.valueOf(0.********))
                .withFirstChargedSeconds(6)
                .withQuotaUpdatesInterval(6)
                .withInitialChargingStatus(Status.STARTED)
                .build();
        
        ctx.setSessionId("205bb3f3-a9fe-4827-b29a-97f155a4385d-SFAN-0");
        ctx.setConnectionId("asterisk1-1-1643241569.7472537");
        
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=vonage-prem,ibasis;GW=vonage-prem;ATTEMPT=1#2;TO_INPS=0";
        event.setUserField(userField);
 
        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, "testing-junit");

        CDRData cdrData =  callLoggerController.buildCDRData(
                ctx,
                "ANSWER",
                SIPCode.OK,
                "<EMAIL>",
                null,
                4,
                eventUserData,
                false); 
         
         String generatedCDR = SIPAppLogger.buildKeyValueCDR(cdrData, CallLoggerController.CallCDROrder);
         
         System.out.println("The generated CDR: "+generatedCDR);
    }

    
    private class LocalCdrEventUserData extends LegacyCdrEventUserData {

        public LocalCdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
            super(event, channelUniqueId);
        }

        @Override
        protected String getOriginSessionDetails(String channelUniqueId) {
            return "junit-test-session";
        }

    }

    @Test
    public void testFormatSipCodeMap() {
        assertEquals("my-gateway:410", SIPAppLogger.formatSipCodeMap(Collections.singletonMap("my-gateway", SIPCode.GONE)));
        assertEquals("my-gateway:null", SIPAppLogger.formatSipCodeMap(Collections.singletonMap("my-gateway", null)));
        // using a LinkedHashMap here for test; the order will generally not be guaranteed at runtime
        assertEquals("my-gateway:503,my-gateway2:504", SIPAppLogger.formatSipCodeMap(new LinkedHashMap<String, SIPCode>() {{put("my-gateway", SIPCode.SERVICE_UNAVAILABLE); put("my-gateway2", SIPCode.SERVER_TIMEOUT); }}));
    }

}
