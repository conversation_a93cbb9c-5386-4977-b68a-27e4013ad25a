package com.nexmo.voice.core.types;

import org.junit.Test;

import java.io.UnsupportedEncodingException;

public class TTSContextTest {

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowIllegalArgumentExceptionGivenValidBadCallbackMethod() throws UnsupportedEncodingException {
        String callDetails = "1938fcc8;http%3A%2F%2Fqaservices1.pillar.npe%3A8555%2Fcallback;PUT_QUERY_PARAMS;;1;;-1;" +
                "en-US;0";
        TTSContext.parse("dummy", callDetails);
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowIllegalArgumentExceptionGivenIncompleteCallDetails() throws UnsupportedEncodingException {
        String callDetails = "1938fcc8;http%3A%2F%2Fqaservices1.pillar.npe%3A8555%2Fcallback;" +
                "en-US;0";
        TTSContext.parse("dummy", callDetails);
    }

}
