package com.nexmo.voice.core.types;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.util.Arrays;

import static org.junit.Assert.*;

@RunWith(Parameterized.class)
public class AuthenticationSourceTest {

    private String header;
    private AuthenticationSource expectedSource;
    private CallInternalFlag expectedFlag;

    public AuthenticationSourceTest(String header, AuthenticationSource expectedSource, CallInternalFlag expectedFlag) {
        this.header = header;
        this.expectedSource = expectedSource;
        this.expectedFlag = expectedFlag;
    }

    @Parameterized.Parameters(name = "{index}: header={0}, expectedSource={1}")
    public static Iterable<Object[]> data() {

        return Arrays.asList(new Object[][] {
                // expected case
                { "domain-acl",  AuthenticationSource.DOMAIN_ACL, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_ACL },
                { "domain-user",  AuthenticationSource.DOMAIN_USER, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_USER },
                { "domain-transition",  AuthenticationSource.DOMAIN_TRANSITION, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_TRANSITION },
                { "domain-transition-service-fallback",  AuthenticationSource.DOMAIN_TRANSITION_SERVICE_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_SERVICE_FALLBACK },
                { "domain-transition-auth-fallback",  AuthenticationSource.DOMAIN_TRANSITION_AUTH_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_AUTH_FALLBACK },
                { "source-ip",  AuthenticationSource.SOURCE_IP, CallInternalFlag.AUTHENTICATED_BY_SOURCE_IP },
                { "apikey",  AuthenticationSource.APIKEY, CallInternalFlag.AUTHENTICATED_BY_APIKEY },
                // all upper case
                { "DOMAIN-ACL",  AuthenticationSource.DOMAIN_ACL, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_ACL },
                { "DOMAIN-USER",  AuthenticationSource.DOMAIN_USER, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_USER },
                { "DOMAIN-TRANSITION",  AuthenticationSource.DOMAIN_TRANSITION, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_TRANSITION },
                { "DOMAIN-TRANSITION-SERVICE-FALLBACK",  AuthenticationSource.DOMAIN_TRANSITION_SERVICE_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_SERVICE_FALLBACK },
                { "DOMAIN-TRANSITION-AUTH-FALLBACK",  AuthenticationSource.DOMAIN_TRANSITION_AUTH_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_AUTH_FALLBACK },
                { "SOURCE-IP",  AuthenticationSource.SOURCE_IP, CallInternalFlag.AUTHENTICATED_BY_SOURCE_IP },
                { "APIKEY",  AuthenticationSource.APIKEY, CallInternalFlag.AUTHENTICATED_BY_APIKEY },
                // mixed case
                { "Domain-Acl",  AuthenticationSource.DOMAIN_ACL, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_ACL },
                { "doMain-uSer",  AuthenticationSource.DOMAIN_USER, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_USER },
                { "dOmAiN-tRaNsItIoN",  AuthenticationSource.DOMAIN_TRANSITION, CallInternalFlag.AUTHENTICATED_BY_DOMAIN_TRANSITION },
                { "domain-TRANSITION-service-FALLBACK",  AuthenticationSource.DOMAIN_TRANSITION_SERVICE_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_SERVICE_FALLBACK },
                { "DOMAIN-transition-AUTH-fallback",  AuthenticationSource.DOMAIN_TRANSITION_AUTH_FALLBACK, CallInternalFlag.AUTHENTICATION_DOMAIN_TRANSITION_AUTH_FALLBACK },
                { "source-IP",  AuthenticationSource.SOURCE_IP, CallInternalFlag.AUTHENTICATED_BY_SOURCE_IP },
                { "apiKey",  AuthenticationSource.APIKEY, CallInternalFlag.AUTHENTICATED_BY_APIKEY },
                // unknown and empty values
                { null,  AuthenticationSource.UNKNOWN, null },
                { "",  AuthenticationSource.UNKNOWN, null },
                { "foobar",  AuthenticationSource.UNKNOWN, null },
                { " ",  AuthenticationSource.UNKNOWN, null },
                { "\r\n",  AuthenticationSource.UNKNOWN, null },
        });
    }

    @Test
    public void testAuthenticationSource() {
        AuthenticationSource source = AuthenticationSource.getForHeaderValue(header);

        assertNotNull(source);
        assertEquals(expectedSource, source);
        assertEquals(expectedFlag, source.getInternalFlag());
    }

}