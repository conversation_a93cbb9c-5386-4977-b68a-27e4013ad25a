package com.nexmo.voice.core.types;

import org.junit.Assert;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.pricing.MessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.PrefixAndNetworkMapMessagePriceMatrix;
import com.thepeachbeetle.messaging.hub.config.pricing.Price;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;
import com.thepeachbeetle.messaging.hub.core.Product;


public class EffectiveCostTest {
    private static int DEFAULT_PRODUCT = Product.PRODUCT_VOICE_CALL;
    private static BigDecimal DEFAULT_PRICE = new BigDecimal("0.01234");
    private static String DEFAULT_PREFIX = "12345";
    private static String EXPECTED_STRING = "EffectiveCost: Usage COST. amount=0.********, prefix=12345";


    @Test
    public void testCreation() {
        EffectiveCost cost = new EffectiveCost(DEFAULT_PRICE, DEFAULT_PREFIX);
        Assert.assertNotNull(cost);

        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost()); // Promoted to 8dp
        Assert.assertEquals(DEFAULT_PREFIX, cost.getPrefix());
    }

    @Test
    public void testToString() {
        EffectiveCost cost = new EffectiveCost(DEFAULT_PRICE, DEFAULT_PREFIX);
        Assert.assertNotNull(cost);

        Assert.assertEquals(EXPECTED_STRING, cost.toString());
    }


    //
    // EffectiveCost.getCostFromMatrixOrDefault() tests
    //

    @Test
    public void testSimplePricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       null /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost());
        Assert.assertEquals("12345", cost.getPrefix());
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testDefaultPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       null /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost()); // PriceMatrix default, as 8dp
        Assert.assertEquals(null, cost.getPrefix()); // No prefix as it didn't match any rules
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testDefaultDefaultPricing() {
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(null /*priceMatrixList*/,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       null /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost()); // DEFAULT_PRICE as 8dp
        Assert.assertEquals("default-cost", cost.getPrefix()); // No prefix as it didn't match any rules
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testSIPPricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "sip:<EMAIL>" /*to*/,
                                                                       null /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost()); // Always zero
        Assert.assertEquals("default-sip-destination-cost", cost.getPrefix());
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testSourcePricing() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       "***********" /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost());
        Assert.assertEquals("12345", cost.getPrefix());
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testUnknownSourcePricing1() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList();
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       "Unknown" /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost());
        Assert.assertEquals("12345", cost.getPrefix());
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }

    @Test
    public void testUnknownSourcePricing2() {
        final PriceMatrixList priceMatrixList = makePriceMatrixList(true); // Rule explicitly matching sourcePrefix="Unknown"
        final SmppAccount account = makeSmppAccount();
        EffectiveCost cost = EffectiveCost.getCostFromMatrixOrDefault(priceMatrixList,
                                                                       account,
                                                                       "***********" /*to*/,
                                                                       "Unknown" /*from*/,
                                                                       null /*appId*/,
                                                                       null /*network*/,
                                                                       DEFAULT_PRICE,
                                                                       false /*isVAPIOutboundToVBC*/,
                                                                       "Purpose Description");

        Assert.assertNotNull(cost);
        Assert.assertEquals(new BigDecimal("0.********"), cost.getCost());
        Assert.assertEquals("12345", cost.getPrefix());
        Assert.assertEquals(null, cost.getPrefixGroup()); // No Prefix Group possible
    }


    //
    // Helper methods (TODO: move these out?)
    //

    private static PriceMatrixList makePriceMatrixList() {
        return makePriceMatrixList(false);
    }

    private static PriceMatrixList makePriceMatrixList(boolean includeUnknown) {
        final List<Price> currentPrices = new ArrayList<Price>();
        currentPrices.add( makePrice("12345", null, new BigDecimal("0.69")) );
        currentPrices.add( makePrice("1235", null, new BigDecimal("0.099")) );
        currentPrices.add( makePrice("12345", "12", new BigDecimal("0.042")) );
        if (includeUnknown)
            currentPrices.add( makePrice("12345", "Unknown", new BigDecimal("0.777")) );

        final Map<Integer, MessagePriceMatrix> matrixMap = new HashMap<>();
        MessagePriceMatrix matrix = new PrefixAndNetworkMapMessagePriceMatrix(DEFAULT_PRODUCT,
                                                                              "mt-cost-matrix" /*matrixNodeName*/,
                                                                              true /*performPriceLookup*/,
                                                                              new BigDecimal("0.1") /*this.defaultPrice*/,
                                                                              false /*rejectSubmissionIfNoPriceFound*/,
                                                                              false /*useDb*/,
                                                                              null /*dbMatrixId*/,
                                                                              false /*useLdap*/,
                                                                              null /*ldapBaseDn*/,
                                                                              currentPrices);
        matrixMap.put(DEFAULT_PRODUCT, matrix);

        final PriceMatrixList ret = new PriceMatrixList("mt-product-cost-matrixes");
        ret.setMatrix(matrixMap);
        return ret;
    }

    private static Price makePrice(final String prefix, final String senderPrefix, final BigDecimal cost) {
        return new Price(DEFAULT_PRODUCT,
                         null /*currency*/,
                         null /*network*/,
                         prefix,
                         senderPrefix,
                         null /*account*/,
                         null /*pricingGroup*/,
                         cost,
                         "No description",
                         null /*countryCode*/,
                         null /*?*/,
                         null /*?*/);
    }

    private static SmppAccount makeSmppAccount() {
        Set<String> capabilities = new HashSet<String>();
        capabilities.add("use-prefix-group-pricing");
        return new SmppAccount.Builder()
                              .setSysId("Badger")
                              .setCapabilities(capabilities)
                              .build();
    }

}
