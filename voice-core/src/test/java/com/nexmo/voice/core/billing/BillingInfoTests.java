package com.nexmo.voice.core.billing;

import java.math.BigDecimal;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import com.nexmo.voice.core.types.AsteriskVersion;
import org.junit.Test;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

public class BillingInfoTests {

    private static final ScheduledExecutorService SCHEDULER = Executors.newSingleThreadScheduledExecutor();

    
    @Test
    public void normalCharge() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        //Start billing
        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - right after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue0 = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-0 "), 0, TimeUnit.MILLISECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - 1 seconds after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue1= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-1 "), 1000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue2= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-2 "), 2000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue3= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-3 "), 3000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue4= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-4 "), 4000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue5= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-5 "), 5000, TimeUnit.MILLISECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - 6 seconds after the call has started - time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue6= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-6 "), 6000, TimeUnit.MILLISECONDS);
        //The call has ended after 6300 millisecs
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 6300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta0 = futureDeltaValue0.get();
        BillingInfoDetails delta1 = futureDeltaValue1.get();
        BillingInfoDetails delta2 = futureDeltaValue2.get();
        BillingInfoDetails delta3 = futureDeltaValue3.get();
        BillingInfoDetails delta4 = futureDeltaValue4.get();
        BillingInfoDetails delta5 = futureDeltaValue5.get();
        BillingInfoDetails delta6 = futureDeltaValue6.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertFalse(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(startChargeValue.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta0.getBillingInfoSnapshot().getStatus());
        assertTrue(delta0.getResult().shouldSkipQuotaUpdate());
        assertNull(delta0.getResult().getAmount());
        assertThat(delta0.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(1L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(2L));

        assertEquals(BillingInfo.Status.STARTED, delta2.getBillingInfoSnapshot().getStatus());
        assertTrue(delta2.getResult().shouldSkipQuotaUpdate());
        assertNull(delta2.getResult().getAmount());
        assertThat(delta2.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(3L));

        assertEquals(BillingInfo.Status.STARTED, delta3.getBillingInfoSnapshot().getStatus());
        assertTrue(delta3.getResult().shouldSkipQuotaUpdate());
        assertNull(delta3.getResult().getAmount());
        assertThat(delta3.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(4L));

        assertEquals(BillingInfo.Status.STARTED, delta4.getBillingInfoSnapshot().getStatus());
        assertTrue(delta4.getResult().shouldSkipQuotaUpdate());
        assertNull(delta4.getResult().getAmount());
        assertThat(delta4.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(5L));

        assertEquals(BillingInfo.Status.STARTED, delta5.getBillingInfoSnapshot().getStatus());
        assertFalse(delta5.getResult().shouldSkipQuotaUpdate());
        assertNotNull(delta5.getResult().getAmount());
        assertThat(delta5.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(6L));

        assertEquals(BillingInfo.Status.STARTED, delta6.getBillingInfoSnapshot().getStatus());
        assertTrue(delta6.getResult().shouldSkipQuotaUpdate());
        assertNull(delta6.getResult().getAmount());
        assertThat(delta6.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(7L));

        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(stopValue.getResult().getAmount());
        assertTrue(stopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Refund
        assertTrue(stopValue.getResult().shouldRefund()); //Refund
        assertFalse(stopValue.getResult().shouldConsume()); //NOT consume
        
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta0.getBillingInfoSnapshot().getSecondsChargedSoFar(), startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta1.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta0.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta2.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta3.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta4.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta3.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(delta5.getBillingInfoSnapshot().getSecondsChargedSoFar() > delta4.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta6.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta5.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta6.getBillingInfoSnapshot().getSecondsChargedSoFar());
        
        assertTrue(startChargeValue.getBillingInfoSnapshot().getLastUpdated() > 0);
        assertTrue(delta0.getBillingInfoSnapshot().getLastUpdated() == startChargeValue.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta1.getBillingInfoSnapshot().getLastUpdated() == delta0.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta2.getBillingInfoSnapshot().getLastUpdated() == delta1.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta3.getBillingInfoSnapshot().getLastUpdated() == delta2.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta4.getBillingInfoSnapshot().getLastUpdated() == delta3.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta5.getBillingInfoSnapshot().getLastUpdated() > delta4.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta6.getBillingInfoSnapshot().getLastUpdated() == delta5.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(stopValue.getBillingInfoSnapshot().getLastUpdated() > delta6.getBillingInfoSnapshot().getLastUpdated());

        assertEquals(startChargeValue.getBillingInfoSnapshot().getIterationsCounter(), 0);
        assertEquals(delta0.getBillingInfoSnapshot().getIterationsCounter(), 1);
        assertEquals(delta1.getBillingInfoSnapshot().getIterationsCounter(), 2);
        assertEquals(delta2.getBillingInfoSnapshot().getIterationsCounter(), 3);
        assertEquals(delta3.getBillingInfoSnapshot().getIterationsCounter(), 4);
        assertEquals(delta4.getBillingInfoSnapshot().getIterationsCounter(), 5);
        assertEquals(delta5.getBillingInfoSnapshot().getIterationsCounter(), 0);
        assertEquals(delta6.getBillingInfoSnapshot().getIterationsCounter(), 1);
        assertEquals(stopValue.getBillingInfoSnapshot().getIterationsCounter(), -1);
        
        assertEquals(configuredPrice, stopValue.getBillingInfoSnapshot().getActualPricePerMinute());

    }


    @Test
    public void startAndStopBeforeRecurringChargeShouldRefund() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        BigDecimal cost = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, cost);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 2200, TimeUnit.MILLISECONDS);

        BillingInfoDetails startValue = futureStartChargeValue.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startValue.getBillingInfoSnapshot().getStatus());
        assertEquals(2*minIncrement, startValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertFalse(startValue.getResult().shouldSkipQuotaUpdate());
        
        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertEquals(2*minIncrement, stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertTrue(stopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Refund
        assertTrue(stopValue.getResult().shouldRefund()); //Refund
        assertFalse(stopValue.getResult().shouldConsume()); //NOT consume
    }
    
    @Test
    public void callInError() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        BigDecimal cost = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithError(minIncrement, recurringIncrement, configuredPrice, forcedPrice, cost);
        assertEquals(BillingInfo.Status.ERROR, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStopWithErrorCDR(crgCtx, event), 2200, TimeUnit.MILLISECONDS);

        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.ERROR, stopValue.getBillingInfoSnapshot().getStatus());
        assertEquals(0, stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNull(stopValue.getResult().getAmount()); 
    }
    
    @Test
    public void callOutOfFundsDuringDelta() throws InterruptedException, ExecutionException {
        long minIncrement = 1;
        long recurringIncrement = 4;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-2200 "), 2200, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue2= SCHEDULER.schedule(BillingTestsHelper.runDeltaOutOfFunds(crgCtx), 5200, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 8300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails delta2 = futureDeltaValue2.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertFalse(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(startChargeValue.getResult().getAmount());
        //number of iterations should be zero at the beginning
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());
        //after 2200 Ms, delta should have been called twice
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(2L));

        assertEquals(BillingInfo.Status.OUT_OF_FUNDS_DURING_CALL, delta2.getBillingInfoSnapshot().getStatus());
        assertTrue(delta2.getResult().shouldSkipQuotaUpdate());
        assertNull(delta2.getResult().getAmount());
        //after 5200 Ms, delta should have been called 5 times
        assertThat(delta2.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(5L));


        assertEquals(BillingInfo.Status.OUT_OF_FUNDS_DURING_CALL, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(stopValue.getResult().getAmount());
        assertTrue(stopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Consume amount should be positive
        assertFalse(stopValue.getResult().shouldRefund()); //Not refund
        assertTrue(stopValue.getResult().shouldConsume()); //Consume

        //2 times the minIncrement (2 * minIncrement)
        assertEquals(2, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta1.getBillingInfoSnapshot().getSecondsChargedSoFar(), startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        //by the second delta, it should have been time to charge (2 * minIncrement + recurringIncrement)
        assertEquals(6, delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());

        assertTrue(startChargeValue.getBillingInfoSnapshot().getLastUpdated() > 0);
        assertEquals(delta1.getBillingInfoSnapshot().getLastUpdated(), startChargeValue.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(stopValue.getBillingInfoSnapshot().getLastUpdated() > delta2.getBillingInfoSnapshot().getLastUpdated());
    }


    @Test
    public void callOutOfFundsDuringDeltaButShouldRefundGivenABiggerMinIncrement() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 4;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-2200 "), 2200, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue2= SCHEDULER.schedule(BillingTestsHelper.runDeltaOutOfFunds(crgCtx), 5200, TimeUnit.MILLISECONDS);
        //Now the BillingInfo status is OUT_OF_FUNDS
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 8300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails delta2 = futureDeltaValue2.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertFalse(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(startChargeValue.getResult().getAmount());
        //number of iterations should be zero at the beginning
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());
        //after 2200 Ms, delta should have been called twice
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(2L));

        assertEquals(BillingInfo.Status.OUT_OF_FUNDS_DURING_CALL, delta2.getBillingInfoSnapshot().getStatus());
        assertTrue(delta2.getResult().shouldSkipQuotaUpdate());
        assertNull(delta2.getResult().getAmount());
        //after 5200 Ms, delta should have been called 5 times
        assertThat(delta2.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(5L));


        assertEquals(BillingInfo.Status.OUT_OF_FUNDS_DURING_CALL, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(stopValue.getResult().getAmount());
        assertTrue(stopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Refund amount should be positive
        assertTrue(stopValue.getResult().shouldRefund()); //Refund
        assertFalse(stopValue.getResult().shouldConsume()); //Not consume

        //2 times the minIncrement (2 * minIncrement)
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta1.getBillingInfoSnapshot().getSecondsChargedSoFar(), startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        //by the second delta, it should have been time to charge (2 * minIncrement + recurringIncrement)
        assertEquals(16, delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());

        assertTrue(startChargeValue.getBillingInfoSnapshot().getLastUpdated() > 0);
        assertEquals(delta1.getBillingInfoSnapshot().getLastUpdated(), startChargeValue.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(stopValue.getBillingInfoSnapshot().getLastUpdated() > delta2.getBillingInfoSnapshot().getLastUpdated());
    }
    
    @Test
    public void forcedPriceOfZeroShouldNeverCharge() throws InterruptedException, ExecutionException {
        long minIncrement = 1;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = BigDecimal.ZERO;
        BigDecimal configuredPrice = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NO_CHARGES, crgCtx.getStatus());
        
        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-2200 "), 2200, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue2= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-5200 "), 5200, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue3= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-6200 "), 6200, TimeUnit.MILLISECONDS);
        //Now the BillingInfo status is OUT_OF_FUNDS
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 8300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails delta2 = futureDeltaValue2.get();
        BillingInfoDetails delta3 = futureDeltaValue3.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        //Make sure no seconds where charged as the price is zero.
        assertEquals(0, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(BillingInfo.Status.NO_CHARGES, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertEquals(0, startChargeValue.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNull(startChargeValue.getResult().getAmount());
        
        assertEquals(0, delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(BillingInfo.Status.NO_CHARGES, delta1.getBillingInfoSnapshot().getStatus());
        assertEquals(0, delta1.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());

        assertEquals(0, delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(BillingInfo.Status.NO_CHARGES, delta2.getBillingInfoSnapshot().getStatus());
        assertEquals(0, delta2.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta2.getResult().shouldSkipQuotaUpdate());
        assertNull(delta2.getResult().getAmount());

        assertEquals(0, delta3.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(BillingInfo.Status.NO_CHARGES, delta3.getBillingInfoSnapshot().getStatus());
        assertEquals(0, delta3.getBillingInfoSnapshot().getLastUpdated());
        assertEquals(0, delta3.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(delta3.getResult().shouldSkipQuotaUpdate());
        assertNull(delta3.getResult().getAmount());
        
        assertEquals(0, stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(BillingInfo.Status.NO_CHARGES, stopValue.getBillingInfoSnapshot().getStatus());
        assertEquals(0, stopValue.getBillingInfoSnapshot().getLastUpdated());
        assertEquals(0, stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNull(stopValue.getResult().getAmount());
        
        assertEquals(forcedPrice, stopValue.getBillingInfoSnapshot().getActualPricePerMinute());
        assertEquals(configuredPrice, stopValue.getBillingInfoSnapshot().getPricePerMinute());

    }
     
    
    @Test
    public void runStopWithoutStartingShouldReturnZeroCharge() throws InterruptedException, ExecutionException {
        long minIncrement = 1;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        event.setStartTimeStamp(System.currentTimeMillis());
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());
        
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 8300, TimeUnit.MILLISECONDS);

        BillingInfoDetails stopValue = futureStopChargeValue.get();
        assertTrue(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNull(stopValue.getResult().getAmount());
    }  
    
    
    @Test
    public void emergenctStopOngoingCall() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        //Start billing
        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - right after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue0 = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-0 "), 0, TimeUnit.MILLISECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - 1 seconds after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue1= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-1 "), 1000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue2= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-2 "), 2000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue3= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-3 "), 3000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue4= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-4 "), 4000, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue5= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-5 "), 5000, TimeUnit.MILLISECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - 6 seconds after the call has started - time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue6= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-6 "), 6000, TimeUnit.MILLISECONDS);
        //The call has emergency stop after 6020 millisecs
        ScheduledFuture<BillingInfoDetails> futureEmergencyStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runEmergencyStop(crgCtx), 6020, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta0 = futureDeltaValue0.get();
        BillingInfoDetails delta1 = futureDeltaValue1.get();
        BillingInfoDetails delta2 = futureDeltaValue2.get();
        BillingInfoDetails delta3 = futureDeltaValue3.get();
        BillingInfoDetails delta4 = futureDeltaValue4.get();
        BillingInfoDetails delta5 = futureDeltaValue5.get();
        BillingInfoDetails delta6 = futureDeltaValue6.get();
        BillingInfoDetails emergencyStopValue = futureEmergencyStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertFalse(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(startChargeValue.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta0.getBillingInfoSnapshot().getStatus());
        assertTrue(delta0.getResult().shouldSkipQuotaUpdate());
        assertNull(delta0.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());

        assertEquals(BillingInfo.Status.STARTED, delta2.getBillingInfoSnapshot().getStatus());
        assertTrue(delta2.getResult().shouldSkipQuotaUpdate());
        assertNull(delta2.getResult().getAmount());

        assertEquals(BillingInfo.Status.STARTED, delta3.getBillingInfoSnapshot().getStatus());
        assertTrue(delta3.getResult().shouldSkipQuotaUpdate());
        assertNull(delta3.getResult().getAmount());

        assertEquals(BillingInfo.Status.STARTED, delta4.getBillingInfoSnapshot().getStatus());
        assertTrue(delta4.getResult().shouldSkipQuotaUpdate());
        assertNull(delta4.getResult().getAmount());

        assertEquals(BillingInfo.Status.STARTED, delta5.getBillingInfoSnapshot().getStatus());
        assertFalse(delta5.getResult().shouldSkipQuotaUpdate());
        assertNotNull(delta5.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta6.getBillingInfoSnapshot().getStatus());
        assertTrue(delta6.getResult().shouldSkipQuotaUpdate());
        assertNull(delta6.getResult().getAmount());
        
        assertNotNull(emergencyStopValue);
        assertEquals(BillingInfo.Status.EMERGENCY_ENDED, emergencyStopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(emergencyStopValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(emergencyStopValue.getResult().getAmount());
        assertTrue(emergencyStopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Refund
        assertTrue(emergencyStopValue.getResult().shouldRefund()); //Refund
        assertFalse(emergencyStopValue.getResult().shouldConsume()); //NOT consume
        assertEquals(7, emergencyStopValue.getBillingInfoSnapshot().getCallDurationInSeconds());
        
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta0.getBillingInfoSnapshot().getSecondsChargedSoFar(), startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta1.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta0.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta2.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta3.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta2.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta4.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta3.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(delta5.getBillingInfoSnapshot().getSecondsChargedSoFar() > delta4.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(delta6.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta5.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(emergencyStopValue.getBillingInfoSnapshot().getSecondsChargedSoFar(), delta6.getBillingInfoSnapshot().getSecondsChargedSoFar());
        
        assertTrue(startChargeValue.getBillingInfoSnapshot().getLastUpdated() > 0);
        assertTrue(delta0.getBillingInfoSnapshot().getLastUpdated() == startChargeValue.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta1.getBillingInfoSnapshot().getLastUpdated() == delta0.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta2.getBillingInfoSnapshot().getLastUpdated() == delta1.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta3.getBillingInfoSnapshot().getLastUpdated() == delta2.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta4.getBillingInfoSnapshot().getLastUpdated() == delta3.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta5.getBillingInfoSnapshot().getLastUpdated() > delta4.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(delta6.getBillingInfoSnapshot().getLastUpdated() == delta5.getBillingInfoSnapshot().getLastUpdated());
        assertTrue(emergencyStopValue.getBillingInfoSnapshot().getLastUpdated() > delta6.getBillingInfoSnapshot().getLastUpdated());

        assertEquals(startChargeValue.getBillingInfoSnapshot().getIterationsCounter(), 0);
        assertEquals(delta0.getBillingInfoSnapshot().getIterationsCounter(), 1);
        assertEquals(delta1.getBillingInfoSnapshot().getIterationsCounter(), 2);
        assertEquals(delta2.getBillingInfoSnapshot().getIterationsCounter(), 3);
        assertEquals(delta3.getBillingInfoSnapshot().getIterationsCounter(), 4);
        assertEquals(delta4.getBillingInfoSnapshot().getIterationsCounter(), 5);
        assertEquals(delta5.getBillingInfoSnapshot().getIterationsCounter(), 0);
        assertEquals(delta6.getBillingInfoSnapshot().getIterationsCounter(), 1);
        assertEquals(emergencyStopValue.getBillingInfoSnapshot().getIterationsCounter(), -1);
        
        assertEquals(configuredPrice, emergencyStopValue.getBillingInfoSnapshot().getActualPricePerMinute());

    }


    @Test
    public void emergencyStopNotStartedYetCall() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        //The call has emergency stop after 20 millisecs
        ScheduledFuture<BillingInfoDetails> futureEmergencyStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runEmergencyStop(crgCtx), 20, TimeUnit.MILLISECONDS);

        BillingInfoDetails emergencyStopValue = futureEmergencyStopChargeValue.get();

        assertNotNull(emergencyStopValue);
        assertEquals(BillingInfo.Status.EMERGENCY_ENDED, emergencyStopValue.getBillingInfoSnapshot().getStatus());
        assertTrue(emergencyStopValue.getResult().shouldSkipQuotaUpdate());
        assertFalse(emergencyStopValue.getResult().shouldRefund()); //Refund
        assertFalse(emergencyStopValue.getResult().shouldConsume()); //NOT consume
        assertEquals(0, emergencyStopValue.getBillingInfoSnapshot().getCallDurationInSeconds());
        
        assertEquals(emergencyStopValue.getBillingInfoSnapshot().getIterationsCounter(), -1);
        
        assertEquals(configuredPrice, emergencyStopValue.getBillingInfoSnapshot().getActualPricePerMinute());

    }

    
    @Test
    public void emergenctStopEndedCall() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        //Start billing
        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - right after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue0 = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-0 "), 0, TimeUnit.MILLISECONDS);
        //The billing info was picked-up by the QuotaUpdateor task - 1 seconds after the call has started - still not time to update
        ScheduledFuture<BillingInfoDetails> futureDeltaValue1= SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx,"Delta-1 "), 1000, TimeUnit.MILLISECONDS);
        //The call has ended after 1500 millisecs
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 1500, TimeUnit.MILLISECONDS);
        //Emergency stop had happened
        ScheduledFuture<BillingInfoDetails> futureEmergencyStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runEmergencyStop(crgCtx), 1502, TimeUnit.MILLISECONDS);

        
        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta0 = futureDeltaValue0.get();
        BillingInfoDetails delta1 = futureDeltaValue1.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();
        BillingInfoDetails emergencyStopValue = futureEmergencyStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertFalse(startChargeValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(startChargeValue.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta0.getBillingInfoSnapshot().getStatus());
        assertTrue(delta0.getResult().shouldSkipQuotaUpdate());
        assertNull(delta0.getResult().getAmount());
        
        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertTrue(delta1.getResult().shouldSkipQuotaUpdate());
        assertNull(delta1.getResult().getAmount());

        
        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNotNull(stopValue.getResult().getAmount());
        assertTrue(stopValue.getResult().getAmount().compareTo(BigDecimal.ZERO) > 0); //Refund
        assertTrue(stopValue.getResult().shouldRefund()); //Refund
        assertFalse(stopValue.getResult().shouldConsume()); //NOT consume
        
        assertEquals(startChargeValue.getBillingInfoSnapshot().getIterationsCounter(), 0);
        assertEquals(delta0.getBillingInfoSnapshot().getIterationsCounter(), 1);
        assertEquals(delta1.getBillingInfoSnapshot().getIterationsCounter(), 2);
        assertEquals(stopValue.getBillingInfoSnapshot().getIterationsCounter(), -1);
        
        assertEquals(configuredPrice, stopValue.getBillingInfoSnapshot().getActualPricePerMinute());
        
        assertNull(emergencyStopValue);

    }
    
    @Test
    public void emergenctStopcallInError() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;
        BigDecimal cost = BigDecimal.TEN;
        
        TestCdrEvent event = new TestCdrEvent("testEvent");
        
        BillingInfo crgCtx = BillingTestsHelper.createContextWithError(minIncrement, recurringIncrement, configuredPrice, forcedPrice, cost);
        assertEquals(BillingInfo.Status.ERROR, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStopWithErrorCDR(crgCtx, event), 2200, TimeUnit.MILLISECONDS);
        //Emergency stop had happened
        ScheduledFuture<BillingInfoDetails> futureEmergencyStopValue = SCHEDULER.schedule(BillingTestsHelper.runEmergencyStop(crgCtx), 2300, TimeUnit.MILLISECONDS);

        BillingInfoDetails stopValue = futureStopChargeValue.get();
        BillingInfoDetails emergencyStopValue = futureEmergencyStopValue.get();

        assertEquals(BillingInfo.Status.ERROR, stopValue.getBillingInfoSnapshot().getStatus());
        assertEquals(0, stopValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertTrue(stopValue.getResult().shouldSkipQuotaUpdate());
        assertNull(stopValue.getResult().getAmount()); 
        
        assertNull(emergencyStopValue);

    }


    @Test
    public void roundupSecsTest() {
        assertEquals(BillingInfo.roundUpToSeconds(0), 0);
        assertEquals(BillingInfo.roundUpToSeconds(1000), 1);
        assertEquals(BillingInfo.roundUpToSeconds(1500), 2);
        assertEquals(BillingInfo.roundUpToSeconds(16540), 17);
        assertEquals(BillingInfo.roundUpToSeconds(16130), 17);
    }

    @Test
    public void shouldHandleEmergencyStopChargingTest() throws Exception {

        assertTrue(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.NOT_STARTED));
        assertTrue(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.STARTED));
        assertTrue(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.NO_CHARGES));

        assertFalse(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.ENDED));
        assertFalse(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.ERROR));
        assertFalse(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.OUT_OF_FUNDS_DURING_CALL));
        assertFalse(BillingInfo.isEmergencyStopChargingRequired(BillingInfo.Status.OUT_OF_FUNDS_DURING_START));
    }

    @Test
    public void shouldHaveAHigherReportBillAbleSecondsGivenByANSWEREDTIME_MS() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent", AsteriskVersion.V16);
        event.setUserField("HANGUPCAUSE=16;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=9000");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx, "Delta-6300 "), 6300, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 7300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(6L));


        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertTrue(stopValue.getResult().shouldRefund()); //Refund

        //2 times the minIncrement
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(18, delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertThat(stopValue.getBillingInfoSnapshot().getCallDurationInSeconds(),is(9L));
    }

    @Test
    public void shouldHaveAHigherReportBillAbleSecondsGivenByANSWEREDTIME_MS_V20() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent", AsteriskVersion.V20);
        event.setUserField("HANGUPCAUSE=16;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=9000");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx, "Delta-6300 "), 6300, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 7300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(6L));


        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertTrue(stopValue.getResult().shouldRefund()); //Refund

        //2 times the minIncrement
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(18, delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertThat(stopValue.getBillingInfoSnapshot().getCallDurationInSeconds(),is(9L));
    }

    @Test
    public void shouldUseCdrEventBillAbleSecondsGivenBadANSWEREDTIME_MS() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent", AsteriskVersion.V16);
        event.setUserField("HANGUPCAUSE=16;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=12xx");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx, "Delta-6300 "), 6300, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 7300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(6L));


        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertTrue(stopValue.getResult().shouldRefund()); //Refund

        //2 times the minIncrement
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(18, delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertThat(stopValue.getBillingInfoSnapshot().getCallDurationInSeconds(),is(7L));
    }

    @Test
    public void shouldUseCdrEventBillAbleSecondsGivenBadANSWEREDTIME_MS_V20() throws InterruptedException, ExecutionException {
        long minIncrement = 6;
        long recurringIncrement = 6;
        BigDecimal forcedPrice = null;
        BigDecimal configuredPrice = BigDecimal.TEN;

        TestCdrEvent event = new TestCdrEvent("testEvent", AsteriskVersion.V20);
        event.setUserField("HANGUPCAUSE=16;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=12xx");

        BillingInfo crgCtx = BillingTestsHelper.createContextWithUnits(minIncrement, recurringIncrement, configuredPrice, forcedPrice, BigDecimal.ONE);
        assertEquals(BillingInfo.Status.NOT_STARTED, crgCtx.getStatus());

        ScheduledFuture<BillingInfoDetails> futureStartChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStart(crgCtx, event), 0, TimeUnit.SECONDS);
        ScheduledFuture<BillingInfoDetails> futureDeltaValue = SCHEDULER.schedule(BillingTestsHelper.runDelta(crgCtx, "Delta-6300 "), 6300, TimeUnit.MILLISECONDS);
        ScheduledFuture<BillingInfoDetails> futureStopChargeValue = SCHEDULER.schedule(BillingTestsHelper.runStop(crgCtx, event), 7300, TimeUnit.MILLISECONDS);

        BillingInfoDetails startChargeValue = futureStartChargeValue.get();
        BillingInfoDetails delta1 = futureDeltaValue.get();
        BillingInfoDetails stopValue = futureStopChargeValue.get();

        assertEquals(BillingInfo.Status.STARTED, startChargeValue.getBillingInfoSnapshot().getStatus());
        assertThat(startChargeValue.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(0L));

        assertEquals(BillingInfo.Status.STARTED, delta1.getBillingInfoSnapshot().getStatus());
        assertThat(delta1.getBillingInfoSnapshot().getTotalNumberOfIterations(), is(6L));


        assertEquals(BillingInfo.Status.ENDED, stopValue.getBillingInfoSnapshot().getStatus());
        assertFalse(stopValue.getResult().shouldSkipQuotaUpdate());
        assertTrue(stopValue.getResult().shouldRefund()); //Refund

        //2 times the minIncrement
        assertEquals(12, startChargeValue.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertEquals(18, delta1.getBillingInfoSnapshot().getSecondsChargedSoFar());
        assertThat(stopValue.getBillingInfoSnapshot().getCallDurationInSeconds(),is(7L));
    }
}
