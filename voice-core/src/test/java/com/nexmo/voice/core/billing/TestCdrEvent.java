package com.nexmo.voice.core.billing;


import com.nexmo.voice.core.sip.event.CdrEventHandler;
import com.nexmo.voice.core.sip.event.CdrEventUserData;
import com.nexmo.voice.core.sip.event.VoiceEventHandlerException;
import com.nexmo.voice.core.types.AsteriskVersion;
import org.apache.commons.lang3.StringUtils;
import org.asteriskjava.manager.event.CdrEvent;

import java.text.SimpleDateFormat;
import java.util.Date;

import static org.apache.commons.lang3.StringUtils.isEmpty;

public class TestCdrEvent extends CdrEvent {

    public long startTimeStamp;
    public long endTimeStamp;

    public TestCdrEvent(Object source) {
        super(source);
    }

    public TestCdrEvent(Object source, AsteriskVersion asteriskVersion) {
        super(source);
        setUniqueId(String.format("asterisk%s-1-1633447465.62", asteriskVersion.getVersion()));
    }

    public long getStartTimeStamp() {
        return startTimeStamp;
    }

    public void setStartTimeStamp(long startTimeStamp) {
        this.startTimeStamp = startTimeStamp;
    }

    public long getEndTimeStamp() {
        return endTimeStamp;
    }

    public void setEndTimeStamp(long endTimeStamp) {
        this.endTimeStamp = endTimeStamp;
    }

    public String formatToCdrEventDate(long timeStamp) {
        Date timeDate = new Date(timeStamp);
        SimpleDateFormat cdrDateFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        return cdrDateFormat.format(timeDate);
    }

    public void generateCdrEvent() {
        setEndTimeStamp(System.currentTimeMillis());

        long lengthInMillis = getEndTimeStamp() - getStartTimeStamp();
        long lengthInSecs = lengthInMillis / 1000;
        setBillableSeconds(Long.valueOf(lengthInSecs).intValue());

        setStartTime(formatToCdrEventDate(getStartTimeStamp()));
        setEndTime(formatToCdrEventDate(getEndTimeStamp()));
    }

    public void generateErrorCdrEvent() {
        setEndTimeStamp(System.currentTimeMillis());

        setBillableSeconds(0);

        setStartTime(formatToCdrEventDate(getStartTimeStamp()));
        setEndTime(formatToCdrEventDate(getEndTimeStamp()));
    }

    public TestCdrEvent copy(){
        final TestCdrEvent testCdrEvent = new TestCdrEvent(this.source);
        testCdrEvent.setBillableSeconds(this.getBillableSeconds());
        testCdrEvent.endTimeStamp = this.endTimeStamp;
        testCdrEvent.startTimeStamp = this.startTimeStamp;
        testCdrEvent.setUserField(getUserField());
        testCdrEvent.setStartTime(getStartTime());
        testCdrEvent.setEndTime(getEndTime());
        return testCdrEvent;
    }

    public CdrEventUserData generateCdrEventUserData(String connectionId) throws VoiceEventHandlerException {
        TestCdrEvent event = this;
        if(isEmpty(this.getUserField())){
            //duplicate and set a dummy userField to avoid exception
            event = this.copy();
            event.setUserField("HANGUPCAUSE=16;DIALSTATUS=ANSWER;");
        }
        if(StringUtils.isEmpty(getUniqueId())){
            return CdrEventUserData.of(AsteriskVersion.V1_8,  event, connectionId);
        }
        return CdrEventUserData.of(CdrEventHandler.toAsteriskVersion(getUniqueId()),  event, connectionId);
    }
}