package com.nexmo.voice.core.types;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.util.Arrays;

import static com.nexmo.voice.core.types.CarrierPlatform.INPS;
import static com.nexmo.voice.core.types.CarrierPlatform.NET;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(Parameterized.class)
public class CarrierPlatformTest {

    private final String input;
    private final CarrierPlatform target;

    public CarrierPlatformTest(String input, CarrierPlatform target) {
        this.input = input;
        this.target = target;
    }

    @Parameterized.Parameters(name = "{index}: {0} {1}")
    public static Iterable<Object[]> data() {
        return Arrays.asList(new Object[][]{
                {null, null},
                {"", null},
                {"9", null},
                {"1", INPS},
                {" 1 ", INPS},
                {"0", NET},
                {" 0 ", NET}
        });
    }

    @Test
    public void shouldBeAbleToCovertIntoCarrierPlatform() {
        assertThat(CarrierPlatform.from(input), equalTo(target));
    }
}
