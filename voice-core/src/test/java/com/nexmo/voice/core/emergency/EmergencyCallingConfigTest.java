package com.nexmo.voice.core.emergency;

import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import static org.junit.Assert.*;

public class EmergencyCallingConfigTest {

    @Test
    public void setLocaleConfig() throws Exception {
        String[] routeArray = new String[]{"routeA", "routeB"};

        EmergencyCallingLocaleConfig localeConfig = new EmergencyCallingLocaleConfig();
        localeConfig.setCountry("US");
        localeConfig.setEmergencyNumbers(Collections.singleton("911"));
        localeConfig.setRoute(Arrays.asList(routeArray));
        Set<EmergencyCallingLocaleConfig> locales = Collections.singleton(localeConfig);

        EmergencyCallingConfig config = new EmergencyCallingConfig();
        config.setLocaleConfig(locales);

        assertNotNull(config);
        assertTrue(config.isEmergencyServiceNumber("911"));
        assertFalse(config.isEmergencyServiceNumber("999"));
        assertFalse(config.isEmergencyServiceNumber("112"));
        assertFalse(config.isEmergencyServiceNumber("***********"));

        assertTrue(config.isEmergencyServiceNumberForLocale("US", "911"));
        assertFalse(config.isEmergencyServiceNumberForLocale("CA", "911"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "999"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "112"));
        assertFalse(config.isEmergencyServiceNumberForLocale("US", "***********"));

        assertNotNull(config.getEmergencyServiceRoutingForLocale("US", "911"));
        assertEquals(routeArray[0], config.getEmergencyServiceRoutingForLocale("US", "911").get(0));
        assertEquals(routeArray[1], config.getEmergencyServiceRoutingForLocale("US", "911").get(1));
    }

    @Test
    public void setLocaleConfigEmptySet() {
        Set<EmergencyCallingLocaleConfig> locales = Collections.emptySet();

        EmergencyCallingConfig config = new EmergencyCallingConfig();
        config.setLocaleConfig(locales);

        // the locales set was empty, we should get back false for everything
        assertFalse(config.isEmergencyServiceNumber("911"));
        assertFalse(config.isEmergencyServiceNumber("999"));
        assertFalse(config.isEmergencyServiceNumber("112"));
        assertFalse(config.isEmergencyServiceNumber("***********"));

        assertFalse(config.isEmergencyServiceNumberForLocale("US", "911"));
        assertFalse(config.isEmergencyServiceNumberForLocale("CA", "911"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "999"));
        assertFalse(config.isEmergencyServiceNumberForLocale("GB", "112"));
        assertFalse(config.isEmergencyServiceNumberForLocale("US", "***********"));

        EmergencyCallingException exception = assertThrows(
                EmergencyCallingException.class,
                () -> config.getEmergencyServiceRoutingForLocale("US", "911")
        );
        assertEquals("No routing for locale=US, number=911", exception.getMessage());
    }

    @Test
    public void testIsAllowedLvnType() {
        EmergencyCallingConfig config = new EmergencyCallingConfig();

        assertTrue(config.isAllowedLvnType(ShortCodeType.LANDLINE));
        assertTrue(config.isAllowedLvnType(ShortCodeType.LANDLINE_PREMIUM));
        assertTrue(config.isAllowedLvnType(ShortCodeType.MOBILE_LVN));

        assertFalse(config.isAllowedLvnType(ShortCodeType.UNKNOWN));
        assertFalse(config.isAllowedLvnType(ShortCodeType.LANDLINE_TOLL_FREE));
        assertFalse(config.isAllowedLvnType(ShortCodeType.MOBILE_SHORTCODE));
        assertFalse(config.isAllowedLvnType(ShortCodeType.NATIONAL));
        assertFalse(config.isAllowedLvnType(ShortCodeType.VERIFIED_CLI));

        assertFalse(config.isAllowedLvnType(null));
    }

    @Test
    public void testIsAllowedLvnTypeWithAllowByonEnabled() {
        EmergencyCallingConfig config = new EmergencyCallingConfig();
        config.setAllowFromByon(true);

        assertTrue(config.isAllowedLvnType(ShortCodeType.VERIFIED_CLI));

        assertTrue(config.isAllowedLvnType(ShortCodeType.LANDLINE));
        assertTrue(config.isAllowedLvnType(ShortCodeType.LANDLINE_PREMIUM));
        assertTrue(config.isAllowedLvnType(ShortCodeType.MOBILE_LVN));

        assertFalse(config.isAllowedLvnType(ShortCodeType.UNKNOWN));
        assertFalse(config.isAllowedLvnType(ShortCodeType.LANDLINE_TOLL_FREE));
        assertFalse(config.isAllowedLvnType(ShortCodeType.MOBILE_SHORTCODE));
        assertFalse(config.isAllowedLvnType(ShortCodeType.NATIONAL));

        assertFalse(config.isAllowedLvnType(null));
    }


}