package com.nexmo.voice.core.stirshaken;

import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.stirshaken.impl.SIPEnforcerService;
import com.nexmo.voice.core.types.VoiceProduct;
import com.thepeachbeetle.messaging.hub.core.exceptions.ShortCodeException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.stream.Stream;

import static com.nexmo.voice.core.stirshaken.Attestation.*;
import static com.nexmo.voice.core.types.VoiceProduct.*;
import static java.util.UUID.randomUUID;
import static java.util.stream.Stream.of;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.doThrow;


@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.script.*"})
public class SIPEnforcerServiceTest extends EnforcerServiceTest {

    private static final Stream<VoiceProduct> PRODUCTS = of(SIP, CALL_API);

    @BeforeClass
    public static void init() throws ShortCodeException {
        EnforcerServiceTest.init();
        doReturn(new SIPEnforcerService()).when(CORE)
                .getSIPEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getTTSEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getVBCEnforcerService();
        doThrow(new IllegalStateException()).when(CORE)
                .getVerifyEnforcerService();
    }

    @Test(expected = IllegalArgumentException.class)
    @PrepareForTest(Core.class)
    public void shouldBeEmptyGivenNonSIPAndTTSProductClass() {
        EnforcerServiceFactory.byProductClass(UNKNOWN)
                .attestationLevel(
                        AttestationValidationParams.builder()
                                .withAccountId(randomUUID().toString())
                                .withHasKyc(true)
                                .withHasDisableMustOwnLVN(true)
                                .withIsCallerIdE164(true)
                                .withIsOwnedLvn(true)
                                .withDestinationCountry(US)
                                .withGatewayName("vonage")
                                .withForcedSender(null)
                                .withSessionId(randomUUID().toString())
                                .build());
    }

    @Test(expected = IllegalStateException.class)
    @PrepareForTest(Core.class)
    public void shouldNotRunGivenNonSIPProductClass() {
        EnforcerServiceFactory.byProductClass(TTS)
                .attestationLevel(
                        AttestationValidationParams.builder()
                                .withAccountId(randomUUID().toString())
                                .withHasKyc(true)
                                .withHasDisableMustOwnLVN(true)
                                .withIsCallerIdE164(true)
                                .withIsOwnedLvn(false)
                                .withDestinationCountry(US)
                                .withGatewayName("vonage")
                                .withForcedSender(null)
                                .withSessionId(randomUUID().toString())
                                .build());
    }


    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveNoneAttestationGivenNonVonageGateway() {
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(randomUUID().toString())
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(US)
                                                .withGatewayName("ibasis")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(NONE)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveNoneAttestationGivenExcludedDestination() {
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(randomUUID().toString())
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(GB)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(NONE)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassCAttestationGivenNonKYCAccount() {
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(randomUUID().toString())
                                                .withHasKyc(false)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(US)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_C)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassBAttestationGivenNonE164CallerId() {
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(randomUUID().toString())
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(false)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(US)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_B)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassAAttestationGivenMustOwnLvnCapabilityAndOwnedCallerId() throws ShortCodeException {
        String accountId = randomUUID().toString();
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(accountId)
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(true)
                                                .withDestinationCountry(US)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_A)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassBAttestationGivenMustOwnLvnCapabilityButNotOwnedCallerId() throws ShortCodeException {
        String accountId = randomUUID().toString();
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(accountId)
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(true)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(US)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_B)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassAAttestationGivenOwnedCallerIdButNotMustOwnLvnCapability() throws ShortCodeException {
        String accountId = randomUUID().toString();
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(accountId)
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(false)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(true)
                                                .withDestinationCountry(CA)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_A)));

    }

    @Test
    @PrepareForTest(Core.class)
    public void shouldHaveClassBAttestationNotGivenMustOwnLvnCapabilityAndCallerId() throws ShortCodeException {
        String accountId = randomUUID().toString();
        PRODUCTS.forEach(voiceProduct ->
                assertThat(
                        EnforcerServiceFactory.byProductClass(voiceProduct)
                                .attestationLevel(
                                        AttestationValidationParams.builder()
                                                .withAccountId(accountId)
                                                .withHasKyc(true)
                                                .withHasDisableMustOwnLVN(false)
                                                .withIsCallerIdE164(true)
                                                .withIsOwnedLvn(false)
                                                .withDestinationCountry(US)
                                                .withGatewayName("vonage")
                                                .withForcedSender(null)
                                                .withSessionId(randomUUID().toString())
                                                .build()),
                        is(CLASS_B)));

    }

}
