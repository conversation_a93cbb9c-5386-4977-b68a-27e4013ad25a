package com.nexmo.voice.core.billing;

import com.nexmo.voice.core.billing.BillingInfo;
import com.nexmo.voice.core.billing.QuotaUpdateDetails;

/**
 * This class goal is to record the BillingInfo details after every operation for later assertions
 * <AUTHOR>
 *
 */

public class BillingInfoDetails  {
    
    private BillingInfo billingInfoSnapshot;    
    private QuotaUpdateDetails result;
    
    public BillingInfoDetails(BillingInfo billingInfoSnapshot, QuotaUpdateDetails result) {
	this.billingInfoSnapshot = billingInfoSnapshot;
	this.result = result;
    }
    
    public BillingInfo getBillingInfoSnapshot() {
        return billingInfoSnapshot;
    }

    public QuotaUpdateDetails getResult() {
        return result;
    }

 
     
}
