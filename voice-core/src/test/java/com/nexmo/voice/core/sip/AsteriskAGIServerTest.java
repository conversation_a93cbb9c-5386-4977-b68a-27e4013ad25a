package com.nexmo.voice.core.sip;

import com.nexmo.voice.config.Config;
import com.nexmo.voice.core.Core;
import com.nexmo.voice.core.emergency.EmergencyCallProperties;
import com.nexmo.voice.core.emergency.EmergencyCallingConfig;
import com.nexmo.voice.core.emergency.EmergencyCallingException;
import com.nexmo.voice.core.routing.RouteData;
import com.nexmo.voice.core.routing.RouteNullPointerException;
import com.thepeachbeetle.messaging.hub.config.accounts.SmppAccount;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCode;
import com.thepeachbeetle.messaging.hub.config.shortcodes.ShortCodeType;
import com.thepeachbeetle.messaging.hub.core.exceptions.DropMessageException;
import com.thepeachbeetle.messaging.hub.core.exceptions.RoutingException;
import org.apache.log4j.BasicConfigurator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AsteriskAGIServerTest {

    private MockedStatic<Core> coreMockedStatic;
    private MockedStatic<AsteriskAGIServerSIPHandler> sipHandlerMockedStatic;


    @Before
    public void beforeTest() {
        BasicConfigurator.configure();
        coreMockedStatic = mockStatic(Core.class);
        sipHandlerMockedStatic = mockStatic(AsteriskAGIServerSIPHandler.class);
    }

    @After
    public void afterTest() {
        coreMockedStatic.close();
        sipHandlerMockedStatic.close();
    }

    @Test
    public void testIsOwnedLvnIsOwnedByAccount() {
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertTrue(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsOwnedLvnNotKnownShortCode() {
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        ShortCode shortCode = null;

        assertFalse(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsOwnedLvnNotOwnedByAnyAccount() {
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(null);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertFalse(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsOwnedLvnIsOwnedByUnrelatedAccount() {
        String apiKey = UUID.randomUUID().toString();
        String otherApiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(otherApiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertFalse(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));

    }

    @Test
    public void testIsOwnedLvnIsOwnedByParentAccountWithoutCanShare() {
        String apiKey = UUID.randomUUID().toString();
        String parentApiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getMasterAccountId()).thenReturn(parentApiKey);
        when(account.isMasterAccountShareMasterShortCodes()).thenReturn(false);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(parentApiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertFalse(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsOwnedLvnIsOwnedByParentAccountWithCanShare() {
        // This scenario is used for emergency calling where for a child account to place
        // emergency calls using the parent account's owned LVN.
        String apiKey = UUID.randomUUID().toString();
        String parentApiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getMasterAccountId()).thenReturn(parentApiKey);
        when(account.isMasterAccountShareMasterShortCodes()).thenReturn(true);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(parentApiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertTrue("THIS BREAKS EMERGENCY CALLING", AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsOwnedLvnWithCanShareIsOwnedByDifferentAccount() {
        String apiKey = UUID.randomUUID().toString();
        String parentApiKey = UUID.randomUUID().toString();
        String shortCodeApiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();
        String lvn = "***********";

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getMasterAccountId()).thenReturn(parentApiKey);
        when(account.isMasterAccountShareMasterShortCodes()).thenReturn(true);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(shortCodeApiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);

        assertFalse(AsteriskAGIServer.isOwnedLvn(account, shortCode, lvn, sessionId));
    }

    @Test
    public void testIsEmergencyCall() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("US"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.getEmergencyServiceRoutingForLocale(anyString(), anyString())).thenReturn(Collections.singletonList("gateway"));
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertTrue(props.isPresent());
        assertEquals(fromPhoneNumber, props.get().getFromNumber());
        assertEquals(locale.toUpperCase(), props.get().getLocale());
        assertEquals(toPhoneNumber, props.get().getToEmergencyNumber());
    }

    @Test
    public void testIsEmergencyCallNullAccount() {
        SmppAccount account = null;
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "***********";
        String sessionId = UUID.randomUUID().toString();

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallEmergencyCallingNotEnabled() {
        SmppAccount account = mock(SmppAccount.class);
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "***********";
        String sessionId = UUID.randomUUID().toString();

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = null;
        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallUnknownLvn() {
        SmppAccount account = mock(SmppAccount.class);
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "***********";
        String sessionId = UUID.randomUUID().toString();

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = null;
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallAccountDoesNotHaveCapability() {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallRequiresCapabilityLocaleButNotSetForLocale() {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("GB")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("GB"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();
        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallRequiresCapabilityLocaleButNullCapabilityTags() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(null);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallNoLocaleOnLVNButValidNumberForSupportedCountry() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("US"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.getEmergencyServiceRoutingForLocale(anyString(), anyString())).thenReturn(Collections.singletonList("gateway"));
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(null);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertTrue(props.isPresent());
        assertEquals(fromPhoneNumber, props.get().getFromNumber());
        assertEquals(locale.toUpperCase(), props.get().getLocale());
        assertEquals(toPhoneNumber, props.get().getToEmergencyNumber());
    }

    @Test
    public void testIsEmergencyCallNoLocaleOnLVNButNotValidE164Number() throws Exception {
        String fromPhoneNumber = "********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(null);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallNotSupportedNumber() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        // account capabilities now checked after qualifying destination number

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallNoRouting() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "us";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("US"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.getEmergencyServiceRoutingForLocale(anyString(), anyString())).thenThrow(EmergencyCallingException.class);
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallUppercaseLocaleTag() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("US"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.getEmergencyServiceRoutingForLocale(anyString(), anyString())).thenReturn(Collections.singletonList("gateway"));
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertTrue(props.isPresent());
        assertEquals(fromPhoneNumber, props.get().getFromNumber());
        assertEquals(locale.toUpperCase(), props.get().getLocale());
        assertEquals(toPhoneNumber, props.get().getToEmergencyNumber());
    }

    @Test
    public void testIsEmergencyCallVerifiedCliTypeIsNotAllowed() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.VERIFIED_CLI);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallVerifiedCliTypeIsAllowed() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);
        when(account.getCapabilities()).thenReturn(Collections.singleton("rtc-emergency-calling"));
        when(account.getCapabilityTags()).thenReturn(Collections.singletonMap("rtc-emergency-calling", Collections.singleton("US")));
        when(account.getCapabilityTagsAsString()).thenReturn("capabilityTagsAsString");
        when(account.getCapabilityTags("rtc-emergency-calling")).thenReturn(Collections.singleton("US"));

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isRequireCapabilityLocale()).thenReturn(true);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);
        when(emergencyCallingConfig.getEmergencyServiceRoutingForLocale(anyString(), anyString())).thenReturn(Collections.singletonList("gateway"));
        when(emergencyCallingConfig.isAllowFromByon()).thenReturn(true);
        when(emergencyCallingConfig.isAllowedLvnType(any(ShortCodeType.class))).thenCallRealMethod();

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getCountry()).thenReturn(locale);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.VERIFIED_CLI);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertTrue(props.isPresent());
        assertEquals(fromPhoneNumber, props.get().getFromNumber());
        assertEquals(locale.toUpperCase(), props.get().getLocale());
        assertEquals(toPhoneNumber, props.get().getToEmergencyNumber());
    }

    @Test
    public void testIsEmergencyCallUnknownType() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.UNKNOWN);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallMobileShortcodeType() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.MOBILE_SHORTCODE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallLandlineTollFreeType() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.LANDLINE_TOLL_FREE);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }

    @Test
    public void testIsEmergencyCallNationalType() throws Exception {
        String fromPhoneNumber = "***********";
        String toPhoneNumber = "933";
        String locale = "US";
        String apiKey = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        SmppAccount account = mock(SmppAccount.class);
        when(account.getSysId()).thenReturn(apiKey);

        Core core = mock(Core.class);
        Config config = mock(Config.class);
        EmergencyCallingConfig emergencyCallingConfig = mock(EmergencyCallingConfig.class);
        when(emergencyCallingConfig.isEmergencyServiceNumberForLocale(anyString(), anyString())).thenReturn(true);

        when(config.getEmergencyCallingConfig()).thenReturn(emergencyCallingConfig);
        when(core.getConfig()).thenReturn(config);
        coreMockedStatic.when(Core::getInstance).thenReturn(core);

        ShortCode shortCode = mock(ShortCode.class);
        when(shortCode.getAccountId()).thenReturn(apiKey);
        when(shortCode.getShortCodeType()).thenReturn(ShortCodeType.NATIONAL);
        sipHandlerMockedStatic.when(() -> AsteriskAGIServerSIPHandler.getShortCodeForNumber(anyString())).thenReturn(shortCode);

        Optional<EmergencyCallProperties> props = AsteriskAGIServer.isEmergencyCall(account, fromPhoneNumber, toPhoneNumber, sessionId);

        assertNotNull(props);
        assertFalse(props.isPresent());
    }


    @Test
    public void testDecideRouteWithDropMessageException() {
        String sessionId = UUID.randomUUID().toString();
        String outboundDestination = "***********";
        String outboundMccMnc = "outboundMccMnc";
        String accountId = "sip-1444";

        RouteData supplierIdRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                .withExc(new RouteNullPointerException("Failed to find a route for outbound destination: 1732910"))
                .build();
        Exception dropException = new DropMessageException("Drop Rule Exception",700);
        RouteData oaPrefixRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.DROPMESSAGE)
                .withExc(dropException)
                .build();

        RouteData routeData = AsteriskAGIServer.decideRoute(supplierIdRouteData, oaPrefixRouteData);
        assert routeData.getRouteExceptionType() == oaPrefixRouteData.getRouteExceptionType();

        DropMessageException e = (DropMessageException) routeData.getExc();
        long droppingRoutingRuleSeq = e.getRoutingRuleSequence();
        assert droppingRoutingRuleSeq == 700;
        assert e.getMessage().equals("Drop Rule Exception");
        String message = sessionId + " Route " + droppingRoutingRuleSeq + " DROPPING the call for outbound destination " + outboundDestination + " on netwrok " + outboundMccMnc + " for account " + accountId + " due to " + e.getMessage() + " " + e.getClass().getName();
        //System.out.println(message);
    }

    @Test
    public void testDecideRouteWithSupplierIdRoutingException() {
        String sessionId = UUID.randomUUID().toString();
        String outboundDestination = "***********";
        String outboundMccMnc = "outboundMccMnc";
        String accountId = "sip-1444";

        RouteData supplierIdRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTING_UNROUTABLE_RANDOMPOOL)
                .withExc(new RoutingException("Routing Exception to supplierId"))
                .build();
        RouteData oaPrefixRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                .withExc(new RouteNullPointerException("Failed to find route for destination 1919444"))
                .build();

        RouteData routeData = AsteriskAGIServer.decideRoute(supplierIdRouteData, oaPrefixRouteData);
        assert routeData.getRouteExceptionType() == supplierIdRouteData.getRouteExceptionType();

        Exception e = routeData.getExc();
        assert e.getMessage().equals("Routing Exception to supplierId");
        String message = sessionId + " Failed to find a route for outbound destination " + outboundDestination + " on netwrok " + outboundMccMnc + " for account " + accountId + " due to " + e.getMessage() + " " + e.getClass().getName();
        //System.out.println(message);
    }

    @Test
    public void testDecideRouteWithRouteNull() {
        String sessionId = UUID.randomUUID().toString();
        String outboundDestination = "***********";
        String outboundMccMnc = "outboundMccMnc";
        String accountId = "sip-1444";

        RouteData supplierIdRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                .withExc(new RouteNullPointerException("Routing Null Exception SupplierId"))
                .build();
        RouteData oaPrefixRouteData = new RouteData.Builder()
                .withRoute(null)
                .withExceptionType(RouteData.RouteExceptionType.ROUTENULL)
                .withExc(new RouteNullPointerException("Routing Null Exception OA prefix"))
                .build();

        RouteData routeData = AsteriskAGIServer.decideRoute(supplierIdRouteData, oaPrefixRouteData);
        assert routeData.getRouteExceptionType() == oaPrefixRouteData.getRouteExceptionType();

        Exception e = routeData.getExc();
        assert e.getMessage().equals("Routing Null Exception OA prefix");
        String message = sessionId + " No Route NullPointer DROPPING the call for outbound destination " + outboundDestination + " on network " + outboundMccMnc + " for account " + accountId + " due to " + e.getMessage() + " " + e.getClass().getName();
        //System.out.println(message);
    }
}