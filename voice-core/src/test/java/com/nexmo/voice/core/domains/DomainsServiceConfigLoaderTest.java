package com.nexmo.voice.core.domains;

import com.nexmo.voice.core.MockXmlContent;
import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.XmlContent;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;

public class DomainsServiceConfigLoaderTest {

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Test
    public void getConfigRequiredUrlNotSet() throws Exception {
        thrown.expect(LoaderException.class);
        thrown.expectMessage("Config doesn't contain url");

        Map<String, String> configMap = new HashMap<>();

        XmlContent xmlContent = new MockXmlContent(configMap);
        DomainsServiceConfigLoader loader = new DomainsServiceConfigLoader("domains-service");
        loader.startNode("domains-service", xmlContent);
        loader.endNode("domains-service", "");

        DomainsServiceConfig config = loader.getConfig();

        // we are expecting that there is an exception thrown instead of config returned
        assertNull(config);
    }


    @Test
    public void getConfigAllDefaults() throws Exception {
        String url = "http://path.to.service:8080/path/to/resource";

        Map<String, String> configMap = new HashMap<>();
        configMap.put("url", url);

        XmlContent xmlContent = new MockXmlContent(configMap);
        DomainsServiceConfigLoader loader = new DomainsServiceConfigLoader("domains-service");
        loader.startNode("domains-service", xmlContent);
        loader.endNode("domains-service", "");

        DomainsServiceConfig config = loader.getConfig();

        assertNotNull(config);
        assertEquals(url, config.getUrl());
        assertEquals(1000, config.getTimeout());
        assertEquals(true, config.isAuthEnabled());
        assertEquals("System", config.getAuthPrincipalType());
        assertEquals("SIPApp", config.getAuthPrincipalValue());
        assertEquals(true, config.isAuthUseTimestamp());
        assertEquals(null, config.getAuthPrivateKey());
        assertEquals(Collections.singleton("sip.vonage.com"), config.getDomainSuffixes());
    }

    @Test
    public void getConfigAllValuesPopulated() throws Exception {
        String url = "http://path.to.service:8080/path/to/resource";
        int timeout = 1500;
        boolean authEnabled = false;
        String authPrincipalType = "myType";
        String authPrincipalValue = "myValue";
        boolean authUseTimestamp = false;
        String authPrivateKey = "theAuthPrivateKeyValue";
        Set<String> domainSuffixes = Stream.of("mydomain1.example.tld", "mydomain2.example.tld").collect(Collectors.toSet());

        Map<String, String> configMap = new HashMap<>();
        configMap.put("url", url);
        configMap.put("timeout", Integer.toString(timeout));
        configMap.put("nexmo-auth-enabled", Boolean.toString(authEnabled));
        configMap.put("nexmo-auth-principal-type", authPrincipalType);
        configMap.put("nexmo-auth-principal-value", authPrincipalValue);
        configMap.put("nexmo-auth-use-timestamp", Boolean.toString(authUseTimestamp));
        configMap.put("nexmo-auth-private-key", authPrivateKey);
        configMap.put("supported-domain-suffixes", String.join(",", domainSuffixes));

        XmlContent xmlContent = new MockXmlContent(configMap);
        DomainsServiceConfigLoader loader = new DomainsServiceConfigLoader("domains-service");
        loader.startNode("domains-service", xmlContent);
        loader.endNode("domains-service", "");

        DomainsServiceConfig config = loader.getConfig();

        assertNotNull(config);
        assertEquals(url, config.getUrl());
        assertEquals(timeout, config.getTimeout());
        assertEquals(authEnabled, config.isAuthEnabled());
        assertEquals(authPrincipalType, config.getAuthPrincipalType());
        assertEquals(authPrincipalValue, config.getAuthPrincipalValue());
        assertEquals(authUseTimestamp, config.isAuthUseTimestamp());
        assertEquals(authPrivateKey, config.getAuthPrivateKey());
        assertEquals(domainSuffixes, config.getDomainSuffixes());
    }

}