package com.nexmo.voice.core.domains;

import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;

public class DomainsServiceClientTest {

    private static final String TEST_PRIVATE_KEY = "MHgCAQAwEAYHKoZIzj0CAQYFK4EEACEEYTBfAgEBBBzYQkDYiNSCe92Z9EGpTM6xZcxJ2/PGm9dWj5KjoTwDOgAEUmjfV1vMxqKa53+JXSqng0QC2C40lOatRD+pUtiHrzW23WRC9L4Yl5/uUT+KRCUG0UdPcqxC3nk=";
    private static final String TEST_PUBLIC_KEY = "ME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEUmjfV1vMxqKa53+JXSqng0QC2C40lOatRD+pUtiHrzW23WRC9L4Yl5/uUT+KRCUG0UdPcqxC3nk=";

    @Test
    public void testBuildNexmoAuthorizationHeaderWithTimestampEnabled() throws Exception {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setAuthPrincipalType("System");
        config.setAuthPrincipalValue("SIPApp");
        config.setAuthUseTimestamp(true);
        config.setAuthPrivateKey(TEST_PRIVATE_KEY);

        DomainsServiceClient client = new DomainsServiceClient(config);

        String authHeaderValue = client.buildNexmoAuthorizationHeader();

        assertNotNull(authHeaderValue);

        String[] authHeaderParts = authHeaderValue.split(", ");

        assertNotNull(authHeaderParts);
        assertEquals(3, authHeaderParts.length);
        assertEquals("System=SIPApp", authHeaderParts[0]);
        assertTrue(authHeaderParts[1].startsWith("Timestamp="));
        assertTrue(authHeaderParts[2].startsWith("Signature="));

        // lets check the signature
        String[] signatureParts = authHeaderValue.split(", Signature=");
        Signature publicSignature = Signature.getInstance("SHA256withECDSA");
        publicSignature.initVerify(
                KeyFactory.getInstance("EC").generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(TEST_PUBLIC_KEY)))
        );
        publicSignature.update(signatureParts[0].getBytes(StandardCharsets.UTF_8));

        assertTrue(publicSignature.verify(Base64.getDecoder().decode(signatureParts[1])));
    }

    @Test
    public void testBuildNexmoAuthorizationHeaderWithTimestampDisabled() throws Exception {
        DomainsServiceConfig config = new DomainsServiceConfig();
        config.setAuthPrincipalType("System");
        config.setAuthPrincipalValue("SIPApp");
        config.setAuthUseTimestamp(false);
        config.setAuthPrivateKey(TEST_PRIVATE_KEY);

        DomainsServiceClient client = new DomainsServiceClient(config);

        String authHeaderValue = client.buildNexmoAuthorizationHeader();

        assertNotNull(authHeaderValue);
        assertFalse(authHeaderValue.contains("Timestamp="));

        String[] authHeaderParts = authHeaderValue.split(", ");

        assertNotNull(authHeaderParts);
        assertEquals(2, authHeaderParts.length);
        assertEquals("System=SIPApp", authHeaderParts[0]);
        assertTrue(authHeaderParts[1].startsWith("Signature="));

        // lets check the signature
        String[] signatureParts = authHeaderValue.split(", Signature=");
        Signature publicSignature = Signature.getInstance("SHA256withECDSA");
        publicSignature.initVerify(
                KeyFactory.getInstance("EC").generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(TEST_PUBLIC_KEY)))
        );
        publicSignature.update(signatureParts[0].getBytes(StandardCharsets.UTF_8));

        assertTrue(publicSignature.verify(Base64.getDecoder().decode(signatureParts[1])));
    }

}