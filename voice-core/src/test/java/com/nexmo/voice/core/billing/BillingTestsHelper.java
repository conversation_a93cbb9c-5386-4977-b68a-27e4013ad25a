package com.nexmo.voice.core.billing;

import java.math.BigDecimal;
import java.util.concurrent.Callable;
import java.util.stream.LongStream;

import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;


@Ignore
public class BillingTestsHelper {

    public static Callable<BillingInfoDetails> runStart(final BillingInfo billingInfo, TestCdrEvent event) {
        return () -> {
            System.out.println("startCharging()");
            event.setStartTimeStamp(System.currentTimeMillis());
            final QuotaUpdateDetails result = billingInfo.startCharging("test-sessionId", "test-connectionId", System.currentTimeMillis());
            BillingInfoDetails billingInfoDetails = new BillingInfoDetails(billingInfo.takeSnapshot(), result);

            System.out.println("startCharging completed with: " + billingInfoDetails.getBillingInfoSnapshot().toString() + " " + result.toString());
            return billingInfoDetails;
        };
    }

    public static Callable<BillingInfoDetails> runStop(final BillingInfo billingInfo, final TestCdrEvent event) {
        return runStop(billingInfo, event, false);
    }

    public static Callable<BillingInfoDetails> runStopWithErrorCDR(final BillingInfo billingInfo, final TestCdrEvent event) {
        return runStop(billingInfo, event, true);
    }

    private static Callable<BillingInfoDetails> runStop(final BillingInfo billingInfo, final TestCdrEvent event, final boolean isErrorCDR) {
        return () -> {
            System.out.println("stopCharging()");
            billingInfo.setEndedStatus("test-SessionId", "test-ConnectionId");
            if (!isErrorCDR)
                event.generateCdrEvent();
            else
                event.generateErrorCdrEvent();

            final QuotaUpdateDetails result = billingInfo.stopCharging("test-sessionId", "test-connectionId", event, event.generateCdrEventUserData("test-connectionId"));
            BillingInfoDetails billingInfoDetails = new BillingInfoDetails(billingInfo.takeSnapshot(), result);

            System.out.println("stopCharging completed with: " + billingInfoDetails.getBillingInfoSnapshot().toString() + " " + result.toString());
            return billingInfoDetails;
        };
    }

    public static Callable<BillingInfoDetails> runEmergencyStop(final BillingInfo billingInfo) {
        return () -> {
            System.out.println("emergencyStop()");
            if (!billingInfo.shouldHandleEmergencyStopCharging("test-SessionId", "test-ConnectionId"))
                return null;

            final QuotaUpdateDetails result = billingInfo.emergencyStopCharging("test-sessionId", "test-connectionId");
            BillingInfoDetails billingInfoDetails = new BillingInfoDetails(billingInfo.takeSnapshot(), result);

            System.out.println("emergencyStopCharging completed with: " + billingInfoDetails.getBillingInfoSnapshot().toString() + " " + result.toString());
            return billingInfoDetails;
        };
    }


    public static Callable<BillingInfoDetails> runDelta(final BillingInfo billingInfo, final String marker) {
        return () -> {
            System.out.println("delta: " + marker);
            runUpToTheSecondLastDeltaCall(billingInfo);
            final QuotaUpdateDetails result = billingInfo.delta("test-sessionId", "test-connectionId");
            BillingInfoDetails billingInfoDetails = new BillingInfoDetails(billingInfo.takeSnapshot(), result);
            System.out.println(marker + " delta completed with: " + billingInfoDetails.getBillingInfoSnapshot().toString() + " " + result.toString());
            return billingInfoDetails;
        };
    }


    public static Callable<BillingInfoDetails> runDeltaOutOfFunds(final BillingInfo billingInfo) {
        return () -> {
            System.out.println("runDeltaOutOfFunds()");
            runUpToTheSecondLastDeltaCall(billingInfo);
            final QuotaUpdateDetails result = billingInfo.delta("test-sessionId", "test-connectionId");

            //This is imitating the case when the quota update of this delta action set the OutOfFundsStatus.
            //It means that no-more delta messages will arrive (this fact is out of the scope of this test - it is verified by the python tests)
            billingInfo.setOutOfFundsStatusDuringCall("test-sessionId", "test-connectionId");

            //In real life, this will trigger the quota update request
            BillingInfoDetails billingInfoDetails = new BillingInfoDetails(billingInfo.takeSnapshot(), result);
            System.out.println("runDeltaOutOfFunds completed with: " + billingInfoDetails.getBillingInfoSnapshot().toString() + " " + result.toString());
            return billingInfoDetails;
        };
    }


    public static BillingInfo createContextWithUnits(final long minimumIncrement, final long recurringIncrement,
                                                     final BigDecimal unitPrice, final BigDecimal forcedPrice, final BigDecimal unitCost) {
        return new BillingInfo(unitPrice, forcedPrice, unitCost, minimumIncrement, recurringIncrement,
                BillingInfo.Status.NOT_STARTED, false, false);
    }

    public static BillingInfo createContextWithError(final long minimumIncrement, final long recurringIncrement,
                                                     final BigDecimal unitPrice, final BigDecimal forcedPrice, final BigDecimal unitCost) {
        return new BillingInfo(unitPrice, forcedPrice, unitCost, minimumIncrement, recurringIncrement,
                BillingInfo.Status.ERROR, false, false);
    }

    /**
     * BillingInfo Delta is meant to be called every second, this function helps to call the delta n-1 times
     * where n is the number of times the delta should have been called up to this point
     * @param billingInfo
     */
    private static void runUpToTheSecondLastDeltaCall(final BillingInfo billingInfo){
        System.out.printf("Total number of previous iterations %d \n", billingInfo.getTotalNumberOfIterations());
        //calculates the number of delta that should have been called till now
        long totalNumberOfDeltaUpTillNow = (System.currentTimeMillis() - billingInfo.getCallStartTime()) / 1000;
        //subtract the previous number of deltas called
        long numberOfDelta = totalNumberOfDeltaUpTillNow - billingInfo.getTotalNumberOfIterations();
        //skip 1 delta call
        LongStream.range(1, numberOfDelta)
                .peek(i -> System.out.printf("Calling Delta %d out of %s \n", i, numberOfDelta-1))
                .forEach(i -> billingInfo.delta("test-sessionId", "test-connectionId"));
    }
}
