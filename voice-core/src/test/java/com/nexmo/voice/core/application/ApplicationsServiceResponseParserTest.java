package com.nexmo.voice.core.application;

import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;


public class ApplicationsServiceResponseParserTest {
    private static final String APPLICATION_ONE = "{\"id\":\"f067fcef-792a-4d04-b4ba-f83617f9e4ce\",\"name\":\"smokeapp\",\"api_key\":\"4bcbb82f\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzcHKA7HLY4Ra/3TFRAyS\\n8tP0oWJMA/3f7gfz2/fhL13Iz2CjZaHZGxlZUYXgfFuxXK6kWZyZNIMfuL8z+L1N\\nmBbBS1iDQpwbImOPFC7VfGys63HRiYFHu716KaFI9fUfb8ZQdV8iDmHCPEqVi89K\\nUw77XvDs+g+HoYpMIrBvudlBBSpbzm7mG7K8+uhvLBtSf5+KZ/CJpzrAGUrjDZRy\\nMe/r42uJAohH9DyTgB9VRMOiJ7OI/OQpGOFwsDpq8HqR0tM2cXB0qeO3p/JVl+8V\\nlTCZIjqB+zUeNOUXFvXFAPRN5+gNv/2ybVkIt5lTKUD7Vskiq/mxAmLzvebXbAT9\\npwIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1596188532000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"},\"answer_url\":{\"address\":\"https://answer.url\",\"http_method\":\"GET\"}}},\"rtc\":{\"webhooks\":{\"event_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}},\"messages\":{\"webhooks\":{\"inbound_url\":{\"address\":\"https://answer.url\",\"http_method\":\"POST\"},\"status_url\":{\"address\":\"https://events.url\",\"http_method\":\"POST\"}}}},\"created_time\":\"2020-07-31T09:42:12Z\",\"last_update_time\":\"2020-07-31T09:42:12Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/f067fcef-792a-4d04-b4ba-f83617f9e4ce\"}}}";
    private static final String APPLICATION_TWO = "{\"id\":\"633703ce-7b8c-4840-a192-cbd6f4bff2ba\",\"name\":\"load_test_09032021153947_1613\",\"api_key\":\"john\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6wTMJ8fzd0wprMfCeZeG\\nPu7f8i01o8dCRYmoOhDc5eF+10Qm+x8kKJvZDASZpRDCHyapVKTkk5P8ctnCjFLa\\nVbqgnbwENtRn+WtEgef7v96arFYl5rFR2/7+WiDxgnIB+JPBZHBhMktsglAWjr9m\\npw+vyrcI8ED18K/DNTbh+kGOOdEBXSMLIDBnAaVatTMY6PEGwu63XqxA5xoOhrXn\\ndhPE8ciE5Syw0e11V9BPPZy4PcuzDXXb0lvBVoZJ6AUTQQOVL8RuicsniUw+pz98\\nLq93sEoCEO6aVUAZ9Qveb6ZYg0uB5E5N8t3lCBGkaTMfOt4V1Gk7gDe7iEdyrL5t\\nowIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1615304389000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"http://***********/callback_vapi.html\",\"http_method\":\"GET\"},\"answer_url\":{\"address\":\"http://***********/nexmo/vapi/ncco_stream.json\",\"http_method\":\"GET\"}}}},\"created_time\":\"2021-03-09T15:39:49Z\",\"last_update_time\":\"2021-03-09T15:39:49Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/633703ce-7b8c-4840-a192-cbd6f4bff2ba\"}}}";
    private static final String APPLICATION_PAYMENT = "{\"id\":\"57c9240c-b504-4cf5-b808-fb8b2d141f74\",\"name\":\"auto_tests_application_name_10032021124051_8654\",\"api_key\":\"payment-7790\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtrd0Aj/QDV2SYpx80mlZ\\nQwl4ffRYEfNtDP2AVCipr+YUfdYU1C2NoFoJGkPlSRqi4SFXfI6dWgqat7iUmnKG\\nnHa4bQYaYgXscaW8y48Xc22FwTJBjilWueFSNMeV5DiN51t4Ga07ZqbeIYEGFDnG\\nPeQi6JiVpA5t3S4z5oYW2uh4J+OJZq0iR5lCVUSlFGw0o+5qgUepXqZJXE9y0KDs\\nXuwnudrjqCEixeEECym1oCVHxYq/yJlkwUFD27kddTyUycxt/pdKcJ9ko5OpsP5e\\n1bZGSMiY1gTzAoonfhRiDd/dx+bC34jMk7cLTojJzocLC84YERBn0ITAIOwE0rEv\\nuQIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1615394451000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"https://ancona-tools.npe.nexmo.io/callback_v2?qa_unique_id=nLemerSNy0G-eVhi-ET_BmgN7cAkRIxwNeqrGELL3To9vXfzEpnuEK9S6RVMwYhPLwnw-dyUyXWZElMLQ5V5qA==\",\"http_method\":\"POST\"},\"answer_url\":{\"address\":\"https://static.dev.nexmoinc.net/svc/ncco/ncco/auto_ncco_connect.php?event_url=https%3A%2F%2Fancona-tools.npe.nexmo.io%2Fcallback_v2%3Fqa_unique_id%3DnLemerSNy0G-eVhi-ET_BmgN7cAkRIxwNeqrGELL3To9vXfzEpnuEK9S6RVMwYhPLwnw-dyUyXWZElMLQ5V5qA%253D%253D&destination=441234000034&destination_type=phone\",\"http_method\":\"POST\"}},\"payment_enabled\":true}},\"created_time\":\"2021-03-10T16:40:51Z\",\"last_update_time\":\"2021-03-10T16:40:51Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/57c9240c-b504-4cf5-b808-fb8b2d141f74\"}}}";
    private static final String APPLICATION_REGION = "{\"id\":\"57c9240c-b504-4cf5-b808-fb8b2d141f74\",\"name\":\"auto_tests_application_name_10032021124051_8654\",\"api_key\":\"region-7790\",\"type\":\"standard\",\"status\":\"enabled\",\"status_details\":{},\"security\":{\"auth\":[{\"id\":\"\",\"type\":\"JWT\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtrd0Aj/QDV2SYpx80mlZ\\nQwl4ffRYEfNtDP2AVCipr+YUfdYU1C2NoFoJGkPlSRqi4SFXfI6dWgqat7iUmnKG\\nnHa4bQYaYgXscaW8y48Xc22FwTJBjilWueFSNMeV5DiN51t4Ga07ZqbeIYEGFDnG\\nPeQi6JiVpA5t3S4z5oYW2uh4J+OJZq0iR5lCVUSlFGw0o+5qgUepXqZJXE9y0KDs\\nXuwnudrjqCEixeEECym1oCVHxYq/yJlkwUFD27kddTyUycxt/pdKcJ9ko5OpsP5e\\n1bZGSMiY1gTzAoonfhRiDd/dx+bC34jMk7cLTojJzocLC84YERBn0ITAIOwE0rEv\\nuQIDAQAB\\n-----END PUBLIC KEY-----\\n\",\"signature_method\":\"rsa-256\",\"created_timestamp\":\"1615394451000\"}]},\"capabilities\":{\"voice\":{\"webhooks\":{\"event_url\":{\"address\":\"https://ancona-tools.npe.nexmo.io/callback_v2?qa_unique_id=nLemerSNy0G-eVhi-ET_BmgN7cAkRIxwNeqrGELL3To9vXfzEpnuEK9S6RVMwYhPLwnw-dyUyXWZElMLQ5V5qA==\",\"http_method\":\"POST\"},\"answer_url\":{\"address\":\"https://static.dev.nexmoinc.net/svc/ncco/ncco/auto_ncco_connect.php?event_url=https%3A%2F%2Fancona-tools.npe.nexmo.io%2Fcallback_v2%3Fqa_unique_id%3DnLemerSNy0G-eVhi-ET_BmgN7cAkRIxwNeqrGELL3To9vXfzEpnuEK9S6RVMwYhPLwnw-dyUyXWZElMLQ5V5qA%253D%253D&destination=441234000034&destination_type=phone\",\"http_method\":\"POST\"}},\"region\":\"us-3\"}},\"created_time\":\"2021-03-10T16:40:51Z\",\"last_update_time\":\"2021-03-10T16:40:51Z\",\"_links\":{\"self\":{\"href\":\"/internal/applications/57c9240c-b504-4cf5-b808-fb8b2d141f74\"}}}";


    @Test
    public void testParsing1() {
        JSONObject json = new JSONObject(APPLICATION_ONE);
        Application app = ApplicationsServiceResponseParser.fromJSON(json);
        Assert.assertEquals("4bcbb82f", app.getApiKey());
        Assert.assertNull(app.getPaymentEnabled());
    }

    @Test
    public void testParsing2() {
        JSONObject json = new JSONObject(APPLICATION_TWO);
        Application app = ApplicationsServiceResponseParser.fromJSON(json);
        Assert.assertEquals("john", app.getApiKey());
        Assert.assertNull(app.getPaymentEnabled());
    }

    @Test
    public void testParsingPayments() {
        JSONObject json = new JSONObject(APPLICATION_PAYMENT);
        Application app = ApplicationsServiceResponseParser.fromJSON(json);
        Assert.assertEquals("payment-7790", app.getApiKey());
        Assert.assertEquals(true, app.getPaymentEnabled());
    }

    @Test
    public void testParsingRegion() {
        JSONObject json = new JSONObject(APPLICATION_REGION);
        Application app = ApplicationsServiceResponseParser.fromJSON(json);
        Assert.assertEquals("region-7790", app.getApiKey());
        Assert.assertEquals("us-3", app.getRegion());
    }

}
