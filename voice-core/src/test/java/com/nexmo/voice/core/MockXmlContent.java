package com.nexmo.voice.core;

import com.thepeachbeetle.common.xml.LoaderException;
import com.thepeachbeetle.common.xml.XmlContent;

import java.io.File;
import java.util.Map;

public class MockXmlContent implements XmlContent {

    private Map<String, String> content;

    public MockXmlContent(Map<String, String> content) {
        this.content = content;
    }

    @Override
    public String getAttribute(String key, boolean required) throws LoaderException {
        if(this.content.containsKey(key)) {
            return this.content.get(key);
        }
        if(required) {
            throw new LoaderException("Config doesn't contain " + key);
        }
        return null;
    }

    @Override
    public String getAttribute(String key, boolean required, String defaultValue) throws LoaderException {
        if(this.content.containsKey(key)) {
            return this.content.get(key);
        }
        return defaultValue;
    }

    @Override
    public File getCurrentFile() {
        return null;
    }
}
