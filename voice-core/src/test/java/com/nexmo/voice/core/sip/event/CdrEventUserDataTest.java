package com.nexmo.voice.core.sip.event;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.nexmo.voice.core.sip.event.cdr.Asterisk16CdrEventUserData;
import com.nexmo.voice.core.sip.event.cdr.LegacyCdrEventUserData;
import org.asteriskjava.live.HangupCause;
import org.asteriskjava.manager.event.CdrEvent;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import static org.hamcrest.Matchers.is;

public class CdrEventUserDataTest {

    private static final String channelUniqueId = "1234";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Test
    public void outboundValidUserFieldTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=2#3";
        event.setUserField(userField);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());
        assertEquals("<EMAIL>", eventUserData.getChannel2Id());
        assertEquals("bogus,nothing,ibasis", eventUserData.getGatewaysList());
        assertEquals("nothing", eventUserData.getCurrentGateway());
        assertEquals(2, eventUserData.getCurrentGWAttempt());
        assertEquals(3, eventUserData.getAvailableGWAttempts());
        assertFalse(eventUserData.isLastRetryGateway());
    }


    @Test
    public void outboundNoMoneyValidUserFieldTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=21;DIALSTATUS=NO_MONEY;LEG2ID=;GWS=;GW=;ATTEMPT=";
        event.setUserField(userField);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_CALL_REJECTED, eventUserData.getHangupCause());
        assertEquals("NO_MONEY", eventUserData.getDialStatus());
        assertNull(eventUserData.getChannel2Id());
        assertNull(eventUserData.getGatewaysList());
        assertNull(eventUserData.getCurrentGateway());
        assertEquals(0, eventUserData.getCurrentGWAttempt());
        assertEquals(0, eventUserData.getAvailableGWAttempts());
        assertTrue(eventUserData.isLastRetryGateway());
    }


    @Test
    public void inboundValidUserFieldTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>";
        event.setUserField(userField);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());
        assertEquals("<EMAIL>", eventUserData.getChannel2Id());
        assertNull(eventUserData.getGatewaysList());
        assertNull(eventUserData.getCurrentGateway());
        assertEquals(0, eventUserData.getCurrentGWAttempt());
        assertEquals(0, eventUserData.getAvailableGWAttempts());
    }


    @Test
    //missing HANGUPCAUSE
    public void userFieldMissingHANGUPCAUSETest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "DIALSTATUS=ANSWER;LEG2ID=<EMAIL>";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField HANGUPCAUSE invalid value.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //missing DIALSTATUS
    public void userFieldMissingDIALSTATUSTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;LEG2ID=<EMAIL>";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField DIALSTATUS invalid value.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //Added AA=BB;MM=FF
    public void userFieldAdditionalValuesTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;AA=BB;MM=FF";
        event.setUserField(userField);

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NORMAL, eventUserData.getHangupCause());
        assertEquals("ANSWER", eventUserData.getDialStatus());
        assertEquals("<EMAIL>", eventUserData.getChannel2Id());
        assertNull(eventUserData.getGatewaysList());
        assertNull(eventUserData.getCurrentGateway());
        assertEquals(0, eventUserData.getCurrentGWAttempt());
        assertEquals(0, eventUserData.getAvailableGWAttempts());
    }

    @Test
    //missing GW
    public void outboundUserFieldMissingGWTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;ATTEMPT=2#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //missing GWS
    public void outboundUserFieldMissingGWListTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GW=nothing;ATTEMPT=2#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //missing ATTEMPT
    public void outboundUserFieldMissingAttemptTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //invalid ATTEMPT=K#3
    public void outboundUserFieldInvalidAttemptTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=K#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid value.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //invalid ATTEMPT=2#R
    public void outboundUserFieldInvalidTotalAttemptsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=2#R";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField " + CdrEventUserData.GATEWAY_FAIL_OVER_ATTEMPT + " invalid value.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //invalid ATTEMPT=-1#3
    public void outboundUserFieldNegativeAttemptTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=-1#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //invalid ATTEMPT=1#-3
    public void outboundUserFieldNegativeAttemptsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=1#-3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //ATTEMPT=0#3
    public void outboundUserFieldZeroAttemptTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=0#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //ATTEMPT=0#0
    public void outboundUserFieldZeroAttemptsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=0#0";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //ATTEMPT=4#3
    public void outboundUserFieldMissmatchedAttemptsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=4#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //wrong number of total available attempts: GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=3#4
    public void outboundUserFieldMissmatchedAttemptsToListTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=nothing;ATTEMPT=3#4";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //Current GW not in available list: GWS=bogus,nothing,ibasis;GW=abc;ATTEMPT=3#3
    public void outboundUserFieldMissmatchedGWToListTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=abc;ATTEMPT=3#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //Current GW position in list does not match attempt number: GWS=bogus,nothing,ibasis;GW=bogus;ATTEMPT=3#3"
    public void outboundUserFieldMissmatchedGWDetailsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=<EMAIL>;GWS=bogus,nothing,ibasis;GW=bogus;ATTEMPT=3#3";
        event.setUserField(userField);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);
    }

    @Test
    //No userField at all - with other CANCEL indicators
    public void noUserFieldWithCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        event.setUserField(null);
        event.setDuration(0);
        event.setLastApplication("AGI");
        event.setDisposition("NO ANSWER");

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());
        assertNull(eventUserData.getChannel2Id());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getGatewaysList());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getCurrentGateway());
        assertEquals(1, eventUserData.getCurrentGWAttempt());
        assertEquals(1, eventUserData.getAvailableGWAttempts());

    }

    @Test
    //No userField at all - without other CANCEL indicators
    public void noUserFieldWithoutCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        event.setUserField(null);
        event.setDuration(0);
        event.setLastApplication(null);
        event.setDisposition("NO ANSWER");

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField not found. channelUniqueId: null");
        new LocalCdrEventUserData(event, channelUniqueId);
    }


    @Test
    //Partial userField - with other CANCEL indicators
    public void partialUserFieldWithCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=;LEG2ID=;GWS=bogus,nothing,ibasis;GW=bogus;ATTEMPT=3#3";
        event.setUserField(userField);
        event.setDuration(0);
        event.setLastApplication("AGI");
        event.setDisposition("NO ANSWER");

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());
        assertNull(eventUserData.getChannel2Id());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getGatewaysList());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getCurrentGateway());
        assertEquals(1, eventUserData.getCurrentGWAttempt());
        assertEquals(1, eventUserData.getAvailableGWAttempts());

    }

    @Test
    //Partial userField - with no other CANCEL indicators
    public void partialUserFieldWithoutCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=;LEG2ID=;GWS=bogus,nothing,ibasis;GW=bogus;ATTEMPT=3#3";
        event.setUserField(userField);
        event.setDuration(3);
        event.setLastApplication("AGI");
        event.setDisposition(null);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error handling CdrEventUserData. userField DIALSTATUS invalid value.");
        new LocalCdrEventUserData(event, channelUniqueId);

    }

    @Test
    //Partial userField with mismatch gw details - with other CANCEL indicators
    public void noGWUserFieldWithCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=;GWS=bogus;GW=;ATTEMPT=";
        event.setUserField(userField);
        event.setDuration(0);
        event.setLastApplication("AGI");
        event.setDisposition("NO ANSWER");

        CdrEventUserData eventUserData = new LocalCdrEventUserData(event, channelUniqueId);
        assertEquals(HangupCause.AST_CAUSE_NOANSWER, eventUserData.getHangupCause());
        assertEquals("CANCEL", eventUserData.getDialStatus());
        assertNull(eventUserData.getChannel2Id());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getGatewaysList());
        assertEquals("Unknown-due-to-fast cancel", eventUserData.getCurrentGateway());
        assertEquals(1, eventUserData.getCurrentGWAttempt());
        assertEquals(1, eventUserData.getAvailableGWAttempts());

    }

    @Test
    //Partial userField with mismatch gw details - with no other CANCEL indicators
    public void noGWUserFieldWithoutCancelIndicatorsTest() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=16;DIALSTATUS=ANSWER;LEG2ID=;GWS=bogus;GW=;ATTEMPT=";
        event.setUserField(userField);
        event.setDuration(3);
        event.setLastApplication("AGI");
        event.setDisposition(null);

        thrown.expect(VoiceEventHandlerException.class);
        thrown.expectMessage("Error creating CdrEventUserData. userField invalid data.");
        new LocalCdrEventUserData(event, channelUniqueId);

    }

    @Test
    public void shouldBeAbleToRoundUpTime(){
        assertThat(CdrEventUserData.roundUpTime(10108), is(11L));
        assertThat(CdrEventUserData.roundUpTime(1), is(1L));
        assertThat(CdrEventUserData.roundUpTime(0), is(0L));
        assertThat(CdrEventUserData.roundUpTime(10000), is(10L));
    }

    @Test
    public void shouldBeAbleToParseToLong(){
        assertThat(CdrEventUserData.toLong("90108", "test-channelId"), is(90108L));
        assertThat(CdrEventUserData.toLong("10108-", "test-channelId"), is(-1L));
    }

    @Test
    public void shouldHavePddGivenUserFieldWithPROGRESSTIME_MS() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;PROGRESSTIME_MS=3000";
        event.setUserField(userField);
        event.setDisposition(null);

        CdrEventUserData cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(3000L));
    }

    @Test
    public void shouldHaveNegativePddGivenUserFieldWithoutOrBadPROGRESSTIME_MS() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;PROGRESSTIME_MS=";
        event.setUserField(userField);
        CdrEventUserData cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;PROGRESSTIME_MS";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;PROGRESSTIME_MS;";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;PROGRESSTIME_MS=xxxx";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getPddTimeMillis(), is(-1L));
    }

    @Test
    public void shouldHaveAnsweredTimeGivenUserFieldWithANSWEREDTIME_MS() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=3000";
        event.setUserField(userField);
        event.setDisposition(null);

        CdrEventUserData cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(3000L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(3L));
    }

    @Test
    public void shouldHaveNegativeAnsweredTimeGivenUserFieldWithoutOrBadANSWEREDTIME_MS() throws VoiceEventHandlerException {
        CdrEvent event = new CdrEvent(new Object());
        String userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=";
        event.setUserField(userField);
        CdrEventUserData cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(-1L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;ANSWEREDTIME_MS";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(-1L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;ANSWEREDTIME_MS;";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(-1L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(-1L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(-1L));

        userField = "HANGUPCAUSE=0;DIALSTATUS=ANSWER;ANSWEREDTIME_MS=xxxx";
        event.setUserField(userField);
        cdrEventUserData = new LocalAsterisk16CdrEventUserData(event, channelUniqueId);
        assertThat(cdrEventUserData.getAnsweredDurationMillis(), is(-1L));
        assertThat(cdrEventUserData.getAnsweredDuration(), is(-1L));
    }

    private class LocalCdrEventUserData extends LegacyCdrEventUserData {

        public LocalCdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
            super(event, channelUniqueId);
        }

        @Override
        protected String getOriginSessionDetails(String channelUniqueId) {
            return "junit-test-session";
        }

    }

    private class LocalAsterisk16CdrEventUserData extends Asterisk16CdrEventUserData {

        public LocalAsterisk16CdrEventUserData(CdrEvent event, String channelUniqueId) throws VoiceEventHandlerException {
            super(event, channelUniqueId);
        }

        @Override
        protected String getOriginSessionDetails(String channelUniqueId) {
            return "junit-test-session";
        }

    }
}
