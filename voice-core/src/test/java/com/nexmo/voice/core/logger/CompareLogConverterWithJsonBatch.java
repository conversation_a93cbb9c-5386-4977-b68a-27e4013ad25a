package com.nexmo.voice.core.logger;

import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Stream;
/**
 * This is a special manual test
 * It will not run as part of the junit tests.
 * It is comparing the full production files of key-value CDRs with json CDRs from all our DCs
 * as produced on Wednesday the 15 Dec 2021 
 * Each DC has its own unique pattern of usage, based on the specific customers of that DC.
 * 
 * Each CDR has a unique CDRUUID, and we will use it in order to match the CDRs from the files.
 * The files are very big, hence will not be part of the SIPApp repository.
 * They will be taken from the local disk, after down loaded from prod.
 * 
 * <AUTHOR>
 *
 */
public class CompareLogConverterWithJsonBatch {

    public static void main(String[] args) throws IOException {
        CompareLogConverterWithJsonBatch job = new CompareLogConverterWithJsonBatch();

        //AMS: no TTSApp, and no sip-attempt
        job.goCompare("sip-outbound", "AMS");
        job.goCompare("sip-inbound", "AMS");
        job.goCompare("sip-rejected", "AMS");
        System.out.println("AMS done");
        
        //LON: TTSApp is just smokes - identical to DAL
        job.goCompare("sip-outbound", "LON");
        job.goCompare("sip-inbound", "LON");
        job.goCompare("sip-rejected", "LON");
        job.goCompare("sip-attempt", "LON");
        System.out.println("LON done");
        
        //SNG: All files available 
        job.goCompare("sip-outbound", "SNG");
        job.goCompare("sip-inbound", "SNG");
        job.goCompare("sip-rejected", "SNG");
        job.goCompare("sip-attempt", "SNG");
        job.goCompare("tts-outbound", "SNG");
        job.goCompare("tts-rejected", "SNG");
        System.out.println("SNG done");
        
        //WDC: All files available 
        job.goCompare("sip-outbound", "WDC");
        job.goCompare("sip-inbound", "WDC");
        job.goCompare("sip-rejected", "WDC");
        job.goCompare("sip-attempt", "WDC");
        job.goCompare("tts-outbound", "WDC");
        job.goCompare("tts-rejected", "WDC");
        System.out.println("WDC done");

        //DAL: TTSApp smokes - there are no rejected CDRs, and no sip-attempt
        job.goCompare("sip-outbound", "DAL");
        job.goCompare("sip-inbound", "DAL");
        job.goCompare("sip-rejected", "DAL");
        job.goCompare("tts-outbound", "DAL");
        System.out.println("DAL done");
        System.out.println("");
        
        System.out.println("No exceptions - we are good");
    }
    
    private void goCompare(String fileType, String dcName) throws IOException {
        String kvFileName = "C://prod//cdrs2json"+dcName+"//cdrs//"+fileType+".2021.12.15.log";
        String jsonFileName = "C://prod//cdrs2json"+dcName+"//json//"+fileType+".2021.12.15.log";
        go(kvFileName, jsonFileName);
        
        System.out.println(dcName+" "+fileType+" - ok");
    }
    
    
    
	private void go(String kvFileName, String jsonFileName) throws IOException {
        
        HashMap<String, String> kvCdrsMap = new HashMap();
        HashMap<String, String> jsonCdrsMap = new HashMap();
        
        try (Stream<String> stream = Files.lines(Paths.get(kvFileName))) {
            stream.forEach(line -> kvCdrsMap.put(getKVCdrUUID(line), line));
        }
        
        try (Stream<String> stream = Files.lines(Paths.get(jsonFileName))) {
            stream.forEach(line -> jsonCdrsMap.put(getJsonCdrUUID(line), line));
        }
        
        assertTrue(jsonCdrsMap.size() > 0);
        assertTrue(kvCdrsMap.size() > 0);
        
        kvCdrsMap.keySet().stream().forEach(key -> verifyCDRs(key, kvCdrsMap, jsonCdrsMap));
    }

    private String getKVCdrUUID(String line) {
        String[] parts = line.split("CDR_UUID=");
        String cdrUUId = (parts[1].split("\"",2))[0];
        return cdrUUId;
    }
    
    private String getJsonCdrUUID(String line) {
        String[] parts = line.split("\"cdrUuid\":\"");
        String cdrUUId = (parts[1].split("\"",2))[0];
        return cdrUUId;
    }
    
    private void verifyCDRs(String key, HashMap<String,String> kvCdrsMap, HashMap<String,String> jsonCdrsMap) {
        String kvCDR = kvCdrsMap.get(key);
        String jsonCDR = jsonCdrsMap.get(key);
        //In prod, it might be that the key-value CDRs and the json-cdrs files do not include exactly the same list of CDRs
        //This is mainly because of the EOD-roll of the key-value file vs the LogCOnverter running time. 
        //Hence some minor number of CDRs might be missing from either of the files.
        if (Objects.nonNull(kvCDR) && Objects.nonNull(jsonCDR))
            CompareLogConverterWithJsonTest.verify(kvCDR, jsonCDR);
        else
            System.out.println(key+" CDR skipped as missing from one of the files");   
    }
    
}
