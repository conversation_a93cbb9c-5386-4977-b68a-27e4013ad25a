package com.nexmo.voice.core.types;

import com.thepeachbeetle.common.callback.types.CallbackMethod;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

import static com.nexmo.voice.core.types.TTSContext.parse;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

@RunWith(Parameterized.class)
public class TTSContextParameterizedTest {


    private final String ttsCallDetails;
    private final TTSContext ttsContext;

    public TTSContextParameterizedTest(String ttsCallDetails, TTSContext ttsContext) {
        this.ttsCallDetails = ttsCallDetails;
        this.ttsContext = ttsContext;
    }

    @Parameters(name = "{index}: {0}")
    public static Iterable<Object[]> data() {

        return Arrays.asList(new Object[][]{
                {"1938fcc8;http%3A%2F%2Fqaservices1.pillar.npe%3A8555%2Fcallback;GET_QUERY_PARAMS;;1;;-1;en-US;0",
                        TTSContext.builder()
                        .setTtsXTRaceId("1938fcc8")
                        .setCallbackUrl("http://qaservices1.pillar.npe:8555/callback")
                        .setCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
                        .setRepeat("1")
                        .setMachineTimeout("-1")
                        .setLanguageName("en-US")
                        .setMbStyle("0")
                        .build()
                },
                {"1938fcc8;;;;1;;-1;en-US;0",
                        TTSContext.builder()
                                .setTtsXTRaceId("1938fcc8")
                                .setRepeat("1")
                                .setMachineTimeout("-1")
                                .setLanguageName("en-US")
                                .setMbStyle("0")
                                .build()
                },
                {";;;;1;;-1;en-US;0",
                        TTSContext.builder()
                                .setRepeat("1")
                                .setMachineTimeout("-1")
                                .setLanguageName("en-US")
                                .setMbStyle("0")
                                .build()
                },
                {"1938fcc8;http%3A%2F%2Fqaservices1.pillar.npe%3A8555%2Fcallback;GET_QUERY_PARAMS;CL;1;hangup;-1;en-US;0",
                        TTSContext.builder()
                                .setTtsXTRaceId("1938fcc8")
                                .setCallbackUrl("http://qaservices1.pillar.npe:8555/callback")
                                .setCallbackMethod(CallbackMethod.GET_QUERY_PARAMS)
                                .setClientReference("CL")
                                .setRepeat("1")
                                .setMachineDetectionType("hangup")
                                .setMachineTimeout("-1")
                                .setLanguageName("en-US")
                                .setMbStyle("0")
                                .build()
                }
        });
    }

    @Test
    public void shouldDeserializeToTTsContextGivenValidTTSCallDetails() throws UnsupportedEncodingException {
        assertThat(parse("dummy", ttsCallDetails), is(ttsContext));
    }
}
