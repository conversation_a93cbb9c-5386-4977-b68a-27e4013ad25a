package com.nexmo.voice.core.domains;

import com.thepeachbeetle.messaging.hub.config.shortcodes.meta.CallbackType;
import org.json.JSONObject;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class DomainRoutingResponseTest {

    @Test
    public void fromJSON() {
        JSONObject json = new JSONObject("{\"returnCode\":\"24\", \"codeDescription\":\"sure why not\", \"uris\":[\"sip:edge.example.com:5060\"],\"siptrunk_region\":\"eu-west\"}");

        DomainRoutingResponse response = DomainRoutingResponse.fromJSON(json);

        assertNotNull(response);
        assertEquals("24", response.getReturnCode());
        assertEquals("sure why not", response.getCodeDescription());
        assertEquals(Collections.singletonList("sip:edge.example.com:5060"), response.getUris());
        assertEquals("eu-west", response.getSiptrunkRegion());
    }

    @Test
    public void isSuccessReturnCodeSuccess() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setReturnCode("0");

        assertNotNull(response);
        assertTrue(response.isSuccess());
    }

    @Test
    public void isSuccessReturnCodeNotSuccess() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setReturnCode("15");

        assertNotNull(response);
        assertFalse(response.isSuccess());
    }

    @Test
    public void isSuccessReturnCodeUnset() {
        DomainRoutingResponse response = new DomainRoutingResponse();

        assertNotNull(response);
        assertFalse(response.isSuccess());
    }

    @Test
    public void isSuccessUnspecifiedFailure() {
        DomainRoutingResponse response = DomainRoutingResponse.unspecifiedFailure();

        assertNotNull(response);
        assertFalse(response.isSuccess());
    }

    @Test
    public void testGetUrisWithoutScheme() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("sip:mydomain1.example.com", "sips:mydomain2.example.com", "mydomain3.example.com"));

        assertNotNull(response);
        assertNotNull(response.getUris());
        assertNotNull(response.getUrisWithoutScheme());
        assertEquals(Arrays.asList("mydomain1.example.com", "mydomain2.example.com", "mydomain3.example.com"), response.getUrisWithoutScheme());
    }

    @Test
    public void testGetUrisWithoutSchemeEmptyList() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Collections.emptyList());

        assertNotNull(response);
        assertNotNull(response.getUris());
        assertNotNull(response.getUrisWithoutScheme());
        assertEquals(Collections.emptyList(), response.getUrisWithoutScheme());
    }

    @Test
    public void testGetUrisWithoutSchemeNullList() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(null);

        assertNotNull(response);
        assertNull(response.getUris());
        assertNotNull(response.getUrisWithoutScheme());
        assertEquals(Collections.emptyList(), response.getUrisWithoutScheme());
    }

    @Test
    public void testGetUrisWithoutSchemeApplication() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("app://my-application-id"));

        assertNotNull(response);
        assertNotNull(response.getUris());
        assertNotNull(response.getUrisWithoutScheme());
        assertEquals(Arrays.asList("my-application-id"), response.getUrisWithoutScheme());
    }

    @Test
    public void testGetForwardsToUrisContainsSip() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("sip:mydomain1.example.com"));

        assertNotNull(response);
        assertEquals(CallbackType.SIP, response.getForwardsTo());
    }

    @Test
    public void testGetForwardsToUrisContainsSips() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("sips:mydomain1.example.com"));

        assertNotNull(response);
        assertEquals(CallbackType.SIP, response.getForwardsTo());
    }

    @Test
    public void testGetForwardsToSipUrisContainsApp() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("app://myapplicationidvalue"));

        assertNotNull(response);
        assertEquals(CallbackType.APPLICATION, response.getForwardsTo());
    }

    @Test
    public void testGetForwardsToSipUrisContainsEmptyList() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Collections.emptyList());

        assertNotNull(response);
        assertEquals(CallbackType.UNKNOWN, response.getForwardsTo());
    }

    @Test
    public void testGetForwardsToSipUrisContainsUnsupportedUri() {
        DomainRoutingResponse response = new DomainRoutingResponse();
        response.setUris(Arrays.asList("wss://this.is.not.supported.com"));

        assertNotNull(response);
        assertEquals(CallbackType.UNKNOWN, response.getForwardsTo());
    }

}