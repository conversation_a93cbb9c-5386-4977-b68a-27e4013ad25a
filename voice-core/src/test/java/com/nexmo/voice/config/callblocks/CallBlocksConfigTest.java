package com.nexmo.voice.config.callblocks;

import org.jdom.Element;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

public class CallBlocksConfigTest {
    @Test
    public void testCreation() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertNotNull(callBlockConfig);
    }

    @Test
    public void testGetCallBlockMap() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertTrue(callBlockMap.equals(callBlockConfig.getCallBlockMap()));
    }

    @Test
    public void testGetCallBlockAccounts() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertEquals(callBlockMap.keySet(),callBlockConfig.getCallBlockAccounts());
    }

    @Test
    public void testHasCallBlocksEmpty() {
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertFalse(callBlockConfig.hasCallBlocks());
    }

    @Test
    public void testHasCallBlocksNotEmpty() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertTrue(callBlockConfig.hasCallBlocks());
    }

    @Test
    public void testHasCallBlockExactMatch() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertTrue(callBlockConfig.hasCallBlock("my-account", "SG", "12345")); // All matching
    }

    @Test
    public void testHasCallBlockDefaultMatch() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("*", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);
        callBlockMap.get("*").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertTrue(callBlockConfig.hasCallBlock("something-else", "SG", "12345")); // default account call id
    }

    @Test
    public void testHasCallBlockNoMatch() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);
        Assert.assertFalse(callBlockConfig.hasCallBlock("something-else", "SG", "123456")); // no find
    }

    @Test
    public void testToXML() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);

        CallBlocksConfig callBlockConfig = new CallBlocksConfig(callBlockMap);

        Element callBlocks = new Element(CallBlocksConfigLoader.ROOT_NODE);

        for (HashMap.Entry<String, HashMap<String, ArrayList<String>>> callBlock : callBlockConfig.getCallBlockMap().entrySet()) {
            for (HashMap.Entry<String, ArrayList<String>> destination : callBlock.getValue().entrySet()) {
                for (String callId : destination.getValue()) {
                    Element callBlockElement = new Element(CallBlockConfigLoader.ROOT_NODE);
                    callBlockElement.setAttribute("account",callBlock.getKey());
                    callBlockElement.setAttribute("destination", destination.getKey());
                    callBlockElement.setAttribute("call-id", callId);
                    callBlocks.addContent(callBlockElement);
                }
            }
        }

        Assert.assertEquals(callBlocks.toString(), callBlockConfig.toXML().toString());
    }

    @Test
    public void testEquals() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("12345");
        list.add("45678");
        HashMap<String, HashMap<String, ArrayList<String>>> callBlockMap = new HashMap<String, HashMap<String, ArrayList<String>>>();
        callBlockMap.put("my-account", new HashMap<String, ArrayList<String>>());
        callBlockMap.put("my-account2", new HashMap<String, ArrayList<String>>());
        callBlockMap.get("my-account").put("SG", list);
        callBlockMap.get("my-account2").put("SG", list);

        CallBlocksConfig callBlockConfig1 = new CallBlocksConfig(callBlockMap);
        CallBlocksConfig callBlockConfig2 = new CallBlocksConfig(callBlockMap);

        Assert.assertTrue(callBlockConfig1.equals(callBlockConfig2));
    }
}
