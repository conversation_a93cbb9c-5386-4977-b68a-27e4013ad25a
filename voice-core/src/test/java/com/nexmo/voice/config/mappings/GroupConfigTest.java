package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class GroupConfigTest {
    @Test
    public void testCreation() {
        GroupConfig lvn = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn);
    }
    
    @Test
    public void testSameContents() {
        GroupConfig lvn1 = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn1);
        GroupConfig lvn2 = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn2);

        Assert.assertEquals(lvn1, lvn2);
    }

    @Test
    public void testDifferentContents() {
        GroupConfig lvn1 = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn1);

        GroupConfig lvn2 = new GroupConfig("LON", true, null);
        Assert.assertNotNull(lvn2);
        Assert.assertNotEquals(lvn1, lvn2);

        GroupConfig lvn3 = new GroupConfig("DAL", false, null);
        Assert.assertNotNull(lvn3);
        Assert.assertNotEquals(lvn1, lvn3);
    }
    
    @Test
    public void testHashes() {
        GroupConfig lvn1 = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn1);
        Assert.assertEquals(0x0023C6CB, lvn1.hashCode());

        GroupConfig lvn2 = new GroupConfig("LON", false, Collections.emptyList());
        Assert.assertNotNull(lvn2);
        Assert.assertEquals(0x0023C6CB, lvn2.hashCode());

        GroupConfig lvn3 = new GroupConfig("LON", true, null);
        Assert.assertNotNull(lvn3);
        Assert.assertEquals(0x0023C6C5, lvn3.hashCode());

        GroupConfig lvn4 = new GroupConfig("DAL", false, null);
        Assert.assertNotNull(lvn4);
        Assert.assertEquals(0x001FEF07, lvn4.hashCode());

        List<MediaDestination> destinations = new ArrayList<MediaDestination>();
        destinations.add( new MediaDestination("dal13", null) );
        GroupConfig lvn5 = new GroupConfig("DAL", false, destinations);
        Assert.assertNotNull(lvn5);
        Assert.assertEquals(0xB40B51C9, lvn5.hashCode());
    }

    @Test
    public void testGeos() {
        GroupConfig lvn1 = new GroupConfig("LON", false, null);
        Assert.assertNotNull(lvn1);
        Assert.assertEquals("[]", lvn1.getGeos().toString());

        GroupConfig lvn2 = new GroupConfig("LON", false, Collections.emptyList());
        Assert.assertNotNull(lvn2);
        Assert.assertEquals("[]", lvn2.getGeos().toString());

        GroupConfig lvn3 = new GroupConfig("DAL", false, Arrays.asList(new MediaDestination("dal13", null)));
        Assert.assertNotNull(lvn3);
        Assert.assertEquals("[dal13]", lvn3.getGeos().toString());

        List<MediaDestination> destinations = new ArrayList<MediaDestination>();
        destinations.add( new MediaDestination("lon1", null) );
        destinations.add( new MediaDestination("ams3", null) );
        GroupConfig lvn4 = new GroupConfig("EU", false, destinations);
        Assert.assertNotNull(lvn4);
        Assert.assertEquals("[lon1, ams3]", lvn4.getGeos().toString());
    }

    public static List<GroupConfig> makeTestGroups() {
        List<GroupConfig> ret = new ArrayList<>();

        ret.add( new GroupConfig("DAL", false, Arrays.asList(new MediaDestination("dal13", null))) );
        ret.add( new GroupConfig("LON", false, Arrays.asList(new MediaDestination("lon1", null))) );
        ret.add( new GroupConfig("SNG", false, Arrays.asList(new MediaDestination("sng1", null))) );
        ret.add( new GroupConfig("WDC", false, Arrays.asList(new MediaDestination("wdc4", null))) );
        
        return ret;
    }

}
