package com.nexmo.voice.config.prefix;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class MappingConfigTest {
    @Test
    public void testCreation() {
        MappingConfig lvn = new MappingConfig("12012010", "806103");
        Assert.assertNotNull(lvn);
    }
    
    // TODO: Test invalid inputs failing
    
    @Test
    public void testSameContents() {
        MappingConfig lvn1 = new MappingConfig("12012010", "806103");
        Assert.assertNotNull(lvn1);
        MappingConfig lvn2 = new MappingConfig("12012010", "806103");
        Assert.assertNotNull(lvn2);

        Assert.assertEquals(lvn1, lvn2);
    }

    @Test
    public void testDifferentContents() {
        MappingConfig lvn1 = new MappingConfig("12012010", "806103");
        Assert.assertNotNull(lvn1);

        MappingConfig lvn2 = new MappingConfig("12012011", "806103");
        Assert.assertNotNull(lvn2);
        Assert.assertNotEquals(lvn1, lvn2);

        MappingConfig lvn3 = new MappingConfig("12012010", "806104");
        Assert.assertNotNull(lvn3);
        Assert.assertNotEquals(lvn1, lvn3);
    }
    
    @Test
    public void testHashes() {
        MappingConfig lvn1 = new MappingConfig("12012010", "806103");
        Assert.assertNotNull(lvn1);
        Assert.assertEquals(0x4F00D4D8, lvn1.hashCode());

        MappingConfig lvn2 = new MappingConfig("12012012", "806104");
        Assert.assertNotNull(lvn2);
        Assert.assertEquals(0x4F00D517, lvn2.hashCode());
    }

    public static List<MappingConfig> makeTestMappings() {
        List<MappingConfig> ret = new ArrayList<>();

        ret.add( new MappingConfig("12012010", "806103") );
        ret.add( new MappingConfig("12012011", "806103") );
        ret.add( new MappingConfig("12012012", "806104") );
        ret.add( new MappingConfig("12012013", "806103") );
        ret.add( new MappingConfig("12012014", "806103") );
        ret.add( new MappingConfig("12012015", "806103") );
        ret.add( new MappingConfig("12012016", "806103") );
        ret.add( new MappingConfig("12012017", "806103") );
        ret.add( new MappingConfig("12012018", "806103") );
        
        return ret;
    }

}
