<?xml version="1.0" encoding="UTF-8"?>
<gateway-info>
    <supplier-mapping name="nexmo-live"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="jr-test"
                      endpoint="0.0.0.0">
        <mo-product-cost-matrixes>
            <mo-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mo-cost-matrix>
        </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="gw2"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="telserv"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
        <mo-product-cost-matrixes>
            <mo-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mo-cost-matrix>
        </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="ibasis"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
     </supplier-mapping>
    <supplier-mapping name="real-ibasis"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
     </supplier-mapping>
    <supplier-mapping name="idt"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="jr-test"
                      endpoint="0.0.0.0">
        <mo-product-cost-matrixes>
            <mo-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mo-cost-matrix>
        </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="tismi"
                      endpoint="0.0.0.0">
        <mo-product-cost-matrixes>
            <mo-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mo-cost-matrix>
        </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="vbc"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="vonage-cnam"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="bics"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="colt"
                      endpoint="0.0.0.0">
        <mt-product-cost-matrixes>
            <mt-cost-matrix product="VOICE-CALL"
                            default-price="0.0666"
                            perform-price-lookup="true"
                            reject-submission-if-no-price-found="false"
                            db="true"
                            ldap="false">
            </mt-cost-matrix>
        </mt-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="test-carrier-aws0"
                      endpoint="1.1.1.1">
    <mt-product-cost-matrixes>
        <mt-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mt-cost-matrix>
    </mt-product-cost-matrixes>
    <mo-product-cost-matrixes>
        <mo-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mo-cost-matrix>
    </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="test-carrier-aws1"
                      endpoint="1.1.1.1">
    <mt-product-cost-matrixes>
        <mt-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mt-cost-matrix>
    </mt-product-cost-matrixes>
    <mo-product-cost-matrixes>
        <mo-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mo-cost-matrix>
    </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="test-carrier-aws2"
                      endpoint="1.1.1.1">
    <mt-product-cost-matrixes>
        <mt-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mt-cost-matrix>
    </mt-product-cost-matrixes>
    <mo-product-cost-matrixes>
        <mo-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mo-cost-matrix>
    </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="test-carrier-aws3"
                      endpoint="1.1.1.1">
    <mt-product-cost-matrixes>
        <mt-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mt-cost-matrix>
    </mt-product-cost-matrixes>
    <mo-product-cost-matrixes>
        <mo-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mo-cost-matrix>
    </mo-product-cost-matrixes>
    </supplier-mapping>
    <supplier-mapping name="test-carrier-aws4"
                      endpoint="1.1.1.1">
    <mt-product-cost-matrixes>
        <mt-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mt-cost-matrix>
    </mt-product-cost-matrixes>
    <mo-product-cost-matrixes>
        <mo-cost-matrix product="VOICE-CALL"
                        default-price="0.0666"
                        perform-price-lookup="true"
                        reject-submission-if-no-price-found="false"
                        db="true"
                        ldap="false">
        </mo-cost-matrix>
    </mo-product-cost-matrixes>
    </supplier-mapping>
</gateway-info>