package com.nexmo.voice.config;

import java.io.File;

import com.nexmo.voice.config.applications.ApplicationsServiceConfig;
import com.nexmo.voice.config.applications.InterServiceAuthConfig;
import com.nexmo.voice.config.caches.CacheConfig;
import com.nexmo.voice.config.caches.CacheType;
import com.nexmo.voice.config.caches.CacheControlConfig;
import com.nexmo.voice.config.gateway.GatewayInfoMatrixConfig;
import com.nexmo.voice.config.gateway.GatewayInfoMatrixSource;
import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.nexmo.voice.config.prefix.PrefixMapSource;
import com.thepeachbeetle.common.app.ServerStartException;
import com.thepeachbeetle.common.xml.LoaderException;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

public class ConfigReaderTest {
    
    @BeforeClass
    public static void setUp() throws Exception {
        // TODO: Stub out the methods which do network connections when loading
        // config, so the changes to XML can be reverted
    }

    @Test
    public void testReadOldFormat() throws Exception {
        // Load config containing directly-included suppliers
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-oldformat.xml"));
        Config config = configReader.getConfig();

        final GatewayInfoMatrixSource gatewaySource = config.getSIPGatewayInfoMatrixSource();
        Assert.assertEquals("sip.xml", gatewaySource.getSource());

        final GatewayInfoMatrixConfig gatewayConfig = config.getSIPGatewayInfoMatrixConfig();
        Assert.assertEquals(17, gatewayConfig.getGatewayCount());

        Assert.assertTrue(gatewayConfig.hasGateways());
    }


    @Test
    public void testReadNewFormat() throws Exception {
        // Load config referencing an external suppliers file
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-newformat.xml"));
        Config config = configReader.getConfig();

        // Different filename due to indirection in <gateway-info> tag
        final GatewayInfoMatrixSource gatewaySource = config.getSIPGatewayInfoMatrixSource();
        final File expected = new File("src/test/java/com/nexmo/voice/config/dev-suppliers.xml");
        final File actual = new File(gatewaySource.getSource());
        Assert.assertEquals(expected, actual);

        final GatewayInfoMatrixConfig gatewayConfig = config.getSIPGatewayInfoMatrixConfig();
        Assert.assertEquals(17, gatewayConfig.getGatewayCount());

        Assert.assertTrue(gatewayConfig.hasGateways());
    }

    @Test
    public void testTTSReadNewFormat() throws Exception {
        // Load config referencing an external suppliers file
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-newformat.xml"));
        Config config = configReader.getConfig();

        // Different filename due to indirection in <gateway-info> tag
        final GatewayInfoMatrixSource gatewaySource = config.getTTSGatewayInfoMatrixSource();
        final File expected = new File("src/test/java/com/nexmo/voice/config/********suppliers.xml");
        final File actual = new File(gatewaySource.getSource());
        Assert.assertEquals(expected, actual);

        final GatewayInfoMatrixConfig gatewayConfig = config.getTTSGatewayInfoMatrixConfig();
        Assert.assertEquals(17, gatewayConfig.getGatewayCount());

        Assert.assertTrue(gatewayConfig.hasGateways());
    }
    
    @Test
    public void testMissingSuppliersFile() throws Exception {
        // Load config referencing a non-existent suppliers file
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test********.xml"));
        Config config = configReader.getConfig();

        final GatewayInfoMatrixSource gatewaySource = config.getSIPGatewayInfoMatrixSource();
        Assert.assertEquals("sip.xml", gatewaySource.getSource()); // Couldn't load from file

        final GatewayInfoMatrixConfig gatewayConfig = config.getSIPGatewayInfoMatrixConfig();
        Assert.assertEquals(0, gatewayConfig.getGatewayCount()); // No suppliers

        // No suppliers!
        Assert.assertFalse(gatewayConfig.hasGateways());
    }

    @Test
    public void testTTSMissingSuppliersFile() throws Exception {
        // Load config referencing a non-existent suppliers file
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test********.xml"));
        Config config = configReader.getConfig();

        final GatewayInfoMatrixSource gatewaySource = config.getTTSGatewayInfoMatrixSource();
        Assert.assertEquals("sip.xml", gatewaySource.getSource()); // Couldn't load from file

        final GatewayInfoMatrixConfig gatewayConfig = config.getTTSGatewayInfoMatrixConfig();
        Assert.assertEquals(0, gatewayConfig.getGatewayCount()); // No suppliers

        // No suppliers!
        Assert.assertFalse(gatewayConfig.hasGateways());
    }

    @Test
    public void testIgnoreUnknownTag() throws Exception {
        // Load config containing well-formed XML that we don't understand
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test********.xml"));
        Config config = configReader.getConfig();

        // Different filename due to indirection in <gateway-info> tag
        final GatewayInfoMatrixSource gatewaySource = config.getSIPGatewayInfoMatrixSource();
        final File expected = new File("src/test/java/com/nexmo/voice/config/dev-suppliers.xml");
        final File actual = new File(gatewaySource.getSource());
        Assert.assertEquals(expected, actual);

        final GatewayInfoMatrixConfig gatewayConfig = config.getSIPGatewayInfoMatrixConfig();
        Assert.assertEquals(17, gatewayConfig.getGatewayCount());

        Assert.assertTrue(gatewayConfig.hasGateways());
    }

    @Test(expected = LoaderException.class)
    public void shouldFailGivenConfigWithMissingTTSGatewayTak() throws Exception {
        // Load config referencing an external suppliers file
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test********tags.xml"));
    }

    @Test
    public void testReadCacheConfig() throws Exception {
        // Load config containing directly-included suppliers
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-caches.xml"));
        Config config = configReader.getConfig();

        final CacheControlConfig cachesConfig = config.getCachesConfig();
        Assert.assertTrue(cachesConfig.isEnabled());
        Assert.assertEquals(1, cachesConfig.getCacheCount());

        // Check application cache
        final CacheConfig cache = cachesConfig.getCacheConfig(CacheType.APPLICATION);
        Assert.assertEquals(100, cache.getSize());
        Assert.assertEquals(3*86400L, cache.getExpiry().getSeconds());
        Assert.assertEquals(0L, cache.getExpiry().getNano());
        Assert.assertEquals(10L, cache.getRefresh().getSeconds());
        Assert.assertEquals(700000000L, cache.getRefresh().getNano());
    }

    @Test
    public void testReadApplicationsServiceConfig() throws Exception {
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-applications.xml"));
        Config config = configReader.getConfig();

        final ApplicationsServiceConfig applicationsConfig = config.getApplicationsServiceConfig();
        Assert.assertNotNull(applicationsConfig);
        Assert.assertTrue(applicationsConfig.isEnabled());

        // Check URI generation
        Assert.assertEquals("http://applications-service.qa:4170/internal/applications/f7d4b423-16e0-4d70-a1a8-02ba537d384d", applicationsConfig.constructUri("f7d4b423-16e0-4d70-a1a8-02ba537d384d"));

        // Check inter-service auth
        final InterServiceAuthConfig authConfig = applicationsConfig.getInterServiceAuthConfig();
        Assert.assertNotNull(authConfig);
        Assert.assertEquals("dev", authConfig.getIssuer());
        Assert.assertEquals("/etc/nexmo_jwt/keys_v2/private/dev/dev.properties", authConfig.getPrivateKeyFile());
        Assert.assertEquals("/etc/nexmo_jwt/keys_v2/public", authConfig.getPublicKeysPath());
    }

    @Test
    public void testReadPrefixMapConfig() throws Exception {
        ConfigReader configReader = new ConfigReader();
        configReader.read(new File("src/test/java/com/nexmo/voice/config/test-prefixmap.xml"));
        Config config = configReader.getConfig();

        // Prefix Map
        final PrefixMapConfig prefixMap = config.getPrefixMapConfig();
        Assert.assertFalse(prefixMap.isEmpty());
        Assert.assertEquals(9, prefixMap.size());

        // Different filename due to indirection in <prefix-map> tag
        final PrefixMapSource prefixMapSource = config.getPrefixMapSource();
        final File expected = new File("src/test/java/com/nexmo/voice/config/dev-prefix.xml");
        final File actual = new File(prefixMapSource.getSource());
        Assert.assertEquals(expected, actual);
    }

}
