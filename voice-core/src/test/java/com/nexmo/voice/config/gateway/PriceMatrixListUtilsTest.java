package com.nexmo.voice.config.gateway;

import org.junit.Test;
import org.junit.Assert;

import com.nexmo.voice.config.gateway.MessagePriceMatrixConfigLoader;
import com.nexmo.voice.config.gateway.MoCostMatrixConfigLoader;
import com.nexmo.voice.config.gateway.MtCostMatrixConfigLoader;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;

import static com.thepeachbeetle.messaging.hub.core.Product.PRODUCT_VOICE_CALL;


public class PriceMatrixListUtilsTest {
    private static final int DEFAULT_PRODUCT = (int)PRODUCT_VOICE_CALL;
    private static final int EXPECTED_HASHCODE = 1433492794;
    private static final String EXPECTED_MT_XML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n" + 
                                                  "<mt-product-cost-matrixes>\r\n" +
                                                  "  <mt-cost-matrix product=\"VOICE-CALL\" default-price=\"0.0666\" perform-price-lookup=\"true\" reject-submission-if-no-price-found=\"false\" db=\"true\" ldap=\"false\" ldap-base-dn=\"null\" />\r\n" +
                                                  "</mt-product-cost-matrixes>\r\n\r\n";

    @Test
    public void testCreation() throws Exception {
        final PriceMatrixList instance = createOutboundPriceMatrixList();
        Assert.assertNotNull(instance);
        Assert.assertNotNull(instance.getMatrix());
        Assert.assertEquals(EXPECTED_MT_XML, instance.toXMLString());
    }

    @Test
    public void testEquals() {
        final PriceMatrixList instance1 = createOutboundPriceMatrixList();
        final PriceMatrixList instance2 = createOutboundPriceMatrixList();
        Assert.assertFalse(instance1 == instance2); // Different objects

        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(instance1,instance2));
    }

    @Test
    public void testHashcode() {
        final PriceMatrixList instance = createOutboundPriceMatrixList();
        Assert.assertEquals(EXPECTED_HASHCODE, PriceMatrixListUtils.hashPriceMatrixList(instance));
    }

    /*package*/ static PriceMatrixList createOutboundPriceMatrixList() {
        MtCostMatrixConfigLoader loader = new MtCostMatrixConfigLoader(MtCostMatrixConfigLoader.MT_COST_MATRIX_ROOT_NODE_NAME, MtCostMatrixConfigLoader.MT_COST_MATRIX_NODE_NAME, true);
        PriceMatrixList ret = new PriceMatrixList(MtCostMatrixConfigLoader.MT_COST_MATRIX_ROOT_NODE_NAME);
        ret.getMatrix().put(DEFAULT_PRODUCT, loader.getDefaultPriceMatrix());
        return ret;
    }

    /*package*/ static PriceMatrixList createInboundPriceMatrixList() {
        MoCostMatrixConfigLoader loader = new MoCostMatrixConfigLoader(MoCostMatrixConfigLoader.MO_COST_MATRIX_ROOT_NODE_NAME, MoCostMatrixConfigLoader.MO_COST_MATRIX_NODE_NAME, true);
        PriceMatrixList ret = new PriceMatrixList(MoCostMatrixConfigLoader.MO_COST_MATRIX_ROOT_NODE_NAME);
        ret.getMatrix().put(DEFAULT_PRODUCT, loader.getDefaultPriceMatrix());
        return ret;
    }

}
