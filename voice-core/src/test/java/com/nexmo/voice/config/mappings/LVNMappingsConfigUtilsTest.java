package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


@RunWith(PowerMockRunner.class)
@PrepareForTest(LVNMappingsConfigUtils.class)
@PowerMockIgnore("javax.management.*")
public class LVNMappingsConfigUtilsTest {
    private static final String MAPPINGS_FILE = "src/test/java/com/nexmo/voice/config/mappings/mappings.xml";
    private static final String EMPTY_FILE = "src/test/java/com/nexmo/voice/config/mappings/empty.xml";
    private static final String MISSING_FILE = "src/test/java/com/nexmo/voice/config/mappings/missing.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/mappings/different.xml";
    private static final String EXPECTED_DIFFS = "Differences:\n" + 
                                                 "Groups added: [AMS]\n" + 
                                                 "Groups removed: [DAL]\n" + 
                                                 "Groups changed: [SNG]\n" +
                                                 "LVNs added: [69]\n" + 
                                                 "LVNs removed: [51]\n" + 
                                                 "LVNs changed: [10, 11, 12, 123456789, 13, 14]\n"; // 10-14 are transitive changes in SNG group

    private static final List<String> EMPTY_FILE_LIST = Collections.emptyList();
    private static final List<String> FILE_LIST;
    
    static {
        FILE_LIST = new ArrayList<String>();
        FILE_LIST.add("dev-19991231235900.xml");
        FILE_LIST.add("dev-20200101000000.xml");
        FILE_LIST.add("dev-19991231235903.xml");
    }
    
    @Before
    public void setUp() throws Exception {
        // We cannot use PowerMockito.mockStatic() because that will also mock the methods we want to test...
        PowerMockito.spy(LVNMappingsConfigUtils.class);

        // We must use PowerMockito.doAnswer(...).when(...) rather than PowerMockito.when(...).thenReturn(...)
        // because getFileList() is *private* so the latter form will not compile
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  EMPTY_FILE_LIST).when(LVNMappingsConfigUtils.class, "getFileList", Matchers.isNull(), Matchers.anyString());
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  FILE_LIST).when(LVNMappingsConfigUtils.class, "getFileList", Matchers.eq("mappings"), Matchers.anyString());
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testSelectNone() {
        String file = LVNMappingsConfigUtils.selectFile(null, "dev-*.xml", "LATEST");

        Assert.assertNull(file);
    }    
    
    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testSelectLatest() {
        String file = LVNMappingsConfigUtils.selectFile("mappings", "dev-*.xml", "LATEST");

        Assert.assertEquals("mappings/dev-20200101000000.xml", file);
    }    
    
    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testSelectSpecific() {
        String file = LVNMappingsConfigUtils.selectFile("mappings", "dev-*.xml", "19991231235900");

        Assert.assertEquals("mappings/dev-19991231235900.xml", file);
    }    

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    @Ignore("TODO: Handle this case")
    public void testSelectSpecificMultiMatch() {
        String file = LVNMappingsConfigUtils.selectFile("mappings", "dev-*.xml", "19991231");

        Assert.assertEquals("mappings/dev-19991231235900.xml", file);
        Assert.assertEquals("mappings/dev-19991231235903.xml", file);
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testLoadConfigFromXMLFile() {
        LVNMappingsConfig config = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILE);

        //TODO: check contents
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testLoadConfigFromEmptyFile() {
        LVNMappingsConfig config = LVNMappingsConfigUtils.loadConfigFromXMLFile(EMPTY_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testLoadConfigFromMissingFile() {
        LVNMappingsConfig config = LVNMappingsConfigUtils.loadConfigFromXMLFile(MISSING_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testLoadConfigNullFilename() {
        LVNMappingsConfig config = LVNMappingsConfigUtils.loadConfigFromXMLFile(null);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(LVNMappingsConfigUtils.class)
    public void testCompareConfigs() {
        LVNMappingsConfig config1 = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILE);
        LVNMappingsConfig config2 = LVNMappingsConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotEquals(config1, config2);

        final String actual = LVNMappingsConfigUtils.compareConfigs(config1,config2);
        Assert.assertEquals(EXPECTED_DIFFS, actual);
    }
    
}
