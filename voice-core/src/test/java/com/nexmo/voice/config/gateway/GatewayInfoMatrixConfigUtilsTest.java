package com.nexmo.voice.config.gateway;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.nexmo.voice.core.Core;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


@RunWith(PowerMockRunner.class)
@PrepareForTest(GatewayInfoMatrixConfigUtils.class)
@PowerMockIgnore("javax.management.*")
public class GatewayInfoMatrixConfigUtilsTest {
    private static final String SUPPLIERS_FILE = "src/test/java/com/nexmo/voice/config/gateway/suppliers.xml";
    private static final String EMPTY_FILE = "src/test/java/com/nexmo/voice/config/gateway/empty.xml";
    private static final String MISSING_FILE = "src/test/java/com/nexmo/voice/config/gateway/missing.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/gateway/different.xml";
    private static final String EXPECTED_DIFFS = "Differences:\n" + 
                                                 "Gateways added: [test-carrier-aws5]\n" + 
                                                 "Gateways removed: [test-carrier-aws0]\n" + 
                                                 "Gateways changed: [gw2, real-ibasis, telserv]\n";

    private static final List<String> EMPTY_FILE_LIST = Collections.emptyList();
    private static final List<String> FILE_LIST;
    
    static {
        FILE_LIST = new ArrayList<String>();
        FILE_LIST.add("dev-19991231235900.xml");
        FILE_LIST.add("dev-20200101000000.xml");
        FILE_LIST.add("dev-19991231235903.xml");
    }
    
    @Before
    public void setUp() throws Exception {
        // We cannot use PowerMockito.mockStatic() because that will also mock the methods we want to test...
        PowerMockito.spy(GatewayInfoMatrixConfigUtils.class);

        // We must use PowerMockito.doAnswer(...).when(...) rather than PowerMockito.when(...).thenReturn(...)
        // because getFileList() is *private* so the latter form will not compile
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  EMPTY_FILE_LIST).when(GatewayInfoMatrixConfigUtils.class, "getFileList", Matchers.isNull(), Matchers.anyString());
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  FILE_LIST).when(GatewayInfoMatrixConfigUtils.class, "getFileList", Matchers.eq("suppliers"), Matchers.anyString());
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testSelectNone() {
        String file = GatewayInfoMatrixConfigUtils.selectFile(null, "dev-*.xml", "LATEST");

        Assert.assertNull(file);
    }    
    
    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testSelectLatest() {
        String file = GatewayInfoMatrixConfigUtils.selectFile("suppliers", "dev-*.xml", "LATEST");

        Assert.assertEquals("suppliers/dev-20200101000000.xml", file);
    }    
    
    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testSelectSpecific() {
        String file = GatewayInfoMatrixConfigUtils.selectFile("suppliers", "dev-*.xml", "19991231235900");

        Assert.assertEquals("suppliers/dev-19991231235900.xml", file);
    }    

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    @Ignore("TODO: Handle this case")
    public void testSelectSpecificMultiMatch() {
        String file = GatewayInfoMatrixConfigUtils.selectFile("suppliers", "dev-*.xml", "19991231");

        Assert.assertEquals("suppliers/dev-19991231235900.xml", file);
        Assert.assertEquals("suppliers/dev-19991231235903.xml", file);
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testLoadConfigFromXMLFile() {
        SIPGatewayInfoMatrixConfig config = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(SUPPLIERS_FILE);

        Assert.assertEquals(17, config.getGatewayCount());
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testLoadConfigFromEmptyFile() {
        SIPGatewayInfoMatrixConfig config = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(EMPTY_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testLoadConfigFromMissingFile() {
        SIPGatewayInfoMatrixConfig config = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(MISSING_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testLoadConfigNullFilename() {
        SIPGatewayInfoMatrixConfig config = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(null);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(GatewayInfoMatrixConfigUtils.class)
    public void testCompareConfigs() {
        SIPGatewayInfoMatrixConfig config1 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(SUPPLIERS_FILE);
        SIPGatewayInfoMatrixConfig config2 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotEquals(config1, config2);

        final String actual = GatewayInfoMatrixConfigUtils.compareConfigs(config1,config2);
        Assert.assertEquals(EXPECTED_DIFFS, actual);
    }
    
}
