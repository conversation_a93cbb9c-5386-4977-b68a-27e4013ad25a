<voice>
    <core instance-id="@core.instance-id@">
        <deployment-check enabled="true">
            <!-- FIXME: Why does _loading a config file_ fail if a _network service_ is unavailable? -->
            <remote-deployment-matrix enabled="false"
                                      deployment-matrix-service-host="@core.deployment-check.remote-deployment-matrix.deployment-matrix-service-host@"
                                      deployment-matrix-service-port="3980"
                                      connection-timeout="3000"
                                      so-timeout="10000">
                <initial-startup-wait-for-remote-to-be-availanle enabled='false' wait-for-unit='minute' wait-for-count='30' retry-interval='1000' />
            </remote-deployment-matrix>
        </deployment-check>
        <cluster enabled="false">
            <multicast enabled="false"
                       multicast-addr="not-used"
                       multicast-port="0"
                       bind-interface="eth0"
                       rsvp-required="false">
                <override-from-deployment-matrix enabled="false"
                                                 multicast-addr-property="CLUSTER-MULTICAST-ADDR"
                                                 multicast-port-property="CLUSTER-MULTICAST-PORT"
                                                 bind-interface-property="CLUSTER-MULTICAST-BIND-INTERFACE"/>
            </multicast>
        </cluster>
        <cache-core>
            <!-- FIXME: Disable global cache -->
            <instance enabled="false"
                      cache-core-id="cache1"
                      config-path="ehcache.xml"/>
        </cache-core>
        <jmx enabled="true"
             listen-addr="@core.jmx.listen-address@"
             listen-port="7713"
             http-enabled="true"
             http-port="7715"
             http-username="@core.jmx.http-username@"
             http-password="@core.jmx.http-password@"/>
        <log dir="logs" log4j-properties-file="log4j.xml" log-level="INFO">
            <call-logging enabled="true" file-prefix="sip-outbound"/>
            <call-inbound-logging enabled="true" file-prefix="sip-inbound"/>
            <rejected-logging enabled="true" file-prefix="sip-rejected"/>
            <attempt-logging enabled="true" file-prefix="sip-attempt"/>
        </log>
        <http enabled="true"
              addr="@core.http.addr@"
              port="7710"
              access-log-location="logs"
              access-log-prefix="access"
              min-threads="10"
              max-threads="100"
              keep-alive-timeout="130000"/>
        <http-internal-api enabled="true"
                           addr="@core.http-internal.addr@"
                           port="7714"
                           access-log-location="logs"
                           access-log-prefix="access"
                           min-threads="10"
                           max-threads="100"
                           keep-alive-timeout="130000"/>
        <http-api>
            <internal-api enabled="true">
                <endpoint enabled="true" context="internal" json-path="api"/>
                <check-balance enabled="true" min-balance="0.10" />
                <check-concurrent-capacity enabled="false" />
            </internal-api>
        </http-api>
        <accounts>
            <account-capabilities>
                 <force-gateway enabled="true" name="force-sip-gateway" />
            </account-capabilities>
        </accounts>
        <monitoring enabled="true">
            <listen addr="@core.monitoring.listen.addr@"
                    port="7712"
                    access-log-location="logs"
                    context="monitoring"
                    path="stats"/>
        </monitoring>
        <event-callback enabled="false"
                        max-age-of-tasks-unit="day"
                        max-age-of-tasks-count="1"/>
        <callback enabled="true"
                  max-callback-threads-in-pool="200" 
                  max-age-of-tasks-unit="day" 
                  max-age-of-tasks-count="1">
            <proxy enabled="false" proxy-host="proxy.internal" proxy-port="8888"/>
            <retry-queue enabled="true" number-of-threads="10"/>
            <trace-logging enabled="true" log-dir="logs" log-prefix="debug-callback-delivery"/>
        </callback>
        <http-callback-blocked-ip-ranges>
            @callback-blocked-ip-ranges@
        </http-callback-blocked-ip-ranges>
        <config-db enabled="false">
            <!-- FIXME: Because _of course_ loading a config file should connect to a database... -->
            <db url="@core.config-db.db.url@"
                username="@core.config-db.db.username@"
                password="@core.config-db.db.password@"
                schema="config"
                sql-logging="false"
                min-connections="2"
                max-connections="10"/>
        </config-db>
        <short-codes-db enabled="true" read-short-code-meta-data-from-db="true">
            <db url="@core.short-codes-db.db.url@"
                username="@core.short-codes-db.db.username@"
                password="@core.short-codes-db.db.password@"
                schema="shortcodes"
                sql-logging="false"
                min-connections="2"
                max-connections="10"/>
        </short-codes-db>
        <quota-api remote-host="@core.quota-api.remote-host@"
                   remote-port="8009"
                   connection-timeout="3000"
                   so-timeout="15000"
                   my-service-id="@core.instance-id@"
                   user-agent="@core.instance-id@"
                   ssl="false"/>
        <extended-quota-api log-enabled="true"/>
<!-- FIXME: Removing this entire block... it *cannot* be disabled otherwise. Even if switched off, the XML validation is always attempted.
        <network-matrix enabled="true" xml-file="../../../../../hlr/static/hlr-lookup-prefixes.xml" xml-files-directory="../../../../../hlr/static/hlr-lookup-prefixes">
            <remote-repository enabled="true"
                               service-host="@core.network-matrix.remote-repository.service-host@"
                               service-port="3100"
                               ssl="false"
                               api-key="vrboeboq50n4b4w9b94n4wnnv84n"
                               connection-timeout="10000"
                               user-agent="@core.instance-id@"
                               so-timeout="10000">
                <initial-startup-wait-for-remote-to-be-availanle enabled='true' wait-for-unit='minute' wait-for-count='30' retry-interval='1000' />
            </remote-repository>
        </network-matrix>
-->
        <context-cache enabled="true" db-persistence="true">
            <cluster enabled="false"/>
            <purge enabled="true"
                   purge-interval-count="10"
                   purge-interval-unit="minute"
                   max-time-to-keep-count="12"
                   max-time-to-keep-unit="hour"/>
        </context-cache>
        <shutdown-cooldown unit="second" count="3" />
        <bar-retry-queue enabled="true" number-listeners="10" />
        <counters>
            <max-global-count max="3300"
                              from-deployment-matrix-enabled="false"
                              from-deployment-matrix-property="MAX-COUNTER" />
            <counter-max-idle-time zero-items-unit="second"
                                   zero-items-count="30"
                                   nonzero-items-unit="day"
                                   nonzero-items-count="1"/>
            <cluster enabled="false" />
        </counters>
        <pdd-calculator duration="90000"/>
        <call-charger duration="90000"/>
    </core>
    @whitelisted-numbers@
    <mt-routing ldap="false" db="true" db-interested-in-meta-data="false">
        @mt-route-overrides@
    </mt-routing>
    <mt-product-price-matrixes>
        <mt-price-matrix product="VOICE-CALL"
                         default-price="0.1"
                         perform-price-lookup="true"
                         reject-submission-if-no-price-found="false"
                         ldap="false"
                         db="true">
        </mt-price-matrix>
    </mt-product-price-matrixes>
    <mo-product-price-matrixes>
            <mo-price-matrix product="VOICE-CALL"
                             default-price="0"
                             perform-price-lookup="true"
                             reject-submission-if-no-price-found="false"
                             ldap="false"
                             db="true">
            </mo-price-matrix>
    </mo-product-price-matrixes>
    <charging-updater enabled="true" shutdown-interval-count="30" shutdown-interval-unit="second">
        <update-task product-class="sip_asterisk" sweeping-interval-count="1" sweeping-interval-unit="second"/>
        <update-task product-class="api" sweeping-interval-count="1" sweeping-interval-unit="second" />
        <update-task product-class="verify" sweeping-interval-count="1" sweeping-interval-unit="second" />
    </charging-updater>
    <auth>
<!-- FIXME: Disabling another network service...
        <smpp-accounts enabled="false"
                       db="true"
                       ldap="false"
                       ldap-base-dn="cn=smppaccounts, dc=nexmo, dc=com">
            <accounts-service enabled="true"
                              service-host="@auth.service.host@"
                              service-port="3600"
                              service-app-key="n3-n5b-94nb-n5binbn409bnjbni4nv"
                              service-connect-timeout="5000"
                              service-so-timeout="5000"
                              service-ssl="false"
                              user-agent="@core.instance-id@"
                              always-try-service-first="false"/>
            <cache enabled="true"
                   cache-core-id="cache1"
                   max-items="100000"
                   eternal="false"
                   time-to-idle="900"
                   time-to-live="3600"/>
            <capacity-threshold-defaults>
                <threshold id="max-concurrent-calls" max="200"/>
            </capacity-threshold-defaults>
        </smpp-accounts>
-->
    </auth>
<!-- FIXME: And yet another network service...
    <short-codes db="true" 
                 db-pre-load-all="false" 
                 ldap="false" 
                 perform-lookup-for-alpha-numbers="false">
        <cache enabled="true"
               cache-core-id="cache1"
               max-items="10000"
               time-to-idle="900"
               time-to-live="3600"
               eternal="false"/>
    </short-codes>
-->
    <!-- The default-sip-price and default-sip-cost are the generic defaults when all else fail.
         They are not related specifically to SIP originated calls nor to SIP destination calls.
         
         SIP-287 introduced the new SIP-DIAL-IN default price (default-sip-dial-in-price)
         SIP-335 setting the sip out default price to zero, until further communications with customers.
         SIP-259 setting the sip out default price to 0.004 per minute.
         -->
         
    <!-- Legacy TTS uses min-price =0.1 while SIP uses min-price="0" -->
    <!-- tts-min-price is used by SIPApp while handling TTS-NG -->
    <charging min-price="0"
              default-cost="0.00666"
              default-sip-price="0"
              default-sip-cost="0"
              default-sip-dial-in-price="0.00400"
              default-sip-destination-price="0.00400" 
              default-inbound-price="0"
              default-inbound-cost="0"
              min-increment="6"
              recurring-increment="6"
              tts-min-price="0.1"> <!-- In the old TTS the used param is min-price=0.1 -->
        <country-specific>
            <price code="mx"
                   min-increment="60"
                   recurring-increment="60" />
        </country-specific>
    </charging>
    <gateway-info path="src/test/java/com/nexmo/voice/config/" filemask="dev-suppliers.xml" version="LATEST">
    </gateway-info>
    <tts-gateway-info path="src/test/java/com/nexmo/voice/config/" filemask="dev-tts-suppliers.xml" version="LATEST">
    </tts-gateway-info>
    <lvn-mappings path="src/test/java/com/nexmo/voice/config/" filemask="dev-mappings.xml" version="LATEST">
    </lvn-mappings>
    <prefix-map path="src/test/java/com/nexmo/voice/config/" filemask="dev-prefix.xml" version="LATEST">
    </prefix-map>
    <cache-control enabled="true">
        <caches>
            <cache type="application" size="100" expiry="P3D" refresh="PT10.7S" />
        </caches>
    </cache-control>
    <applications-service enabled="true" applications-service-host="applications-service.qa"
                                         applications-service-port="4170"
                                         applications-service-failover-self-host="applications-service-self-url.qa"
                                         applications-service-failover-sister-host="applications-service-sister-url.qa"
                                         timeout="400"
                                         retry-count="2"
                                         retry-timeout="100"
                                         base-path="/internal/applications">
        <inter-service-auth issuer="dev"
                            private-key-file="/etc/nexmo_jwt/keys_v2/private/dev/dev.properties"
                            public-keys-path="/etc/nexmo_jwt/keys_v2/public" />
        <!-- TODO: TLS params to enable HTTPS -->
    </applications-service>
    <asterisk-manager enabled="true"
                      hostname="@core.asterisk-manager.hostname@"
                      port="5038"
                      username="@core.asterisk-manager.username@"
                      password="@core.asterisk-manager.password@">
        <asterisk-ping-thread enabled="true"
                              interval="2000"
                              timeout="5000" />
        <initial-retry enabled="true"
                       number-retries="90"
                       timeout="1"
                       unit="second" />
    </asterisk-manager>
    <asterisk-agi-server enabled="true"
                         hostname="@core.asterisk-agi-server.hostname@"
                         port="5019"
                         pool-size="20"
                         maximum-pool-size="100"/>
    <provisioning-api
            remote-host="@core.provisioning-api.remote-host@"
            remote-port="8019"
            connection-timeout="3000"
            so-timeout="15000"
            user-agent="@core.instance-id@"
            ssl="false" />


    <!-- The counters-service-client is disabled and not used
    <counters-service-client enabled="false"  />   -->
</voice>
