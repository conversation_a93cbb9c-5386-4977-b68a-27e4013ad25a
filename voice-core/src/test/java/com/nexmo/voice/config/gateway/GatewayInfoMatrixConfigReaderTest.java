package com.nexmo.voice.config.gateway;

import java.io.File;

import org.junit.Assert;
import org.junit.Test;

import com.thepeachbeetle.common.xml.LoaderException;


public class GatewayInfoMatrixConfigReaderTest {
    private static final String OLD_FORMAT_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/suppliers.xml";
    private static final String COMPACT_FORMAT_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/compact.xml";
    private static final String EMPTY_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/empty.xml";
    private static final String MISSING_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/missing.xml";

    @Test
    public void testLoadOldFormatXMLFile() throws Exception {
        SIPGatewayInfoMatrixConfigReader reader = new SIPGatewayInfoMatrixConfigReader(OLD_FORMAT_FILENAME);
        reader.read(new File(OLD_FORMAT_FILENAME));
        SIPGatewayInfoMatrixConfig config = reader.getConfig();

        Assert.assertNotNull(config);
        Assert.assertEquals(17, config.getGatewayCount());
    }

    @Test
    public void testLoadCompactXMLFile() throws Exception {
        SIPGatewayInfoMatrixConfigReader reader = new SIPGatewayInfoMatrixConfigReader(COMPACT_FORMAT_FILENAME);
        reader.read(new File(COMPACT_FORMAT_FILENAME));
        SIPGatewayInfoMatrixConfig config = reader.getConfig();

        Assert.assertNotNull(config);
        Assert.assertEquals(17, config.getGatewayCount());
    }

    @Test(expected = LoaderException.class)
    public void testLoadEmptyFile() throws Exception {
        SIPGatewayInfoMatrixConfigReader reader = new SIPGatewayInfoMatrixConfigReader(EMPTY_FILENAME);
        reader.read(new File(EMPTY_FILENAME));
        SIPGatewayInfoMatrixConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

    @Test(expected = LoaderException.class)
    public void testLoadMissingFile() throws Exception {
        File f = new File(MISSING_FILENAME);
        Assert.assertFalse(f.exists());
        
        SIPGatewayInfoMatrixConfigReader reader = new SIPGatewayInfoMatrixConfigReader(MISSING_FILENAME);
        reader.read(new File(MISSING_FILENAME));
        SIPGatewayInfoMatrixConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

    @Test
    public void testEquivalentContents() throws Exception {
        SIPGatewayInfoMatrixConfigReader reader1 = new SIPGatewayInfoMatrixConfigReader(OLD_FORMAT_FILENAME);
        reader1.read(new File(OLD_FORMAT_FILENAME));
        SIPGatewayInfoMatrixConfig config1 = reader1.getConfig();
        Assert.assertNotNull(config1);

        SIPGatewayInfoMatrixConfigReader reader2 = new SIPGatewayInfoMatrixConfigReader(COMPACT_FORMAT_FILENAME);
        reader2.read(new File(COMPACT_FORMAT_FILENAME));
        SIPGatewayInfoMatrixConfig config2 = reader2.getConfig();
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }
    
}
