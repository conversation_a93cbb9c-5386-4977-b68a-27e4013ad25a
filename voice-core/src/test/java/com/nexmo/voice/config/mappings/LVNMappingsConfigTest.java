package com.nexmo.voice.config.mappings;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class LVNMappingsConfigTest {
    private static final String MAPPINGS_FILENAME = "src/test/java/com/nexmo/voice/config/mappings/mappings.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/mappings/different.xml";
    private static final String[] CHANGED_NUMBERS = new String[] {"10", "11", "12", "13", "14"};

    @Test
    public void testCreation() {
        LVNMappingsConfig config = new LVNMappingsConfig(GroupConfigTest.makeTestGroups(), LVNConfigTest.makeTestLVNs());
        Assert.assertNotNull(config);

        // TODO: Check contents here
    }

    @Test
    public void testSameContents() {
        LVNMappingsConfig config1 = new LVNMappingsConfig(GroupConfigTest.makeTestGroups(), LVNConfigTest.makeTestLVNs());
        Assert.assertNotNull(config1);
        LVNMappingsConfig config2 = new LVNMappingsConfig(GroupConfigTest.makeTestGroups(), LVNConfigTest.makeTestLVNs());
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }

    @Test
    public void testLoadSameConfig() {
        LVNMappingsConfig config1 = new LVNMappingsConfig(GroupConfigTest.makeTestGroups(), LVNConfigTest.makeTestLVNs());
        Assert.assertNotNull(config1);
        LVNMappingsConfig config2 = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }

    @Test
    public void testLoadSameFile() {
        LVNMappingsConfig config1 = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config1);
        LVNMappingsConfig config2 = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }
    
    @Test
    public void testLoadDifferentConfigs() {
        LVNMappingsConfig config1 = LVNMappingsConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config1);
        LVNMappingsConfig config2 = LVNMappingsConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotNull(config1);

        Assert.assertNotEquals(config1, config2);
        
        // Check for all differences between configs

        // LVNs added: [69]
        Assert.assertEquals(null, config1.getDestinationGeos("69"));
        Assert.assertEquals("[ams3, eu-west-1]", config2.getDestinationGeos("69").getGeos().toString());
        // LVNs removed: [51]
        Assert.assertEquals("[dal13]", config1.getDestinationGeos("51").getGeos().toString());
        Assert.assertEquals(null, config2.getDestinationGeos("51"));
        // LVNs changed: [10, 11, 12, 123456789, 13, 14]
        // ... 13 is a change of comment
        Assert.assertNotEquals(config1.getDestinationGeos("13"), config2.getDestinationGeos("13"));
        Assert.assertEquals(config1.getDestinationGeos("13").getGeos().toString(), config2.getDestinationGeos("13").getGeos().toString());
        Assert.assertNotEquals(config1.getDestinationGeos("13").isStrict(), config2.getDestinationGeos("13").isStrict());
        // ... 123456789 has moved from LON to WDC
        Assert.assertNotEquals(config1.getDestinationGeos("123456789"), config2.getDestinationGeos("123456789"));
        Assert.assertEquals("[lon1]", config1.getDestinationGeos("123456789").getGeos().toString());
        Assert.assertEquals("[wdc4]", config2.getDestinationGeos("123456789").getGeos().toString());
        Assert.assertEquals(config1.getDestinationGeos("123456789").isStrict(), config2.getDestinationGeos("123456789").isStrict());
        // ... 10-14 are now strict
        for (String number : CHANGED_NUMBERS) {
            Assert.assertNotEquals(config1.getDestinationGeos(number), config2.getDestinationGeos(number));
            Assert.assertEquals("[sng1]", config1.getDestinationGeos(number).getGeos().toString());
            Assert.assertEquals("[sng1]", config2.getDestinationGeos(number).getGeos().toString());
            Assert.assertFalse(config1.getDestinationGeos(number).isStrict());
            Assert.assertTrue(config2.getDestinationGeos(number).isStrict());
        }
    }

    @Test
    public void testHashes() {
        LVNMappingsConfig lvn1 = new LVNMappingsConfig(GroupConfigTest.makeTestGroups(), LVNConfigTest.makeTestLVNs());
        Assert.assertNotNull(lvn1);
        Assert.assertEquals(0x5DE3EC5F, lvn1.hashCode());

    }

}
