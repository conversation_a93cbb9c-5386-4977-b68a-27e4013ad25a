package com.nexmo.voice.config.prefix;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


@RunWith(PowerMockRunner.class)
@PrepareForTest(PrefixMapConfigUtils.class)
@PowerMockIgnore("javax.management.*")
public class PrefixMapConfigUtilsTest {
    private static final String PREFIX_FILE = "src/test/java/com/nexmo/voice/config/prefix/prefix.xml";
    private static final String EMPTY_FILE = "src/test/java/com/nexmo/voice/config/prefix/empty.xml";
    private static final String MISSING_FILE = "src/test/java/com/nexmo/voice/config/prefix/missing.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/prefix/different.xml";
    private static final String EXPECTED_DIFFS = "Differences:\n" + 
                                                 "Prefixes added: [1201202]\n" +
                                                 "Prefixes removed: [12012013]\n" +
                                                 "Prefixes changed: [12012016]\n";

    private static final List<String> EMPTY_FILE_LIST = Collections.emptyList();
    private static final List<String> FILE_LIST;
    
    static {
        FILE_LIST = new ArrayList<String>();
        FILE_LIST.add("dev-19991231235900.xml");
        FILE_LIST.add("dev-20200101000000.xml");
        FILE_LIST.add("dev-19991231235903.xml");
    }
    
    @Before
    public void setUp() throws Exception {
        // We cannot use PowerMockito.mockStatic() because that will also mock the methods we want to test...
        PowerMockito.spy(PrefixMapConfigUtils.class);

        // We must use PowerMockito.doReturn(...).when(...) rather than PowerMockito.when(...).thenReturn(...)
        // because getFileList() is *private* so the latter form will not compile
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  EMPTY_FILE_LIST).when(PrefixMapConfigUtils.class, "getFileList", Matchers.isNull(), Matchers.anyString());
        PowerMockito.doAnswer((Answer<List<String>>) invocation ->  FILE_LIST).when(PrefixMapConfigUtils.class, "getFileList", Matchers.eq("prefix"), Matchers.anyString());
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testSelectNone() {
        String file = PrefixMapConfigUtils.selectFile(null, "dev-*.xml", "LATEST");

        Assert.assertNull(file);
    }    
    
    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testSelectLatest() {
        String file = PrefixMapConfigUtils.selectFile("prefix", "dev-*.xml", "LATEST");

        Assert.assertEquals("prefix/dev-20200101000000.xml", file);
    }    
    
    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testSelectSpecific() {
        String file = PrefixMapConfigUtils.selectFile("prefix", "dev-*.xml", "19991231235900");

        Assert.assertEquals("prefix/dev-19991231235900.xml", file);
    }    

    @Test
    @Ignore("TODO: Handle this case")
    public void testSelectSpecificMultiMatch() {
        String file = PrefixMapConfigUtils.selectFile("prefix", "dev-*.xml", "19991231");

        Assert.assertEquals("prefix/dev-19991231235900.xml", file);
        Assert.assertEquals("prefix/dev-19991231235903.xml", file);
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testLoadConfigFromXMLFile() {
        PrefixMapConfig config = PrefixMapConfigUtils.loadConfigFromXMLFile(PREFIX_FILE);
        Assert.assertNotNull(config);

        // Expected contents
        PrefixMapConfig expected = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertEquals(expected, config);
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testLoadConfigFromEmptyFile() {
        PrefixMapConfig config = PrefixMapConfigUtils.loadConfigFromXMLFile(EMPTY_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testLoadConfigFromMissingFile() {
        PrefixMapConfig config = PrefixMapConfigUtils.loadConfigFromXMLFile(MISSING_FILE);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testLoadConfigNullFilename() {
        PrefixMapConfig config = PrefixMapConfigUtils.loadConfigFromXMLFile(null);

        Assert.assertNull(config);
    }

    @Test
    @PrepareForTest(PrefixMapConfigUtils.class)
    public void testCompareConfigs() {
        PrefixMapConfig config1 = PrefixMapConfigUtils.loadConfigFromXMLFile(PREFIX_FILE);
        PrefixMapConfig config2 = PrefixMapConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotEquals(config1, config2);

        final String actual = PrefixMapConfigUtils.compareConfigs(config1,config2);
        Assert.assertEquals(EXPECTED_DIFFS, actual);
    }
    
}
