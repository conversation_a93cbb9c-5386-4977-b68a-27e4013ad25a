package com.nexmo.voice.config.mappings;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class MediaDestinationTest {
    @Test
    public void testCreation() {
        MediaDestination dest = new MediaDestination("lon1", null);
        Assert.assertNotNull(dest);
    }
    
    @Test
    public void testSameContents() {
        MediaDestination dest1 = new MediaDestination("lon1", null);
        Assert.assertNotNull(dest1);
        MediaDestination dest2 = new MediaDestination("lon1", null);
        Assert.assertNotNull(dest2);

        Assert.assertEquals(dest1, dest2);
    }

    @Test
    public void testDifferentContents() {
        MediaDestination dest1 = new MediaDestination("lon1", null);
        Assert.assertNotNull(dest1);
        MediaDestination dest2 = new MediaDestination("sng1", null);
        Assert.assertNotNull(dest2);

        Assert.assertNotEquals(dest1, dest2);
    }
    
    @Test
    public void testHashes() {
        MediaDestination dest1 = new MediaDestination("lon1", null);
        Assert.assertNotNull(dest1);
        Assert.assertEquals(0x0626063B, dest1.hashCode());

        MediaDestination dest2 = new MediaDestination("lon1", "");
        Assert.assertNotNull(dest2);
        Assert.assertEquals(0x0626063B, dest2.hashCode()); // Same hash because "".hashCode() == 0

        MediaDestination dest3 = new MediaDestination("lon1", "Created as a test!");
        Assert.assertNotNull(dest3);
        Assert.assertEquals(0xEDF5E5F5, dest3.hashCode());

        MediaDestination dest4 = new MediaDestination("dal13", null);
        Assert.assertNotNull(dest4);
        Assert.assertEquals(0xB02D5FF0, dest4.hashCode());
    }
    
}
