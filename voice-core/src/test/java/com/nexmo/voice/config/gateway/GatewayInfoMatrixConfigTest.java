package com.nexmo.voice.config.gateway;

import org.junit.Assert;
import org.junit.Test;


public class GatewayInfoMatrixConfigTest {
    private static final String OLD_FORMAT_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/suppliers.xml";
    private static final String COMPACT_FORMAT_FILENAME = "src/test/java/com/nexmo/voice/config/gateway/compact.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/gateway/different.xml";


    @Test
    public void testEquivalentContents() {
        SIPGatewayInfoMatrixConfig config1 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(OLD_FORMAT_FILENAME);
        Assert.assertNotNull(config1);
        SIPGatewayInfoMatrixConfig config2 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(COMPACT_FORMAT_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }
    
    @Test
    public void testMetadataIsIgnored() {
        SIPGatewayInfoMatrixConfig config1 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(OLD_FORMAT_FILENAME);
        Assert.assertNotNull(config1);
        SIPGatewayInfoMatrixConfig config2 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(COMPACT_FORMAT_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }
    
    @Test
    public void testSameContents() {
        SIPGatewayInfoMatrixConfig config1 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(OLD_FORMAT_FILENAME);
        Assert.assertNotNull(config1);
        SIPGatewayInfoMatrixConfig config2 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(OLD_FORMAT_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);

        Assert.assertEquals(config1.getGatewayCount(), config2.getGatewayCount());
        // TODO: Check other fields here
    }
    
    @Test
    public void testDifferentConfigs() {
        SIPGatewayInfoMatrixConfig config1 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(COMPACT_FORMAT_FILENAME);
        Assert.assertNotNull(config1);
        SIPGatewayInfoMatrixConfig config2 = GatewayInfoMatrixConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotNull(config1);

        Assert.assertNotEquals(config1, config2);
        
        // Check for all differences in configs...
        //  - test-carrier-aws0 removed
        SupplierMappingConfig removed1 = config1.getGatewayInfo("test-carrier-aws0");
        Assert.assertNotNull(removed1);
        SupplierMappingConfig removed2 = config2.getGatewayInfo("test-carrier-aws0");
        Assert.assertNull(removed2);
        //  - test-carrier-aws5 added
        SupplierMappingConfig added1 = config1.getGatewayInfo("test-carrier-aws5");
        Assert.assertNull(added1);
        SupplierMappingConfig added2 = config2.getGatewayInfo("test-carrier-aws5");
        Assert.assertNotNull(added2);
        //  - gw2 changed (endpoint 0.0.0.0 -> *******)
        SupplierMappingConfig changed1_1 = config1.getGatewayInfo("gw2");
        Assert.assertNotNull(changed1_1);
        SupplierMappingConfig changed1_2 = config2.getGatewayInfo("gw2");
        Assert.assertNotNull(changed1_2);
        Assert.assertNotEquals(changed1_1, changed1_2);
        //  - telserv changed (mt-cost-matrix removed)
        SupplierMappingConfig changed2_1 = config1.getGatewayInfo("telserv");
        Assert.assertNotNull(changed2_1);
        SupplierMappingConfig changed2_2 = config2.getGatewayInfo("telserv");
        Assert.assertNotNull(changed2_2);
        Assert.assertNotEquals(changed2_1, changed2_2);
        // real-ibasis changed (default price 0.0666 -> 0.0789)
        SupplierMappingConfig changed3_1 = config1.getGatewayInfo("real-ibasis");
        Assert.assertNotNull(changed3_1);
        SupplierMappingConfig changed3_2 = config2.getGatewayInfo("real-ibasis");
        Assert.assertNotNull(changed3_2);
        Assert.assertNotEquals(changed3_1, changed3_2);
    }
    
}
