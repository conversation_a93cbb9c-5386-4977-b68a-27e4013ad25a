package com.nexmo.voice.config.gateway;

import org.junit.Test;
import org.junit.Assert;

import com.nexmo.voice.config.gateway.SupplierMappingConfig;
import com.thepeachbeetle.messaging.hub.config.pricing.PriceMatrixList;


public class SupplierMappingConfigTest {
    private static final String TEST_NAME = "test-name";
    private static final String TEST_ENDPOINT = "1.1.1.1";
    private static final String TEST_CALLERID = "42";
    private static final String TEST_PREFIX = "X";
    private static final PriceMatrixList TEST_PRICE_MATRIX_LIST = null;

    private static final int EXPECTED_HASHCODE = -1387781969;
    private static final String EXPECTED_XML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n" + 
                                               "<supplier-mapping name=\"test-name\" endpoint=\"1.1.1.1\" caller-id=\"42\" number-prefix=\"X\" />\r\n\r\n";

    @Test
    public void testCreation() {
        final SupplierMappingConfig instance = createTestInstance();
        Assert.assertEquals(TEST_NAME, instance.getName());
        Assert.assertEquals(TEST_ENDPOINT, instance.getEndpoint());
        Assert.assertEquals(TEST_CALLERID, instance.getCallerId());
        Assert.assertEquals(TEST_PREFIX, instance.getNumberPrefix());
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(TEST_PRICE_MATRIX_LIST,instance.getCostMatrix()));
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(TEST_PRICE_MATRIX_LIST,instance.getInboundCostMatrix()));
    }

    @Test
    public void testCreationWithNulls() {
        final SupplierMappingConfig instance = new SupplierMappingConfig(null, null, null, null, null, null);
        Assert.assertNull(instance.getName());
        Assert.assertNull(instance.getEndpoint());
        Assert.assertNull(instance.getCallerId());
        Assert.assertNull(instance.getNumberPrefix());
        Assert.assertNull(instance.getCostMatrix());
        Assert.assertNull(instance.getInboundCostMatrix());
    }

    @Test
    public void testToString() {
        final SupplierMappingConfig instance = createTestInstance();
        Assert.assertEquals(EXPECTED_XML, instance.toString());
    }

    @Test
    public void testEquals() {
        final SupplierMappingConfig instance1 = createTestInstance();
        final SupplierMappingConfig instance2 = createTestInstance();
        Assert.assertFalse(instance1 == instance2); // Different objects

        // All fields identical
        Assert.assertEquals(instance1.getName(), instance2.getName());
        Assert.assertEquals(instance1.getEndpoint(), instance2.getEndpoint());
        Assert.assertEquals(instance1.getCallerId(), instance2.getCallerId());
        Assert.assertEquals(instance1.getNumberPrefix(), instance2.getNumberPrefix());
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(instance1.getCostMatrix(),instance2.getCostMatrix()));
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(instance1.getInboundCostMatrix(),instance2.getInboundCostMatrix()));

        Assert.assertEquals(instance1, instance2);
    }

    @Test
    public void testDifference() {
        final SupplierMappingConfig instance1 = createTestInstance();
        final SupplierMappingConfig instance2 = new SupplierMappingConfig("DifferentName", TEST_ENDPOINT, TEST_CALLERID, TEST_PREFIX, TEST_PRICE_MATRIX_LIST, TEST_PRICE_MATRIX_LIST);
        Assert.assertFalse(instance1 == instance2); // Different objects

        // Difference in name only
        Assert.assertNotEquals(instance1.getName(), instance2.getName());
        Assert.assertEquals(instance1.getEndpoint(), instance2.getEndpoint());
        Assert.assertEquals(instance1.getEndpoint(), instance2.getEndpoint());
        Assert.assertEquals(instance1.getCallerId(), instance2.getCallerId());
        Assert.assertEquals(instance1.getNumberPrefix(), instance2.getNumberPrefix());
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(instance1.getCostMatrix(),instance2.getCostMatrix()));
        Assert.assertTrue(PriceMatrixListUtils.arePriceMatrixListsEquivalent(instance1.getInboundCostMatrix(),instance2.getInboundCostMatrix()));

        Assert.assertNotEquals(instance1, instance2);
    }

    @Test
    public void testHashcode() {
        final SupplierMappingConfig instance = createTestInstance();
        Assert.assertEquals(EXPECTED_HASHCODE, instance.hashCode());
    }

    private static SupplierMappingConfig createTestInstance() {
        return new SupplierMappingConfig(TEST_NAME,
                                         TEST_ENDPOINT,
                                         TEST_CALLERID,
                                         TEST_PREFIX,
                                         TEST_PRICE_MATRIX_LIST,
                                         TEST_PRICE_MATRIX_LIST);
    }

}
