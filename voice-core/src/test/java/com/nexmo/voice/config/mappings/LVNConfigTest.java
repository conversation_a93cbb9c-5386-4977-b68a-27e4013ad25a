package com.nexmo.voice.config.mappings;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class LVNConfigTest {
    @Test
    public void testCreation() {
        LVNConfig lvn = new LVNConfig("123456789", "LON", null);
        Assert.assertNotNull(lvn);
    }
    
    @Test
    public void testSameContents() {
        LVNConfig lvn1 = new LVNConfig("123456789", "LON", null);
        Assert.assertNotNull(lvn1);
        LVNConfig lvn2 = new LVNConfig("123456789", "LON", null);
        Assert.assertNotNull(lvn2);

        Assert.assertEquals(lvn1, lvn2);
    }

    @Test
    public void testDifferentContents() {
        LVNConfig lvn1 = new LVNConfig("123456789", "LON", null);
        Assert.assertNotNull(lvn1);
        LVNConfig lvn2 = new LVNConfig("51", "DAL", null);
        Assert.assertNotNull(lvn2);

        Assert.assertNotEquals(lvn1, lvn2);
    }
    
    @Test
    public void testHashes() {
        LVNConfig lvn1 = new LVNConfig("123456789", "LON", null);
        Assert.assertNotNull(lvn1);
        Assert.assertEquals(0x2C930989, lvn1.hashCode());

        LVNConfig lvn2 = new LVNConfig("123456789", "LON", "");
        Assert.assertNotNull(lvn2);
        Assert.assertEquals(0x2C930989, lvn2.hashCode()); // Same hash because "".hashCode() == 0

        LVNConfig lvn3 = new LVNConfig("123456789", "LON", "Created as a test!");
        Assert.assertNotNull(lvn3);
        Assert.assertEquals(0x1462E943, lvn3.hashCode());

        LVNConfig lvn4 = new LVNConfig("51", "DAL", null);
        Assert.assertNotNull(lvn4);
        Assert.assertEquals(0x00392A6C, lvn4.hashCode());
    }

    public static List<LVNConfig> makeTestLVNs() {
        List<LVNConfig> ret = new ArrayList<>();

        ret.add( new LVNConfig("1", "WDC", null) );
        ret.add( new LVNConfig("2", "WDC", null) );
        ret.add( new LVNConfig("3", "WDC", null) );
        ret.add( new LVNConfig("4", "WDC", null) );
        ret.add( new LVNConfig("5", "WDC", null) );
        ret.add( new LVNConfig("6", "WDC", null) );
        ret.add( new LVNConfig("7", "WDC", null) );
        ret.add( new LVNConfig("8", "WDC", null) );
        ret.add( new LVNConfig("10", "SNG", null) );
        ret.add( new LVNConfig("11", "SNG", null) );
        ret.add( new LVNConfig("12", "SNG", null) );
        ret.add( new LVNConfig("13", "SNG", null) );
        ret.add( new LVNConfig("14", "SNG", null) );
        ret.add( new LVNConfig("15:30", "LON", null) );
        ret.add( new LVNConfig("30:50", "LON", null) );
        ret.add( new LVNConfig("123456789", "LON", null) );
        ret.add( new LVNConfig("51", "DAL", null) );

        return ret;
    }

}
