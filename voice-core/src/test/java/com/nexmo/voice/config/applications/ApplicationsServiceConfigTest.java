package com.nexmo.voice.config.applications;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class ApplicationsServiceConfigTest {
    
    @Test
    public void testCreation() {
        ApplicationsServiceConfig config = new ApplicationsServiceConfig(false, null, 0, null, null, null, null, 0, 0, 0);
        Assert.assertNotNull(config);
    }
/*
    @Test
    public void testDefaultContents() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache);

        Assert.assertEquals(1000, cache.getSize());
        Assert.assertEquals(Duration.ofMinutes(60), cache.getExpiry());
        Assert.assertEquals(Duration.ofMinutes(10), cache.getRefresh());
    }

    @Test
    public void testSameContents() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, "1000", "PT60M", "PT10M");
        Assert.assertNotNull(cache1);
        CacheConfig cache2 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache2);

        Assert.assertEquals(cache1, cache2);
    }

    @Test
    public void testDifferentContents() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache1);
        CacheConfig cache2 = new CacheConfig("random", null, null, null);
        Assert.assertNotNull(cache2);

        Assert.assertNotEquals(cache1, cache2);
    }

    @Test
    public void testHashes() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache1);
        Assert.assertEquals(0xB364B061, cache1.hashCode());

    }
*/

}
