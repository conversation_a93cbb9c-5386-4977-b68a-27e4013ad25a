package com.nexmo.voice.config.mappings;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;

import com.nexmo.voice.config.mappings.LVNMappingsConfig;
import com.thepeachbeetle.common.xml.LoaderException;


public class LVNMappingsConfigReaderTest {
    private static final String MAPPINGS_FILENAME = "src/test/java/com/nexmo/voice/config/mappings/mappings.xml";
    private static final String EMPTY_FILENAME = "src/test/java/com/nexmo/voice/config/mappings/empty.xml";
    private static final String MISSING_FILENAME = "src/test/java/com/nexmo/voice/config/mappings/missing.xml";


    @Test
    public void testLoadXMLFile() throws Exception {
        LVNMappingsConfigReader reader = new LVNMappingsConfigReader(MAPPINGS_FILENAME);
        reader.read(new File(MAPPINGS_FILENAME));
        LVNMappingsConfig config = reader.getConfig();

        Assert.assertNotNull(config);

        // TODO: check contents
    }

    @Test(expected = LoaderException.class)
    public void testLoadEmptyFile() throws Exception {
        LVNMappingsConfigReader reader = new LVNMappingsConfigReader(EMPTY_FILENAME);
        reader.read(new File(EMPTY_FILENAME));
        LVNMappingsConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

    @Test(expected = LoaderException.class)
    public void testLoadMissingFile() throws Exception {
        File f = new File(MISSING_FILENAME);
        Assert.assertFalse(f.exists());
        
        LVNMappingsConfigReader reader = new LVNMappingsConfigReader(MISSING_FILENAME);
        reader.read(new File(MISSING_FILENAME));
        LVNMappingsConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

}
