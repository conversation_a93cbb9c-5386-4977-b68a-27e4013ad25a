package com.nexmo.voice.config.prefix;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;

import com.nexmo.voice.config.prefix.PrefixMapConfig;
import com.thepeachbeetle.common.xml.LoaderException;


public class PrefixMapConfigReaderTest {
    private static final String PREFIX_FILENAME = "src/test/java/com/nexmo/voice/config/prefix/prefix.xml";
    private static final String EMPTY_FILENAME = "src/test/java/com/nexmo/voice/config/prefix/empty.xml";
    private static final String MISSING_FILENAME = "src/test/java/com/nexmo/voice/config/prefix/missing.xml";


    @Test
    public void testLoadXMLFile() throws Exception {
        PrefixMapConfigReader reader = new PrefixMapConfigReader(PREFIX_FILENAME);
        reader.read(new File(PREFIX_FILENAME));
        PrefixMapConfig config = reader.getConfig();

        Assert.assertNotNull(config);

        // Expected contents
        PrefixMapConfig expected = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertEquals(expected, config);
    }

    @Test(expected = LoaderException.class)
    public void testLoadEmptyFile() throws Exception {
        PrefixMapConfigReader reader = new PrefixMapConfigReader(EMPTY_FILENAME);
        reader.read(new File(EMPTY_FILENAME));
        PrefixMapConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

    @Test(expected = LoaderException.class)
    public void testLoadMissingFile() throws Exception {
        File f = new File(MISSING_FILENAME);
        Assert.assertFalse(f.exists());
        
        PrefixMapConfigReader reader = new PrefixMapConfigReader(MISSING_FILENAME);
        reader.read(new File(MISSING_FILENAME));
        PrefixMapConfig config = reader.getConfig();

        Assert.assertNull(config); // Shouldn't reach here
    }

}
