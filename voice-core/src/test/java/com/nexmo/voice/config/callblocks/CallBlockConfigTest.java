package com.nexmo.voice.config.callblocks;

import org.jdom.Element;
import org.junit.Assert;
import org.junit.Test;

public class CallBlockConfigTest {

    @Test
    public void testCreation() {
        CallBlockConfig callBlockConfig = new CallBlockConfig("my-account", "SG", "63000");
        Assert.assertNotNull(callBlockConfig);
    }

    @Test
    public void testGetters() {
        CallBlockConfig callBlockConfig = new CallBlockConfig("my-account", "SG", "63000");
        Assert.assertEquals("my-account", callBlockConfig.getAccount());
        Assert.assertEquals("SG", callBlockConfig.getDestinationCountryCode());
        Assert.assertEquals("63000", callBlockConfig.getCallIdPrefix());
    }

    @Test
    public void testToXML() {
        CallBlockConfig callBlockConfig = new CallBlockConfig("my-account", "SG", "63000");
        final Element callBlock = new Element(CallBlockConfigLoader.ROOT_NODE);
        callBlock.setAttribute("account", callBlockConfig.getAccount());
        callBlock.setAttribute("destination", callBlockConfig.getDestinationCountryCode());
        callBlock.setAttribute("call-id", callBlockConfig.getCallIdPrefix());
        Assert.assertEquals(callBlock.toString(), callBlockConfig.toXML().toString());
    }

    @Test
    public void testToString() {
        CallBlockConfig callBlockConfig = new CallBlockConfig("my-account", "SG", "63000");
        String expected = "CallBlockConfig [account=" + callBlockConfig.getAccount() + "; destination=" + callBlockConfig.getDestinationCountryCode() + "; call-id=" + callBlockConfig.getCallIdPrefix() + "]";
        Assert.assertEquals(expected, callBlockConfig.toString());
    }
}
