package com.nexmo.voice.config.caches;

import java.time.Duration;
import java.time.format.DateTimeParseException;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class CacheConfigTest {
    public final static String KEY_NAME = "application";
    
    @Test
    public void testCreation() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache);
    }
    
    @Test
    public void testDefaultContents() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache);

        Assert.assertEquals(1000, cache.getSize());
        Assert.assertEquals(Duration.ofMinutes(60), cache.getExpiry());
        Assert.assertEquals(Duration.ofMinutes(10), cache.getRefresh());
    }

    @Test
    public void testSameContents() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, "1000", "PT60M", "PT10M");
        Assert.assertNotNull(cache1);
        CacheConfig cache2 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache2);

        Assert.assertEquals(cache1, cache2);
    }

    @Test
    public void testDifferentContents() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache1);
        CacheConfig cache2 = new CacheConfig("random", null, null, null);
        Assert.assertNotNull(cache2);

        Assert.assertNotEquals(cache1, cache2);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testBadParameters1() {
        CacheConfig cache = new CacheConfig("badger", null, null, null);
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBadParameters2() {
        CacheConfig cache = new CacheConfig(KEY_NAME, "-3", null, null);
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = DateTimeParseException.class)
    public void testBadParameters3() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, "3D", null); // Must start with P
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = DateTimeParseException.class)
    public void testBadParameters4() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, "P6W"); // No "weeks"
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = DateTimeParseException.class)
    public void testBadParameters5() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, "PT0.1234567890S"); // 0..9 digits for fraction
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = DateTimeParseException.class)
    public void testBadParameters6() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, null, "P60M"); // Minutes must come after T
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBadParameters7() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, "p-3D", null); // No negative durations
        Assert.fail("Should have thrown an exception");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBadParameters8() {
        CacheConfig cache = new CacheConfig(KEY_NAME, null, "-Pt1H", null); // No negative durations
        Assert.fail("Should have thrown an exception");
    }

    @Test
    public void testHashes() {
        CacheConfig cache1 = new CacheConfig(KEY_NAME, null, null, null);
        Assert.assertNotNull(cache1);
        Assert.assertEquals(0xB364B061, cache1.hashCode());

    }


}
