package com.nexmo.voice.config.prefix;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.log4j.Logger;
import org.junit.Assert;
import org.junit.Test;


public class PrefixMapConfigTest {
    private static final String MAPPINGS_FILENAME = "src/test/java/com/nexmo/voice/config/prefix/prefix.xml";
    private static final String DIFFS_FILE = "src/test/java/com/nexmo/voice/config/prefix/different.xml";
    private static final String EXPECTED_STRING = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n" +
                                                  "<prefix-map country-code=\"806\">...9 prefix mappings...</prefix-map>\r\n\r\n";

    @Test
    public void testCreation() {
        PrefixMapConfig config = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config);

        Assert.assertEquals("806", config.getCountryCode());
        Assert.assertFalse(config.isEmpty());
        Assert.assertEquals(9, config.size());
        Assert.assertEquals("806103", config.getGroup("12012017"));
        Assert.assertTrue(config.isEnabled());
    }

    @Test
    public void testToString() {
        PrefixMapConfig config = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config);

        Assert.assertEquals(EXPECTED_STRING, config.toString());
    }

    @Test
    public void testSameContents() {
        PrefixMapConfig config1 = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config1);
        PrefixMapConfig config2 = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }

    @Test
    public void testLoadSameConfig() {
        PrefixMapConfig config1 = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config1);
        PrefixMapConfig config2 = PrefixMapConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }

    @Test
    public void testLoadSameFile() {
        PrefixMapConfig config1 = PrefixMapConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config1);
        PrefixMapConfig config2 = PrefixMapConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config2);

        Assert.assertEquals(config1, config2);
    }
    
    @Test
    public void testLoadDifferentConfigs() {
        PrefixMapConfig config1 = PrefixMapConfigUtils.loadConfigFromXMLFile(MAPPINGS_FILENAME);
        Assert.assertNotNull(config1);
        PrefixMapConfig config2 = PrefixMapConfigUtils.loadConfigFromXMLFile(DIFFS_FILE);
        Assert.assertNotNull(config1);

        Assert.assertNotEquals(config1, config2);
        
        // Check for all differences between configs

        // Prefix added: [1201202]
        Assert.assertEquals(null, config1.get("1201202"));
        Assert.assertEquals("1201202=>806142", config2.get("1201202").toString());
        // Prefix removed: [12012013]
        Assert.assertEquals("12012013=>806103", config1.get("12012013").toString());
        Assert.assertEquals(null, config2.get("12012013"));
        // Prefix changed: [12012016]
        Assert.assertNotEquals(config1.get("12012016"), config2.get("12012016").toString());
        Assert.assertEquals("12012016=>806103", config1.get("12012016").toString());
        Assert.assertEquals("12012016=>806100", config2.get("12012016").toString());
    }

    @Test
    public void testHashes() {
        PrefixMapConfig config = new PrefixMapConfig("806", MappingConfigTest.makeTestMappings());
        Assert.assertNotNull(config);
        Assert.assertEquals(0x817A3D53, config.hashCode());

    }

    @Test(expected = IllegalArgumentException.class)
    public void testInvalidPrefix() {
        List<MappingConfig> mappings = new ArrayList<MappingConfig>();
        mappings.add(new MappingConfig("12345", "806001"));
        PrefixMapConfig config = new PrefixMapConfig("888", mappings);
    }

    @Test
    public void testPrefixLookup() {
        List<MappingConfig> mappings = new ArrayList<MappingConfig>();
        mappings.add(new MappingConfig("1234", "000001"));
        mappings.add(new MappingConfig("12345", "000002"));
        mappings.add(new MappingConfig("12346", "000003"));
        mappings.add(new MappingConfig("1235", "000004"));
        PrefixMapConfig config = new PrefixMapConfig(null, mappings);
        Assert.assertEquals(null, config.lookup("0000000000"));
        Assert.assertEquals("12345=>000002", config.lookup("1234567890").toString());
        Assert.assertEquals("1234=>000001", config.lookup("1234700000").toString());
        Assert.assertEquals("1235=>000004", config.lookup("12358132134").toString());
        Assert.assertEquals(null, config.lookup("1236660000"));
        Assert.assertEquals(null, config.lookup("2222222222"));
    }

}
