# CHANGELIST
**41.10**
* VOICEN-675: Route lookup by Supplier Id
* VOICEN-441: Insert locationId into emergency call CDR

**41.9**
* VOICEN-215: Integrate messaging jar random pool cache into SipApp
* VOICEN-726: Send prefixTo to bss team even when To is less than 4 digits

**41.8**
* VOICEPH-1217: upon enforcer failure, retry with sister regional url

**41.7**
* VOICEN-597: Remove usage of rtc-use-original-cli flag
* VOICEN-630: Add time threshold on DynamoDB findItem operations
* VOICEPH-1102: Parallelize build process (incl per-class parallelization of the tests)

**41.6**
* VOICEN-647: Treat all PSIP trunking calls as PSTN
* VOICEN-659: Send dest network type in the CDR for billing purposes
* VOICEN-638: Route lookup using Supplier Id

**41.5**
* VOICEN-535: Capture emergency call failover reason in CDR
* VOICEN-517: Fix a thread leak in the DynamoDB client by ensuring only one instance is created
* VOICEN-616: <PERSON><PERSON><PERSON><PERSON> should not search for destination in Shortcodes db for outbound calls
* VOICEN-503: <PERSON><PERSON><PERSON><PERSON> disable-must-own for countries specified in tags
* VOICEN-621: Only emergency calls that failed over primary route should be chargable

**41.4**
* VOICEPH-964: Handle NegativeBalanceRefundException for vPricing migration

**41.3**
* VOICEPH-886: Define a metric to track the number of times the prices are different with and without vPricing enabled
* VOICEPH-905: Handle vQuota down and account-out-of-balance scenarios
* VOICEPH-945: BSS vQuota service timeout should be less than voice side configuration
* VOICEPH-946: Reject voice call with 501 error instead of BYE If balance Is insufficient in first 12 seconds of BRIDGE event
* VOICEPH-948: Implement 'force-price' for vQuota in Voice calls
* VOICEPH-949: Implement 'real-price' in callback and CDR for vQuota in Voice calls
* VOICEPH-961: Send 'to' number for voice_inbound product-type in price-Impact API
* VOICEPH-1106: Consistent precision for totalPrice in CDR for BSS vPricing
* VOICEPH-1105: Handle scenario where PHUB price is explicitly set to zero but BSS vPricing price is non-zero

**41.2**
* VOICEN-503 : SipApp disable-must-own for countries specified in tags
* VOICEN-459: Emergency calling metrics
* VOICEN-516: Emergency call price and cost should be zero
* VOICEN-482: Restrict LVN types for emergency call
* VOICEN-560: Additional emergency calling metrics

**41.1**
* VAPI-5072 : Routing based on diversion

**41.0**
* VOICEN-24: Implement Emergency Calling 

**40.3**
* APIVOICEF-241: BYON STIR/SHAKEN A-attestation
* VOICEN-349: SipApp: Read the authentication source header and set CDR flag

**40.2**
* VOICEN-297: SipApp should write final CDR for all non-retry-able errors
* VOICEN-275: SipApp: Applications Service add failover logic

**40.1**
* VOICEN-244: SipApp - Add new CDR fields for routing rule used
* VAPI-4956: get-route-for-number returns the trunk region
* APIVOICEF-163: Voice migration to vPricing
* APIVOICEF-233: Use routing's forceSender number as the source for numberType, sourceCountry, and callType in Voice CDRs.

**40.0**
* VOICEN-11: NCCO application to psip domain
* VOICEN-253: Update messaging jar to remove routing info logs

**39.0**
* VOICEN-206: Change original cli capability name to include "rtc"

**38.9**
* VOICEN-130: Send callbacks
* VOICEN-141: Block only non-digit CLIs for now
* VOICEN-166: Update phonelib to 8.13.43
* VOICEN-167: save incoming PSTN call CLI in the in region dynamodb
* VOICEN-174: Use original cli and diversion header
* VOICEN-196: Add repo for jfrog in build.gradle

**38.8**
* VOICEN-1:  migrating from GET to POST evaluate endpoint
* VOICEN-48: call type new CDR field
* VOICEPH-264: refactoring of number type field in CDR

**38.7**
* VOICEN-65: Enhance TTSNG handler in SipApp to write Source Country in tts and verify CDRs
* VOICEN-132: Revert handlecallback flow when quota is down (RTC-2913)
* VOICEN-137: Add logs for valid e164 callerId

**38.6**
* RTC-3816: pass phub rtc-sip-absolute-timeout capability value (if any) towards asterisk
* RTC-3377: add number type information to Voice CDRs
* RTC-3900: amend chargeable CDR field to Voice CDRs

**38.5**
* RTC-3788: SipApp to allow to make a demo tts voice call with randompool number as CLI
* RTC-2913: Send callbacks as soon as the call ends even if the cdr is not rated because of quota issue
* RTC-3549: Domestic routing for Voice - Messaging jar
* RTC-3666: Domestic Routing: create a SipApp feature flag for domestic routing feature

**38.4**
* RTC-2846: integrate with enforcer v3 evaluate endpoint and add BLOCKING_SUBSYSTEM to CDR

**38.3**
* RTC-3462: Messaging Jar update to fix random pool issue.
* RTC-2693: Rejected 402 "Not enough credit" CDR created for successful VBC calls if account has no funds and price is zero
* RTC-3506: Inbound call routing for LVN linked to application domain

**38.2**
* RTC-3338: Domain Service Metrics
* RTC-3291: SG Prefix 658084* Request to update to the latest Google libphone version
* RTC-2855: SIPApp should failover domains service if region's primary is unavailable

**38.1**
* RTC-3020: Bugfix: handle event for congestion calls as cancelled and final to prevent thread pool leak
* RTC-3128: Replace the list of SIP URIs for inbound domain trunking in API request
* RTC-3018: SipApp - Add country value to CDRs for blocked calls
* RTC-2960: Handle lookupRoute for no route found flow for TTS Verify calls
* RTC-2995: SipApp - Enhance SIPApp reason description in unknown cli blocked call CDRs

**38.0**
* RTC-1868: Domains service SIP trunking

**37.5**
* RTC-2477: SipApp retry for 503 error from enforcer-service
* RTC-2421: Handle lookupRoute for no route found flow

**37.4**
* IM-3956: SipApp dirty shutdown - Increase heap size to 16GB from 8GB

**37.3**
* RTC-2513: SIP Attestation is broken and not checking for KYC when used own LVN 
* RTC-2474: Do not block Unknown CLI calls to Germany for DT Project

**37.2**
* RTC-2207: SipApp update messaging jar libraries with exclude
* RTC-2366: Debug logs for unknown cli & time reporting for JMX reload notifications
* RTC-1974: implement retry, if the http requests fails AND if the time of request-response is less than 100ms

**37.1**
* RTC-1723: SipApp - Verify attestation
* RTC-1971: Update dockerfile java 8 base image

**37.0**
* SIP-1763: Asterisk 20 Upgrade
* SIP-2054: Extract PDD from userField
* SIP-1764: Calculate Duration from userField
* SIP-2116: Keep the Asterisk version in the call context and in the CDR
* SIP-2050: Upgrade Asterisk-Java Lib
* SIP-2044: Fix Negative Test
  
**36.9**
* RTC-1623: ConfigDB error metrics
* RTC-1713: ShortCodesDB error metrics
* RTC-1818: ConversationService error metrics

**36.8**
* update libphonenumber to 8.13.16

**36.7**
* RTC-1557: Attestation-Level fix for BYON
  
**36.6**
* RTC-1439: Release SipApp with BYON feature
* RTC-1160: SipApp should not treat the non vonage number as LVN for routing purposes 
* RTC-1158: byon verified caller id support for outbound calls

**36.5**
* RTC-1399: SIPApp CDR New Fields Addition - productPath

**36.4**
* SipApp Enforcer V2 Release

**36.3**
* RTC-1003: Bugfix: allow calls from number sourced from random pool when unknown CLI blocking enabled
* RTC-1017: Bugfix: CDR for blocked call due to unknown CLI has wrong status
* RTC-1210: Log the validation status of the calling number

**36.2**
* RTC-1055:Callblocking API failures taking more than 250 ms

**36.1.5**
* AsyncQuota Deployment

**36.1**
* VAPI-4199:Add region to application

**36.0**
* Use debian based nexmo-java image as newer image needs more performance testing done
* PREQ-3741 : fixing anonymous and unknown CLI
* SIP-2904:Callblocking Sipapp changes

**35.1**
* SIP-3122: PSIP Network type fields should have values in CDRs
* RTC-935: Fix SIPApp build to use nexmo-java:8-corretto-jdk-minideb

**35.0**
* RTC-919: Remove unused static lib with unused jars

**34.0**
* SIP-2954: Fix randomizer for voice
* SIP-2933: Fix vulnerable packages coming in to voice-core(SIPApp) as part of messaging.jar

**33.9**
* SIP-2954: Fix randomizer for voice
* SIP-2847: Add d.sipTransport filed in CDR containing TCP or TLS related calls
* HTOCR-1554:Remove messaging.jar org.jvnet.staxex dependency for gradle error
* RTC-834: Remove VERBOSE_CALL_MONITOR to free up VictoriaMetrics space

**33.8**
* SIP-2263: Strip whitespace in caller ID for source-based pricing
* SIP-2765: Update Asterisk Ping Errors To Counters

**33.7**
* HTOCR-1457:Modify logs for AWS log rotation
* SIP-2615: Add dynamic jmx based call blocks

**33.6**
* SIP-2677:SIPApp Prometheus Metrics on CDRs success vs rejection ratio

**33.5**
* HTOCR-1457:Add Separate logs for AWS for log rotation
* SIP-1918: SIPApp - Randompool number change is not reflected in SipApp
* SIP-2714: SipApp - Implement new SG Call blocking rules as per PREQ-4203

**33.4**
* SIP-2546:Add origination country to CDR
* SIP-2215: Return main account apikey and cps limit for get-route-for-sipcall
* SIP-2630: nexmoinc/voice repo - Upgrade Junit jar to recommended version

**33.3**
* SIP-2611: Add more CLIs to SG Blocking CLI list 
* SIP-2438: Bugfix: Recording apikey bug fix

**33.2**
* SIP-2552: SG Blocking rule optimization - SipApp
* SIP-2105: Add the Hangupcause to CDR
* SIP-2436: Add prometheus metric for Asterisk Ping failures and Dirty Shutdown triggers
* SIP-2563: Add more CLIs to SG Call spoof Blocking list
* SIP-2548: Add call spoofing metric

**33.1**
* SIP-2430: Blocking of SG Voice numbers

**33.0**
* SIP-2430: Bugfix: AsteriskSingleThreadedPool not shutdown before removal from poolMap

**32.9**
* SIP-2357: Use psip as product class for customer domain cdr
* SIP-2385: Call from sip->lvn->application to use psip product class
* SIP-2428: Quota service failures should not affect VCC calls

**32.8.1**
* SIP-2344: Disable From check for VAPI NCCO connect to sip action

**32.8**
* SIP-1782: Add Dynamic Pricing for Programmable SIP

**32.7**
* SIP-2134: CDRs on Emergency Shutdown - logs enhancements
* SIP-1949: Create utility to convert CDR to json
* SIP-2165: Add CDR header from SIPApp instead of messaging.jar
* SIP-2166: Add CDR type configuration parameter for SIPApp
* SIP-2254: Add flag to generate backup key-value CDRs instead of the regular CDRs

**32.6**
* SIP-2306: Add Traffic Steering in account capability to Internal Api
* SIP-2311: Obfuscate Callback Price
* SIP-2316: Upgrade LibPhone library version

**32.5**
* SIP-2197: TTS Gateway Failover
* SIP-2206: Add Monitoring for Asterisk Ping Thread
* SIP-2211: Handle Dirty Shutdown in a separated dedicated thread

**32.4.1**
* SIP-2225: Block JNDI lookups from log4j (CVE-2021-44228)
* SIP-2225: Upgrade log4j to version 2.17.0 (CVE-2021-45046, CVE-2021-45105)

**32.4**
* SIP-2074: Add Monitoring for Prometheus
* SIP-2191: Fix Missing SRTP Variable in Outbound CDR
* SIP-2182: Shutdown on JMX
* SIP-2202: Add placeholder com.nexmo.voice logging at INFO level

**32.3.1**
* SIP-2225: Block JNDI lookups from log4j (CVE-2021-44228)

**32.3**
* SIP-2118: Verify the NEXMOPAY AGI param
* SIP-2134: CDRs on Emergency Shutdown

**32.2**
* SIP-2112: Add SRTP to CDR
* SIP-2112: Change PSTN_PLATFORM to CARRIER_PLATFORM

**32.1.1**
* SIP-2011: Wait for deployment matrix on startup - only in NPE and Staging

**32.1**
* SIP-1908: Add CallInternalFlag to the CDRs
* SIP-1042: Add the drop seq number to the rejected CDR of dropped call
* SIP-1867: Skip quota based on capability
* SIP-2008: SIPApp - handle calls which terminate with error after they already started 

**32.0**
* SIP-1882: Sender-prefix pricing
* SIP-1925: Fix forced-sender behaviour for sender-prefix pricing

**31.6**
* SIP-1530: Enhance Quota Logs
* SIP-1812: Redirect Not Acknowledged Call in Rejected CDR to Inbound/Outbound
* SIP-1879: Fix NotifyComplete of `UpdateTaskConfigLoader.java`
* SIP-1894: Add price/cost rule fields to CDR

**31.5**
* SIP-1797: Add PSTN_PLATFORM to CDR
* SIP-1565: Stop on-going calls of banned account
* SIP-1852: Load Prefix Map on startup
* SIP-1849: Add call margin value to the CDR
 
**31.4.1**
* SIP-1834: Use has-kyc instead of has_kyc

**31.4**
* SIP-1377: Add Host Name to Internal Api
* SIP-1739: Prefix Group pricing
* SIP-1806: Update libphone lib

**31.3.1**
* SIP-1815: Calculate Outbound Stir Shaken for Single Inbound AGI Request with Outbound destination

**31.3**
* SIP-1617: Implement STIR_SHAKEN

**31.2**
* SIP-1678: remove local DNS resolution
* SIP-1678: remove xbill.org/dns-java DNS library
* SIP-1651: Fix NPE Internal Callback Configuration
* SIP-1675: Add NPE LB to Staging Allowed Ips
* SIP-1585: Make applications service the default, allow changing via JMX

**31.1**
* SIP-1585: use applications service in parallel with PHUB

**31.0**
* SIP-1657: get-route-for-number forwards_to: sip - missing forward_address

**30.9**
* SIP-1531: Add cache for Applications
* SIP-1602: Remove (unused) handler for HangupEvent

**30.8**
* SIP-1582: Fix accumulation of purged threads

**30.7**
* SIP-1498: Fix Out-Of-Memory error on startup
* SIP-1064: Voxeo Removal

**30.6**
* SIP-1374: Enhance SIPApp with `payment_enabled` attribute
* SIP-1389: Enhance Inbound CDR with payment 
* SIP-1416: Fix unhandled exception(s) in `BridgeEventHandler`
* SIP-1464: Change `permitted_geos` to `permitted_regions`
* SIP-1424: Add JMX method to view TargetGroup cache
* SIP-1437: Fix emergency stop charges call duration calculation

**30.5**
* SIP-1154: Enhance get-route-for-number to return LVN Mappings
* SIP-1228: Expose Suppliers File Version
* SIP-1228: Expose Mappings File Version
* SIP-1237: SIPApp : Replace hostname with canonical name
* SIP-1028: SIPApp : Increase cost field precision scale to 8 
* SIP-1296: Fix special chars like spaces on getting route for sipcall
* SIP-1299: Expose Random Pool cache

**30.4**
* SIP-1247: 402 on BridgeEvent

**30.3**
* SIP-1170: SIPApp / Asterisk: Missing BridgeEvent
* SIP-1171: Correct unit tests in SIPApp

**30.2**
* SIP-1168: New accounts can make calls to non-permitted destinations

**30.1**
* SIP-936: Reloading suppliers list at runtime
* SIP-1104: Fetch suppliers costs from DB

**30.0**
* SIP-794: Threads issue

**20.4** 
* SIP-1100: fix for negative call length
* CON-1290: add smartcom - sip - voice

**20.3**
* SIP-1083: enable libphone validation of numbers
* SIP-1092: enable permitted destination list account restrictions
* CON-1227: add epsilon - sip - voice
* CON-1239: add ftl - sip - voice
* CON-1257: add telcovillage-tmobilero - sip - voice
* CON-1263: add nospt - sip - voice
* CON-1254: add commschoice - sip - voice
* OPS-18658: remove telxira - sip - voice 
* OPS-18606: remove vodacode - sip - voice
* CON-1272: add idindosat - sip - voice
* CON-1271: add tmobilero - sip - voice
* CON-1286: add it-teknologi - sip - voice
* OPS-18719: add missing MO configuration for inet

**20.2**
* CON-1200: add globalroam-sgvoip - sip - voice
* CON-1201: add globalroam-sglandline - sip - voice
* OPS-17542: remove callbound-voice - sip - voice
* CON-1207: add globalroam-sgothers - sip - voice
* SIP-881: SIPApp - add dummy suppliers to the test configuration

**20.1**
* SIP-818: SIPApp - disable UDP cluster
* CON-1175: add nextmobile-voice - sip - voice

**20.0**
* SIP-795: Split TTSApp / SIPApp REPOs
* OPS-17219: remove ips-voice - sip - voice
* OPS-15542: remove pldt - sip - voice
* OPS-14070: remove synqafrica - sip - voice
* OPS-14069: remove pldintl - sip - voice
* SIP-790: Add placeholders for AWS migration
* SIP-806: SIPApp - add colt and bics to the tests suppliers xml
* OPS-17123: Add STCS

**14.0**
* CON-1122: add ips-voice - sip - voice
* CON-1112: add callbound-voice - sip - voice
* CON-1152: add alcom-voice - sip - voice
* SIP-565: Calls to virtual numbers without Voice features fail internally
* SIP-707: Add a unique field to CDRs

**13.9**
* SIP-409: Programmable sip
* CON-1119: add rostelecom-voice - sip - voice
* CON-1047: remove multibyte - sip - voice

**13.8**
* SIP-639: Upgrade Asterisk client lib
* CON-1110: add vodacode - sip - voice

**13.7**
* SIP-554: Add new GW for Vonage
* SIP-560: add vonage-cnam gateway to test envs

**13.6**
* CON-1109: add hgcindia - sip - voice

**13.5**
* CON-1097: add coolwave - sip - voice
* OPS-15984: add quality-testing - sip - voice
* CON-1102: add surem-voice - sip - voice

**13.4**
* CON-1090: add hgc - sip - voice
* CON-1072: add lanck-lvn-ru - sip - voice
* CON-1086: add lanck-lvn-lv - sip - voice
* SIP-456: SIPApp - do not fail-over in case of 480

**13.3**
* SIP-316: SIPApp - Add pricing and network details to CDRs

**13.2**
* CON-1088: add tata-voice - sip - voice MO
* SIP-438: SIPApp - whitelist TTS-NG all domains callback domain name	 

**13.1**
* SIP-426: Enhance TTS Access log to support the new Verify Client 

**13.0**
* CON-1079: add imobile - sip - voice
* CON-1078: add atlasat - sip - voice
* OPS-15319: Add telenor-voice alias

**12.9**
* SIP-422: SIPApp - Add TTS-NG to allowed callbacks destinations

**12.8**
* SIP-220: SIPApp - TTS-Verify-NG support

**12.7**
* SIP-398: SIPApp - enhance logs of Asterisk connection disconnect

**12.6**
* SIP-377: SIPApp - enhance logs of quota updates responses

**12.5**
* CON-1047: add multibyte - sip - voice
* SIP-362: handle channel in thread safe mode

**12.4**
* SIP-364: TTSApp - Quota reference is missing from the quota updates
* SIP-367: SIPApp/TTSApp : Add quota updates log

**12.3**
* SIP-230: SIPApp - Quota updates reference
* SIP-273: SIPApp - Rejected re-invites

**12.2**
* SIP-355: SIPApp - SIP calls with gateway fail over does not identify well whether the fail over will take place
* SIP-356: SIPApp - provide an internal API to verify whether a specific sessionId is in the cache

**12.1**
* SIP-259: Set SIP out price to 0.004

**12.0**
* SIP-287: SIP-Dial-in
* SIP-335: Set SIP out price to zero

**11.9**
* CON-1012: Removed synermaxx-voice
* OPS-14052: Add carrier place holders for testing
* OPS-14076: Add carrer place holder for Granite (Dominos)

**11.8**
* CON-1026: add pinyin - sip - voice
* CON-994: missing MO configuration for ezphoneru added
* OPS-13857: Add alxi placeholder
* CON-1028: add eris - sip - voice

**11.7**
* SIP-282: VBC Outbound

**11.6**
* CON-1012: add synermaxx-voice - sip - voice
* SIP-243: SIP Dial in for TokBox

**11.5**
* OPS-13229: Allow Verify callbacks in WDC4 and LON1

**11.4**
* OPS-13203: add c3ntro outbound to - sip - voice

**11.3**
* CON-1005: telecall-brazil - sip - voice
**11.2**
* SIP-266 SIPApp - handle edge case of BridgeEvent processing failure

**11.1**
* SIP-246 Enhance Counters management logs
* SIP-251 Clean the cache for cancelled calls
* SIP-261 CDR is not created during gws failover, if the cdr event is HANGUPCAUSE=16;DIALSTATUS=NOANSWER

**11.0**
* SIP-260 SIPApp - lvn callback is not sent back for failed calls 

**10.9**
* CON-996 shinetown - sip - voice 
* CON-994 ezphoneru - sip - voice

**10.8**
*  OPS-12666 Lanckru - sip - voice
*  PREQ-1214 - preliminary workaround for withheld calls

**10.7**
* SIP-154 Amend tts logs

**10.6**
* SIP-227 TTS call to new Vietnamese number failed: Invalid destination address

**10.5**
* SIP-222 CdrEvent is missing the userField

**10.4**
* SIP-221 Support apikey as supplierID from voice LVN's

**10.3**
* SIP-213 overcharges
* SIP-216 Session Timers RE-INVITES sent by Asterisk sometimes end up in charging indefinitely
* SIP-202 SIPApp- amend the messaging.jar retries configuration - this will affect only NPEs
* OPS-11928: Remove Keyyo - sip - voice
* OPS-12154: Remove Vasudev - sip - Voice

**10.2**
* SIP-167: VBC Support
* CON-955: add smartnet-cli/ smartnet-nocli/ smartnet-mixed gateways


**10.1**
* SIP-83: SIPApp - Handle Gatways fail-over CDRs and race condition
* SIP-41: SIPApp: SIP destinations fail over race condition
* SIP-135: SIPApp - log the canAcceptRequest and getRouteForNumber rejection response to the log
*

**10.0**
* OPS-10469: Remove leadtelecom gateway
* OPS-11002: Remove rtgtel gateway 
* OPS-11334: Remove bics-dropcall gateway
* CON-953: sprintru - sip - voice

**9.9**
* OPS-11568: Remove aupa gateway
* COMOPS-3563: Remove itip gateway

**9.8**
* CON-932: pldintl - sip - voice
* CON-934: telxira - sip - voice 

**9.7**
* CON-928: vonage-shortduration - sip - voice
* CON-930: vonage-whs - sip - voice
* OPS-11645 Added missing outbound tts suppliers

**9.6**
* CON-922: Add telefonicalatam to suppliers
* CON-923: Add bts to suppliers
* CON-898: Fix inbound for Telekomalaysia
* OPS-10859 Remove knowlarity voice
* OPS-10860 Remove citichk voice
* OPS-10967 Remove simple2call voice

**9.5**
* SIP-106: M800 not in suppliers/live-suppliers.xml

**9.4**
* SIP-93: return cps limit
* SIP-102: Add log messages of the InternalAPIServlet request and response
* CON-909: viber-pstn interconnect
* CON-910: vasudev interconnet

**9.3**
* OPS-10138 removed cootel
* OPS-10566 added voice-load-test supplier

**9.2**
* CON-887: o2uk - sip - voice
* CON-898: Telekomalaysia - SIP - Voice
* CON-894: Keyyo - sip - voice

**9.1**
* CON-882: hayotel - SIP - VOICE
* CON-828: africastalking - SIP - VOICE
* CON-XXX: colt-es / colt-fr / colt-gb / colt-de
* SIP-84: PREFIX_TO PREFIX_FROM stage 2

**9.0**
* OPS-9832 remove lebara, m800,velo voice
* OPS-9828 remove gateway compatel
* SIP-67: stop/start - fix the memory leak of calls directed to application hence not creating CDRs

**8.9**
* SIP-77 TTS: PREFIX_TO CDR content is wrong

**8.8**
* SIP-70 gc logs

**8.7**
* OPS-9492 telstraglobal SIP Voice Interconnect
* OPS-9420 argontdm - SIP - VOICE

**8.6**
* SIP-59: add cdr prefix elements

**8.5**
* SIP-40: Migrate sip/tts/drop to common build plugin
* OPS-9968 indosat - SIP - VOICE
* OPS-9871 inet - SIP - VOICE

**8.4**
* CON-857: meopt - SIP - VOICE
* CON-858: telin - SIP - VOICE
* CON-850: daisy - SIP - VOICE
* CON-856: dexatel - SIP - VOICE

**8.3**
* OPS-9543: Auditing Carriers
* OPS-9584: Please remove outbound voice gateways(aaatel, ccs (not Outbound SMS), spactron,telcoson,velo (not Outbound SMS),virginemea) from AD

**8.2**
* OPS-9527: Ibasis-euro missing config on the sip App

**8.1**
* CON-838: argontdm - SIP - VOICE
* CON-845: bics-euro - SIP - VOICE
* CON-846: telstra - SIP - VOICE
* TECH-3319: legos - Remove country code prefix


**8.0**
* CON-835 compatel - outbound voice
* SIP-33-keys-rotation

**7.9**
* OPS-9289 OPS-9279 OPS-9278: Remove unused interconnects

**7.8**
* CON-828: africastalking

**7.7**
* CON-819: globalabs-transit

**7.6**
* OPS-8874 Destroy, nuke, get rid of, demolish, devastate, eradicate, kill, extirpate â�¤ â�¤ â�¤ â�¤  epsilon-starhubsg  â�¤ â�¤ â�¤ â�¤ â�¤

**7.5**
* OPS-8731 Remove voxbone & virginemea from voice connections
* OPS-8843 Configure a dummy carrier / supplier on production for use in the voice smoke tests
* OPS-8482: Remove vonage-kddiglobal

**7.4**

* CON-765: ibasis-euro
* CON-767: synqafrica
* CON-769: multiconnect-at-voice

**7.3**

* CON-742: g9telecom - SIP - VOICE

**7.2**

* OPS-7985: Rename pccw to pccw-voice
* CON-733: sktelink - SIP - VOICE
* OPS-7991: Increase RAM for SIP
* CON-740: colt-nl - SIP - VOICE

**6.7.1**

* CON-680: tata - SIP - VOICE
* OPS-7325: Renames pccw-voice to pccw
* OPS-7309: Removes pccw from sip / tts .xml
* OPS-7346: Remove Seatel
* OPS-7628,CON-693,CON-697: Add new carriers (pldt, cootel) and remove telcoson
* OPS-7813: Increase RAM for SIP/TTS

**6.7**

* CON-666: exchangetelecom - SIP - VOICE
* CON-665: telcoson - SIP - VOICE

**6.6**

* messaging jar update
* randomize fix

**6.5**

* CON-663: seatel - SIP - VOICE
* CON-659: sudonum - SIP - VOICE

**6.4**

* SIP retry logic should be working now
* VOIC-539: Add reroute supplier to voice logs

**6.3**

* sanntuu to sip and tts

**6.2**

* CON-651: sanntuu - SIP - VOICE

**6.1**

* CON-647: simple2call - SIP - VOICE

**6.0**

* CON-646 lebara - SIP - VOICE

**5.9**

* update google-phone lib

**5.8**

* CON-620 pccw-voice - SIP - VOICE

**5.7**

* TTS retry

**5.6**

* CON-627 leadtelecom - SIP - VOICE

**5.5**

* messaging.jar bumped to 49871

**5.4**

* CON-614: cat - SIP - voice
* VOIC-511: tts phone address validation bugfix

**5.3**
* change in tts and sip config

**5.2**

* CON-591 CON-597 CON-603

**5.1**

* CON-570 CON-588 CON-581
* VOIC-514: Update messaging

**v5.0**

* JSON logging for call counters. Also disabled counter limit preventing the user from making more calls.

**v4.9**

* CON-571 pccw-hk - SIP - VOICE

**v4.8**

* CON-568 091israel - SIP - VOICE

**v4.7**

* CON-549 CON-558 CON-562 CON-563 voice suppliers"

**v4.6**

* OPS-3997 Remove live voice suppliers

**v4.5**

* CON-557 virginemea - SIP - VOICE

**v4.4**

* DROP call IP address CDRs

**v4.3**

* VOICE-489 bump messaging to support dep matrix changes
* VOICE-491 add `REQUEST_IP` to TTS CDRs
* VOICE-493 add `REQUEST_IP` to SIP CDRs

**v4.2**

* (SIP & TTS live) OPS-3627 Destroy gmtelecom
* (SIP & TTS live) CON-536 rtgtel - SIP - VOICE
* (SIP & TTS live) CON-537 citichk - SIP - VOICE

**v4.1**

* (SIP Live ) CON-513 vodafonein-conferencing - SIP - VOICE
* (SIP Live + TTS) CON-512 vonage-kddiglobal - SIP - VOICE
* Inbound calls routed to applications should have only 1 CDR (DIRECTION=IN) and send only one completed callback

**v4.0**

* (TTS & SIP Live ) CON-526 callr - SIP - VOICE

**v3.9**

* (TTS & SIP Live ) CON-517 aupa - SIP - VOICE

**v3.8**

* VOICE-475 add event callback to tts app

**v3.7**

* (TTS & SIP & Drop QA) Updated for new environment
* (TTS & SIP Live ) CON-512 + CON-513 new kddi + vodafonein voice suppliers

**v3.6**

* (TTS & SIP Live ) CON-496 + CON-510 + CON-511 new voice suppliers

**v3.5**

* Removed (SIP & TTS Live) OPS-2719 Removed useless suppliers
* (Dropcall QA) Changed interface for machine due to new env
* (TTS QA) Changed proxy for voxeo due to new env
* (TTS & SIP Live ) OPS-2854 Removed inglobe
* (SIP Live) CON-509 c3ntro - SIP - VOICE inbound

**v3.4**

* Added (SIP & TTS Live) CON-463 & CON-464 new voice suppliers

**v3.3**

* OPS-2523 Removed (TTS Lve) gw colt

**v3.2**

* removed googlephonelib validation for the sip app (it should be only used for formatting the numbers)

**v3.1**

* Added (SIP Live) whitelisted range 2217 trading

**v3.0**

* SIP product class will be set to "api" for calls generated or terminating on an application

**v2.9**

* Addded (SIP Live) whitelisted range 53 trading

**v2.8**

* Added (TTS Live) IP for voice1 & voice2, new machines in mon1

**v2.7**

* Live SIP added whitelisted range 6748819 for trading

**v2.6**

* CON-441 (SIP Live) Added peerless inbound trunk

**v2.5**

* VOIC-440 (Remove LVN routing for voice)
* Added inglobe on sip & tts config, was missing
* VOIC-444 (Update messaging.jar to r.44704)

**v2.4**

* Added (live tts & sip) CON-437 spactron

**v2.3**

* Added (live tts & sip) CON-425 vonage-prem trunk
* Added (live sip) CON-426 circuit4 inbound trunk

**v2.2**

* Removed (testing tts) qa1 & qa2 endpoints from permitted callbacks
* Added (live tts & sip) CON-425 vonage trunk

**v2.1**

* bumped messaging to 44001

**v2.0**

* Added (both qa tts) qa1 and qa2 endpoints on config file for http-callback-blocked-ip-ranges
* VOIC-427 delete spaces from phone number, and 00 if prepended

**v1.9**

* Moved (live tts) http-callback-blocked-ip-ranges inside config core
* Allowed (live tts) verify callbacks into tts config

**v1.8**

* VOIC-421 fixMessegingJar
* OPS-1859 Mapped sip status 0 to 487
* Added PDD event sender for all TTS

**v1.7**

* Added callback blacklist. It should block all internal addresses when used as callbacks in production (except the one allowed).

**v1.6**

* Added internal callback url for sip apps to report leg status for all the legs (unlike the public one which is bound to the IB number)

**v1.5**

* Added call per second throttling to the sip app
* TTS calls are routed to Voxeo if either 'repeat' or 'machine_detection' params are provided in the request

**v1.4**

* Added (testing TTS) ja-jp language & real-ibasis
* Added (testing drop) real-ibasis, ibasis, idt, aql
* Fixed (testing drop) supplier-mapping config
* Fixed (live drop) supplier-mapping config
* Updated messaging jar to 42038 (added secret-signature-key capability)

**v1.3**

* Added real-ibasis for testing
* Changed (live) purge configuration
* Added (live) caching proxy for audio files

**v1.2**

* Added supplier (testing): tismi
* Removed supplier: bics (drop), aasmartcom (sip)
* Enabled proxy cache for tts audio files

**v1.1**

* VOIC-355: Fixed some rounding errors when calculating the required balance necessary for a SIP call
* VOIC-348: SIP should know how to handle APP callback type for inbound LVNs
* VOIC-127: Calls with two legs should no longer charge incorrectly. Implemented a synchronizer to prevent this from happening
* VOIC-358: Reduced some log levels from error to warn

**v1.0**

* Initial migration from svn
