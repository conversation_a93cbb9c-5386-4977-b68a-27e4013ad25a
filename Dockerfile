FROM ************.dkr.ecr.eu-west-1.amazonaws.com/nexmo-java:8-corretto-jdk-minideb
LABEL maintainer="<EMAIL>"

ARG service_name
ARG service_user=${service_name}
ARG service_uid=1000
ARG service_group=${service_user}
ARG service_gid=1000
ARG local_deb_path
ARG jwt_group=sip-1
ARG jwt_gid=1039

ENV service_name=${service_name}
ENV service_user=${service_user}
ENV service_uid=${service_uid}
ENV service_group=${service_group}
ENV service_gid=${service_gid}
ENV jwt_group=${jwt_group}
ENV jwt_gid=${jwt_gid}
ENV DAEMON_RUNTIME=/home/<USER>/runtime
ENV bootlog=${DAEMON_RUNTIME}/boot.log
ENV logfile=${DAEMON_RUNTIME}/logs/nexmo-${service_name}.log
ENV pidfile=/var/run/nexmo/nexmo-${service_name}.pid
ENV JAVA_TOOL_OPTIONS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/runtime/dumps/heap-dump-nexmo-$service_name.hprof"

USER root
COPY ${local_deb_path} ${local_deb_path}

RUN echo "service-name=${service_name}" && \
    echo "service-user=${service_user}" && \
    echo "service-group=${service_group}" && \
    echo "service-uid-gid=${service_uid}:${service_gid}" && \
    echo "jwt-group=${jwt_group}" && \
    echo "jwt-gid=${jwt_gid}" && \
    echo "local-deb=${local_deb_path}" && \
    apt-get update && apt-get dist-upgrade -y && \
    mkdir -p /usr/share/man/man1 && \
    apt-get update && apt-get install -y apt-utils \
                                         apt-transport-https \
                                         ca-certificates \
                                         lsb-base \
                                         procps && \
    rm -rf /var/lib/apt/lists/* && \
    mkdir -p /var/run/nexmo && \
    groupadd --gid ${jwt_gid} ${jwt_group} && \
    useradd --create-home --uid ${service_uid} --gid ${service_gid} --groups ${jwt_gid} --shell /bin/bash ${service_user} && \
    mkdir -p ${DAEMON_RUNTIME}/logs && \
    mkdir -p ${DAEMON_RUNTIME}/activemq-data && \
    mkdir -p ${DAEMON_RUNTIME}/activemq-sched && \
    touch ${bootlog} && \
    touch ${logfile} && \
    dpkg -i ${local_deb_path} && apt-get install -f; \
    chown -R ${service_user} /var/run/nexmo /etc/init.d ${bootlog} ${DAEMON_RUNTIME}/logs ${DAEMON_RUNTIME}/activemq-data ${DAEMON_RUNTIME}/activemq-sched
USER ${service_user}
WORKDIR ${DAEMON_RUNTIME}
CMD sed -i '0,/DAEMON_JVM_OPTS=/s//DAEMON_JVM_OPTS="'"$TELCO_JMX_JAVA_OPTS -Djava.rmi.server.hostname=$(hostname -f) -Dlog4j2.formatMsgNoLookups=true"'"/' /etc/init.d/nexmo-${service_name} && \
    /etc/init.d/nexmo-${service_name} start && \
    if [ -e "$pidfile" ]; then SPID=`pgrep -U "$service_user" java -F "$pidfile"` && tail -f --pid=${SPID} ${logfile}; fi
