apply plugin:'java'
project.ext {
    version = '1.0.0-SNAPSHOT'                       // Default version for local builds
    messagingVersion = '-2024-10-21_200506-c407c5dbaa1518163a7028d848eb37188692af48'
}

buildscript {
    def artifactoryHome = System.getenv('ARTIFACTORY_HOME') ?: 'artifactory.internal'
    println("Using $artifactoryHome for buildscript")
    repositories {
       mavenCentral()
       maven {url "http://$artifactoryHome/artifactory/plugins-release-local"}
       maven {url "https://jfrog.bintray.com/jfrog-jars/"}
       maven {url "https://plugins.gradle.org/m2/"}
    }
    dependencies {
        classpath 'com.nexmo.gradle:common-build:5.0.6'
        classpath("org.eclipse.jgit:org.eclipse.jgit:5.13.0.202109080827-r") {
            force = true
        }
    }
}
dependencies {
    implementation 'software.amazon.awssdk:bom:2.20.152'
    implementation 'software.amazon.awssdk:dynamodb:'
    implementation 'org.apache.activemq:activemq-broker:5.15.13'
    implementation 'org.apache.activemq:activemq-client:5.15.13'
    implementation 'org.apache.activemq:activemq-all:5.15.13'
    implementation 'org.apache.activemq:activemq-jaas:5.15.14'
    implementation 'org.apache.activemq:artemis-server:2.25.0'
    implementation 'org.apache.activemq:activemq-osgi:5.16.1'
    implementation 'org.apache.ant:ant:1.10.12'
    implementation 'org.apache.ant:ant-junitlauncher:1.10.12'
    implementation 'org.apache.axis2:axis2:1.8.1'
    implementation 'org.webjars:angularjs:1.5.0-rc.0'
    implementation 'org.webjars:bootstrap:5.1.1'
    implementation 'org.webjars.npm:bootstrap:5.1.1'
    implementation 'org.webjars:bootstrap-sass:3.3.7'
    implementation 'org.webjars.npm:bootstrap-sass:3.3.7'
    implementation 'com.mchange:c3p0:0.9.5.4'
    implementation 'commons-codec:commons-codec:1.13'
    implementation 'commons-collections:commons-collections:3.2.2'
    implementation 'org.apache.commons:commons-collections4:4.2'
    implementation 'org.apache.synapse:Apache-Synapse:3.0.1'
    implementation 'org.apache.commons:commons-compress:1.21'
    implementation 'commons-fileupload:commons-fileupload:1.3.3'
    implementation 'org.apache.tomcat.embed:tomcat-embed-core:10.1.0-M8'
    implementation 'org.apache.tomcat:tomcat-coyote:10.1.0-M8'
    implementation 'commons-httpclient:commons-httpclient:3.1-jenkins-1'
    implementation 'commons-httpclient:commons-httpclient:3.1-redhat-3'
    implementation 'commons-httpclient:commons-httpclient:3.1-HTTPCLIENT-1265'
    implementation 'commons-io:commons-io:2.9.0'
    implementation 'org.dom4j:dom4j:2.1.3'
    implementation 'com.google.guava:guava:24.1.1-android'
    implementation 'com.google.guava:guava:24.1.1-jre'
    implementation 'org.hibernate:hibernate-core:6.1.1.Final'
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'
    implementation 'org.apache.httpcomponents:httpclient-osgi:4.5.13'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.0.3'
    implementation 'com.fasterxml.jackson.core:jackson-databind:********'
    implementation 'org.jdom:jdom2:2.0.6'
    implementation 'org.apache.servicemix.bundles:org.apache.servicemix.bundles.jdom:2.0.5_1'
    implementation 'org.eclipse.jetty:jetty-server:11.0.12'
    implementation 'org.eclipse.jetty:jetty-http:11.0.12'
    implementation 'org.eclipse.jetty.aggregate:jetty-client:8.1.20.v20160902'
    implementation 'org.eclipse.jetty:jetty-servlets:11.0.8'
    implementation 'org.eclipse.jetty:jetty-io:11.0.12'
    implementation 'org.eclipse.jetty:jetty-runner:11.0.8'
    implementation 'org.eclipse.jetty:jetty-util:11.0.8'
    implementation 'org.eclipse.jetty:jetty-webapp:11.0.8'
    implementation 'org.apache.logging.log4j:log4j-core:2.18.0'
    compileOnly 'org.ops4j.pax.logging:pax-logging-log4j2:2.1.2'
    implementation 'mysql:mysql-connector-java:8.0.30'
    implementation 'org.apache.poi:poi-scratchpad:5.2.2'
    implementation 'org.apache.poi:poi:5.2.2'
    implementation 'org.apache.poi:poi-examples:5.2.2'
    implementation 'org.apache.poi:poi-ooxml:5.2.2'
    implementation 'org.apache.xmlbeans:xmlbeans:5.0.1'
    implementation 'axis:axis:1.2.1'


    testImplementation 'junit:junit:4.13.1'
}
