apply plugin: 'application'

mainClassName = 'com.nexmo.voice.Server'

project.ext {
    println("Initializing project variables ...")
    debianRepoQA = project.hasProperty('forceDebianRepo') ? forceDebianRepo : 'qaservice1.internal'
    debianRepoProd = project.hasProperty('forceDebianRepo') ? forceDebianRepo : 'repo-src.hq'
    serviceName = 'sip-1'
    userName = 'sip'
    ext.checksFile = "$projectDir/../nexmo_checks.xml"
    configFileName = 'sip.xml'

    initScriptDefault = true
}

configurations {
   compile.exclude group: 'log4j'
}

if (System.getenv('ARTIFACTORY_HOME')) {
    def artifactoryHome = System.getenv('ARTIFACTORY_HOME')
    println("Using ARTIFACTORY_HOME=$artifactoryHome")
    repositories {
        mavenCentral()
        maven {url "http://$artifactoryHome/artifactory/plugins-release-local"}
        maven {url "http://$artifactoryHome/artifactory/libs-release"}
        maven {url "http://$artifactoryHome/artifactory"}
    }
}

dependencies {
    compile project(':voice-core')
}


apply plugin: "com.nexmo.gradle.common-app-build"


//
// Replace the debPushPackage task with our own
//
remotes {
    qa_deb_repo {
        role 'QA Debian repository'
        host = debianRepoQA
        user = 'jenkinsdeploy'
        identity = file(System.getenv('CREDENTIALS')?:'/var/lib/jenkins/.ssh/nexmo-ops.pem')
    }
    prod_deb_repo {
        role 'Prod Debian repository'
        host = debianRepoProd
        user = 'repo'
        identity = file(System.getenv('CREDENTIALS')?:'/etc/nexmo/pacco/prod.key.pem')
    }
}

task debPushPackageQA() << {
    def repoImportDir = '/var/debrepo/incoming'
    ssh.run {
        session(remotes.qa_deb_repo) {
            def debFile = buildDeb.archivePath.path
            def changesFile = debFile.replace('.deb', '.changes')
            put from: debFile, into: repoImportDir
            put from: changesFile, into: repoImportDir
        }
    }
}
debPushPackageQA.dependsOn debCreatePackage

task debPushPackageProd() << {
    def repoImportDir = '/incoming'
    ssh.run {
        session(remotes.prod_deb_repo) {
            def debFile = buildDeb.archivePath.path
            //def changesFile = debFile.replace('.deb', '.changes')
            put from: debFile, into: repoImportDir
            //put from: changesFile, into: repoImportDir
        }
    }
}
debPushPackageProd.dependsOn debCreatePackage
