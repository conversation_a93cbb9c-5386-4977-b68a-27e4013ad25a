<short-code-based-routing enabled='true' allow-drop-routing-rules-to-override='false'>
    <match>
        <enable-for-networks>
            <network>302</network>
            <network>310</network>
            <network>311</network>
            <network>316</network>
            <network>US-</network>
        </enable-for-networks>
        <enabled-for-destination-msisdn-prefixes>
            <!--
                prefix = ' prefix of the 'sender' , ie, the LVN or short-code
                network-prefix = ' prefix of the network of the destination address
            -->
<!--
            <msisdn-prefix prefix='1' network-prefix='302,310,311' />
-->
            <msisdn-prefix prefix='27' network-prefix='655'/>
            <msisdn-prefix prefix='32' network-prefix='206'/>
            <msisdn-prefix prefix='33' network-prefix='208'/>
            <msisdn-prefix prefix='34' network-prefix='214'/>
            <msisdn-prefix prefix='35' network-prefix='244'/>
            <msisdn-prefix prefix='43' network-prefix='232'/>
            <msisdn-prefix prefix='44' network-prefix='234'/>
            <msisdn-prefix prefix='48' network-prefix='260'/>
            <msisdn-prefix prefix='52' network-prefix='334'/>
            <msisdn-prefix prefix='852' network-prefix='454'/>
            <msisdn-prefix prefix='86' network-prefix='460'/>
            <msisdn-prefix prefix='92' network-prefix='410'/>
            <msisdn-prefix prefix='79' network-prefix='250'/>
            <msisdn-prefix prefix='60' network-prefix='502'/>
            <msisdn-prefix prefix='62' network-prefix='510'/>
            <msisdn-prefix prefix='90' network-prefix='286'/>
            <msisdn-prefix prefix='56' network-prefix='730'/>
            <msisdn-prefix prefix='420' network-prefix='230'/>
            <msisdn-prefix prefix='250' network-prefix='635'/>
            <msisdn-prefix prefix='385' network-prefix='219'/>
            <msisdn-prefix prefix='40' network-prefix='226'/>
        </enabled-for-destination-msisdn-prefixes>
    </match>
</short-code-based-routing>
