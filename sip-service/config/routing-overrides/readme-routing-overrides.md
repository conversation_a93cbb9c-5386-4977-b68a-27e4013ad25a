*routing-overrides configuration files guide.

sip is used by 3 different environments:
1. NPE
2. Voice QA aka staging
3. Production

Each of the above environments requires a different set of <mt-routing .......> definitions in the sip.xml
The <mt-routing .......> definitions are kept in a separate file, loaded during run-time.
The file name is environment based :

Staging: dev-overrides.xml
NPE: npe-overrides.xml
Production: **THERE ARE NO OVERRIDES FOR PRODUCTION**

The sip.xml include the following routings-overrides configuration definition:
```
    <mt-routing .......>
        @mt-route-overrides@
    </mt-routing>
```

@mt-route-overrides@ is defined in qa.properties prod.properties as follows:
```
mt-route-overrides=<XML-INCLUDE>routing-overrides/~~dyn~NEXMO_ENV~dev~~-overrides.xml</XML-INCLUDE>
```

dyn~NEXMO_ENV is converted to the relevan4 environment name, and so selecting the proper [env]-overrides.xml file
to be loaded during runtime.
