<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
    <Appenders>

        <RollingRandomAccessFile name="ERROR-LOG"
                                 fileName="logs/errors.log"
                                 append="false"
                                 filePattern="logs/errors.log.%d{yyyy-MM-dd}">
            <!-- <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/> -->
            <PatternLayout pattern="%d %-5r (%t) %-5p [%c] %x %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="MAIN-LOG"
                                 fileName="logs/messaging.log"
                                 append="true"
                                 filePattern="logs/messaging.log.%d{yyyy-MM-dd}">
            <!-- The full pattern: Date MS (Thread Name) Priority [Category] (Thread:NDC) Message\n   -->
            <PatternLayout pattern="%d %-5r (%t) %-5p [%c] %x %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>
        
        <RollingRandomAccessFile name="QUOTA-UPDATES-LOG"
                                 fileName="logs/quota-updates.log"
                                 append="true"
                                 filePattern="logs/quota-updates-%d{yyyy.MM.dd}.log">
            <PatternLayout pattern="%d %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="COUNTER-LOG"
                                 fileName="logs/application-counter.log"
                                 append="true"
                                 filePattern="logs/application-counter.log.%d{yyyy-MM-dd}">
            <PatternLayout pattern="%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>

        <Async name="ASYNC-MAIN">
            <AppenderRef ref="MAIN-LOG"/>
        </Async>

        <Async name="ASYNC-ERROR">
            <AppenderRef ref="ERROR-LOG"/>
        </Async>

        <Async name="ASYNC-QUOTA-UPDATES">
            <AppenderRef ref="QUOTA-UPDATES-LOG"/>
        </Async>
        <Async name="ASYNC-COUNTER">
            <AppenderRef ref="COUNTER-LOG"/>
        </Async>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="ASYNC-MAIN"/>
            <AppenderRef ref="ASYNC-ERROR" level="ERROR"/>
        </Root>

        <!-- ================ -->
        <!-- Limit categories -->
        <!-- ================ -->
        
        <Logger name="com.nexmo.voice" level="info"/>
        <!-- <Logger name="com.thepeachbeetle.messaging.hub.core.quota.client" level="debug"/> -->

        <Logger name="org.apache.logging.log4j2" level="info"/>
        
        <!-- Quieten noise regarding the Asterisk's BridgeEvent building -->
        <Logger name="org.asteriskjava.manager.internal.EventBuilderImpl" level="error"/>
        <logger name="quota.updates.log" level="info">
            <AppenderRef ref="ASYNC-QUOTA-UPDATES"/>
        </logger>

        <logger name="com.nexmo.common.logging.JsonLogger" level="info">
            <AppenderRef ref="ASYNC-COUNTER"/>
        </logger>

        <!-- Limit the org.apache.commons category to INFO as its DEBUG is verbose -->
        <Logger name="org.apache.commons" level="info"/>

        <Logger name="org.apache.http" level="info"/>

        <!-- latest aws sdk logs huge POST request bodies ... disable this ... -->
        <Logger name="com.amazonaws.request" level="error"/>

        <Logger name="org.hibernate" level="error"/>

        <Logger name="httpclient.wire" level="error"/>

        <Logger name="org.apache.commons.httpclient.HttpMethodBase" level="error"/>

        <Logger name="org.apache.commons.httpclient.HttpMethodDirector" level="fatal"/>

        <!-- Quieten noise from hibernates c3po connection pool -->
        <Logger name="com.mchange" level="error"/>

        <!-- Quieten silly timeout noise from jetty ... -->
        <Logger name="org.mortbay" level="info"/>
        <Logger name="org.eclipse.jetty" level="info"/>

        <!-- Quieten noise from sshtools SFTP Library -->
        <Logger name="com.sshtools.j2ssh.transport.TransportProtocolCommon" level="warn"/>
        <Logger name="com.sshtools.j2ssh.connection" level="warn"/>

        <!-- Quieten noise from sshtools SFTP Library -->
        <Logger name="com.sshtools.j2ssh.transport.TransportProtocolCommon" level="warn"/>
        <Logger name="com.sshtools.j2ssh.connection" level="warn"/>

        <!-- Quieten down stupid hibernate errors... -->
        <Logger name="org.hibernate.util.DTDEntityResolver" level="fatal"/>
        <Logger name="org.hibernate.util.JDBCExceptionReporter" level="fatal"/>
        <Logger name="org.hibernate.event.def.AbstractFlushingEventListener" level="fatal"/>
        <Logger name="org.hibernate.jdbc.AbstractBatcher" level="fatal"/>
        <Logger name="org.hibernate.transaction.JDBCTransaction" level="fatal"/>

        <!-- eh-cache is quite noisy! -->
        <Logger name="net.sf.ehcache" level="info"/>
        <!-- Ignore error caused by ehcache bugs -->
        <Logger name="net.sf.ehcache.hibernate.strategy.AbstractReadWriteEhcacheAccessStrategy" level="fatal"/>

        <!-- active-mq makes a lot of noise -->
        <Logger name="org.apache.activemq" level="info"/>

        <!-- ignore silly active-mq broker startup warnings .. -->
        <Logger name="org.apache.activemq.broker.BrokerService" level="fatal"/>

        <!-- Prevent the Asterisk library from logging to error directly. It should be handled in the wrapping -->
        <Logger name="org.asteriskjava.fastagi" level="fatal"/>

        <!-- this is a bit annoying .... -->
        <Logger name="sun.rmi.transport.tcp" level="warn"/>
        <Logger name="sun.rmi.loader" level="warn"/>
        <Logger name="sun.rmi.server.call" level="warn"/>
        <Logger name="sun.rmi.client.ref" level="warn"/>

    </Loggers>
</Configuration>
