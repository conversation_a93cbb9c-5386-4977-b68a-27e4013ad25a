*whitelisted-numbers configuration files guide.

sip is used by 3 different environments:
1. NPE
2. Voice QA aka staging
3. Production

Each of the above environments requires a different set of whitelisted definitions in the sip.xml
The whitelisted numbers  definitions are kept in a separate file, loaded during run-time.
The file name is environment based :

NPE: npe-whitelisted-numbers.xml
Staging: dev-whitelisted-numbers.xml
Production: live-whitelisted-numbers.xml

The sip.xml include the following gateways configuration definition:
```
@whitelisted-numbers@
```

@whitelisted-numbers@ is defined in qa.properties and prod.properties as follows:
```
whitelisted-numbers=<XML-INCLUDE>whitelisted-numbers/~~dyn~NEXMO_ENV~dev~~-whitelisted-numbers.xml</XML-INCLUDE>```

dyn~NEXMO_ENV is converted to the relevan environment name, and so selecting the proper [env]-whitelisted-numbers.xml file
to be loaded during runtime.

