<!-- WHitelisted numbers feature should be enabled only in test. It
     provides a bypass to the google numers' verification tool
     When this feature is enabled, it is possible to use numbers which starts with the configured prefix.-->
<whitelisted-numbers enabled="true">
    <number>6748819</number>
    <number>53</number>
    <number>2217</number>
    <number>123456</number>
    <number>1333</number>
    <number>1444</number>
    <number>44113</number>
</whitelisted-numbers>