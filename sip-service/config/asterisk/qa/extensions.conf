[general]
static=yes
writeprotect=no
clearglobalvars=no

[globals]
;CONSOLE=Console/dsp				; Console interface for demo
;CONSOLE=DAHDI/1
;CONSOLE=Phone/phone0
;IAXINFO=guest					; IAXtel username/password
;IAXINFO=myuser:mypass
;TRUNK=DAHDI/G2					; Trunk interface
;TRUNKMSD=1					; MSD digits to strip (usually 1 or 0)

[public]
include => nexmo-sip-agi
;include => demo
;include => dial
;include => local-channel-inbound

[default]
include => nexmo-sip-agi
;include => demo

[nexmo-sip]
exten => _X.,1,Noop( --- Nexmo SIP call to ${EXTEN} ---)
exten => _X.,n,Wait(2)
exten => _X.,n,Set(__NEXMOID=${SIPCALLID})
exten => _X.,n,Set(ACC=9b1137fc)
;exten => _X.,n,Set(ACC=nadalin)
exten => _X.,n,Set(GW=-1)
exten => _X.,n,Verbose(${NEXMOID})
exten => _X.,n,Wait(5)
exten => _X.,n,GotoIf($["${GW}" = "-1"]?error)
exten => _X.,n,SIPAddHeader(X-Nexmo-Account: ${ACC})
exten => _X.,n,SIPAddHeader(X-Gateway: ${GW})
exten => _X.,n,Dial(SIP/alejandro@localhost:5080)
; didn't set the route
exten => _X.,n(error),Verbose("Unroutable")
exten => _X.,n,Hangup()

[nexmo-sip-agi]
exten => _X.,1,Noop( --- Nexmo SIP call to ${EXTEN} ---)
;exten => _X.,n,Set(__NEXMOID=${SIPCALLID})
exten => _X.,n,Set(ACC=9b1137fc)
exten => _X.,n,AGI(agi://localhost:5050/com.nexmo.voice.core.sip.AsteriskAGIServer?extension=${EXTEN}&uniqueId=${UNIQUEID}&sipCallId=${SIPCALLID}&callerId=${CALLERID(name)}&accountId=${ACC})
exten => _X.,n,SIPAddHeader(X-Nexmo-Account: ${ACC})
exten => _X.,n,SIPAddHeader(X-Gateway: ${GW})
exten => _X.,n,Dial(SIP/alejandro@localhost:5080)
; didn't set the route
exten => _X.,n(error),Verbose("Unroutable")
exten => _X.,n,Hangup()

[demo]
exten => 100,1,Noop( --- Hello World ${EXTEN} --- )
exten => 100,n,Ringing(2)
exten => 100,n,Wait(2)
exten => 100,n,Answer()
exten => 100,n,Playback(tt-monkeys)
exten => 100,n,Wait(1)
exten => 100,n,Hangup()

[dial]
exten => 200,1,Noop( --- Hello World ${EXTEN} --- )
exten => 200,n,Ringing(2)
exten => 200,n,Wait(2)
exten => 200,n,Dial(SIP/lpedrosa@localhost:5080,30)
exten => 200,n,Verbose(${DIALSTATUS})
exten => 200,n,Hangup()

[local-channel-inbound]
exten => 300,1,Noop( -- [inbound] Local Chan Demo -- )
exten => 300,n,Wait(2)
exten => 300,n,Set(DUMMY=4)
exten => 300,n,Dial(Local/301@outbound,10)
exten => 300,n,Verbose("[inbound] Dialed out! ${DIALSTATUS}")
exten => 300,n,Goto(s-${DIALSTATUS},1)
;User did not answer (BUSY)
exten => s-BUSY,1,Wait(2)
exten => s-BUSY,n,Answer()
exten => s-BUSY,n,Playback(tt-monkeys)
exten => s-BUSY,n,Hangup()
;User did not answer (NOANSWER)
exten => s-NOANSWER,1,Wait(2)
exten => s-NOANSWER,n,Hangup()
;Everything else
exten => _s-.,1,Verbose("[inbound] Something else happened!")
exten => _s-.,n,Hangup()

[outbound]
exten => 301,1,Noop( -- [outbound] Local Chan Demo -- )
exten => 301,n,Verbose("[outbound] DUMMY=${DUMMY}")
exten => 301,n,Wait(1)
exten => 301,n,Dial(SIP/lpedrosa@localhost:5080,10)
exten => 301,n,Verbose("[outbound] Dialed out! ${DIALSTATUS}")
exten => 301,n,Wait(10)
