*allowed / blocked ips configuration files guide.

sip is used by 3 different environments:
1. NPE
2. Voice QA aka staging
3. Production

Each of the above environments requires a different set of http-callback-blocked-ip-ranges definitions in the sip.xml
The http-callback-blocked-ip-ranges definitions are kept in a separate file, loaded during run-time.
The file name is environment based :

NPE: npe-blocked-ips.xml
Staging: dev-blocked-ips.xml
Production: live-blocked-ips.xml

The sip.xml include the following http-callback-blocked-ip-ranges configuration definition:
```
 <http-callback-blocked-ip-ranges>
            @callback-blocked-ip-ranges@
 </http-callback-blocked-ip-ranges>
```

@callback-blocked-ip-ranges@ is defined in qa.properties and prod.properties as follows:
```
callback-blocked-ip-ranges=<XML-INCLUDE>allowed-ips/~~dyn~NEXMO_ENV~dev~~-blocked-ips.xml</XML-INCLUDE>
```

dyn~NEXMO_ENV is converted to the relevant environment name, and so selecting the proper [env]-blocked-ips.xml file
to be loaded during runtime.

Should the external file include specific servers names (for example in NPE), a corresponding .dyn 
file should be created.
In our case, this dynamic naming is required only for the NPE, hence the we have **npe-blocked-ips.xml.dyn** file
